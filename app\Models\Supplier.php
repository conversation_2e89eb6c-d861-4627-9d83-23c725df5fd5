<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Supplier extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'company_name',
        'contact_person',
        'phone_number',
        'phone_number_2',
        'email',
        'address',
        'city',
        'country',
        'tax_number',
        'commercial_register',
        'payment_terms',
        'credit_limit',
        'current_balance',
        'notes',
        'is_active',
        'contact_info',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'credit_limit' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'is_active' => 'boolean',
        'contact_info' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get inventory items supplied by this supplier.
     */
    public function inventoryItems(): HasMany
    {
        return $this->hasMany(InventoryItem::class, 'primary_supplier_id');
    }

    /**
     * Get purchase orders for this supplier.
     */
    public function purchaseOrders(): HasMany
    {
        return $this->hasMany(PurchaseOrder::class);
    }

    /**
     * Scope for active suppliers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for suppliers with outstanding balance.
     */
    public function scopeWithBalance($query)
    {
        return $query->where('current_balance', '>', 0);
    }

    /**
     * Scope for suppliers near credit limit.
     */
    public function scopeNearCreditLimit($query, $percentage = 80)
    {
        return $query->whereRaw('current_balance >= (credit_limit * ?)', [$percentage / 100]);
    }

    /**
     * Search suppliers by name, company, or contact info.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('company_name', 'like', "%{$search}%")
              ->orWhere('contact_person', 'like', "%{$search}%")
              ->orWhere('phone_number', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%");
        });
    }

    /**
     * Get payment terms display name.
     */
    public function getPaymentTermsDisplayAttribute(): string
    {
        return match($this->payment_terms) {
            'cash' => __('app.inventory.payment_terms.cash'),
            'credit_7' => __('app.inventory.payment_terms.credit_7'),
            'credit_15' => __('app.inventory.payment_terms.credit_15'),
            'credit_30' => __('app.inventory.payment_terms.credit_30'),
            'credit_60' => __('app.inventory.payment_terms.credit_60'),
            'credit_90' => __('app.inventory.payment_terms.credit_90'),
            default => $this->payment_terms
        };
    }

    /**
     * Get available credit amount.
     */
    public function getAvailableCreditAttribute(): float
    {
        return max(0, $this->credit_limit - $this->current_balance);
    }

    /**
     * Check if supplier is near credit limit.
     */
    public function isNearCreditLimit($percentage = 80): bool
    {
        if ($this->credit_limit <= 0) {
            return false;
        }
        
        return $this->current_balance >= ($this->credit_limit * ($percentage / 100));
    }

    /**
     * Check if supplier has exceeded credit limit.
     */
    public function hasExceededCreditLimit(): bool
    {
        return $this->credit_limit > 0 && $this->current_balance > $this->credit_limit;
    }

    /**
     * Get credit utilization percentage.
     */
    public function getCreditUtilizationAttribute(): float
    {
        if ($this->credit_limit <= 0) {
            return 0;
        }
        
        return min(100, ($this->current_balance / $this->credit_limit) * 100);
    }

    /**
     * Get total purchase amount for a period.
     */
    public function getTotalPurchases($startDate = null, $endDate = null): float
    {
        $query = $this->purchaseOrders()
            ->where('status', '!=', 'cancelled');
            
        if ($startDate) {
            $query->where('order_date', '>=', $startDate);
        }
        
        if ($endDate) {
            $query->where('order_date', '<=', $endDate);
        }
        
        return $query->sum('total_amount');
    }

    /**
     * Get supplier performance metrics.
     */
    public function getPerformanceMetrics(): array
    {
        $orders = $this->purchaseOrders();
        
        return [
            'total_orders' => $orders->count(),
            'completed_orders' => $orders->where('status', 'completed')->count(),
            'on_time_deliveries' => $orders->whereRaw('actual_delivery_date <= expected_delivery_date')->count(),
            'average_delivery_time' => $orders->whereNotNull('actual_delivery_date')
                ->selectRaw('AVG(DATEDIFF(actual_delivery_date, order_date)) as avg_days')
                ->value('avg_days') ?? 0,
            'total_purchase_amount' => $orders->sum('total_amount'),
            'current_balance' => $this->current_balance,
        ];
    }

    /**
     * Update current balance.
     */
    public function updateBalance(float $amount, string $operation = 'add'): void
    {
        if ($operation === 'add') {
            $this->increment('current_balance', $amount);
        } else {
            $this->decrement('current_balance', $amount);
        }
    }

    /**
     * Get primary contact information.
     */
    public function getPrimaryContactAttribute(): string
    {
        $contact = $this->contact_person ?: $this->name;
        
        if ($this->phone_number) {
            $contact .= ' - ' . $this->phone_number;
        }
        
        return $contact;
    }

    /**
     * Get full address string.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->country
        ]);
        
        return implode(', ', $parts);
    }
}
