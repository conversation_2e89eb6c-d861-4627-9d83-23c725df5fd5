<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Supplier extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'company_name',
        'contact_person',
        'phone_number',
        'phone_number_2',
        'email',
        'address',
        'city',
        'country',
        'tax_number',
        'commercial_register',
        'payment_terms',
        'credit_limit',
        'current_balance',
        'notes',
        'is_active',
        'contact_info',
        // New performance tracking fields
        'average_delivery_days',
        'on_time_delivery_rate',
        'quality_rating',
        'total_orders',
        'completed_orders',
        'total_purchase_amount',
        'last_order_date',
        'last_delivery_date',
        'performance_metrics',
        'preferred_status',
        'blacklist_reason'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'credit_limit' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'is_active' => 'boolean',
        'contact_info' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        // New performance tracking casts
        'average_delivery_days' => 'decimal:1',
        'on_time_delivery_rate' => 'decimal:2',
        'quality_rating' => 'decimal:2',
        'total_purchase_amount' => 'decimal:2',
        'last_order_date' => 'date',
        'last_delivery_date' => 'date',
        'performance_metrics' => 'array',
    ];

    /**
     * Get inventory items supplied by this supplier.
     */
    public function inventoryItems(): HasMany
    {
        return $this->hasMany(InventoryItem::class, 'primary_supplier_id');
    }

    /**
     * Get purchase orders for this supplier.
     */
    public function purchaseOrders(): HasMany
    {
        return $this->hasMany(PurchaseOrder::class);
    }

    /**
     * Scope for active suppliers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for suppliers with outstanding balance.
     */
    public function scopeWithBalance($query)
    {
        return $query->where('current_balance', '>', 0);
    }

    /**
     * Scope for suppliers near credit limit.
     */
    public function scopeNearCreditLimit($query, $percentage = 80)
    {
        return $query->whereRaw('current_balance >= (credit_limit * ?)', [$percentage / 100]);
    }

    /**
     * Search suppliers by name, company, or contact info.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('company_name', 'like', "%{$search}%")
              ->orWhere('contact_person', 'like', "%{$search}%")
              ->orWhere('phone_number', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%");
        });
    }

    /**
     * Get payment terms display name.
     */
    public function getPaymentTermsDisplayAttribute(): string
    {
        return match($this->payment_terms) {
            'cash' => __('app.inventory.payment_terms.cash'),
            'credit_7' => __('app.inventory.payment_terms.credit_7'),
            'credit_15' => __('app.inventory.payment_terms.credit_15'),
            'credit_30' => __('app.inventory.payment_terms.credit_30'),
            'credit_60' => __('app.inventory.payment_terms.credit_60'),
            'credit_90' => __('app.inventory.payment_terms.credit_90'),
            default => $this->payment_terms
        };
    }

    /**
     * Get available credit amount.
     */
    public function getAvailableCreditAttribute(): float
    {
        return max(0, $this->credit_limit - $this->current_balance);
    }

    /**
     * Check if supplier is near credit limit.
     */
    public function isNearCreditLimit($percentage = 80): bool
    {
        if ($this->credit_limit <= 0) {
            return false;
        }

        return $this->current_balance >= ($this->credit_limit * ($percentage / 100));
    }

    /**
     * Check if supplier has exceeded credit limit.
     */
    public function hasExceededCreditLimit(): bool
    {
        return $this->credit_limit > 0 && $this->current_balance > $this->credit_limit;
    }

    /**
     * Get credit utilization percentage.
     */
    public function getCreditUtilizationAttribute(): float
    {
        if ($this->credit_limit <= 0) {
            return 0;
        }

        return min(100, ($this->current_balance / $this->credit_limit) * 100);
    }

    /**
     * Get total purchase amount for a period.
     */
    public function getTotalPurchases($startDate = null, $endDate = null): float
    {
        $query = $this->purchaseOrders()
            ->where('status', '!=', 'cancelled');

        if ($startDate) {
            $query->where('order_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('order_date', '<=', $endDate);
        }

        return $query->sum('total_amount');
    }

    /**
     * Get supplier performance metrics.
     */
    public function getPerformanceMetrics(): array
    {
        $orders = $this->purchaseOrders();

        return [
            'total_orders' => $orders->count(),
            'completed_orders' => $orders->where('status', 'completed')->count(),
            'on_time_deliveries' => $orders->whereRaw('actual_delivery_date <= expected_delivery_date')->count(),
            'average_delivery_time' => $orders->whereNotNull('actual_delivery_date')
                ->selectRaw('AVG(DATEDIFF(actual_delivery_date, order_date)) as avg_days')
                ->value('avg_days') ?? 0,
            'total_purchase_amount' => $orders->sum('total_amount'),
            'current_balance' => $this->current_balance,
        ];
    }

    /**
     * Update current balance.
     */
    public function updateBalance(float $amount, string $operation = 'add'): void
    {
        if ($operation === 'add') {
            $this->increment('current_balance', $amount);
        } else {
            $this->decrement('current_balance', $amount);
        }
    }

    /**
     * Get primary contact information.
     */
    public function getPrimaryContactAttribute(): string
    {
        $contact = $this->contact_person ?: $this->name;

        if ($this->phone_number) {
            $contact .= ' - ' . $this->phone_number;
        }

        return $contact;
    }

    /**
     * Get full address string.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->country
        ]);

        return implode(', ', $parts);
    }

    /**
     * Get supplier performance history.
     */
    public function performanceHistory(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(SupplierPerformanceHistory::class);
    }

    /**
     * Get supplier contracts.
     */
    public function contracts(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(SupplierContract::class);
    }

    /**
     * Get inventory reorder rules.
     */
    public function reorderRules(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(InventoryReorderRule::class);
    }

    /**
     * Update supplier performance metrics.
     */
    public function updatePerformanceMetrics(): void
    {
        $orders = $this->purchaseOrders()
            ->where('status', '!=', 'cancelled')
            ->get();

        if ($orders->isEmpty()) {
            return;
        }

        $completedOrders = $orders->where('status', 'completed');

        // Update basic counts
        $this->total_orders = $orders->count();
        $this->completed_orders = $completedOrders->count();
        $this->total_purchase_amount = $orders->sum('total_amount');

        // Update delivery metrics
        if ($completedOrders->isNotEmpty()) {
            $this->average_delivery_days = $completedOrders->whereNotNull('delivery_days_actual')->avg('delivery_days_actual') ?? 0;
            $onTimeDeliveries = $completedOrders->where('delivered_on_time', true)->count();
            $this->on_time_delivery_rate = ($onTimeDeliveries / $completedOrders->count()) * 100;

            // Update quality rating
            $qualityRatings = $completedOrders->whereNotNull('quality_score');
            $this->quality_rating = $qualityRatings->avg('quality_score') ?? 0;
        }

        // Update dates
        $this->last_order_date = $orders->max('order_date');
        $this->last_delivery_date = $completedOrders->max('delivery_date');

        $this->save();
    }

    /**
     * Get supplier performance score.
     */
    public function getPerformanceScore(): float
    {
        if ($this->total_orders === 0) {
            return 0;
        }

        // Weighted performance score
        $deliveryScore = min(100, $this->on_time_delivery_rate);
        $qualityScore = ($this->quality_rating / 5) * 100;
        $completionScore = ($this->completed_orders / $this->total_orders) * 100;

        return ($deliveryScore * 0.4 + $qualityScore * 0.4 + $completionScore * 0.2);
    }

    /**
     * Get preferred status display.
     */
    public function getPreferredStatusDisplayAttribute(): string
    {
        $statuses = [
            'preferred' => app()->getLocale() === 'ar' ? 'مفضل' : 'Preferred',
            'standard' => app()->getLocale() === 'ar' ? 'عادي' : 'Standard',
            'blacklisted' => app()->getLocale() === 'ar' ? 'محظور' : 'Blacklisted',
        ];

        return $statuses[$this->preferred_status] ?? $this->preferred_status;
    }

    /**
     * Get preferred status color for UI.
     */
    public function getPreferredStatusColorAttribute(): string
    {
        return match ($this->preferred_status) {
            'preferred' => 'success',
            'standard' => 'primary',
            'blacklisted' => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Check if supplier is blacklisted.
     */
    public function isBlacklisted(): bool
    {
        return $this->preferred_status === 'blacklisted';
    }

    /**
     * Blacklist supplier.
     */
    public function blacklist(string $reason): void
    {
        $this->update([
            'preferred_status' => 'blacklisted',
            'blacklist_reason' => $reason,
            'is_active' => false
        ]);
    }

    /**
     * Remove from blacklist.
     */
    public function removeFromBlacklist(): void
    {
        $this->update([
            'preferred_status' => 'standard',
            'blacklist_reason' => null,
            'is_active' => true
        ]);
    }

    /**
     * Set as preferred supplier.
     */
    public function setAsPreferred(): void
    {
        $this->update(['preferred_status' => 'preferred']);
    }

    /**
     * Get latest performance evaluation.
     */
    public function getLatestPerformanceEvaluation(): ?SupplierPerformanceHistory
    {
        return $this->performanceHistory()
            ->orderBy('evaluation_date', 'desc')
            ->first();
    }

    /**
     * Scope for preferred suppliers.
     */
    public function scopePreferred($query)
    {
        return $query->where('preferred_status', 'preferred');
    }

    /**
     * Scope for blacklisted suppliers.
     */
    public function scopeBlacklisted($query)
    {
        return $query->where('preferred_status', 'blacklisted');
    }

    /**
     * Scope for suppliers with good performance.
     */
    public function scopeGoodPerformance($query, float $minScore = 75)
    {
        return $query->where('on_time_delivery_rate', '>=', $minScore)
                     ->where('quality_rating', '>=', 3.5);
    }
}
