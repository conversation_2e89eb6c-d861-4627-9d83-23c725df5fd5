<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class RepairStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $statuses = [
            [
                'name' => 'مستلم',
                'description' => 'تم استلام الجهاز وتسجيله في النظام',
                'color' => '#3B82F6', // Blue
                'sort_order' => 1,
                'is_final' => false,
            ],
            [
                'name' => 'قيد التنفيذ',
                'description' => 'الجهاز قيد التشخيص أو الإصلاح حالياً',
                'color' => '#F59E0B', // Amber
                'sort_order' => 2,
                'is_final' => false,
            ],
            [
                'name' => 'في انتظار القطع',
                'description' => 'الإصلاح معلق في انتظار قطع الغيار',
                'color' => '#8B5CF6', // Purple
                'sort_order' => 3,
                'is_final' => false,
            ],
            [
                'name' => 'جاهز للاستلام',
                'description' => 'اكتمل الإصلاح والجهاز جاهز لاستلام العميل',
                'color' => '#10B981', // Green
                'sort_order' => 4,
                'is_final' => false,
            ],
            [
                'name' => 'مكتمل',
                'description' => 'تم استلام الجهاز من قبل العميل',
                'color' => '#059669', // Dark Green
                'sort_order' => 5,
                'is_final' => true,
            ],
            [
                'name' => 'لا يمكن إصلاحه',
                'description' => 'لا يمكن إصلاح الجهاز لأسباب تقنية أو اقتصادية',
                'color' => '#DC2626', // Red
                'sort_order' => 6,
                'is_final' => true,
            ],
            [
                'name' => 'رفض العميل',
                'description' => 'رفض العميل الإصلاح بعد تقديم العرض',
                'color' => '#6B7280', // Gray
                'sort_order' => 7,
                'is_final' => true,
            ],
        ];

        foreach ($statuses as $status) {
            DB::table('repair_statuses')->insert([
                'name' => $status['name'],
                'slug' => Str::slug($status['name']),
                'description' => $status['description'],
                'color' => $status['color'],
                'sort_order' => $status['sort_order'],
                'is_final' => $status['is_final'],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
