<?php

namespace App\Helpers;

use NumberFormatter;
use Illuminate\Support\Facades\App;

class ArabicFormatter
{
    /**
     * Format a number for Arabic locale
     */
    public static function formatNumber($number, $decimals = 0): string
    {
        if (App::getLocale() === 'ar') {
            // Use Arabic-Indic digits
            $formatter = new NumberFormatter('ar_SA', NumberFormatter::DECIMAL);
            $formatter->setAttribute(NumberFormatter::FRACTION_DIGITS, $decimals);
            return $formatter->format($number);
        }
        
        return number_format($number, $decimals);
    }

    /**
     * Format currency for Arabic locale
     */
    public static function formatCurrency($amount, $currency = 'SAR'): string
    {
        if (App::getLocale() === 'ar') {
            $formatter = new NumberFormatter('ar_SA', NumberFormatter::CURRENCY);
            return $formatter->formatCurrency($amount, $currency);
        }
        
        return number_format($amount, 2) . ' ' . $currency;
    }

    /**
     * Format percentage for Arabic locale
     */
    public static function formatPercentage($number, $decimals = 1): string
    {
        if (App::getLocale() === 'ar') {
            $formatter = new NumberFormatter('ar_SA', NumberFormatter::PERCENT);
            $formatter->setAttribute(NumberFormatter::FRACTION_DIGITS, $decimals);
            return $formatter->format($number / 100);
        }
        
        return number_format($number, $decimals) . '%';
    }

    /**
     * Convert Western Arabic numerals to Eastern Arabic numerals
     */
    public static function toArabicNumerals($string): string
    {
        if (App::getLocale() === 'ar') {
            $western = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
            $eastern = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
            return str_replace($western, $eastern, $string);
        }
        
        return $string;
    }

    /**
     * Convert Eastern Arabic numerals to Western Arabic numerals
     */
    public static function toWesternNumerals($string): string
    {
        $eastern = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        $western = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        return str_replace($eastern, $western, $string);
    }

    /**
     * Format file size for Arabic locale
     */
    public static function formatFileSize($bytes): string
    {
        $units = App::getLocale() === 'ar' 
            ? ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت']
            : ['B', 'KB', 'MB', 'GB', 'TB'];

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        $formatted = self::formatNumber($bytes, $pow > 0 ? 2 : 0);
        
        return $formatted . ' ' . $units[$pow];
    }

    /**
     * Format duration in Arabic
     */
    public static function formatDuration($seconds): string
    {
        if (App::getLocale() === 'ar') {
            $units = [
                'ثانية' => 1,
                'دقيقة' => 60,
                'ساعة' => 3600,
                'يوم' => 86400,
                'أسبوع' => 604800,
                'شهر' => 2629746,
                'سنة' => 31556952
            ];
        } else {
            $units = [
                'second' => 1,
                'minute' => 60,
                'hour' => 3600,
                'day' => 86400,
                'week' => 604800,
                'month' => 2629746,
                'year' => 31556952
            ];
        }

        foreach (array_reverse($units, true) as $unit => $value) {
            if ($seconds >= $value) {
                $count = floor($seconds / $value);
                $formatted = self::formatNumber($count);
                return $formatted . ' ' . $unit . ($count > 1 && App::getLocale() !== 'ar' ? 's' : '');
            }
        }

        return self::formatNumber($seconds) . ' ' . (App::getLocale() === 'ar' ? 'ثانية' : 'second' . ($seconds != 1 ? 's' : ''));
    }

    /**
     * Format date range in Arabic
     */
    public static function formatDateRange($startDate, $endDate): string
    {
        if (App::getLocale() === 'ar') {
            return 'من ' . $startDate->toArabicDateString() . ' إلى ' . $endDate->toArabicDateString();
        }
        
        return 'From ' . $startDate->format('M j, Y') . ' to ' . $endDate->format('M j, Y');
    }

    /**
     * Get ordinal number in Arabic
     */
    public static function getOrdinal($number): string
    {
        if (App::getLocale() === 'ar') {
            // Arabic ordinals are complex, simplified version
            return 'الـ' . self::formatNumber($number);
        }
        
        $suffix = ['th', 'st', 'nd', 'rd', 'th', 'th', 'th', 'th', 'th', 'th'];
        if ((($number % 100) >= 11) && (($number % 100) <= 13)) {
            return $number . 'th';
        }
        return $number . $suffix[$number % 10];
    }
}
