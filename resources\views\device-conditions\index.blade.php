@extends('layouts.app')

@section('title', __('app.device_conditions.title'))

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ __('app.device_conditions.title') }}</h1>
            <p class="text-muted mb-0">{{ __('app.device_conditions.manage_description') }}</p>
        </div>
        <a href="{{ route('device-conditions.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>{{ __('app.device_conditions.create') }}
        </a>
    </div>

    <!-- Success Message -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Error Message -->
    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Device Conditions Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">{{ __('app.device_conditions.list') }}</h5>
        </div>
        <div class="card-body">
            @if($deviceConditions->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{{ __('app.device_conditions.name') }}</th>
                                <th>{{ __('app.device_conditions.description') }}</th>
                                <th>{{ __('app.common.sort_order') }}</th>
                                <th>{{ __('app.common.status') }}</th>
                                <th>{{ __('app.common.tickets_count') }}</th>
                                <th>{{ __('app.common.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($deviceConditions as $condition)
                                <tr>
                                    <td>
                                        <div class="fw-bold">{{ $condition->name }}</div>
                                        <small class="text-muted">{{ $condition->slug }}</small>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ $condition->description ?? __('app.common.no_description') }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $condition->sort_order }}</span>
                                    </td>
                                    <td>
                                        @if($condition->is_active)
                                            <span class="badge bg-success">{{ __('app.common.active') }}</span>
                                        @else
                                            <span class="badge bg-danger">{{ __('app.common.inactive') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $condition->repair_tickets_count ?? 0 }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <!-- Quick View Button -->
                                            <a href="{{ route('device-conditions.show', $condition) }}" class="btn btn-sm btn-outline-info" title="{{ __('app.common.view') }}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            <!-- Quick Edit Button -->
                                            <a href="{{ route('device-conditions.edit', $condition) }}" class="btn btn-sm btn-outline-primary" title="{{ __('app.common.edit') }}">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            
                                            <!-- Actions Dropdown -->
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('device-conditions.show', $condition) }}">
                                                            <i class="fas fa-eye me-2"></i>{{ __('app.common.view') }}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('device-conditions.edit', $condition) }}">
                                                            <i class="fas fa-edit me-2"></i>{{ __('app.common.edit') }}
                                                        </a>
                                                    </li>
                                                    @if($condition->repair_tickets_count > 0)
                                                        <li>
                                                            <a class="dropdown-item" href="{{ route('repair-tickets.index', ['condition' => $condition->id]) }}">
                                                                <i class="fas fa-list me-2"></i>{{ __('app.common.view_tickets') }} ({{ $condition->repair_tickets_count }})
                                                            </a>
                                                        </li>
                                                    @endif
                                                    <li><hr class="dropdown-divider"></li>
                                                    @if($condition->repair_tickets_count == 0)
                                                        <li>
                                                            <form action="{{ route('device-conditions.destroy', $condition) }}" method="POST" class="d-inline" onsubmit="return confirm('{{ __('app.device_conditions.confirm_delete') }}')">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="dropdown-item text-danger">
                                                                    <i class="fas fa-trash me-2"></i>{{ __('app.common.delete') }}
                                                                </button>
                                                            </form>
                                                        </li>
                                                    @else
                                                        <li>
                                                            <span class="dropdown-item text-muted" title="{{ __('app.common.cannot_delete_has_tickets') }}">
                                                                <i class="fas fa-trash me-2"></i>{{ __('app.common.delete') }} ({{ __('app.common.disabled') }})
                                                            </span>
                                                        </li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-mobile-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{{ __('app.device_conditions.no_conditions') }}</h5>
                    <p class="text-muted">{{ __('app.device_conditions.create_first_condition') }}</p>
                    <a href="{{ route('device-conditions.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{{ __('app.device_conditions.create') }}
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
