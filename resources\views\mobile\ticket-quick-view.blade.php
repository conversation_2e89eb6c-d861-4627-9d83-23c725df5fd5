@extends('layouts.app')

@section('content')
<div class="container-fluid px-2">
    <!-- Mobile Header -->
    <div class="d-flex justify-content-between align-items-center mb-3 bg-primary text-white p-3 rounded">
        <div>
            <h5 class="mb-0">{{ $ticket->ticket_number }}</h5>
            <small>{{ $ticket->customer->name }}</small>
        </div>
        <div class="text-end">
            <span class="badge bg-{{ $ticket->repairStatus->color ?? 'secondary' }} fs-6">
                {{ $ticket->repairStatus->name }}
            </span>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row g-2 mb-3">
        <div class="col-6">
            <button class="btn btn-success w-100" onclick="updateStatus('completed')">
                <i class="bi bi-check-circle"></i><br>
                <small>مكتمل</small>
            </button>
        </div>
        <div class="col-6">
            <button class="btn btn-warning w-100" onclick="updateStatus('in-progress')">
                <i class="bi bi-gear"></i><br>
                <small>قيد العمل</small>
            </button>
        </div>
        <div class="col-6">
            <button class="btn btn-info w-100" onclick="callCustomer()">
                <i class="bi bi-telephone"></i><br>
                <small>اتصال</small>
            </button>
        </div>
        <div class="col-6">
            <button class="btn btn-secondary w-100" onclick="addNote()">
                <i class="bi bi-chat-text"></i><br>
                <small>ملاحظة</small>
            </button>
        </div>
    </div>

    <!-- Device Info Card -->
    <div class="card mb-3">
        <div class="card-header">
            <h6 class="mb-0"><i class="bi bi-phone"></i> معلومات الجهاز</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-6">
                    <strong>الماركة:</strong><br>
                    {{ $ticket->brand->name }}
                </div>
                <div class="col-6">
                    <strong>الموديل:</strong><br>
                    {{ $ticket->device_model }}
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-6">
                    <strong>الحالة:</strong><br>
                    {{ $ticket->deviceCondition->name }}
                </div>
                <div class="col-6">
                    <strong>تاريخ الاستلام:</strong><br>
                    {{ $ticket->received_date->format('Y-m-d') }}
                </div>
            </div>
        </div>
    </div>

    <!-- Problem Description -->
    <div class="card mb-3">
        <div class="card-header">
            <h6 class="mb-0"><i class="bi bi-exclamation-triangle"></i> المشكلة المبلغ عنها</h6>
        </div>
        <div class="card-body">
            <p class="mb-0">{{ $ticket->reported_problem }}</p>
        </div>
    </div>

    <!-- Technician Comments -->
    <div class="card mb-3">
        <div class="card-header">
            <h6 class="mb-0"><i class="bi bi-chat-dots"></i> ملاحظات الفني</h6>
        </div>
        <div class="card-body">
            <form id="updateCommentsForm">
                @csrf
                <textarea class="form-control" name="technician_comments" rows="3" 
                          placeholder="أضف ملاحظاتك هنا...">{{ $ticket->technician_comments }}</textarea>
                <button type="submit" class="btn btn-primary mt-2 w-100">
                    <i class="bi bi-save"></i> حفظ الملاحظات
                </button>
            </form>
        </div>
    </div>

    <!-- Customer Contact -->
    <div class="card mb-3">
        <div class="card-header">
            <h6 class="mb-0"><i class="bi bi-person"></i> معلومات العميل</h6>
        </div>
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>{{ $ticket->customer->name }}</strong><br>
                    <small class="text-muted">{{ $ticket->customer->phone_number }}</small>
                </div>
                <div>
                    <a href="tel:{{ $ticket->customer->phone_number }}" class="btn btn-success btn-sm">
                        <i class="bi bi-telephone"></i>
                    </a>
                    @if($ticket->customer->email)
                    <a href="mailto:{{ $ticket->customer->email }}" class="btn btn-info btn-sm">
                        <i class="bi bi-envelope"></i>
                    </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Security Pattern (if exists) -->
    @if($ticket->pattern_type !== 'none')
    <div class="card mb-3">
        <div class="card-header">
            <h6 class="mb-0"><i class="bi bi-shield-lock"></i> نمط الحماية</h6>
        </div>
        <div class="card-body">
            @if($ticket->pattern_type === 'text' || $ticket->pattern_type === 'both')
                <div class="mb-2">
                    <strong>النمط النصي:</strong>
                    <span class="badge bg-secondary">{{ $ticket->security_pattern ? '●●●●●●' : 'غير محدد' }}</span>
                </div>
            @endif
            @if($ticket->pattern_type === 'visual' || $ticket->pattern_type === 'both')
                <div>
                    <strong>النمط المرئي:</strong>
                    <span class="badge bg-secondary">{{ $ticket->visual_pattern ? 'محدد' : 'غير محدد' }}</span>
                </div>
            @endif
        </div>
    </div>
    @endif

    <!-- Bottom Navigation -->
    <div class="fixed-bottom bg-white border-top p-2">
        <div class="row g-1">
            <div class="col-3">
                <a href="{{ route('repair-tickets.index') }}" class="btn btn-outline-primary btn-sm w-100">
                    <i class="bi bi-list"></i><br>
                    <small>القائمة</small>
                </a>
            </div>
            <div class="col-3">
                <a href="{{ route('repair-tickets.edit', $ticket) }}" class="btn btn-outline-secondary btn-sm w-100">
                    <i class="bi bi-pencil"></i><br>
                    <small>تعديل</small>
                </a>
            </div>
            <div class="col-3">
                <button class="btn btn-outline-info btn-sm w-100" onclick="shareTicket()">
                    <i class="bi bi-share"></i><br>
                    <small>مشاركة</small>
                </button>
            </div>
            <div class="col-3">
                <a href="{{ route('print-export.ticket.print', $ticket) }}" class="btn btn-outline-success btn-sm w-100">
                    <i class="bi bi-printer"></i><br>
                    <small>طباعة</small>
                </a>
            </div>
        </div>
    </div>

    <!-- Add some bottom padding to account for fixed navigation -->
    <div style="height: 80px;"></div>
</div>

@push('scripts')
<script>
// Mobile-specific JavaScript
function updateStatus(status) {
    if (confirm('هل أنت متأكد من تغيير حالة التذكرة؟')) {
        // Implementation for status update
        fetch(`/repair-tickets/{{ $ticket->id }}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ status: status })
        }).then(response => {
            if (response.ok) {
                location.reload();
            }
        });
    }
}

function callCustomer() {
    if (confirm('اتصال بالعميل {{ $ticket->customer->name }}؟')) {
        window.location.href = 'tel:{{ $ticket->customer->phone_number }}';
    }
}

function addNote() {
    const note = prompt('أضف ملاحظة سريعة:');
    if (note) {
        const currentComments = document.querySelector('[name="technician_comments"]').value;
        const timestamp = new Date().toLocaleString('ar-SA');
        const newComments = currentComments + '\n\n[' + timestamp + '] ' + note;
        document.querySelector('[name="technician_comments"]').value = newComments;
    }
}

function shareTicket() {
    if (navigator.share) {
        navigator.share({
            title: 'تذكرة إصلاح {{ $ticket->ticket_number }}',
            text: 'تذكرة إصلاح للعميل {{ $ticket->customer->name }} - {{ $ticket->repairStatus->name }}',
            url: window.location.href
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        navigator.clipboard.writeText(window.location.href);
        alert('تم نسخ رابط التذكرة');
    }
}

// Auto-save comments
document.getElementById('updateCommentsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(`/repair-tickets/{{ $ticket->id }}`, {
        method: 'PATCH',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: formData
    }).then(response => {
        if (response.ok) {
            // Show success message
            const btn = this.querySelector('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="bi bi-check"></i> تم الحفظ';
            btn.classList.add('btn-success');
            btn.classList.remove('btn-primary');
            
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.classList.add('btn-primary');
                btn.classList.remove('btn-success');
            }, 2000);
        }
    });
});

// Offline detection
window.addEventListener('online', function() {
    document.body.classList.remove('offline');
    // Sync offline data
});

window.addEventListener('offline', function() {
    document.body.classList.add('offline');
    // Show offline indicator
});
</script>
@endpush

@push('styles')
<style>
/* Mobile-specific styles */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 8px !important;
        padding-right: 8px !important;
    }
    
    .card {
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .btn {
        border-radius: 8px;
    }
    
    .fixed-bottom {
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    }
}

/* Offline indicator */
.offline::before {
    content: "غير متصل - Offline";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #dc3545;
    color: white;
    text-align: center;
    padding: 5px;
    z-index: 9999;
    font-size: 12px;
}

/* Touch-friendly buttons */
.btn-sm {
    min-height: 44px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
</style>
@endpush
@endsection
