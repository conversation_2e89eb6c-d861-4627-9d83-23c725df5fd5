@extends('layouts.app')

@section('title', __('app.reports.dashboard'))

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-graph-up"></i> {{ __('app.reports.dashboard') }}
                </h1>

                <div class="d-flex gap-3">
                    <!-- Export Button -->
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="bi bi-download"></i> {{ __('app.export') }}
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ route('print-export.reports.export-pdf', array_merge(request()->query(), ['type' => 'dashboard'])) }}" target="_blank">
                                <i class="bi bi-file-earmark-pdf"></i> {{ __('app.reports.export_pdf') }}
                            </a></li>
                        </ul>
                    </div>

                    <!-- Date Range Filter -->
                    <div class="card">
                        <div class="card-body p-3">
                            <form method="GET" action="{{ route('reports.dashboard') }}" class="d-flex align-items-center gap-3">
                                <div>
                                    <label class="form-label mb-1 small">{{ __('app.reports.from_date') }}</label>
                                    <input type="date"
                                           name="start_date"
                                           value="{{ $dateRange['start_formatted'] }}"
                                           class="form-control form-control-sm">
                                </div>
                                <div>
                                    <label class="form-label mb-1 small">{{ __('app.reports.to_date') }}</label>
                                    <input type="date"
                                           name="end_date"
                                           value="{{ $dateRange['end_formatted'] }}"
                                           class="form-control form-control-sm">
                                </div>
                                <div class="align-self-end">
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="bi bi-funnel"></i> {{ __('app.filter') }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- KPI Cards -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">@arabicNumber($kpis['total_tickets'])</h4>
                                    <small>{{ __('app.dashboard.total_tickets') }}</small>
                                </div>
                                <i class="bi bi-ticket display-6"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">@arabicNumber($kpis['completed_tickets'])</h4>
                                    <small>{{ __('app.repair_tickets.completed') }}</small>
                                </div>
                                <i class="bi bi-check-circle display-6"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">@arabicNumber($kpis['overdue_tickets'])</h4>
                                    <small>{{ __('app.repair_tickets.overdue') }}</small>
                                </div>
                                <i class="bi bi-exclamation-triangle display-6"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">@arabicPercentage($kpis['completion_rate'])</h4>
                                    <small>{{ __('app.reports.completion_rate') }}</small>
                                </div>
                                <i class="bi bi-percent display-6"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">@arabicNumber($kpis['avg_repair_time'])</h4>
                                    <small>{{ __('app.reports.avg_days') }}</small>
                                </div>
                                <i class="bi bi-clock display-6"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="card bg-dark text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">@arabicCurrency($kpis['total_revenue'])</h4>
                                    <small>{{ __('app.reports.revenue') }}</small>
                                </div>
                                <i class="bi bi-currency-dollar display-6"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row 1 -->
            <div class="row mb-4">
                <!-- Tickets Over Time -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-graph-up"></i> {{ __('app.reports.tickets_over_time') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="ticketsOverTimeChart" height="100"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Status Distribution -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-pie-chart"></i> {{ __('app.reports.status_distribution') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="statusDistributionChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row 2 -->
            <div class="row mb-4">
                <!-- Brand Distribution -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-bar-chart"></i> {{ __('app.reports.top_brands') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="brandDistributionChart" height="150"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Average Repair Time -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-clock-history"></i> {{ __('app.reports.avg_repair_time_trend') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="averageRepairTimeChart" height="150"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technician Performance -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-people"></i> {{ __('app.reports.technician_performance') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="technicianPerformanceChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-lightning"></i> {{ __('app.dashboard.quick_actions') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="{{ route('reports.customer-analytics') }}" class="btn btn-outline-primary w-100 mb-2">
                                        <i class="bi bi-person-lines-fill"></i><br>
                                        {{ __('app.reports.customer_analytics') }}
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ route('reports.business-intelligence') }}" class="btn btn-outline-success w-100 mb-2">
                                        <i class="bi bi-graph-up-arrow"></i><br>
                                        {{ __('app.reports.business_intelligence') }}
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ route('repair-tickets.index') }}" class="btn btn-outline-info w-100 mb-2">
                                        <i class="bi bi-ticket-detailed"></i><br>
                                        All Tickets
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ route('customers.index') }}" class="btn btn-outline-warning w-100 mb-2">
                                        <i class="bi bi-people"></i><br>
                                        Customers
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Chart.js default configuration
    Chart.defaults.responsive = true;
    Chart.defaults.maintainAspectRatio = false;

    // Tickets Over Time Chart
    const ticketsOverTimeCtx = document.getElementById('ticketsOverTimeChart').getContext('2d');
    new Chart(ticketsOverTimeCtx, {
        type: 'line',
        data: {
            labels: @json($chartsData['ticketsOverTime']['labels']),
            datasets: [{
                label: 'Tickets',
                data: @json($chartsData['ticketsOverTime']['data']),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Status Distribution Chart
    const statusDistributionCtx = document.getElementById('statusDistributionChart').getContext('2d');
    new Chart(statusDistributionCtx, {
        type: 'doughnut',
        data: {
            labels: @json($chartsData['statusDistribution']['labels']),
            datasets: [{
                data: @json($chartsData['statusDistribution']['data']),
                backgroundColor: @json($chartsData['statusDistribution']['colors'])
            }]
        },
        options: {
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Brand Distribution Chart
    const brandDistributionCtx = document.getElementById('brandDistributionChart').getContext('2d');
    new Chart(brandDistributionCtx, {
        type: 'bar',
        data: {
            labels: @json($chartsData['brandDistribution']['labels']),
            datasets: [{
                label: 'Tickets',
                data: @json($chartsData['brandDistribution']['data']),
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Average Repair Time Chart
    const averageRepairTimeCtx = document.getElementById('averageRepairTimeChart').getContext('2d');
    new Chart(averageRepairTimeCtx, {
        type: 'line',
        data: {
            labels: @json($chartsData['averageRepairTime']['labels']),
            datasets: [{
                label: 'Average Days',
                data: @json($chartsData['averageRepairTime']['data']),
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Technician Performance Chart
    const technicianPerformanceCtx = document.getElementById('technicianPerformanceChart').getContext('2d');
    new Chart(technicianPerformanceCtx, {
        type: 'bar',
        data: {
            labels: @json($chartsData['technicianPerformance']['technicians']),
            datasets: [{
                label: 'Total Tickets',
                data: @json($chartsData['technicianPerformance']['total_tickets']),
                backgroundColor: 'rgba(75, 192, 192, 0.8)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }, {
                label: 'Completed Tickets',
                data: @json($chartsData['technicianPerformance']['completed_tickets']),
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
@endpush
