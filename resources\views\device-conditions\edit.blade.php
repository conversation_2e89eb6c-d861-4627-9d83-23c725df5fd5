@extends('layouts.app')

@section('title', __('app.device_conditions.edit'))

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ __('app.device_conditions.edit') }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('device-conditions.index') }}">{{ __('app.device_conditions.title') }}</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('device-conditions.show', $deviceCondition) }}">{{ $deviceCondition->name }}</a></li>
                    <li class="breadcrumb-item active">{{ __('app.device_conditions.edit') }}</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="{{ route('device-conditions.show', $deviceCondition) }}" class="btn btn-secondary">
                <i class="fas fa-eye me-2"></i>{{ __('app.common.view') }}
            </a>
            <a href="{{ route('device-conditions.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>{{ __('app.common.back') }}
            </a>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.device_conditions.condition_details') }}</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('device-conditions.update', $deviceCondition) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Condition Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label">{{ __('app.device_conditions.name') }} <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $deviceCondition->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">{{ __('app.device_conditions.description') }}</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description', $deviceCondition->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Sort Order -->
                        <div class="mb-3">
                            <label for="sort_order" class="form-label">{{ __('app.common.sort_order') }}</label>
                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                   id="sort_order" name="sort_order" value="{{ old('sort_order', $deviceCondition->sort_order) }}" min="0">
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('app.common.sort_order_help') }}</div>
                        </div>

                        <!-- Status Options -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input @error('is_active') is-invalid @enderror" 
                                       type="checkbox" id="is_active" name="is_active" value="1" 
                                       {{ old('is_active', $deviceCondition->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    {{ __('app.common.is_active') }}
                                </label>
                            </div>
                            @error('is_active')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('app.device_conditions.is_active_help') }}</div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('device-conditions.show', $deviceCondition) }}" class="btn btn-secondary">
                                {{ __('app.common.cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ __('app.common.update') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Usage Statistics -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.common.usage_statistics') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12">
                            <div class="mb-2">
                                <h4 class="text-primary mb-0">{{ $deviceCondition->repair_tickets_count ?? 0 }}</h4>
                                <small class="text-muted">{{ __('app.common.tickets_using_condition') }}</small>
                            </div>
                        </div>
                    </div>
                    @if($deviceCondition->repair_tickets_count > 0)
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {{ __('app.device_conditions.has_tickets_warning') }}
                        </div>
                    @endif
                </div>
            </div>

            <!-- Info Card -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.device_conditions.help_title') }}</h5>
                </div>
                <div class="card-body">
                    <h6>{{ __('app.device_conditions.examples_title') }}</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-mobile-alt text-danger me-2"></i>{{ __('app.device_conditions.example_broken') }}</li>
                        <li><i class="fas fa-tint text-primary me-2"></i>{{ __('app.device_conditions.example_water') }}</li>
                        <li><i class="fas fa-battery-quarter text-warning me-2"></i>{{ __('app.device_conditions.example_battery') }}</li>
                        <li><i class="fas fa-bug text-info me-2"></i>{{ __('app.device_conditions.example_software') }}</li>
                        <li><i class="fas fa-tools text-success me-2"></i>{{ __('app.device_conditions.example_maintenance') }}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
