<?php

namespace App\Http\Controllers;

use App\Models\RepairTicket;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\View\View;
use Barryvdh\DomPDF\Facade\Pdf;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\RepairTicketsExport;
use App\Exports\CustomersExport;
use App\Exports\ReportsExport;

class PrintExportController extends Controller
{
    /**
     * Get PDF options with appropriate font for current locale
     */
    private function getPdfOptions(): array
    {
        $options = [
            'isHtml5ParserEnabled' => true,
            'isRemoteEnabled' => true,
        ];

        // Set Arabic font if locale is Arabic
        if (app()->getLocale() === 'ar') {
            $options['defaultFont'] = 'Cairo';
        } else {
            $options['defaultFont'] = 'Arial';
        }

        return $options;
    }
    /**
     * Print a single repair ticket
     */
    public function printTicket(RepairTicket $repairTicket): Response
    {
        $pdf = Pdf::loadView('print.ticket', compact('repairTicket'))
            ->setPaper('a4', 'portrait')
            ->setOptions($this->getPdfOptions());

        return $pdf->stream("ticket-{$repairTicket->ticket_number}.pdf");
    }

    /**
     * Print multiple repair tickets
     */
    public function printMultipleTickets(Request $request): Response
    {
        $request->validate([
            'ticket_ids' => 'required|array|min:1',
            'ticket_ids.*' => 'exists:repair_tickets,id',
        ]);

        $tickets = RepairTicket::with(['customer', 'brand', 'repairStatus', 'deviceCondition', 'assignedTo'])
            ->whereIn('id', $request->ticket_ids)
            ->get();

        $pdf = Pdf::loadView('print.multiple-tickets', compact('tickets'))
            ->setPaper('a4', 'portrait')
            ->setOptions($this->getPdfOptions());

        return $pdf->stream('multiple-tickets-' . now()->format('Y-m-d') . '.pdf');
    }

    /**
     * Export repair tickets to Excel
     */
    public function exportTicketsExcel(Request $request)
    {
        $filters = $request->only(['status', 'brand', 'search', 'technician', 'start_date', 'end_date']);

        return Excel::download(
            new RepairTicketsExport($filters),
            'repair-tickets-' . now()->format('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Export customers to Excel
     */
    public function exportCustomersExcel(Request $request)
    {
        $filters = $request->only(['search', 'start_date', 'end_date']);

        return Excel::download(
            new CustomersExport($filters),
            'customers-' . now()->format('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Export reports to PDF
     */
    public function exportReportsPdf(Request $request): Response
    {
        $dateRange = $this->getDateRange($request);
        $reportType = $request->get('type', 'dashboard');

        switch ($reportType) {
            case 'customer-analytics':
                return $this->exportCustomerAnalyticsPdf($dateRange);
            case 'business-intelligence':
                return $this->exportBusinessIntelligencePdf($dateRange);
            default:
                return $this->exportDashboardPdf($dateRange);
        }
    }

    /**
     * Export dashboard report to PDF
     */
    private function exportDashboardPdf(array $dateRange): Response
    {
        // Get the same data as dashboard
        $kpis = $this->getKPIs($dateRange);
        $chartsData = $this->getChartsData($dateRange);

        $pdf = Pdf::loadView('print.dashboard-report', compact('kpis', 'chartsData', 'dateRange'))
            ->setPaper('a4', 'landscape')
            ->setOptions([
                'defaultFont' => 'Arial',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
            ]);

        return $pdf->stream('dashboard-report-' . now()->format('Y-m-d') . '.pdf');
    }

    /**
     * Export customer analytics to PDF
     */
    private function exportCustomerAnalyticsPdf(array $dateRange): Response
    {
        $analytics = $this->getCustomerAnalytics($dateRange);

        $pdf = Pdf::loadView('print.customer-analytics-report', compact('analytics', 'dateRange'))
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'defaultFont' => 'Arial',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
            ]);

        return $pdf->stream('customer-analytics-' . now()->format('Y-m-d') . '.pdf');
    }

    /**
     * Export business intelligence to PDF
     */
    private function exportBusinessIntelligencePdf(array $dateRange): Response
    {
        $intelligence = $this->getBusinessIntelligence($dateRange);

        $pdf = Pdf::loadView('print.business-intelligence-report', compact('intelligence', 'dateRange'))
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'defaultFont' => 'Arial',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
            ]);

        return $pdf->stream('business-intelligence-' . now()->format('Y-m-d') . '.pdf');
    }

    /**
     * Show print preview for ticket
     */
    public function previewTicket(RepairTicket $repairTicket): View
    {
        return view('print.ticket-preview', compact('repairTicket'));
    }

    /**
     * Get date range from request
     */
    private function getDateRange(Request $request): array
    {
        $startDate = $request->get('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        return [
            'start' => \Carbon\Carbon::parse($startDate),
            'end' => \Carbon\Carbon::parse($endDate),
            'start_formatted' => $startDate,
            'end_formatted' => $endDate,
        ];
    }

    /**
     * Get KPIs data (reuse from ReportsController logic)
     */
    private function getKPIs(array $dateRange): array
    {
        $query = RepairTicket::whereBetween('received_date', [$dateRange['start'], $dateRange['end']]);

        $totalTickets = $query->count();
        $completedTickets = $query->completed()->count();
        $overdueTickets = $query->overdue()->count();

        $completionRate = $totalTickets > 0 ? round(($completedTickets / $totalTickets) * 100, 1) : 0;

        $totalRevenue = RepairTicket::whereBetween('received_date', [$dateRange['start'], $dateRange['end']])
            ->whereNotNull('final_cost')
            ->sum('final_cost');

        return [
            'total_tickets' => $totalTickets,
            'completed_tickets' => $completedTickets,
            'overdue_tickets' => $overdueTickets,
            'completion_rate' => $completionRate,
            'total_revenue' => $totalRevenue,
        ];
    }

    /**
     * Get charts data for reports
     */
    private function getChartsData(array $dateRange): array
    {
        // Simplified version for PDF export
        return [
            'summary' => 'Charts data processed for PDF export',
            'period' => $dateRange['start']->format('M d, Y') . ' - ' . $dateRange['end']->format('M d, Y'),
        ];
    }

    /**
     * Get customer analytics data
     */
    private function getCustomerAnalytics(array $dateRange): array
    {
        // Simplified customer analytics for PDF
        $topCustomers = RepairTicket::select(
                'customers.name',
                'customers.phone_number',
                \DB::raw('COUNT(*) as ticket_count'),
                \DB::raw('SUM(COALESCE(final_cost, 0)) as total_spent')
            )
            ->join('customers', 'repair_tickets.customer_id', '=', 'customers.id')
            ->whereBetween('repair_tickets.received_date', [$dateRange['start'], $dateRange['end']])
            ->groupBy('customers.id', 'customers.name', 'customers.phone_number')
            ->orderBy('ticket_count', 'desc')
            ->limit(10)
            ->get();

        return [
            'top_customers' => $topCustomers->toArray(),
            'summary' => 'Customer analytics for period: ' . $dateRange['start']->format('M d, Y') . ' - ' . $dateRange['end']->format('M d, Y'),
        ];
    }

    /**
     * Get business intelligence data
     */
    private function getBusinessIntelligence(array $dateRange): array
    {
        // Simplified business intelligence for PDF
        $profitabilityByBrand = RepairTicket::select(
                'brands.name',
                \DB::raw('COUNT(*) as ticket_count'),
                \DB::raw('SUM(COALESCE(final_cost, 0)) as total_revenue'),
                \DB::raw('AVG(COALESCE(final_cost, 0)) as avg_revenue')
            )
            ->join('brands', 'repair_tickets.brand_id', '=', 'brands.id')
            ->whereBetween('repair_tickets.received_date', [$dateRange['start'], $dateRange['end']])
            ->whereNotNull('repair_tickets.final_cost')
            ->groupBy('brands.id', 'brands.name')
            ->orderBy('total_revenue', 'desc')
            ->get();

        return [
            'profitability_by_brand' => $profitabilityByBrand->toArray(),
            'summary' => 'Business intelligence for period: ' . $dateRange['start']->format('M d, Y') . ' - ' . $dateRange['end']->format('M d, Y'),
        ];
    }
}
