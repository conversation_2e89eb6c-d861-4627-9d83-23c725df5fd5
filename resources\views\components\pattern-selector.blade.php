{{-- Pattern Selector Component --}}
@props(['ticket' => null, 'required' => false])

<div class="security-pattern-selector">
    <div class="mb-3">
        <label class="form-label fw-bold">
            <i class="bi bi-shield-lock"></i> نوع كلمة المرور/النمط
            @if($required) <span class="text-danger">*</span> @endif
        </label>
        <div class="form-text mb-3">
            اختر نوع الحماية المستخدم في الجهاز
        </div>
    </div>

    <div class="pattern-type-options">
        <div class="pattern-type-option">
            <input type="radio" 
                   id="pattern_type_none" 
                   name="pattern_type_selection" 
                   value="none" 
                   class="pattern-type-radio"
                   {{ (!$ticket || $ticket->pattern_type === 'none') ? 'checked' : '' }}>
            <label for="pattern_type_none" class="pattern-type-label">
                <div class="pattern-type-icon">
                    <i class="bi bi-x-circle text-muted"></i>
                </div>
                <div class="pattern-type-info">
                    <h4>لا يوجد حماية</h4>
                    <p>الجهاز غير محمي بكلمة مرور أو نمط</p>
                </div>
            </label>
        </div>

        <div class="pattern-type-option">
            <input type="radio" 
                   id="pattern_type_text" 
                   name="pattern_type_selection" 
                   value="text" 
                   class="pattern-type-radio"
                   {{ ($ticket && $ticket->pattern_type === 'text') ? 'checked' : '' }}>
            <label for="pattern_type_text" class="pattern-type-label">
                <div class="pattern-type-icon">
                    <i class="bi bi-key-fill text-primary"></i>
                </div>
                <div class="pattern-type-info">
                    <h4>كلمة مرور نصية</h4>
                    <p>رقم سري، كلمة مرور، أو PIN</p>
                </div>
            </label>
        </div>

        <div class="pattern-type-option">
            <input type="radio" 
                   id="pattern_type_visual" 
                   name="pattern_type_selection" 
                   value="visual" 
                   class="pattern-type-radio"
                   {{ ($ticket && $ticket->pattern_type === 'visual') ? 'checked' : '' }}>
            <label for="pattern_type_visual" class="pattern-type-label">
                <div class="pattern-type-icon">
                    <i class="bi bi-grid-3x3-gap-fill text-success"></i>
                </div>
                <div class="pattern-type-info">
                    <h4>نمط بصري</h4>
                    <p>نمط الفتح بالنقاط (مثل Android)</p>
                </div>
            </label>
        </div>

        <div class="pattern-type-option">
            <input type="radio" 
                   id="pattern_type_both" 
                   name="pattern_type_selection" 
                   value="both" 
                   class="pattern-type-radio"
                   {{ ($ticket && $ticket->pattern_type === 'both') ? 'checked' : '' }}>
            <label for="pattern_type_both" class="pattern-type-label">
                <div class="pattern-type-icon">
                    <i class="bi bi-shield-check text-warning"></i>
                </div>
                <div class="pattern-type-info">
                    <h4>كلاهما</h4>
                    <p>كلمة مرور نصية + نمط بصري</p>
                </div>
            </label>
        </div>
    </div>

    {{-- Text Password Section --}}
    <div id="text-password-section" class="pattern-input-section" style="display: none;">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="bi bi-key-fill"></i> كلمة المرور النصية
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="security_pattern" class="form-label">
                        كلمة المرور/الرقم السري
                    </label>
                    <input type="password"
                           class="form-control @error('security_pattern') is-invalid @enderror"
                           id="security_pattern"
                           name="security_pattern"
                           value="{{ old('security_pattern', $ticket?->security_pattern) }}"
                           placeholder="أدخل كلمة المرور أو الرقم السري">
                    @error('security_pattern')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <div class="form-text">
                        <small class="text-warning">
                            <i class="bi bi-shield-exclamation"></i>
                            سيتم تشفير كلمة المرور لحماية خصوصية العميل
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Visual Pattern Section --}}
    <div id="visual-pattern-section" class="pattern-input-section" style="display: none;">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="bi bi-grid-3x3-gap-fill"></i> النمط البصري
                </h6>
            </div>
            <div class="card-body">
                <div class="pattern-lock-container">
                    <div class="pattern-lock-header">
                        <div class="pattern-lock-title">
                            <i class="bi bi-hand-index"></i>
                            ارسم نمط فتح الجهاز
                        </div>
                        <div class="pattern-lock-instructions">
                            اضغط واسحب لتوصيل النقاط (على الأقل 4 نقاط)
                        </div>
                    </div>
                    
                    <div class="pattern-lock-canvas-wrapper">
                        <div id="pattern-canvas-container"></div>
                    </div>
                    
                    <div class="pattern-lock-controls">
                        <button type="button" id="clear-pattern-btn" class="pattern-lock-btn">
                            <i class="bi bi-arrow-clockwise"></i>
                            مسح النمط
                        </button>
                        <button type="button" id="show-pattern-btn" class="pattern-lock-btn" style="display: none;">
                            <i class="bi bi-eye"></i>
                            عرض النمط
                        </button>
                    </div>
                    
                    <div id="pattern-status" class="pattern-lock-status" style="display: none;"></div>
                </div>
                
                <input type="hidden" 
                       id="visual_pattern" 
                       name="visual_pattern" 
                       value="{{ old('visual_pattern', $ticket?->visual_pattern) }}">
                
                @error('visual_pattern')
                    <div class="alert alert-danger mt-2">
                        <i class="bi bi-exclamation-triangle"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>
    </div>

    {{-- Hidden inputs for form submission --}}
    <input type="hidden" id="pattern_type" name="pattern_type" value="{{ old('pattern_type', $ticket?->pattern_type ?? 'none') }}">
    <input type="hidden" id="device_pattern" name="device_pattern" value="{{ old('device_pattern', $ticket?->device_pattern ? '1' : '0') }}">
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const patternTypeRadios = document.querySelectorAll('input[name="pattern_type_selection"]');
    const textSection = document.getElementById('text-password-section');
    const visualSection = document.getElementById('visual-pattern-section');
    const patternTypeInput = document.getElementById('pattern_type');
    const devicePatternInput = document.getElementById('device_pattern');
    const visualPatternInput = document.getElementById('visual_pattern');
    
    let patternLock = null;
    
    // Initialize pattern lock when visual section is shown
    function initializePatternLock() {
        if (patternLock) return;
        
        patternLock = new PatternLock('pattern-canvas-container', {
            canvasSize: 280,
            dotRadius: 18,
            lineWidth: 6
        });
        
        // Set existing pattern if available
        const existingPattern = visualPatternInput.value;
        if (existingPattern) {
            patternLock.setPattern(existingPattern);
            showPatternStatus('تم تحميل النمط الموجود', 'info');
        }
        
        // Handle pattern completion
        patternLock.onPatternComplete = function(pattern) {
            visualPatternInput.value = pattern;
            showPatternStatus('تم حفظ النمط بنجاح (' + pattern.split('-').length + ' نقاط)', 'success');
        };
        
        patternLock.onPatternTooShort = function() {
            showPatternStatus('النمط قصير جداً - يجب توصيل 4 نقاط على الأقل', 'error');
        };
    }
    
    function showPatternStatus(message, type) {
        const statusDiv = document.getElementById('pattern-status');
        statusDiv.textContent = message;
        statusDiv.className = 'pattern-lock-status ' + type;
        statusDiv.style.display = 'block';
        
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }
    }
    
    function updateSections() {
        const selectedType = document.querySelector('input[name="pattern_type_selection"]:checked').value;
        
        // Hide all sections first
        textSection.style.display = 'none';
        visualSection.style.display = 'none';
        
        // Show relevant sections
        switch (selectedType) {
            case 'text':
                textSection.style.display = 'block';
                devicePatternInput.value = '1';
                break;
            case 'visual':
                visualSection.style.display = 'block';
                devicePatternInput.value = '1';
                setTimeout(initializePatternLock, 100); // Delay to ensure DOM is ready
                break;
            case 'both':
                textSection.style.display = 'block';
                visualSection.style.display = 'block';
                devicePatternInput.value = '1';
                setTimeout(initializePatternLock, 100);
                break;
            case 'none':
            default:
                devicePatternInput.value = '0';
                break;
        }
        
        patternTypeInput.value = selectedType;
    }
    
    // Event listeners
    patternTypeRadios.forEach(radio => {
        radio.addEventListener('change', updateSections);
    });
    
    // Clear pattern button
    document.getElementById('clear-pattern-btn').addEventListener('click', function() {
        if (patternLock) {
            patternLock.clear();
            visualPatternInput.value = '';
            showPatternStatus('تم مسح النمط', 'info');
        }
    });
    
    // Show pattern button (for editing)
    document.getElementById('show-pattern-btn').addEventListener('click', function() {
        if (patternLock && visualPatternInput.value) {
            patternLock.setPattern(visualPatternInput.value);
            showPatternStatus('تم عرض النمط الحالي', 'info');
        }
    });
    
    // Initialize on page load
    updateSections();
    
    // Show the show pattern button if we have an existing pattern
    @if($ticket && $ticket->hasVisualPattern())
        document.getElementById('show-pattern-btn').style.display = 'inline-flex';
    @endif
});
</script>
