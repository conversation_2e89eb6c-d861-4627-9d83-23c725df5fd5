<?php

namespace App\Services;

use App\Models\SecurityLog;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Str;

class SecurityService
{
    const MAX_LOGIN_ATTEMPTS = 5;
    const LOCKOUT_DURATION = 30; // minutes
    const PASSWORD_MIN_LENGTH = 8;
    const SESSION_TIMEOUT = 120; // minutes

    /**
     * Check if IP is blocked due to too many failed login attempts.
     */
    public function isIpBlocked(string $ip): bool
    {
        $cacheKey = "blocked_ip_{$ip}";
        return Cache::has($cacheKey);
    }

    /**
     * Block IP address for failed login attempts.
     */
    public function blockIp(string $ip, int $minutes = self::LOCKOUT_DURATION): void
    {
        $cacheKey = "blocked_ip_{$ip}";
        Cache::put($cacheKey, true, now()->addMinutes($minutes));

        SecurityLog::logSecurityViolation('ip_blocked', [
            'ip' => $ip,
            'duration_minutes' => $minutes,
            'reason' => 'Too many failed login attempts'
        ]);
    }

    /**
     * Check failed login attempts for IP.
     */
    public function checkFailedLoginAttempts(string $ip): int
    {
        return SecurityLog::getFailedLoginsForIp($ip, self::LOCKOUT_DURATION);
    }

    /**
     * Handle failed login attempt.
     */
    public function handleFailedLogin(string $email, string $ip): void
    {
        SecurityLog::logLogin($email, false, ['ip' => $ip]);

        $failedAttempts = $this->checkFailedLoginAttempts($ip);
        
        if ($failedAttempts >= self::MAX_LOGIN_ATTEMPTS) {
            $this->blockIp($ip);
        }
    }

    /**
     * Handle successful login.
     */
    public function handleSuccessfulLogin(User $user): void
    {
        SecurityLog::logLogin($user->email, true);
        $user->updateLastLogin();
        
        // Clear any IP blocks for this IP
        $ip = Request::ip();
        $cacheKey = "blocked_ip_{$ip}";
        Cache::forget($cacheKey);
    }

    /**
     * Validate password strength.
     */
    public function validatePasswordStrength(string $password): array
    {
        $errors = [];

        if (strlen($password) < self::PASSWORD_MIN_LENGTH) {
            $errors[] = "Password must be at least " . self::PASSWORD_MIN_LENGTH . " characters long";
        }

        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = "Password must contain at least one uppercase letter";
        }

        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = "Password must contain at least one lowercase letter";
        }

        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = "Password must contain at least one number";
        }

        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = "Password must contain at least one special character";
        }

        // Check against common passwords
        if ($this->isCommonPassword($password)) {
            $errors[] = "Password is too common, please choose a more secure password";
        }

        return $errors;
    }

    /**
     * Check if password is commonly used.
     */
    private function isCommonPassword(string $password): bool
    {
        $commonPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey',
            '1234567890', 'password1', '123123', 'admin123'
        ];

        return in_array(strtolower($password), $commonPasswords);
    }

    /**
     * Generate secure random password.
     */
    public function generateSecurePassword(int $length = 12): string
    {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

        $password = '';
        
        // Ensure at least one character from each category
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        $password .= $symbols[random_int(0, strlen($symbols) - 1)];

        // Fill the rest randomly
        $allChars = $uppercase . $lowercase . $numbers . $symbols;
        for ($i = 4; $i < $length; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }

        // Shuffle the password
        return str_shuffle($password);
    }

    /**
     * Check for suspicious activity patterns.
     */
    public function detectSuspiciousActivity(User $user): array
    {
        $suspiciousActivities = [];
        $userId = $user->id;
        $ip = Request::ip();

        // Check for multiple login locations
        $recentLogins = SecurityLog::where('user_id', $userId)
            ->where('action', 'login_attempt')
            ->where('status', 'success')
            ->where('created_at', '>=', now()->subHours(24))
            ->distinct('ip_address')
            ->count('ip_address');

        if ($recentLogins > 3) {
            $suspiciousActivities[] = 'Multiple login locations detected';
        }

        // Check for unusual activity hours
        $currentHour = now()->hour;
        if ($currentHour < 6 || $currentHour > 22) {
            $userActivity = SecurityLog::where('user_id', $userId)
                ->whereTime('created_at', '>=', '06:00:00')
                ->whereTime('created_at', '<=', '22:00:00')
                ->where('created_at', '>=', now()->subDays(30))
                ->count();

            $totalActivity = SecurityLog::where('user_id', $userId)
                ->where('created_at', '>=', now()->subDays(30))
                ->count();

            if ($totalActivity > 0 && ($userActivity / $totalActivity) > 0.8) {
                $suspiciousActivities[] = 'Unusual activity hours detected';
            }
        }

        // Check for rapid successive actions
        $recentActions = SecurityLog::where('user_id', $userId)
            ->where('created_at', '>=', now()->subMinutes(5))
            ->count();

        if ($recentActions > 50) {
            $suspiciousActivities[] = 'Unusually high activity rate detected';
        }

        return $suspiciousActivities;
    }

    /**
     * Generate CSRF token.
     */
    public function generateCsrfToken(): string
    {
        return Str::random(40);
    }

    /**
     * Validate CSRF token.
     */
    public function validateCsrfToken(string $token): bool
    {
        return hash_equals(session()->token(), $token);
    }

    /**
     * Sanitize input data.
     */
    public function sanitizeInput(string $input): string
    {
        // Remove potentially dangerous characters
        $input = strip_tags($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        
        // Remove SQL injection patterns
        $sqlPatterns = [
            '/(\s*(union|select|insert|update|delete|drop|create|alter|exec|execute)\s+)/i',
            '/(\s*(or|and)\s+\d+\s*=\s*\d+)/i',
            '/(\s*;\s*)/i'
        ];
        
        foreach ($sqlPatterns as $pattern) {
            $input = preg_replace($pattern, '', $input);
        }

        return trim($input);
    }

    /**
     * Check if request is from trusted source.
     */
    public function isTrustedSource(): bool
    {
        $trustedIps = config('security.trusted_ips', []);
        $currentIp = Request::ip();

        return in_array($currentIp, $trustedIps) || $currentIp === '127.0.0.1';
    }

    /**
     * Log security event with context.
     */
    public function logSecurityEvent(string $event, array $context = []): void
    {
        SecurityLog::logEvent(
            $event,
            $context['resource_type'] ?? null,
            $context['resource_id'] ?? null,
            $context,
            $context['severity'] ?? SecurityLog::SEVERITY_MEDIUM,
            $context['status'] ?? SecurityLog::STATUS_SUCCESS
        );
    }

    /**
     * Get security dashboard data.
     */
    public function getSecurityDashboardData(): array
    {
        return [
            'recent_failed_logins' => SecurityLog::where('action', 'login_attempt')
                ->where('status', 'failed')
                ->where('created_at', '>=', now()->subHours(24))
                ->count(),
            
            'permission_denials' => SecurityLog::where('action', 'permission_denied')
                ->where('created_at', '>=', now()->subHours(24))
                ->count(),
            
            'security_violations' => SecurityLog::where('severity', 'high')
                ->where('created_at', '>=', now()->subHours(24))
                ->count(),
            
            'active_sessions' => User::where('last_login_at', '>=', now()->subMinutes(self::SESSION_TIMEOUT))
                ->count(),
            
            'blocked_ips' => $this->getBlockedIpsCount(),
            
            'recent_events' => SecurityLog::getRecentEvents(24, 10),
        ];
    }

    /**
     * Get count of currently blocked IPs.
     */
    private function getBlockedIpsCount(): int
    {
        // This is a simplified count - in production you might want to store this in database
        return Cache::get('blocked_ips_count', 0);
    }

    /**
     * Force password reset for user.
     */
    public function forcePasswordReset(User $user, string $reason = ''): void
    {
        $user->update([
            'password_reset_required' => true,
            'password_reset_reason' => $reason,
        ]);

        SecurityLog::logPasswordChange($user->id, true);
    }

    /**
     * Check if user session is valid.
     */
    public function isValidSession(User $user): bool
    {
        if (!$user->last_login_at) {
            return false;
        }

        return $user->last_login_at->addMinutes(self::SESSION_TIMEOUT)->isFuture();
    }
}
