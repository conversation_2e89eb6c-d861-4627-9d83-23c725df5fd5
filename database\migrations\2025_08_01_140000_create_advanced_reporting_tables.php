<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Financial Reports Cache
        Schema::create('financial_reports_cache', function (Blueprint $table) {
            $table->id();
            $table->string('report_type');
            $table->string('report_key')->unique();
            $table->date('report_date');
            $table->string('period_type'); // daily, weekly, monthly, quarterly, yearly
            $table->json('report_data');
            $table->json('report_metadata')->nullable();
            $table->timestamp('generated_at');
            $table->timestamp('expires_at')->nullable();
            $table->boolean('is_cached')->default(true);
            $table->timestamps();

            $table->index(['report_type', 'report_date']);
            $table->index(['report_key', 'expires_at']);
        });

        // Profit & Loss Statements
        Schema::create('profit_loss_statements', function (Blueprint $table) {
            $table->id();
            $table->date('statement_date');
            $table->string('period_type'); // monthly, quarterly, yearly
            $table->decimal('total_revenue', 12, 2)->default(0);
            $table->decimal('repair_services_revenue', 12, 2)->default(0);
            $table->decimal('parts_sales_revenue', 12, 2)->default(0);
            $table->decimal('pos_sales_revenue', 12, 2)->default(0);
            $table->decimal('other_revenue', 12, 2)->default(0);
            $table->decimal('cost_of_goods_sold', 12, 2)->default(0);
            $table->decimal('gross_profit', 12, 2)->default(0);
            $table->decimal('operating_expenses', 12, 2)->default(0);
            $table->decimal('staff_salaries', 12, 2)->default(0);
            $table->decimal('rent_utilities', 12, 2)->default(0);
            $table->decimal('marketing_expenses', 12, 2)->default(0);
            $table->decimal('equipment_depreciation', 12, 2)->default(0);
            $table->decimal('other_expenses', 12, 2)->default(0);
            $table->decimal('operating_income', 12, 2)->default(0);
            $table->decimal('interest_income', 12, 2)->default(0);
            $table->decimal('interest_expense', 12, 2)->default(0);
            $table->decimal('net_income', 12, 2)->default(0);
            $table->decimal('gross_profit_margin', 5, 2)->default(0);
            $table->decimal('operating_profit_margin', 5, 2)->default(0);
            $table->decimal('net_profit_margin', 5, 2)->default(0);
            $table->json('detailed_breakdown')->nullable();
            $table->timestamps();

            $table->unique(['statement_date', 'period_type']);
            $table->index('statement_date');
        });

        // Cash Flow Statements
        Schema::create('cash_flow_statements', function (Blueprint $table) {
            $table->id();
            $table->date('statement_date');
            $table->string('period_type'); // monthly, quarterly, yearly
            $table->decimal('opening_cash_balance', 12, 2)->default(0);
            $table->decimal('cash_from_operations', 12, 2)->default(0);
            $table->decimal('cash_receipts_customers', 12, 2)->default(0);
            $table->decimal('cash_payments_suppliers', 12, 2)->default(0);
            $table->decimal('cash_payments_employees', 12, 2)->default(0);
            $table->decimal('cash_payments_operating', 12, 2)->default(0);
            $table->decimal('net_cash_from_operations', 12, 2)->default(0);
            $table->decimal('cash_from_investing', 12, 2)->default(0);
            $table->decimal('equipment_purchases', 12, 2)->default(0);
            $table->decimal('equipment_sales', 12, 2)->default(0);
            $table->decimal('net_cash_from_investing', 12, 2)->default(0);
            $table->decimal('cash_from_financing', 12, 2)->default(0);
            $table->decimal('loans_received', 12, 2)->default(0);
            $table->decimal('loans_repaid', 12, 2)->default(0);
            $table->decimal('owner_investments', 12, 2)->default(0);
            $table->decimal('owner_withdrawals', 12, 2)->default(0);
            $table->decimal('net_cash_from_financing', 12, 2)->default(0);
            $table->decimal('net_change_in_cash', 12, 2)->default(0);
            $table->decimal('closing_cash_balance', 12, 2)->default(0);
            $table->json('detailed_breakdown')->nullable();
            $table->timestamps();

            $table->unique(['statement_date', 'period_type']);
            $table->index('statement_date');
        });

        // Balance Sheet Snapshots
        Schema::create('balance_sheet_snapshots', function (Blueprint $table) {
            $table->id();
            $table->date('snapshot_date');
            $table->decimal('current_assets', 12, 2)->default(0);
            $table->decimal('cash_and_equivalents', 12, 2)->default(0);
            $table->decimal('accounts_receivable', 12, 2)->default(0);
            $table->decimal('inventory_value', 12, 2)->default(0);
            $table->decimal('prepaid_expenses', 12, 2)->default(0);
            $table->decimal('fixed_assets', 12, 2)->default(0);
            $table->decimal('equipment_cost', 12, 2)->default(0);
            $table->decimal('accumulated_depreciation', 12, 2)->default(0);
            $table->decimal('total_assets', 12, 2)->default(0);
            $table->decimal('current_liabilities', 12, 2)->default(0);
            $table->decimal('accounts_payable', 12, 2)->default(0);
            $table->decimal('accrued_expenses', 12, 2)->default(0);
            $table->decimal('short_term_debt', 12, 2)->default(0);
            $table->decimal('long_term_liabilities', 12, 2)->default(0);
            $table->decimal('long_term_debt', 12, 2)->default(0);
            $table->decimal('total_liabilities', 12, 2)->default(0);
            $table->decimal('owners_equity', 12, 2)->default(0);
            $table->decimal('retained_earnings', 12, 2)->default(0);
            $table->decimal('total_equity', 12, 2)->default(0);
            $table->json('detailed_breakdown')->nullable();
            $table->timestamps();

            $table->unique('snapshot_date');
            $table->index('snapshot_date');
        });

        // Key Performance Indicators (KPIs)
        Schema::create('business_kpis', function (Blueprint $table) {
            $table->id();
            $table->date('kpi_date');
            $table->string('period_type'); // daily, weekly, monthly, quarterly, yearly
            $table->decimal('revenue_per_customer', 10, 2)->default(0);
            $table->decimal('average_repair_value', 10, 2)->default(0);
            $table->decimal('customer_acquisition_cost', 10, 2)->default(0);
            $table->decimal('customer_lifetime_value', 10, 2)->default(0);
            $table->decimal('inventory_turnover_ratio', 8, 2)->default(0);
            $table->integer('average_repair_time_hours')->default(0);
            $table->decimal('first_time_fix_rate', 5, 2)->default(0);
            $table->decimal('customer_satisfaction_score', 5, 2)->default(0);
            $table->decimal('employee_productivity_score', 5, 2)->default(0);
            $table->integer('repeat_customer_count')->default(0);
            $table->decimal('repeat_customer_percentage', 5, 2)->default(0);
            $table->integer('new_customer_count')->default(0);
            $table->decimal('customer_retention_rate', 5, 2)->default(0);
            $table->decimal('gross_margin_percentage', 5, 2)->default(0);
            $table->decimal('operating_margin_percentage', 5, 2)->default(0);
            $table->decimal('cash_conversion_cycle_days', 8, 2)->default(0);
            $table->decimal('accounts_receivable_turnover', 8, 2)->default(0);
            $table->decimal('working_capital', 12, 2)->default(0);
            $table->decimal('debt_to_equity_ratio', 8, 2)->default(0);
            $table->decimal('return_on_assets', 5, 2)->default(0);
            $table->decimal('return_on_equity', 5, 2)->default(0);
            $table->json('additional_metrics')->nullable();
            $table->timestamps();

            $table->unique(['kpi_date', 'period_type']);
            $table->index('kpi_date');
        });

        // Expense Categories
        Schema::create('expense_categories', function (Blueprint $table) {
            $table->id();
            $table->string('category_name');
            $table->string('category_code')->unique();
            $table->text('description')->nullable();
            $table->string('parent_category_code')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_tax_deductible')->default(true);
            $table->string('account_code')->nullable();
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['is_active', 'sort_order']);
            $table->index('parent_category_code');
        });

        // Enhanced Expenses table (extending existing)
        Schema::table('expenses', function (Blueprint $table) {
            $table->foreignId('expense_category_id')->nullable()->after('id')->constrained()->onDelete('set null');
            $table->string('expense_number')->nullable()->after('expense_category_id');
            $table->enum('expense_type', ['operational', 'capital', 'administrative', 'marketing', 'other'])->default('operational')->after('amount');
            $table->boolean('is_recurring')->default(false)->after('expense_type');
            $table->string('recurrence_pattern')->nullable()->after('is_recurring');
            $table->date('next_due_date')->nullable()->after('recurrence_pattern');
            $table->boolean('is_tax_deductible')->default(true)->after('next_due_date');
            $table->decimal('tax_amount', 10, 2)->nullable()->after('is_tax_deductible');
            $table->string('reference_number')->nullable()->after('tax_amount');
            $table->json('attachments')->nullable()->after('reference_number');
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])->default('pending')->after('attachments');
            $table->foreignId('approved_by')->nullable()->after('approval_status')->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable()->after('approved_by');
            $table->text('approval_notes')->nullable()->after('approved_at');

            $table->index(['expense_type', 'expense_date']);
            $table->index(['is_recurring', 'next_due_date']);
            $table->index('approval_status');
        });

        // Budget Planning
        Schema::create('budget_plans', function (Blueprint $table) {
            $table->id();
            $table->string('budget_name');
            $table->integer('budget_year');
            $table->string('budget_period'); // monthly, quarterly, yearly
            $table->date('start_date');
            $table->date('end_date');
            $table->decimal('total_revenue_budget', 12, 2)->default(0);
            $table->decimal('total_expense_budget', 12, 2)->default(0);
            $table->decimal('net_income_budget', 12, 2)->default(0);
            $table->json('revenue_breakdown')->nullable();
            $table->json('expense_breakdown')->nullable();
            $table->enum('status', ['draft', 'active', 'completed', 'archived'])->default('draft');
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['budget_year', 'status']);
            $table->index('status');
        });

        // Budget vs Actual Tracking
        Schema::create('budget_actuals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('budget_plan_id')->constrained()->onDelete('cascade');
            $table->date('period_date');
            $table->string('category'); // revenue, expense_category_code
            $table->decimal('budgeted_amount', 12, 2)->default(0);
            $table->decimal('actual_amount', 12, 2)->default(0);
            $table->decimal('variance_amount', 12, 2)->default(0);
            $table->decimal('variance_percentage', 5, 2)->default(0);
            $table->text('variance_notes')->nullable();
            $table->timestamps();

            $table->unique(['budget_plan_id', 'period_date', 'category']);
            $table->index(['budget_plan_id', 'period_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('budget_actuals');
        Schema::dropIfExists('budget_plans');
        
        Schema::table('expenses', function (Blueprint $table) {
            $table->dropForeign(['expense_category_id']);
            $table->dropForeign(['approved_by']);
            $table->dropColumn([
                'expense_category_id',
                'expense_number',
                'expense_type',
                'is_recurring',
                'recurrence_pattern',
                'next_due_date',
                'is_tax_deductible',
                'tax_amount',
                'reference_number',
                'attachments',
                'approval_status',
                'approved_by',
                'approved_at',
                'approval_notes'
            ]);
        });
        
        Schema::dropIfExists('expense_categories');
        Schema::dropIfExists('business_kpis');
        Schema::dropIfExists('balance_sheet_snapshots');
        Schema::dropIfExists('cash_flow_statements');
        Schema::dropIfExists('profit_loss_statements');
        Schema::dropIfExists('financial_reports_cache');
    }
};
