<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class WhatsAppCustomerPreference extends Model
{
    use HasFactory;

    protected $table = 'whatsapp_customer_preferences';

    protected $fillable = [
        'customer_phone',
        'customer_id',
        'whatsapp_enabled',
        'sms_fallback_enabled',
        'preferred_language',
        'timezone',
        'status_updates',
        'appointment_reminders',
        'payment_reminders',
        'marketing_messages',
        'promotional_offers',
        'quiet_hours_start',
        'quiet_hours_end',
        'preferred_days',
        'max_messages_per_day',
        'max_marketing_per_week',
        'last_marketing_sent',
        'opted_in_at',
        'opted_out_at',
        'opt_out_reason',
        'is_opted_out',
        'blocked_templates',
        'preferred_templates',
        'total_messages_received',
        'total_messages_sent',
        'response_rate',
        'last_interaction_at',
        'last_response_at',
        'satisfaction_score',
        'satisfaction_votes',
        'last_feedback_at',
        'data_processing_consent',
        'consent_given_at',
        'consent_source',
        'marketing_consent',
    ];

    protected $casts = [
        'whatsapp_enabled' => 'boolean',
        'sms_fallback_enabled' => 'boolean',
        'status_updates' => 'boolean',
        'appointment_reminders' => 'boolean',
        'payment_reminders' => 'boolean',
        'marketing_messages' => 'boolean',
        'promotional_offers' => 'boolean',
        'preferred_days' => 'array',
        'is_opted_out' => 'boolean',
        'blocked_templates' => 'array',
        'preferred_templates' => 'array',
        'data_processing_consent' => 'boolean',
        'marketing_consent' => 'boolean',
        'quiet_hours_start' => 'datetime:H:i',
        'quiet_hours_end' => 'datetime:H:i',
        'last_marketing_sent' => 'datetime',
        'opted_in_at' => 'datetime',
        'opted_out_at' => 'datetime',
        'last_interaction_at' => 'datetime',
        'last_response_at' => 'datetime',
        'last_feedback_at' => 'datetime',
        'consent_given_at' => 'datetime',
        'satisfaction_score' => 'decimal:2',
        'response_rate' => 'decimal:2',
    ];

    /**
     * Get the customer that owns the preferences.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * Scope for opted-in customers.
     */
    public function scopeOptedIn($query)
    {
        return $query->where('whatsapp_enabled', true)
                    ->where('is_opted_out', false);
    }

    /**
     * Scope for customers who accept marketing.
     */
    public function scopeAcceptsMarketing($query)
    {
        return $query->optedIn()
                    ->where('marketing_messages', true)
                    ->where('marketing_consent', true);
    }

    /**
     * Scope by preferred language.
     */
    public function scopeByLanguage($query, $language)
    {
        return $query->where('preferred_language', $language);
    }

    /**
     * Get or create preferences for a customer.
     */
    public static function getOrCreateForCustomer(string $customerPhone, ?int $customerId = null): self
    {
        return static::firstOrCreate(
            ['customer_phone' => $customerPhone],
            [
                'customer_id' => $customerId,
                'whatsapp_enabled' => true,
                'preferred_language' => 'ar',
                'timezone' => 'Asia/Riyadh',
                'status_updates' => true,
                'appointment_reminders' => true,
                'payment_reminders' => true,
                'marketing_messages' => false,
                'promotional_offers' => false,
                'data_processing_consent' => true,
                'consent_given_at' => now(),
                'consent_source' => 'whatsapp',
            ]
        );
    }

    /**
     * Check if customer can receive WhatsApp messages.
     */
    public function canReceiveWhatsApp(): bool
    {
        return $this->whatsapp_enabled && !$this->is_opted_out;
    }

    /**
     * Check if customer can receive marketing messages.
     */
    public function canReceiveMarketing(): bool
    {
        return $this->canReceiveWhatsApp()
            && $this->marketing_messages
            && $this->marketing_consent;
    }

    /**
     * Check if customer can receive a specific message type.
     */
    public function canReceiveMessageType(string $type): bool
    {
        if (!$this->canReceiveWhatsApp()) {
            return false;
        }

        return match($type) {
            'status_update' => $this->status_updates,
            'appointment' => $this->appointment_reminders,
            'payment' => $this->payment_reminders,
            'marketing' => $this->canReceiveMarketing(),
            'promotional' => $this->promotional_offers && $this->marketing_consent,
            default => true,
        };
    }

    /**
     * Check if it's within quiet hours.
     */
    public function isQuietHours(?Carbon $time = null): bool
    {
        $time = $time ?? now($this->timezone);
        $currentTime = $time->format('H:i');

        $quietStart = $this->quiet_hours_start->format('H:i');
        $quietEnd = $this->quiet_hours_end->format('H:i');

        // Handle overnight quiet hours (e.g., 22:00 to 08:00)
        if ($quietStart > $quietEnd) {
            return $currentTime >= $quietStart || $currentTime <= $quietEnd;
        }

        return $currentTime >= $quietStart && $currentTime <= $quietEnd;
    }

    /**
     * Check if today is a preferred day for messages.
     */
    public function isPreferredDay(?Carbon $date = null): bool
    {
        if (empty($this->preferred_days)) {
            return true; // No restriction
        }

        $date = $date ?? now($this->timezone);
        $dayOfWeek = $date->dayOfWeek; // 0 = Sunday, 6 = Saturday

        return in_array($dayOfWeek, $this->preferred_days);
    }

    /**
     * Check if daily message limit is reached.
     */
    public function isDailyLimitReached(): bool
    {
        $today = now($this->timezone)->startOfDay();
        $messagesCount = WhatsAppMessage::where('customer_phone', $this->customer_phone)
            ->where('direction', 'outbound')
            ->where('created_at', '>=', $today)
            ->count();

        return $messagesCount >= $this->max_messages_per_day;
    }

    /**
     * Check if weekly marketing limit is reached.
     */
    public function isWeeklyMarketingLimitReached(): bool
    {
        $weekStart = now($this->timezone)->startOfWeek();
        $marketingCount = WhatsAppTemplateUsage::where('customer_phone', $this->customer_phone)
            ->whereHas('template', function ($query) {
                $query->whereHas('category', function ($q) {
                    $q->where('whatsapp_category', 'MARKETING');
                });
            })
            ->where('created_at', '>=', $weekStart)
            ->count();

        return $marketingCount >= $this->max_marketing_per_week;
    }

    /**
     * Check if template is blocked.
     */
    public function isTemplateBlocked(int $templateId): bool
    {
        return in_array($templateId, $this->blocked_templates ?? []);
    }

    /**
     * Block a template.
     */
    public function blockTemplate(int $templateId): void
    {
        $blocked = $this->blocked_templates ?? [];
        if (!in_array($templateId, $blocked)) {
            $blocked[] = $templateId;
            $this->update(['blocked_templates' => $blocked]);
        }
    }

    /**
     * Unblock a template.
     */
    public function unblockTemplate(int $templateId): void
    {
        $blocked = $this->blocked_templates ?? [];
        $blocked = array_filter($blocked, fn($id) => $id !== $templateId);
        $this->update(['blocked_templates' => array_values($blocked)]);
    }

    /**
     * Opt out from WhatsApp messages.
     */
    public function optOut(string $reason = null): void
    {
        $this->update([
            'is_opted_out' => true,
            'opted_out_at' => now(),
            'opt_out_reason' => $reason,
            'whatsapp_enabled' => false,
        ]);
    }

    /**
     * Opt back in to WhatsApp messages.
     */
    public function optIn(): void
    {
        $this->update([
            'is_opted_out' => false,
            'opted_in_at' => now(),
            'whatsapp_enabled' => true,
            'opted_out_at' => null,
            'opt_out_reason' => null,
        ]);
    }

    /**
     * Record message interaction.
     */
    public function recordInteraction(bool $isResponse = false): void
    {
        $updates = [
            'last_interaction_at' => now(),
        ];

        if ($isResponse) {
            $updates['last_response_at'] = now();
            $updates['total_messages_sent'] = $this->total_messages_sent + 1;
        } else {
            $updates['total_messages_received'] = $this->total_messages_received + 1;
        }

        $this->update($updates);
        $this->calculateResponseRate();
    }

    /**
     * Calculate response rate.
     */
    protected function calculateResponseRate(): void
    {
        if ($this->total_messages_received > 0) {
            $rate = ($this->total_messages_sent / $this->total_messages_received) * 100;
            $this->update(['response_rate' => round($rate, 2)]);
        }
    }

    /**
     * Record satisfaction feedback.
     */
    public function recordSatisfaction(float $score): void
    {
        $currentTotal = $this->satisfaction_score * $this->satisfaction_votes;
        $newTotal = $currentTotal + $score;
        $newVotes = $this->satisfaction_votes + 1;
        $newAverage = $newTotal / $newVotes;

        $this->update([
            'satisfaction_score' => round($newAverage, 2),
            'satisfaction_votes' => $newVotes,
            'last_feedback_at' => now(),
        ]);
    }

    /**
     * Get customer engagement level.
     */
    public function getEngagementLevelAttribute(): string
    {
        if ($this->response_rate >= 80) {
            return 'high';
        } elseif ($this->response_rate >= 40) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Get satisfaction level.
     */
    public function getSatisfactionLevelAttribute(): string
    {
        if (!$this->satisfaction_score) {
            return 'unknown';
        }

        if ($this->satisfaction_score >= 4.5) {
            return 'excellent';
        } elseif ($this->satisfaction_score >= 3.5) {
            return 'good';
        } elseif ($this->satisfaction_score >= 2.5) {
            return 'average';
        } else {
            return 'poor';
        }
    }
}
