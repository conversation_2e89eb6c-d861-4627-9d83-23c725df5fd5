<?php

namespace App\Http\Controllers;

use App\Models\PaymentMethodConfig;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;

class PaymentMethodConfigController extends Controller
{
    /**
     * Display a listing of payment methods.
     */
    public function index(Request $request): View
    {
        $query = PaymentMethodConfig::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('method_name_ar', 'like', "%{$search}%")
                  ->orWhere('method_name_en', 'like', "%{$search}%")
                  ->orWhere('method_code', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $paymentMethods = $query->orderBy('sort_order')
            ->orderBy('method_name_ar')
            ->paginate(15)
            ->withQueryString();

        return view('payment-methods.index', compact('paymentMethods'));
    }

    /**
     * Show the form for creating a new payment method.
     */
    public function create(): View
    {
        return view('payment-methods.create');
    }

    /**
     * Store a newly created payment method.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'method_code' => 'required|string|max:50|unique:payment_method_configs',
            'method_name_ar' => 'required|string|max:255',
            'method_name_en' => 'required|string|max:255',
            'icon_class' => 'nullable|string|max:100',
            'is_active' => 'boolean',
            'requires_reference' => 'boolean',
            'requires_card_info' => 'boolean',
            'requires_bank_info' => 'boolean',
            'processing_fee_percentage' => 'nullable|numeric|min:0|max:100',
            'processing_fee_fixed' => 'nullable|numeric|min:0',
            'settlement_days' => 'nullable|integer|min:0',
            'additional_fields' => 'nullable|json',
            'validation_rules' => 'nullable|json',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        // Set defaults
        $validated['is_active'] = $validated['is_active'] ?? true;
        $validated['requires_reference'] = $validated['requires_reference'] ?? false;
        $validated['requires_card_info'] = $validated['requires_card_info'] ?? false;
        $validated['requires_bank_info'] = $validated['requires_bank_info'] ?? false;
        $validated['processing_fee_percentage'] = $validated['processing_fee_percentage'] ?? 0;
        $validated['processing_fee_fixed'] = $validated['processing_fee_fixed'] ?? 0;
        $validated['settlement_days'] = $validated['settlement_days'] ?? 0;
        $validated['sort_order'] = $validated['sort_order'] ?? 999;

        // Parse JSON fields
        if (isset($validated['additional_fields'])) {
            $validated['additional_fields'] = json_decode($validated['additional_fields'], true);
        }
        if (isset($validated['validation_rules'])) {
            $validated['validation_rules'] = json_decode($validated['validation_rules'], true);
        }

        $paymentMethod = PaymentMethodConfig::create($validated);

        return redirect()->route('payment-methods.show', $paymentMethod)
            ->with('success', 'تم إنشاء طريقة الدفع بنجاح');
    }

    /**
     * Display the specified payment method.
     */
    public function show(PaymentMethodConfig $paymentMethod): View
    {
        return view('payment-methods.show', compact('paymentMethod'));
    }

    /**
     * Show the form for editing the specified payment method.
     */
    public function edit(PaymentMethodConfig $paymentMethod): View
    {
        return view('payment-methods.edit', compact('paymentMethod'));
    }

    /**
     * Update the specified payment method.
     */
    public function update(Request $request, PaymentMethodConfig $paymentMethod): RedirectResponse
    {
        $validated = $request->validate([
            'method_code' => 'required|string|max:50|unique:payment_method_configs,method_code,' . $paymentMethod->id,
            'method_name_ar' => 'required|string|max:255',
            'method_name_en' => 'required|string|max:255',
            'icon_class' => 'nullable|string|max:100',
            'is_active' => 'boolean',
            'requires_reference' => 'boolean',
            'requires_card_info' => 'boolean',
            'requires_bank_info' => 'boolean',
            'processing_fee_percentage' => 'nullable|numeric|min:0|max:100',
            'processing_fee_fixed' => 'nullable|numeric|min:0',
            'settlement_days' => 'nullable|integer|min:0',
            'additional_fields' => 'nullable|json',
            'validation_rules' => 'nullable|json',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        // Set defaults
        $validated['is_active'] = $validated['is_active'] ?? false;
        $validated['requires_reference'] = $validated['requires_reference'] ?? false;
        $validated['requires_card_info'] = $validated['requires_card_info'] ?? false;
        $validated['requires_bank_info'] = $validated['requires_bank_info'] ?? false;
        $validated['processing_fee_percentage'] = $validated['processing_fee_percentage'] ?? 0;
        $validated['processing_fee_fixed'] = $validated['processing_fee_fixed'] ?? 0;
        $validated['settlement_days'] = $validated['settlement_days'] ?? 0;
        $validated['sort_order'] = $validated['sort_order'] ?? 999;

        // Parse JSON fields
        if (isset($validated['additional_fields'])) {
            $validated['additional_fields'] = json_decode($validated['additional_fields'], true);
        }
        if (isset($validated['validation_rules'])) {
            $validated['validation_rules'] = json_decode($validated['validation_rules'], true);
        }

        $paymentMethod->update($validated);

        return redirect()->route('payment-methods.show', $paymentMethod)
            ->with('success', 'تم تحديث طريقة الدفع بنجاح');
    }

    /**
     * Remove the specified payment method.
     */
    public function destroy(PaymentMethodConfig $paymentMethod): RedirectResponse
    {
        // Check if payment method is being used
        $paymentsCount = \App\Models\Payment::where('payment_method', $paymentMethod->method_code)->count();
        
        if ($paymentsCount > 0) {
            return back()->with('error', 'لا يمكن حذف طريقة الدفع لأنها مستخدمة في مدفوعات موجودة');
        }

        $paymentMethod->delete();

        return redirect()->route('payment-methods.index')
            ->with('success', 'تم حذف طريقة الدفع بنجاح');
    }

    /**
     * Seed default payment methods.
     */
    public function seedDefaults(): RedirectResponse
    {
        try {
            PaymentMethodConfig::seedDefaultMethods();
            return back()->with('success', 'تم إضافة طرق الدفع الافتراضية بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء إضافة طرق الدفع الافتراضية: ' . $e->getMessage());
        }
    }

    /**
     * Get active payment methods for API.
     */
    public function getActivePaymentMethods(): JsonResponse
    {
        $paymentMethods = PaymentMethodConfig::getActivePaymentMethods();
        
        return response()->json([
            'success' => true,
            'data' => $paymentMethods->map(function ($method) {
                return [
                    'id' => $method->id,
                    'code' => $method->method_code,
                    'name' => $method->display_name,
                    'icon' => $method->icon_class,
                    'requires_reference' => $method->requires_reference,
                    'requires_card_info' => $method->requires_card_info,
                    'requires_bank_info' => $method->requires_bank_info,
                    'processing_fee_percentage' => $method->processing_fee_percentage,
                    'processing_fee_fixed' => $method->processing_fee_fixed,
                    'additional_fields' => $method->getAdditionalFields(),
                    'validation_rules' => $method->getValidationRules(),
                ];
            })
        ]);
    }

    /**
     * Calculate processing fee for amount.
     */
    public function calculateProcessingFee(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'method_code' => 'required|exists:payment_method_configs,method_code',
            'amount' => 'required|numeric|min:0'
        ]);

        $paymentMethod = PaymentMethodConfig::where('method_code', $validated['method_code'])->first();
        $processingFee = $paymentMethod->calculateProcessingFee($validated['amount']);

        return response()->json([
            'success' => true,
            'data' => [
                'amount' => $validated['amount'],
                'processing_fee' => $processingFee,
                'net_amount' => $validated['amount'] - $processingFee,
                'method' => [
                    'code' => $paymentMethod->method_code,
                    'name' => $paymentMethod->display_name,
                    'fee_percentage' => $paymentMethod->processing_fee_percentage,
                    'fee_fixed' => $paymentMethod->processing_fee_fixed,
                ]
            ]
        ]);
    }

    /**
     * Toggle payment method status.
     */
    public function toggleStatus(PaymentMethodConfig $paymentMethod): RedirectResponse
    {
        $paymentMethod->update(['is_active' => !$paymentMethod->is_active]);
        
        $status = $paymentMethod->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';
        
        return back()->with('success', $status . ' طريقة الدفع بنجاح');
    }

    /**
     * Update sort order.
     */
    public function updateSortOrder(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:payment_method_configs,id',
            'items.*.sort_order' => 'required|integer|min:0'
        ]);

        foreach ($validated['items'] as $item) {
            PaymentMethodConfig::where('id', $item['id'])
                ->update(['sort_order' => $item['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث ترتيب طرق الدفع بنجاح'
        ]);
    }
}
