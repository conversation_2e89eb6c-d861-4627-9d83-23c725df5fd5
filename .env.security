# Security Configuration Template
# Copy these settings to your .env file

# Application Security
APP_DEBUG=false
APP_ENV=production

# Session Security
SESSION_LIFETIME=480
SESSION_ENCRYPT=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict

# Database Security
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
# Use strong database credentials
DB_DATABASE=nj_repair_shop
DB_USERNAME=your_db_user
DB_PASSWORD=your_strong_db_password

# Cache Security
CACHE_DRIVER=redis
REDIS_PASSWORD=your_redis_password

# Mail Security (for password resets)
MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls

# Rate Limiting
THROTTLE_LOGIN_ATTEMPTS=5
THROTTLE_LOGIN_DECAY=15
THROTTLE_API_ATTEMPTS=100
THROTTLE_API_DECAY=1

# Security Headers
SECURITY_HEADERS_ENABLED=true
CSP_ENABLED=true
HSTS_ENABLED=true

# Backup Security
BACKUP_ENCRYPTION_KEY=your_backup_encryption_key
BACKUP_RETENTION_DAYS=30

# Logging Security
LOG_CHANNEL=daily
LOG_LEVEL=warning
LOG_DEPRECATIONS_CHANNEL=null

# Additional Security
BCRYPT_ROUNDS=12
PASSWORD_TIMEOUT=10800
