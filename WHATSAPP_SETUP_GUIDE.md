# 📱 WhatsApp Business API Setup Guide for NJ Repair Shop

## 🎯 Overview
This guide will help you set up the WhatsApp Business API integration for your repair shop management system. The integration provides 24/7 automated customer service in Arabic with self-service capabilities.

## 📋 Prerequisites
- Facebook Business Account
- WhatsApp Business Account
- Verified phone number
- SSL-enabled domain (https://njrepair.com)
- Laravel application running

## 🚀 Step 1: Facebook App Setup

### 1.1 Create Facebook App
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Click "Create App" → "Business" → "Continue"
3. Enter app name: "NJ Repair Shop WhatsApp"
4. Enter contact email and select Business Manager account
5. Click "Create App"

### 1.2 Add WhatsApp Product
1. In your app dashboard, click "Add Product"
2. Find "WhatsApp" and click "Set Up"
3. Select your Business Manager account
4. Click "Continue"

### 1.3 Get API Credentials
1. Go to WhatsApp → Getting Started
2. Copy the following values:
   - **App ID**: Found in App Settings → Basic
   - **App Secret**: Found in App Settings → Basic
   - **Access Token**: Temporary token from Getting Started
   - **Phone Number ID**: From Getting Started
   - **Business Account ID**: From Getting Started

## 🔧 Step 2: Laravel Configuration

### 2.1 Update Environment Variables
Add these to your `.env` file:

```env
# WhatsApp Business API Configuration
WHATSAPP_API_VERSION=v18.0
WHATSAPP_BASE_URL=https://graph.facebook.com
WHATSAPP_APP_ID=your_facebook_app_id
WHATSAPP_APP_SECRET=your_facebook_app_secret
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=nj_repair_shop_webhook_2024
WHATSAPP_WEBHOOK_URL=https://njrepair.com/api/whatsapp/webhook
WHATSAPP_BUSINESS_NAME="ورشة إصلاح NJ"
WHATSAPP_BUSINESS_PHONE="+************"
WHATSAPP_AUTO_RESPONSES=true
WHATSAPP_TICKET_QUERIES=true
WHATSAPP_LOG_MESSAGES=true
```

### 2.2 Run Database Migrations
```bash
php artisan migrate
```

### 2.3 Clear Caches
```bash
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

## 🔗 Step 3: Webhook Configuration

### 3.1 Set Webhook URL
1. In Facebook App → WhatsApp → Configuration
2. Set Webhook URL: `https://njrepair.com/api/whatsapp/webhook`
3. Set Verify Token: `nj_repair_shop_webhook_2024`
4. Subscribe to these fields:
   - `messages`
   - `message_deliveries`
   - `message_reads`

### 3.2 Verify Webhook
1. Click "Verify and Save"
2. Facebook will send a GET request to verify your webhook
3. Check Laravel logs to confirm verification

## 📞 Step 4: Phone Number Setup

### 4.1 Add Phone Number
1. Go to WhatsApp → Phone Numbers
2. Click "Add Phone Number"
3. Enter your business phone number
4. Complete verification process

### 4.2 Configure Business Profile
```bash
# Update business profile via API
curl -X POST "https://graph.facebook.com/v18.0/YOUR_PHONE_NUMBER_ID/whatsapp_business_profile" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "about": "ورشة إصلاح NJ - خدمة عملاء 24/7",
    "address": "شارع الملك فهد، الرياض، المملكة العربية السعودية",
    "description": "ورشة متخصصة في إصلاح الهواتف والحاسوب",
    "email": "<EMAIL>",
    "websites": ["https://njrepair.com"]
  }'
```

## 🧪 Step 5: Testing

### 5.1 Run Integration Tests
```bash
php tests/whatsapp-integration-test.php
```

### 5.2 Test Message Sending
```bash
# Send test message (development only)
curl -X POST "https://njrepair.com/api/whatsapp/test-message" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "************",
    "message": "Test message from NJ Repair Shop"
  }'
```

### 5.3 Test Bot Responses
Send these messages to your WhatsApp number:
- `مرحبا` (Should trigger welcome message)
- `حالة الجهاز` (Should ask for phone number)
- `ساعات العمل` (Should show business hours)
- `الموقع` (Should send location)

## 🔒 Step 6: Security & Production

### 6.1 Generate Permanent Access Token
1. Go to System Users in Business Manager
2. Create new system user: "NJ Repair WhatsApp Bot"
3. Assign WhatsApp Business Management permission
4. Generate permanent access token
5. Update `WHATSAPP_ACCESS_TOKEN` in `.env`

### 6.2 Enable Webhook Signature Verification
Ensure your webhook handler verifies signatures:
```php
// This is already implemented in WhatsAppController
$signature = $request->header('X-Hub-Signature-256');
if (!$this->whatsappService->verifyWebhookSignature($request->getContent(), $signature)) {
    return response('Unauthorized', 401);
}
```

### 6.3 Rate Limiting
The system includes built-in rate limiting:
- 10 messages per minute per customer
- 60 messages per minute globally
- Automatic rate limit reset

## 📊 Step 7: Monitoring & Analytics

### 7.1 Access WhatsApp Dashboard
Visit: `https://njrepair.com/whatsapp/dashboard`

### 7.2 Monitor Logs
```bash
# View WhatsApp logs
tail -f storage/logs/laravel.log | grep WhatsApp

# View webhook requests
tail -f storage/logs/laravel.log | grep webhook
```

### 7.3 Analytics API
```bash
# Get analytics data
curl "https://njrepair.com/api/whatsapp/analytics" \
  -H "Authorization: Bearer YOUR_API_TOKEN"
```

## 🎯 Step 8: Arabic Message Templates

### 8.1 Create Message Templates
Create these templates in Facebook Business Manager:

**Welcome Message (welcome_message_ar)**
```
أهلاً وسهلاً {{1}} 👋

مرحباً بك في ورشة إصلاح NJ
كيف يمكنني مساعدتك اليوم؟

🔍 الاستفسار عن حالة جهازك
🕐 معرفة ساعات العمل  
📍 معرفة موقع الورشة
```

**Status Update (ticket_status_update_ar)**
```
{{1}} تحديث حالة جهازك

عزيزي {{2}}،
تم تحديث حالة جهازك رقم {{3}}
الحالة الجديدة: {{4}}

ورشة إصلاح NJ
```

## 🚨 Troubleshooting

### Common Issues:

**1. Webhook Verification Failed**
- Check webhook URL is accessible via HTTPS
- Verify token matches exactly
- Check Laravel logs for errors

**2. Messages Not Sending**
- Verify access token is valid
- Check phone number format (966XXXXXXXXX)
- Ensure phone number is verified

**3. Bot Not Responding**
- Check webhook is receiving messages
- Verify database migrations ran
- Check intent detection logic

**4. Rate Limiting Issues**
- Monitor rate limit logs
- Adjust limits in config if needed
- Implement message queuing for high volume

## 📞 Support

For technical support:
- Check Laravel logs: `storage/logs/laravel.log`
- Review WhatsApp API documentation
- Contact Facebook Developer Support
- Email: <EMAIL>

## 🎉 Success Metrics

After setup, you should see:
- ✅ Webhook verification successful
- ✅ Test messages sending/receiving
- ✅ Bot responding to Arabic keywords
- ✅ Customer ticket queries working
- ✅ Analytics dashboard showing data

Your WhatsApp Business API integration is now ready to provide 24/7 automated customer service in Arabic! 🚀
