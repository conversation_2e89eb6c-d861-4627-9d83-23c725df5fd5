<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SmsService
{
    protected $apiUrl;
    protected $username;
    protected $password;
    protected $senderId;

    public function __construct()
    {
        $this->apiUrl = config('services.sms.api_url');
        $this->username = config('services.sms.username');
        $this->password = config('services.sms.password');
        $this->senderId = config('services.sms.sender_id', 'NJ-Repair');
    }

    /**
     * Send SMS to a single recipient
     */
    public function sendSms(string $phoneNumber, string $message): bool
    {
        try {
            // Clean phone number (remove spaces, dashes, etc.)
            $phoneNumber = $this->cleanPhoneNumber($phoneNumber);
            
            // Validate Saudi phone number
            if (!$this->isValidSaudiNumber($phoneNumber)) {
                Log::warning('Invalid Saudi phone number: ' . $phoneNumber);
                return false;
            }

            $response = Http::timeout(30)->post($this->apiUrl, [
                'username' => $this->username,
                'password' => $this->password,
                'sender' => $this->senderId,
                'mobile' => $phoneNumber,
                'message' => $message,
                'unicode' => 1, // For Arabic text
                'return' => 'json'
            ]);

            if ($response->successful()) {
                $result = $response->json();
                
                if (isset($result['status']) && $result['status'] === 'success') {
                    Log::info('SMS sent successfully to: ' . $phoneNumber);
                    return true;
                }
            }

            Log::error('SMS sending failed', [
                'phone' => $phoneNumber,
                'response' => $response->body()
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error('SMS service error: ' . $e->getMessage(), [
                'phone' => $phoneNumber,
                'message' => $message
            ]);
            return false;
        }
    }

    /**
     * Send bulk SMS
     */
    public function sendBulkSms(array $recipients): array
    {
        $results = [];
        
        foreach ($recipients as $recipient) {
            $phoneNumber = $recipient['phone'];
            $message = $recipient['message'];
            
            $results[$phoneNumber] = $this->sendSms($phoneNumber, $message);
            
            // Add delay to avoid rate limiting
            usleep(500000); // 0.5 second delay
        }
        
        return $results;
    }

    /**
     * Send repair status update SMS
     */
    public function sendRepairStatusUpdate($ticket, $newStatus): bool
    {
        $customerName = $ticket->customer->name;
        $ticketNumber = $ticket->ticket_number;
        $statusName = $newStatus->name;
        
        $message = "عزيزي {$customerName}،\n";
        $message .= "تم تحديث حالة جهازك رقم {$ticketNumber}\n";
        $message .= "الحالة الجديدة: {$statusName}\n";
        
        if ($newStatus->is_final) {
            $message .= "جهازك جاهز للاستلام!\n";
            $message .= "يرجى زيارة الورشة لاستلام جهازك.\n";
        }
        
        $message .= "\nورشة إصلاح NJ\n";
        $message .= "للاستفسار: " . config('app.phone', '0501234567');
        
        return $this->sendSms($ticket->customer->phone_number, $message);
    }

    /**
     * Send welcome SMS for new customers
     */
    public function sendWelcomeSms($customer): bool
    {
        $message = "أهلاً وسهلاً {$customer->name}،\n";
        $message .= "نرحب بك في ورشة إصلاح NJ\n";
        $message .= "يمكنك متابعة حالة جهازك عبر الرابط:\n";
        $message .= route('customer.portal') . "\n";
        $message .= "رقم هاتفك هو اسم المستخدم\n";
        $message .= "\nشكراً لثقتك بنا";
        
        return $this->sendSms($customer->phone_number, $message);
    }

    /**
     * Send appointment reminder SMS
     */
    public function sendAppointmentReminder($ticket, $appointmentDate): bool
    {
        $customerName = $ticket->customer->name;
        $ticketNumber = $ticket->ticket_number;
        $date = $appointmentDate->format('Y-m-d');
        $time = $appointmentDate->format('H:i');
        
        $message = "تذكير موعد - {$customerName}\n";
        $message .= "موعدك لاستلام الجهاز رقم {$ticketNumber}\n";
        $message .= "التاريخ: {$date}\n";
        $message .= "الوقت: {$time}\n";
        $message .= "\nورشة إصلاح NJ";
        
        return $this->sendSms($ticket->customer->phone_number, $message);
    }

    /**
     * Clean phone number format
     */
    private function cleanPhoneNumber(string $phoneNumber): string
    {
        // Remove all non-numeric characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);
        
        // Convert to international format for Saudi Arabia
        if (strlen($cleaned) === 10 && substr($cleaned, 0, 1) === '0') {
            // Remove leading 0 and add country code
            $cleaned = '966' . substr($cleaned, 1);
        } elseif (strlen($cleaned) === 9) {
            // Add country code
            $cleaned = '966' . $cleaned;
        }
        
        return $cleaned;
    }

    /**
     * Validate Saudi phone number
     */
    private function isValidSaudiNumber(string $phoneNumber): bool
    {
        // Saudi numbers should be 12 digits starting with 966
        if (strlen($phoneNumber) !== 12) {
            return false;
        }
        
        if (!str_starts_with($phoneNumber, '966')) {
            return false;
        }
        
        // Valid Saudi mobile prefixes after 966
        $validPrefixes = ['50', '51', '52', '53', '54', '55', '56', '57', '58', '59'];
        $prefix = substr($phoneNumber, 3, 2);
        
        return in_array($prefix, $validPrefixes);
    }

    /**
     * Get SMS balance (if supported by provider)
     */
    public function getBalance(): ?float
    {
        try {
            $response = Http::timeout(10)->post($this->apiUrl . '/balance', [
                'username' => $this->username,
                'password' => $this->password,
                'return' => 'json'
            ]);

            if ($response->successful()) {
                $result = $response->json();
                return $result['balance'] ?? null;
            }
        } catch (\Exception $e) {
            Log::error('Failed to get SMS balance: ' . $e->getMessage());
        }
        
        return null;
    }

    /**
     * Get delivery status of sent SMS
     */
    public function getDeliveryStatus(string $messageId): ?string
    {
        try {
            $response = Http::timeout(10)->post($this->apiUrl . '/status', [
                'username' => $this->username,
                'password' => $this->password,
                'message_id' => $messageId,
                'return' => 'json'
            ]);

            if ($response->successful()) {
                $result = $response->json();
                return $result['status'] ?? null;
            }
        } catch (\Exception $e) {
            Log::error('Failed to get delivery status: ' . $e->getMessage());
        }
        
        return null;
    }
}
