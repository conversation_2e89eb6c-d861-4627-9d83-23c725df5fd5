<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'phone_number',
        'address',
        'hire_date',
        'hourly_rate',
        'is_active',
        'preferred_language',
        'permissions',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'hire_date' => 'date',
        'hourly_rate' => 'decimal:2',
        'is_active' => 'boolean',
        'permissions' => 'array',
        'last_login_at' => 'datetime',
    ];

    /**
     * Get the repair tickets created by this user.
     */
    public function createdRepairTickets(): HasMany
    {
        return $this->hasMany(RepairTicket::class, 'created_by');
    }

    /**
     * Get the repair tickets assigned to this user as technician.
     */
    public function assignedRepairTickets(): HasMany
    {
        return $this->hasMany(RepairTicket::class, 'assigned_technician_id');
    }

    /**
     * Check if user has a specific role.
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if user has any of the specified roles.
     */
    public function hasAnyRole(array $roles): bool
    {
        return in_array($this->role, $roles);
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is manager or admin.
     */
    public function isManager(): bool
    {
        return in_array($this->role, ['admin', 'manager']);
    }

    /**
     * Check if user is technician.
     */
    public function isTechnician(): bool
    {
        return $this->role === 'technician';
    }

    /**
     * Check if user can manage tickets.
     */
    public function canManageTickets(): bool
    {
        return in_array($this->role, ['admin', 'manager', 'technician']);
    }

    /**
     * Check if user can manage customers.
     */
    public function canManageCustomers(): bool
    {
        return in_array($this->role, ['admin', 'manager', 'receptionist']);
    }

    /**
     * Check if user can view reports.
     */
    public function canViewReports(): bool
    {
        return in_array($this->role, ['admin', 'manager']);
    }

    /**
     * Update last login information.
     */
    public function updateLastLogin(): void
    {
        $this->update([
            'last_login_at' => now(),
            'last_login_ip' => request()->ip(),
        ]);
    }

    // ===== PERMISSION SYSTEM METHODS =====

    /**
     * Get the user's role.
     */
    public function userRole(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'role', 'slug');
    }

    /**
     * Get the user's direct permissions.
     */
    public function directPermissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'user_permissions')
            ->withPivot(['granted', 'expires_at', 'granted_by', 'reason'])
            ->withTimestamps();
    }

    /**
     * Check if user has a specific permission.
     */
    public function hasPermission(string $permission): bool
    {
        // Check if user is admin (has all permissions)
        if ($this->role === 'admin') {
            return true;
        }

        // Check direct user permissions first
        $directPermission = $this->directPermissions()
            ->where('slug', $permission)
            ->where('granted', true)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->first();

        if ($directPermission) {
            return true;
        }

        // Check role permissions
        $userRole = $this->userRole;
        if ($userRole) {
            return $userRole->hasPermission($permission);
        }

        return false;
    }

    /**
     * Check if user has any of the given permissions.
     */
    public function hasAnyPermission(array $permissions): bool
    {
        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if user has all of the given permissions.
     */
    public function hasAllPermissions(array $permissions): bool
    {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($permission)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Give permission directly to user.
     */
    public function givePermission(string $permission, ?string $grantedBy = null, ?string $reason = null, ?\DateTime $expiresAt = null): void
    {
        $permissionModel = Permission::where('slug', $permission)->first();

        if ($permissionModel) {
            $this->directPermissions()->syncWithoutDetaching([
                $permissionModel->id => [
                    'granted' => true,
                    'expires_at' => $expiresAt,
                    'granted_by' => $grantedBy,
                    'reason' => $reason,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            ]);
        }
    }

    /**
     * Revoke permission from user.
     */
    public function revokePermission(string $permission): void
    {
        $permissionModel = Permission::where('slug', $permission)->first();

        if ($permissionModel) {
            $this->directPermissions()->updateExistingPivot($permissionModel->id, [
                'granted' => false,
                'updated_at' => now(),
            ]);
        }
    }

    /**
     * Get all user permissions (role + direct).
     */
    public function getAllPermissions(): array
    {
        $permissions = [];

        // Get role permissions
        $userRole = $this->userRole;
        if ($userRole) {
            $rolePermissions = $userRole->permissions()
                ->where('is_active', true)
                ->pluck('slug')
                ->toArray();
            $permissions = array_merge($permissions, $rolePermissions);
        }

        // Get direct permissions
        $directPermissions = $this->directPermissions()
            ->where('granted', true)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->pluck('slug')
            ->toArray();

        $permissions = array_merge($permissions, $directPermissions);

        return array_unique($permissions);
    }

    /**
     * Check if user can manage another user.
     */
    public function canManageUser(User $user): bool
    {
        // Admin can manage everyone
        if ($this->role === 'admin') {
            return true;
        }

        // Can't manage yourself
        if ($this->id === $user->id) {
            return false;
        }

        // Check role hierarchy
        $myRole = $this->userRole;
        $targetRole = $user->userRole;

        if ($myRole && $targetRole) {
            return $myRole->isHigherThan($targetRole);
        }

        return false;
    }

    /**
     * Get user's role level.
     */
    public function getRoleLevel(): int
    {
        $userRole = $this->userRole;
        return $userRole ? $userRole->level : 0;
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user has role.
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }
}
