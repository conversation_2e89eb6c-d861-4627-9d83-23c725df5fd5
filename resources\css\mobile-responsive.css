/* NJ Repair Shop - Mobile Responsive Enhancements */

/* Mobile-First Approach */
@media (max-width: 575.98px) {
    /* Extra small devices (phones) */
    
    /* Navigation adjustments */
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .navbar-collapse {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    /* Card adjustments */
    .card {
        margin-bottom: 1rem;
        border-radius: 12px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .card-header {
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
    }
    
    /* Button groups to stack vertically */
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        border-radius: 8px !important;
        margin-bottom: 0.25rem;
        width: 100%;
    }
    
    .btn-group .btn:last-child {
        margin-bottom: 0;
    }
    
    /* Dropdown menus full width */
    .dropdown-menu {
        width: 100%;
        border-radius: 12px;
        margin-top: 0.5rem;
    }
    
    /* Form adjustments */
    .form-control, .form-select {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 0.75rem;
        border-radius: 12px;
    }
    
    .input-group .form-control {
        border-radius: 12px 0 0 12px;
    }
    
    .input-group .btn {
        border-radius: 0 12px 12px 0;
    }
    
    /* Table responsive enhancements */
    .table-responsive {
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .table {
        font-size: 0.875rem;
    }
    
    .table th, .table td {
        padding: 0.5rem;
        vertical-align: middle;
    }
    
    /* Hide less important columns on mobile */
    .table .d-none-mobile {
        display: none !important;
    }
    
    /* Stack table actions vertically */
    .table .btn-group-sm {
        flex-direction: column;
    }
    
    .table .btn-group-sm .btn {
        margin-bottom: 0.25rem;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    /* Modal adjustments */
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
    
    .modal-content {
        border-radius: 16px;
    }
    
    .modal-header {
        padding: 1rem;
        border-radius: 16px 16px 0 0;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .modal-footer {
        padding: 1rem;
        border-radius: 0 0 16px 16px;
    }
    
    /* Search enhancements */
    .search-container {
        margin-bottom: 1rem;
    }
    
    .search-container .form-control {
        padding-left: 3rem;
        padding-right: 3rem;
    }
    
    /* Statistics cards */
    .stats-card {
        margin-bottom: 1rem;
        text-align: center;
        padding: 1.5rem 1rem;
    }
    
    .stats-card .display-6 {
        font-size: 2rem;
    }
    
    /* Pagination */
    .pagination {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .page-link {
        padding: 0.5rem 0.75rem;
        margin: 0.125rem;
        border-radius: 8px;
    }
    
    /* Toast notifications */
    .toast {
        max-width: calc(100vw - 2rem);
        margin: 0 1rem;
    }
    
    /* Floating action button for mobile */
    .fab-mobile {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        border: none;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        transition: all 0.3s ease;
    }
    
    .fab-mobile:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 16px rgba(0, 123, 255, 0.6);
    }
    
    /* Hide desktop-only elements */
    .d-mobile-none {
        display: none !important;
    }
    
    /* Show mobile-only elements */
    .d-mobile-block {
        display: block !important;
    }
    
    .d-mobile-flex {
        display: flex !important;
    }
}

@media (min-width: 576px) and (max-width: 767.98px) {
    /* Small devices (landscape phones) */
    
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-columns {
        column-count: 2;
    }
    
    .btn-group {
        flex-wrap: wrap;
    }
    
    .btn-group .btn {
        flex: 1 1 auto;
        min-width: 120px;
    }
}

@media (min-width: 768px) and (max-width: 991.98px) {
    /* Medium devices (tablets) */
    
    .card-columns {
        column-count: 2;
    }
    
    .table-responsive {
        border-radius: 12px;
    }
    
    /* Show more table columns */
    .table .d-md-table-cell {
        display: table-cell !important;
    }
}

@media (min-width: 992px) {
    /* Large devices and up */
    
    .card-columns {
        column-count: 3;
    }
    
    /* Enhanced hover effects for desktop */
    .card:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .btn:hover {
        transform: translateY(-3px);
    }
    
    .table tbody tr:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
}

/* Touch-friendly enhancements */
@media (hover: none) and (pointer: coarse) {
    /* Touch devices */
    
    .btn {
        min-height: 44px; /* Apple's recommended touch target size */
        min-width: 44px;
    }
    
    .form-control, .form-select {
        min-height: 44px;
    }
    
    .nav-link {
        min-height: 44px;
        display: flex;
        align-items: center;
    }
    
    /* Remove hover effects on touch devices */
    .card:hover {
        transform: none;
        box-shadow: var(--box-shadow);
    }
    
    .btn:hover {
        transform: none;
    }
    
    .table tbody tr:hover {
        transform: none;
        background-color: transparent;
    }
    
    /* Add tap highlight */
    .btn, .nav-link, .dropdown-item {
        -webkit-tap-highlight-color: rgba(0, 123, 255, 0.2);
    }
}

/* Landscape orientation adjustments */
@media screen and (orientation: landscape) and (max-height: 500px) {
    .navbar {
        padding: 0.25rem 1rem;
    }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    .modal-dialog {
        margin: 0.25rem;
        max-height: calc(100vh - 0.5rem);
    }
    
    .modal-content {
        max-height: 100%;
        overflow-y: auto;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .card {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .btn {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    }
}

/* Accessibility enhancements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus indicators for keyboard navigation */
.btn:focus,
.form-control:focus,
.form-select:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Skip to content link for screen readers */
.skip-to-content {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
}

.skip-to-content:focus {
    top: 6px;
}

/* Print optimizations */
@media print {
    .no-print,
    .btn,
    .navbar,
    .fab-mobile {
        display: none !important;
    }
    
    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .table {
        font-size: 12px;
    }
    
    .page-break {
        page-break-before: always;
    }
}
