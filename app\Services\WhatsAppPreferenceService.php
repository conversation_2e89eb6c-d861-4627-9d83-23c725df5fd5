<?php

namespace App\Services;

use App\Models\WhatsAppCustomerPreference;
use App\Models\Customer;
use Illuminate\Support\Facades\Log;

class WhatsAppPreferenceService
{
    /**
     * Get or create customer preferences
     */
    public function getCustomerPreferences(string $customerPhone, ?int $customerId = null): WhatsAppCustomerPreference
    {
        return WhatsAppCustomerPreference::getOrCreateForCustomer($customerPhone, $customerId);
    }

    /**
     * Update customer preferences
     */
    public function updatePreferences(string $customerPhone, array $preferences): bool
    {
        try {
            $customerPrefs = $this->getCustomerPreferences($customerPhone);
            $customerPrefs->update($preferences);
            
            Log::info('Customer preferences updated', [
                'customer_phone' => $customerPhone,
                'preferences' => $preferences
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update customer preferences', [
                'customer_phone' => $customerPhone,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Opt customer out of WhatsApp messages
     */
    public function optOut(string $customerPhone, string $reason = null): bool
    {
        try {
            $preferences = $this->getCustomerPreferences($customerPhone);
            $preferences->optOut($reason);
            
            Log::info('Customer opted out of WhatsApp', [
                'customer_phone' => $customerPhone,
                'reason' => $reason
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to opt out customer', [
                'customer_phone' => $customerPhone,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Opt customer back in to WhatsApp messages
     */
    public function optIn(string $customerPhone): bool
    {
        try {
            $preferences = $this->getCustomerPreferences($customerPhone);
            $preferences->optIn();
            
            Log::info('Customer opted back in to WhatsApp', [
                'customer_phone' => $customerPhone
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to opt in customer', [
                'customer_phone' => $customerPhone,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Set customer language preference
     */
    public function setLanguage(string $customerPhone, string $language): bool
    {
        if (!in_array($language, ['ar', 'en'])) {
            return false;
        }

        return $this->updatePreferences($customerPhone, [
            'preferred_language' => $language
        ]);
    }

    /**
     * Set customer notification preferences
     */
    public function setNotificationPreferences(string $customerPhone, array $notifications): bool
    {
        $allowedNotifications = [
            'status_updates',
            'appointment_reminders',
            'payment_reminders',
            'marketing_messages',
            'promotional_offers'
        ];

        $validNotifications = array_intersect_key($notifications, array_flip($allowedNotifications));
        
        return $this->updatePreferences($customerPhone, $validNotifications);
    }

    /**
     * Set customer quiet hours
     */
    public function setQuietHours(string $customerPhone, string $startTime, string $endTime): bool
    {
        try {
            // Validate time format
            if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $startTime) ||
                !preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $endTime)) {
                return false;
            }

            return $this->updatePreferences($customerPhone, [
                'quiet_hours_start' => $startTime,
                'quiet_hours_end' => $endTime
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to set quiet hours', [
                'customer_phone' => $customerPhone,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Block a specific template for customer
     */
    public function blockTemplate(string $customerPhone, int $templateId): bool
    {
        try {
            $preferences = $this->getCustomerPreferences($customerPhone);
            $preferences->blockTemplate($templateId);
            
            Log::info('Template blocked for customer', [
                'customer_phone' => $customerPhone,
                'template_id' => $templateId
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to block template', [
                'customer_phone' => $customerPhone,
                'template_id' => $templateId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Unblock a template for customer
     */
    public function unblockTemplate(string $customerPhone, int $templateId): bool
    {
        try {
            $preferences = $this->getCustomerPreferences($customerPhone);
            $preferences->unblockTemplate($templateId);
            
            Log::info('Template unblocked for customer', [
                'customer_phone' => $customerPhone,
                'template_id' => $templateId
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to unblock template', [
                'customer_phone' => $customerPhone,
                'template_id' => $templateId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Record customer satisfaction feedback
     */
    public function recordSatisfaction(string $customerPhone, float $score): bool
    {
        if ($score < 1 || $score > 5) {
            return false;
        }

        try {
            $preferences = $this->getCustomerPreferences($customerPhone);
            $preferences->recordSatisfaction($score);
            
            Log::info('Customer satisfaction recorded', [
                'customer_phone' => $customerPhone,
                'score' => $score
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to record satisfaction', [
                'customer_phone' => $customerPhone,
                'score' => $score,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Get customer engagement analytics
     */
    public function getCustomerEngagement(string $customerPhone): array
    {
        try {
            $preferences = $this->getCustomerPreferences($customerPhone);
            
            return [
                'total_messages_received' => $preferences->total_messages_received,
                'total_messages_sent' => $preferences->total_messages_sent,
                'response_rate' => $preferences->response_rate,
                'engagement_level' => $preferences->engagement_level,
                'satisfaction_score' => $preferences->satisfaction_score,
                'satisfaction_level' => $preferences->satisfaction_level,
                'last_interaction' => $preferences->last_interaction_at?->diffForHumans(),
                'last_response' => $preferences->last_response_at?->diffForHumans(),
                'is_opted_out' => $preferences->is_opted_out,
                'preferred_language' => $preferences->preferred_language,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get customer engagement', [
                'customer_phone' => $customerPhone,
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    /**
     * Get customers by engagement level
     */
    public function getCustomersByEngagement(string $level = 'all'): array
    {
        $query = WhatsAppCustomerPreference::with('customer');

        switch ($level) {
            case 'high':
                $query->where('response_rate', '>=', 80);
                break;
            case 'medium':
                $query->whereBetween('response_rate', [40, 79]);
                break;
            case 'low':
                $query->where('response_rate', '<', 40);
                break;
            case 'inactive':
                $query->where('last_interaction_at', '<', now()->subDays(30));
                break;
        }

        return $query->get()->map(function ($preference) {
            return [
                'customer_phone' => $preference->customer_phone,
                'customer_name' => $preference->customer?->name,
                'engagement_level' => $preference->engagement_level,
                'response_rate' => $preference->response_rate,
                'satisfaction_score' => $preference->satisfaction_score,
                'last_interaction' => $preference->last_interaction_at?->diffForHumans(),
            ];
        })->toArray();
    }

    /**
     * Bulk update preferences for multiple customers
     */
    public function bulkUpdatePreferences(array $customerPhones, array $preferences): array
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($customerPhones as $phone) {
            if ($this->updatePreferences($phone, $preferences)) {
                $results['success']++;
            } else {
                $results['failed']++;
                $results['errors'][] = $phone;
            }
        }

        return $results;
    }

    /**
     * Get preference statistics
     */
    public function getPreferenceStatistics(): array
    {
        $total = WhatsAppCustomerPreference::count();
        $optedIn = WhatsAppCustomerPreference::optedIn()->count();
        $acceptsMarketing = WhatsAppCustomerPreference::acceptsMarketing()->count();
        $arabicUsers = WhatsAppCustomerPreference::byLanguage('ar')->count();
        $englishUsers = WhatsAppCustomerPreference::byLanguage('en')->count();

        $avgSatisfaction = WhatsAppCustomerPreference::whereNotNull('satisfaction_score')
            ->avg('satisfaction_score');

        $avgResponseRate = WhatsAppCustomerPreference::where('total_messages_received', '>', 0)
            ->avg('response_rate');

        return [
            'total_customers' => $total,
            'opted_in' => $optedIn,
            'opted_out' => $total - $optedIn,
            'opt_in_rate' => $total > 0 ? round(($optedIn / $total) * 100, 2) : 0,
            'accepts_marketing' => $acceptsMarketing,
            'marketing_acceptance_rate' => $optedIn > 0 ? round(($acceptsMarketing / $optedIn) * 100, 2) : 0,
            'arabic_users' => $arabicUsers,
            'english_users' => $englishUsers,
            'arabic_percentage' => $total > 0 ? round(($arabicUsers / $total) * 100, 2) : 0,
            'average_satisfaction' => $avgSatisfaction ? round($avgSatisfaction, 2) : null,
            'average_response_rate' => $avgResponseRate ? round($avgResponseRate, 2) : null,
            'engagement_levels' => [
                'high' => WhatsAppCustomerPreference::where('response_rate', '>=', 80)->count(),
                'medium' => WhatsAppCustomerPreference::whereBetween('response_rate', [40, 79])->count(),
                'low' => WhatsAppCustomerPreference::where('response_rate', '<', 40)->count(),
            ]
        ];
    }
}
