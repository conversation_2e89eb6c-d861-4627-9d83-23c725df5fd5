<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PosTransactionPayment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'transaction_id',
        'payment_method',
        'amount',
        'payment_reference',
        'card_last_four',
        'bank_name',
        'payment_details',
        'status',
        'payment_date',
        'change_amount',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'change_amount' => 'decimal:2',
        'payment_details' => 'array',
        'payment_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the transaction that owns this payment.
     */
    public function transaction(): BelongsTo
    {
        return $this->belongsTo(PosTransaction::class, 'transaction_id');
    }

    /**
     * Get payment method configuration.
     */
    public function paymentMethodConfig(): BelongsTo
    {
        return $this->belongsTo(PaymentMethodConfig::class, 'payment_method', 'method_code');
    }

    /**
     * Get payment method display name.
     */
    public function getPaymentMethodDisplayAttribute(): string
    {
        $config = $this->paymentMethodConfig;
        if ($config) {
            return $config->display_name;
        }

        // Fallback to manual translation
        $methods = [
            'cash' => app()->getLocale() === 'ar' ? 'نقداً' : 'Cash',
            'card' => app()->getLocale() === 'ar' ? 'بطاقة' : 'Card',
            'bank_transfer' => app()->getLocale() === 'ar' ? 'تحويل بنكي' : 'Bank Transfer',
            'mobile_payment' => app()->getLocale() === 'ar' ? 'دفع عبر الجوال' : 'Mobile Payment',
            'check' => app()->getLocale() === 'ar' ? 'شيك' : 'Check',
            'other' => app()->getLocale() === 'ar' ? 'أخرى' : 'Other',
        ];

        return $methods[$this->payment_method] ?? $this->payment_method;
    }

    /**
     * Get status display.
     */
    public function getStatusDisplayAttribute(): string
    {
        $statuses = [
            'pending' => app()->getLocale() === 'ar' ? 'في الانتظار' : 'Pending',
            'completed' => app()->getLocale() === 'ar' ? 'مكتمل' : 'Completed',
            'failed' => app()->getLocale() === 'ar' ? 'فشل' : 'Failed',
            'refunded' => app()->getLocale() === 'ar' ? 'مسترد' : 'Refunded',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'warning',
            'completed' => 'success',
            'failed' => 'danger',
            'refunded' => 'info',
            default => 'secondary',
        };
    }

    /**
     * Mark payment as completed.
     */
    public function markAsCompleted(): void
    {
        $this->update(['status' => 'completed']);
    }

    /**
     * Mark payment as failed.
     */
    public function markAsFailed(string $reason = null): void
    {
        $details = $this->payment_details ?? [];
        if ($reason) {
            $details['failure_reason'] = $reason;
        }

        $this->update([
            'status' => 'failed',
            'payment_details' => $details
        ]);
    }

    /**
     * Process refund.
     */
    public function processRefund(string $reason = null): void
    {
        if ($this->status !== 'completed') {
            throw new \Exception('Can only refund completed payments');
        }

        $details = $this->payment_details ?? [];
        $details['refund_date'] = now()->toISOString();
        if ($reason) {
            $details['refund_reason'] = $reason;
        }

        $this->update([
            'status' => 'refunded',
            'payment_details' => $details
        ]);
    }

    /**
     * Calculate processing fee.
     */
    public function getProcessingFeeAttribute(): float
    {
        $config = $this->paymentMethodConfig;
        if (!$config) {
            return 0;
        }

        return $config->calculateProcessingFee($this->amount);
    }

    /**
     * Get net amount after processing fee.
     */
    public function getNetAmountAttribute(): float
    {
        return $this->amount - $this->processing_fee;
    }

    /**
     * Check if payment requires additional verification.
     */
    public function requiresVerification(): bool
    {
        $config = $this->paymentMethodConfig;
        if (!$config) {
            return false;
        }

        // Check if payment method requires verification for amounts above threshold
        $verificationThreshold = $config->getConfigValue('verification_threshold', 1000);
        return $this->amount >= $verificationThreshold;
    }

    /**
     * Get payment icon class.
     */
    public function getIconClassAttribute(): string
    {
        $config = $this->paymentMethodConfig;
        if ($config && $config->icon_class) {
            return $config->icon_class;
        }

        // Fallback icons
        $icons = [
            'cash' => 'bi bi-cash-coin',
            'card' => 'bi bi-credit-card',
            'bank_transfer' => 'bi bi-bank',
            'mobile_payment' => 'bi bi-phone',
            'check' => 'bi bi-file-text',
            'other' => 'bi bi-wallet2',
        ];

        return $icons[$this->payment_method] ?? 'bi bi-wallet2';
    }

    /**
     * Create cash payment.
     */
    public static function createCashPayment(float $amount, float $cashReceived = null): array
    {
        $cashReceived = $cashReceived ?? $amount;
        $changeAmount = max(0, $cashReceived - $amount);

        return [
            'payment_method' => 'cash',
            'amount' => $amount,
            'change_amount' => $changeAmount,
            'status' => 'completed',
            'payment_details' => [
                'cash_received' => $cashReceived,
                'change_given' => $changeAmount
            ]
        ];
    }

    /**
     * Create card payment.
     */
    public static function createCardPayment(float $amount, array $cardDetails = []): array
    {
        return [
            'payment_method' => 'card',
            'amount' => $amount,
            'card_last_four' => $cardDetails['last_four'] ?? null,
            'payment_reference' => $cardDetails['reference'] ?? null,
            'status' => 'completed',
            'payment_details' => array_merge([
                'card_type' => $cardDetails['card_type'] ?? null,
                'authorization_code' => $cardDetails['auth_code'] ?? null,
                'terminal_id' => $cardDetails['terminal_id'] ?? null,
            ], $cardDetails)
        ];
    }

    /**
     * Create bank transfer payment.
     */
    public static function createBankTransferPayment(float $amount, array $transferDetails = []): array
    {
        return [
            'payment_method' => 'bank_transfer',
            'amount' => $amount,
            'bank_name' => $transferDetails['bank_name'] ?? null,
            'payment_reference' => $transferDetails['reference'] ?? null,
            'status' => 'completed',
            'payment_details' => array_merge([
                'account_number' => $transferDetails['account_number'] ?? null,
                'transfer_date' => $transferDetails['transfer_date'] ?? now()->toDateString(),
            ], $transferDetails)
        ];
    }

    /**
     * Create mobile payment.
     */
    public static function createMobilePayment(float $amount, array $mobileDetails = []): array
    {
        return [
            'payment_method' => 'mobile_payment',
            'amount' => $amount,
            'payment_reference' => $mobileDetails['reference'] ?? null,
            'status' => 'completed',
            'payment_details' => array_merge([
                'provider' => $mobileDetails['provider'] ?? null,
                'phone_number' => $mobileDetails['phone_number'] ?? null,
                'transaction_id' => $mobileDetails['transaction_id'] ?? null,
            ], $mobileDetails)
        ];
    }

    /**
     * Scope for completed payments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for cash payments.
     */
    public function scopeCash($query)
    {
        return $query->where('payment_method', 'cash');
    }

    /**
     * Scope for card payments.
     */
    public function scopeCard($query)
    {
        return $query->where('payment_method', 'card');
    }

    /**
     * Scope for today's payments.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('payment_date', today());
    }

    /**
     * Get payment summary for a collection of payments.
     */
    public static function getPaymentSummary($payments): array
    {
        $summary = [];
        $total = 0;

        foreach ($payments as $payment) {
            $method = $payment->payment_method;
            if (!isset($summary[$method])) {
                $summary[$method] = [
                    'count' => 0,
                    'total' => 0,
                    'display_name' => $payment->payment_method_display
                ];
            }
            $summary[$method]['count']++;
            $summary[$method]['total'] += $payment->amount;
            $total += $payment->amount;
        }

        // Calculate percentages
        foreach ($summary as $method => &$data) {
            $data['percentage'] = $total > 0 ? ($data['total'] / $total) * 100 : 0;
        }

        return [
            'by_method' => $summary,
            'total_amount' => $total,
            'total_count' => count($payments)
        ];
    }
}
