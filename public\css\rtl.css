/* RTL (Right-to-Left) Support for Arabic Language */

/* Base RTL styles */
body[dir="rtl"] {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
}

/* Navigation RTL styles */
body[dir="rtl"] .navbar-brand {
    font-family: 'Cairo', sans-serif;
}

body[dir="rtl"] .dropdown-menu {
    text-align: right;
}

body[dir="rtl"] .dropdown-menu .dropdown-item {
    text-align: right;
}

/* Form elements RTL styles */
body[dir="rtl"] .form-control,
body[dir="rtl"] .form-select,
body[dir="rtl"] .form-control:focus,
body[dir="rtl"] .form-select:focus {
    text-align: right;
    direction: rtl;
}

body[dir="rtl"] .form-label {
    text-align: right;
}

body[dir="rtl"] .input-group-text {
    text-align: right;
}

/* Alert messages RTL styles */
body[dir="rtl"] .alert {
    text-align: right;
    direction: rtl;
}

/* Card components RTL styles */
body[dir="rtl"] .card-header,
body[dir="rtl"] .card-body,
body[dir="rtl"] .card-footer {
    text-align: right;
}

body[dir="rtl"] .card-title,
body[dir="rtl"] .card-text {
    text-align: right;
}

/* Table RTL styles */
body[dir="rtl"] .table {
    text-align: right;
}

body[dir="rtl"] .table th,
body[dir="rtl"] .table td {
    text-align: right;
}

/* Button RTL styles */
body[dir="rtl"] .btn {
    text-align: center;
}

/* Badge RTL styles */
body[dir="rtl"] .badge {
    text-align: center;
}

/* Pagination RTL styles */
body[dir="rtl"] .pagination {
    direction: rtl;
}

body[dir="rtl"] .page-link {
    text-align: center;
}

/* Modal RTL styles */
body[dir="rtl"] .modal-header,
body[dir="rtl"] .modal-body,
body[dir="rtl"] .modal-footer {
    text-align: right;
}

body[dir="rtl"] .modal-title {
    text-align: right;
}

/* List group RTL styles */
body[dir="rtl"] .list-group-item {
    text-align: right;
}

/* Breadcrumb RTL styles */
body[dir="rtl"] .breadcrumb {
    direction: rtl;
}

body[dir="rtl"] .breadcrumb-item {
    text-align: right;
}

/* Search form RTL styles */
body[dir="rtl"] .search-form .form-control {
    text-align: right;
    direction: rtl;
}

/* Status badge RTL styles */
body[dir="rtl"] .status-badge {
    text-align: center;
}

/* Pattern display RTL styles */
body[dir="rtl"] .pattern-display {
    direction: rtl;
}

body[dir="rtl"] .pattern-grid {
    direction: ltr; /* Keep pattern grid LTR for consistency */
}

body[dir="rtl"] .pattern-info {
    text-align: right;
}

/* Repair ticket specific RTL styles */
body[dir="rtl"] .ticket-header,
body[dir="rtl"] .ticket-details,
body[dir="rtl"] .customer-info,
body[dir="rtl"] .device-info {
    text-align: right;
}

/* Dashboard RTL styles */
body[dir="rtl"] .dashboard-card .card-body {
    text-align: right;
}

body[dir="rtl"] .stat-card .card-title {
    text-align: right;
}

/* Print styles for RTL */
@media print {
    body[dir="rtl"] {
        direction: rtl;
        text-align: right;
    }

    body[dir="rtl"] .print-header,
    body[dir="rtl"] .print-content,
    body[dir="rtl"] .print-footer {
        text-align: right;
    }
}

/* Mobile responsive RTL styles */
@media (max-width: 768px) {
    body[dir="rtl"] .navbar-nav {
        text-align: right;
    }

    body[dir="rtl"] .navbar-collapse {
        text-align: right;
    }

    body[dir="rtl"] .mobile-menu {
        text-align: right;
    }
}

/* Fix for Bootstrap RTL icons */
body[dir="rtl"] .bi {
    margin-left: 0.5rem;
    margin-right: 0;
}

body[dir="rtl"] .nav-link .bi {
    margin-left: 0.5rem;
    margin-right: 0;
}

body[dir="rtl"] .dropdown-item .bi {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Custom spacing for Arabic text */
body[dir="rtl"] h1,
body[dir="rtl"] h2,
body[dir="rtl"] h3,
body[dir="rtl"] h4,
body[dir="rtl"] h5,
body[dir="rtl"] h6 {
    text-align: right;
    font-family: 'Noto Sans Arabic', sans-serif;
}

body[dir="rtl"] p {
    text-align: right;
    line-height: 1.8; /* Better line height for Arabic text */
}

/* Fix for floating elements */
body[dir="rtl"] .float-start {
    float: right !important;
}

body[dir="rtl"] .float-end {
    float: left !important;
}

/* Fix for text alignment utilities */
body[dir="rtl"] .text-start {
    text-align: right !important;
}

body[dir="rtl"] .text-end {
    text-align: left !important;
}

/* Fix for margin and padding utilities */
body[dir="rtl"] .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

body[dir="rtl"] .ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}
