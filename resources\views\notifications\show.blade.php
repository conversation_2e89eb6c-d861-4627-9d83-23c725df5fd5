@extends('layouts.app')

@section('title', __('app.notifications.notification_details'))

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ __('app.notifications.notification_details') }}</h1>
            <p class="text-muted">{{ __('app.view_notification_information') }}</p>
        </div>
        <div class="d-flex gap-2">
            @if($notification->status === 'failed')
                <form method="POST" action="{{ route('notifications.resend', $notification) }}" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-warning" 
                            onclick="return confirm('{{ __('app.confirm_resend') }}')">
                        <i class="fas fa-redo"></i> {{ __('app.notifications.resend') }}
                    </button>
                </form>
            @endif
            <a href="{{ route('notifications.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> {{ __('app.back') }}
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Notification Details -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.notification_information') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>{{ __('app.basic_information') }}</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{{ __('app.notifications.type') }}:</strong></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-{{ $notification->type === 'sms' ? 'sms' : ($notification->type === 'whatsapp' ? 'whatsapp' : 'envelope') }}"></i>
                                            {{ $notification->type_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('app.notifications.category') }}:</strong></td>
                                    <td><span class="badge bg-info">{{ $notification->category_display }}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('app.notifications.status') }}:</strong></td>
                                    <td><span class="badge {{ $notification->status_badge_class }}">{{ __('app.notifications.' . $notification->status) }}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('app.notifications.recipient') }}:</strong></td>
                                    <td>{{ $notification->formatted_recipient }}</td>
                                </tr>
                                @if($notification->cost > 0)
                                <tr>
                                    <td><strong>{{ __('app.notifications.cost') }}:</strong></td>
                                    <td>@arabicCurrency($notification->cost)</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>{{ __('app.timing_information') }}</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{{ __('app.created_at') }}:</strong></td>
                                    <td>{{ $notification->created_at->toArabicDateTimeString() }}</td>
                                </tr>
                                @if($notification->scheduled_at)
                                <tr>
                                    <td><strong>{{ __('app.notifications.scheduled_at') }}:</strong></td>
                                    <td>{{ $notification->scheduled_at->toArabicDateTimeString() }}</td>
                                </tr>
                                @endif
                                @if($notification->sent_at)
                                <tr>
                                    <td><strong>{{ __('app.notifications.sent_at') }}:</strong></td>
                                    <td>{{ $notification->sent_at->toArabicDateTimeString() }}</td>
                                </tr>
                                @endif
                                @if($notification->delivered_at)
                                <tr>
                                    <td><strong>{{ __('app.notifications.delivered_at') }}:</strong></td>
                                    <td>{{ $notification->delivered_at->toArabicDateTimeString() }}</td>
                                </tr>
                                @endif
                                @if($notification->read_at)
                                <tr>
                                    <td><strong>{{ __('app.notifications.read_at') }}:</strong></td>
                                    <td>{{ $notification->read_at->toArabicDateTimeString() }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message Content -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.message_content') }}</h5>
                </div>
                <div class="card-body">
                    @if($notification->subject)
                        <div class="mb-3">
                            <h6>{{ __('app.subject') }}:</h6>
                            <p class="border p-2 bg-light rounded">{{ $notification->subject }}</p>
                        </div>
                    @endif
                    
                    <h6>{{ __('app.notifications.message') }}:</h6>
                    <div class="border p-3 bg-light rounded">
                        {!! nl2br(e($notification->message)) !!}
                    </div>
                </div>
            </div>

            @if($notification->error_message)
            <!-- Error Information -->
            <div class="card mt-4 border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i> {{ __('app.error_details') }}
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-danger mb-0">{{ $notification->error_message }}</p>
                </div>
            </div>
            @endif
        </div>

        <div class="col-lg-4">
            <!-- Customer Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.customer_information') }}</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style="width: 50px; height: 50px;">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <h6 class="mb-0">{{ $notification->customer->name }}</h6>
                            <small class="text-muted">{{ __('app.customer') }}</small>
                        </div>
                    </div>
                    
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><i class="fas fa-phone text-muted"></i></td>
                            <td>{{ $notification->customer->phone_number }}</td>
                        </tr>
                        @if($notification->customer->email)
                        <tr>
                            <td><i class="fas fa-envelope text-muted"></i></td>
                            <td>{{ $notification->customer->email }}</td>
                        </tr>
                        @endif
                        @if($notification->customer->address)
                        <tr>
                            <td><i class="fas fa-map-marker-alt text-muted"></i></td>
                            <td>{{ $notification->customer->address }}</td>
                        </tr>
                        @endif
                    </table>

                    <a href="{{ route('customers.show', $notification->customer) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> {{ __('app.view_customer') }}
                    </a>
                </div>
            </div>

            @if($notification->repairTicket)
            <!-- Repair Ticket Information -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.repair_ticket_information') }}</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style="width: 50px; height: 50px;">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div>
                            <h6 class="mb-0">{{ $notification->repairTicket->ticket_number }}</h6>
                            <small class="text-muted">{{ $notification->repairTicket->device_model }}</small>
                        </div>
                    </div>
                    
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>{{ __('app.brand') }}:</strong></td>
                            <td>{{ $notification->repairTicket->brand->name }}</td>
                        </tr>
                        <tr>
                            <td><strong>{{ __('app.status') }}:</strong></td>
                            <td>
                                <span class="badge" style="background-color: {{ $notification->repairTicket->repairStatus->color }}">
                                    {{ $notification->repairTicket->repairStatus->name }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{{ __('app.received_date') }}:</strong></td>
                            <td>{{ $notification->repairTicket->received_date->toArabicDateString() }}</td>
                        </tr>
                        @if($notification->repairTicket->final_cost)
                        <tr>
                            <td><strong>{{ __('app.final_cost') }}:</strong></td>
                            <td>@arabicCurrency($notification->repairTicket->final_cost)</td>
                        </tr>
                        @endif
                    </table>

                    <a href="{{ route('repair-tickets.show', $notification->repairTicket) }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-eye"></i> {{ __('app.view_ticket') }}
                    </a>
                </div>
            </div>
            @endif

            <!-- Created By Information -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.created_by') }}</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style="width: 40px; height: 40px;">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div>
                            <h6 class="mb-0">{{ $notification->createdBy->name }}</h6>
                            <small class="text-muted">{{ $notification->created_at->toArabicDateTimeString() }}</small>
                        </div>
                    </div>
                </div>
            </div>

            @if($notification->external_id)
            <!-- External Reference -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.external_reference') }}</h5>
                </div>
                <div class="card-body">
                    <p class="mb-0"><code>{{ $notification->external_id }}</code></p>
                    <small class="text-muted">{{ __('app.provider_message_id') }}</small>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
