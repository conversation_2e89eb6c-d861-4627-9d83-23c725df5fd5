<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_orders', function (Blueprint $table) {
            $table->id();
            $table->string('po_number')->unique(); // Purchase Order Number
            $table->foreignId('supplier_id')->constrained()->onDelete('restrict');
            $table->enum('status', [
                'draft',        // Being prepared
                'sent',         // Sent to supplier
                'confirmed',    // Confirmed by supplier
                'partial',      // Partially received
                'completed',    // Fully received
                'cancelled'     // Cancelled
            ])->default('draft');
            
            // Dates
            $table->date('order_date'); // When order was placed
            $table->date('expected_delivery_date')->nullable(); // Expected delivery
            $table->date('actual_delivery_date')->nullable(); // Actual delivery
            
            // Financial Information
            $table->decimal('subtotal', 10, 2)->default(0); // Subtotal before tax
            $table->decimal('tax_rate', 5, 2)->default(15); // Tax rate (VAT 15%)
            $table->decimal('tax_amount', 10, 2)->default(0); // Tax amount
            $table->decimal('shipping_cost', 10, 2)->default(0); // Shipping cost
            $table->decimal('discount_amount', 10, 2)->default(0); // Discount amount
            $table->decimal('total_amount', 10, 2)->default(0); // Total amount
            
            // Payment Information
            $table->enum('payment_status', ['pending', 'partial', 'paid', 'overdue'])->default('pending');
            $table->decimal('paid_amount', 10, 2)->default(0); // Amount paid so far
            $table->date('payment_due_date')->nullable(); // Payment due date
            
            // Additional Information
            $table->text('notes')->nullable(); // Order notes
            $table->text('terms_conditions')->nullable(); // Terms and conditions
            $table->string('delivery_address')->nullable(); // Delivery address if different
            $table->json('attachments')->nullable(); // Array of attachment file paths
            
            // Tracking
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->index('po_number');
            $table->index('supplier_id');
            $table->index('status');
            $table->index('order_date');
            $table->index('expected_delivery_date');
            $table->index('payment_status');
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_orders');
    }
};
