@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="bi bi-chat-square-text text-primary"></i> إدارة قوالب واتساب</h2>
                    <p class="text-muted">إنشاء وإدارة قوالب الرسائل للواتساب بيزنس</p>
                </div>
                <div class="btn-group">
                    <a href="{{ route('whatsapp.templates.create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-lg"></i> قالب جديد
                    </a>
                    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#filtersModal">
                        <i class="bi bi-funnel"></i> تصفية
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ $stats['total'] }}</h3>
                    <p class="mb-0">إجمالي القوالب</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ $stats['approved'] }}</h3>
                    <p class="mb-0">معتمد</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ $stats['pending'] }}</h3>
                    <p class="mb-0">في الانتظار</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>{{ $stats['rejected'] }}</h3>
                    <p class="mb-0">مرفوض</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ $stats['active'] }}</h3>
                    <p class="mb-0">نشط</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h3>{{ $categories->count() }}</h3>
                    <p class="mb-0">الفئات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Templates Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-list-ul"></i> قائمة القوالب</h5>
                </div>
                <div class="card-body">
                    @if($templates->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>القالب</th>
                                        <th>الفئة</th>
                                        <th>اللغة</th>
                                        <th>الحالة</th>
                                        <th>الاستخدام</th>
                                        <th>معدل النجاح</th>
                                        <th>آخر استخدام</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($templates as $template)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $template->display_name_ar }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $template->name }}</small>
                                                @if($template->has_media)
                                                    <span class="badge bg-info ms-1">
                                                        <i class="bi bi-paperclip"></i> {{ $template->media_type }}
                                                    </span>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge" style="background-color: {{ $template->category->color }}">
                                                <i class="{{ $template->category->icon }}"></i>
                                                {{ $template->category->name_ar }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                {{ $template->language === 'ar' ? 'عربي' : 'English' }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $template->status_color }}">
                                                {{ $template->status }}
                                            </span>
                                            @if($template->quality_rating !== 'UNKNOWN')
                                                <br>
                                                <small class="badge bg-{{ $template->quality_color }}">
                                                    {{ $template->quality_rating }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            <strong>{{ number_format($template->usage_count) }}</strong>
                                            @if($template->usage_count > 0)
                                                <br>
                                                <small class="text-success">{{ $template->success_count }} نجح</small>
                                                <br>
                                                <small class="text-danger">{{ $template->failure_count }} فشل</small>
                                            @endif
                                        </td>
                                        <td>
                                            @if($template->usage_count > 0)
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-success"
                                                         style="width: {{ $template->success_rate }}%">
                                                        {{ $template->success_rate }}%
                                                    </div>
                                                </div>
                                            @else
                                                <span class="text-muted">لا يوجد</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($template->last_used_at)
                                                {{ $template->last_used_at->diffForHumans() }}
                                            @else
                                                <span class="text-muted">لم يُستخدم</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('whatsapp.templates.show', $template) }}"
                                                   class="btn btn-outline-primary" title="عرض">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                @if($template->status !== 'APPROVED')
                                                    <a href="{{ route('whatsapp.templates.edit', $template) }}"
                                                       class="btn btn-outline-secondary" title="تعديل">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                @endif
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-secondary dropdown-toggle"
                                                            data-bs-toggle="dropdown">
                                                        <i class="bi bi-three-dots"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item"
                                                               href="{{ route('whatsapp.templates.duplicate', $template) }}">
                                                                <i class="bi bi-files"></i> نسخ
                                                            </a>
                                                        </li>
                                                        @if($template->status === 'DRAFT')
                                                            <li>
                                                                <form method="POST"
                                                                      action="{{ route('whatsapp.templates.submit-approval', $template) }}"
                                                                      class="d-inline">
                                                                    @csrf
                                                                    <button type="submit" class="dropdown-item">
                                                                        <i class="bi bi-check-circle"></i> تقديم للموافقة
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        @endif
                                                        @if($template->status === 'PENDING' && auth()->check() && auth()->user()->can('approve-templates'))
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li>
                                                                <button type="button" class="dropdown-item text-success"
                                                                        onclick="approveTemplate({{ $template->id }})">
                                                                    <i class="bi bi-check-lg"></i> اعتماد
                                                                </button>
                                                            </li>
                                                            <li>
                                                                <button type="button" class="dropdown-item text-danger"
                                                                        onclick="rejectTemplate({{ $template->id }})">
                                                                    <i class="bi bi-x-lg"></i> رفض
                                                                </button>
                                                            </li>
                                                        @endif
                                                        @if($template->isApproved() && !$template->whatsapp_template_id)
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li>
                                                                <form method="POST"
                                                                      action="{{ route('whatsapp.templates.submit-whatsapp', $template) }}"
                                                                      class="d-inline">
                                                                    @csrf
                                                                    <button type="submit" class="dropdown-item">
                                                                        <i class="bi bi-whatsapp"></i> إرسال لواتساب
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        @endif
                                                        @if($template->canBeUsed())
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li>
                                                                <button type="button" class="dropdown-item"
                                                                        onclick="testTemplate({{ $template->id }})">
                                                                    <i class="bi bi-send"></i> اختبار الإرسال
                                                                </button>
                                                            </li>
                                                        @endif
                                                        @if($template->status !== 'APPROVED' || $template->usage_count === 0)
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li>
                                                                <form method="POST"
                                                                      action="{{ route('whatsapp.templates.destroy', $template) }}"
                                                                      class="d-inline"
                                                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا القالب؟')">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                    <button type="submit" class="dropdown-item text-danger">
                                                                        <i class="bi bi-trash"></i> حذف
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        @endif
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $templates->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="bi bi-chat-square-text display-1 text-muted"></i>
                            <h4 class="mt-3">لا توجد قوالب</h4>
                            <p class="text-muted">ابدأ بإنشاء قالب جديد لرسائل واتساب</p>
                            <a href="{{ route('whatsapp.templates.create') }}" class="btn btn-primary">
                                <i class="bi bi-plus-lg"></i> إنشاء قالب جديد
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Modal -->
<div class="modal fade" id="filtersModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تصفية القوالب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="GET">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ request('search') }}" placeholder="ابحث في أسماء ووصف القوالب">
                    </div>
                    <div class="mb-3">
                        <label for="category" class="form-label">الفئة</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">جميع الفئات</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}"
                                        {{ request('category') == $category->id ? 'selected' : '' }}>
                                    {{ $category->name_ar }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="DRAFT" {{ request('status') === 'DRAFT' ? 'selected' : '' }}>مسودة</option>
                            <option value="PENDING" {{ request('status') === 'PENDING' ? 'selected' : '' }}>في الانتظار</option>
                            <option value="APPROVED" {{ request('status') === 'APPROVED' ? 'selected' : '' }}>معتمد</option>
                            <option value="REJECTED" {{ request('status') === 'REJECTED' ? 'selected' : '' }}>مرفوض</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="language" class="form-label">اللغة</label>
                        <select class="form-select" id="language" name="language">
                            <option value="">جميع اللغات</option>
                            <option value="ar" {{ request('language') === 'ar' ? 'selected' : '' }}>عربي</option>
                            <option value="en" {{ request('language') === 'en' ? 'selected' : '' }}>English</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <a href="{{ route('whatsapp.templates.index') }}" class="btn btn-outline-secondary">إعادة تعيين</a>
                    <button type="submit" class="btn btn-primary">تطبيق التصفية</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function approveTemplate(templateId) {
    const notes = prompt('ملاحظات الموافقة (اختياري):');
    if (notes !== null) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/whatsapp/templates/${templateId}/approve`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        const notesInput = document.createElement('input');
        notesInput.type = 'hidden';
        notesInput.name = 'approval_notes';
        notesInput.value = notes;

        form.appendChild(csrfToken);
        form.appendChild(notesInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function rejectTemplate(templateId) {
    const reason = prompt('سبب الرفض (مطلوب):');
    if (reason && reason.trim()) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/whatsapp/templates/${templateId}/reject`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        const reasonInput = document.createElement('input');
        reasonInput.type = 'hidden';
        reasonInput.name = 'rejection_reason';
        reasonInput.value = reason;

        form.appendChild(csrfToken);
        form.appendChild(reasonInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function testTemplate(templateId) {
    const phone = prompt('رقم الهاتف للاختبار (مثال: 966501234567):');
    if (phone && phone.trim()) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/whatsapp/templates/${templateId}/test-send`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        const phoneInput = document.createElement('input');
        phoneInput.type = 'hidden';
        phoneInput.name = 'test_phone';
        phoneInput.value = phone;

        form.appendChild(csrfToken);
        form.appendChild(phoneInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
