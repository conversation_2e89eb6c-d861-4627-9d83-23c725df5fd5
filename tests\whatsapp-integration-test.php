<?php

/**
 * WhatsApp Integration Testing Script
 * 
 * This script tests the WhatsApp Business API integration
 * Run with: php tests/whatsapp-integration-test.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

class WhatsAppIntegrationTest
{
    private $whatsappService;
    private $botService;
    private $testPhoneNumber;
    
    public function __construct()
    {
        $this->whatsappService = app(\App\Services\WhatsAppService::class);
        $this->botService = app(\App\Services\WhatsAppBotService::class);
        $this->testPhoneNumber = '966501234567'; // Replace with your test number
    }
    
    public function runAllTests()
    {
        echo "🚀 Starting WhatsApp Integration Tests\n";
        echo "=====================================\n\n";
        
        $this->testConfiguration();
        $this->testDatabaseConnection();
        $this->testWebhookVerification();
        $this->testMessageSending();
        $this->testBotResponses();
        $this->testSessionManagement();
        $this->testIntentDetection();
        
        echo "\n✅ All tests completed!\n";
    }
    
    public function testConfiguration()
    {
        echo "📋 Testing Configuration...\n";
        
        $requiredConfigs = [
            'whatsapp.access_token',
            'whatsapp.phone_number_id',
            'whatsapp.webhook_verify_token',
            'whatsapp.business_name'
        ];
        
        foreach ($requiredConfigs as $config) {
            $value = config($config);
            if (empty($value)) {
                echo "❌ Missing configuration: {$config}\n";
            } else {
                echo "✅ {$config}: " . (strlen($value) > 20 ? substr($value, 0, 20) . '...' : $value) . "\n";
            }
        }
        
        echo "\n";
    }
    
    public function testDatabaseConnection()
    {
        echo "🗄️ Testing Database Connection...\n";
        
        try {
            // Test WhatsApp messages table
            $messageCount = \App\Models\WhatsAppMessage::count();
            echo "✅ WhatsApp messages table accessible (count: {$messageCount})\n";
            
            // Test WhatsApp sessions table
            $sessionCount = \App\Models\WhatsAppSession::count();
            echo "✅ WhatsApp sessions table accessible (count: {$sessionCount})\n";
            
            // Test customers table integration
            $customerCount = \App\Models\Customer::count();
            echo "✅ Customers table accessible (count: {$customerCount})\n";
            
        } catch (\Exception $e) {
            echo "❌ Database error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    public function testWebhookVerification()
    {
        echo "🔗 Testing Webhook Verification...\n";
        
        try {
            $controller = new \App\Http\Controllers\WhatsAppController(
                $this->whatsappService,
                $this->botService
            );
            
            // Test valid verification
            $request = Request::create('/api/whatsapp/webhook', 'GET', [
                'hub_mode' => 'subscribe',
                'hub_verify_token' => config('whatsapp.webhook_verify_token'),
                'hub_challenge' => 'test_challenge_123'
            ]);
            
            $response = $controller->verifyWebhook($request);
            
            if ($response->getStatusCode() === 200 && $response->getContent() === 'test_challenge_123') {
                echo "✅ Webhook verification successful\n";
            } else {
                echo "❌ Webhook verification failed\n";
            }
            
            // Test invalid verification
            $request = Request::create('/api/whatsapp/webhook', 'GET', [
                'hub_mode' => 'subscribe',
                'hub_verify_token' => 'invalid_token',
                'hub_challenge' => 'test_challenge_123'
            ]);
            
            $response = $controller->verifyWebhook($request);
            
            if ($response->getStatusCode() === 403) {
                echo "✅ Invalid webhook token properly rejected\n";
            } else {
                echo "❌ Invalid webhook token not properly rejected\n";
            }
            
        } catch (\Exception $e) {
            echo "❌ Webhook test error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    public function testMessageSending()
    {
        echo "📤 Testing Message Sending...\n";
        
        if (app()->environment('production')) {
            echo "⚠️ Skipping message sending test in production\n\n";
            return;
        }
        
        try {
            // Test text message
            $result = $this->whatsappService->sendTextMessage(
                $this->testPhoneNumber,
                "🧪 Test message from NJ Repair Shop WhatsApp integration"
            );
            
            if ($result['success']) {
                echo "✅ Text message sent successfully (ID: {$result['message_id']})\n";
            } else {
                echo "❌ Text message failed: " . $result['error'] . "\n";
            }
            
            // Test interactive message
            $buttons = [
                ['id' => 'test_btn_1', 'title' => 'خيار 1'],
                ['id' => 'test_btn_2', 'title' => 'خيار 2']
            ];
            
            $result = $this->whatsappService->sendInteractiveMessage(
                $this->testPhoneNumber,
                "🧪 رسالة تفاعلية للاختبار",
                $buttons
            );
            
            if ($result['success']) {
                echo "✅ Interactive message sent successfully\n";
            } else {
                echo "❌ Interactive message failed: " . $result['error'] . "\n";
            }
            
        } catch (\Exception $e) {
            echo "❌ Message sending error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    public function testBotResponses()
    {
        echo "🤖 Testing Bot Responses...\n";
        
        try {
            // Test greeting detection
            $testMessages = [
                ['content' => 'مرحبا', 'expected_intent' => 'greeting'],
                ['content' => 'حالة الجهاز', 'expected_intent' => 'ticket_status'],
                ['content' => 'ساعات العمل', 'expected_intent' => 'business_hours'],
                ['content' => 'الموقع', 'expected_intent' => 'location'],
                ['content' => '0501234567', 'expected_intent' => 'phone_number'],
            ];
            
            foreach ($testMessages as $test) {
                $intent = $this->detectIntentForTest($test['content']);
                
                if ($intent === $test['expected_intent']) {
                    echo "✅ Intent detection: '{$test['content']}' -> {$intent}\n";
                } else {
                    echo "❌ Intent detection failed: '{$test['content']}' -> expected {$test['expected_intent']}, got {$intent}\n";
                }
            }
            
        } catch (\Exception $e) {
            echo "❌ Bot response test error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    public function testSessionManagement()
    {
        echo "🔄 Testing Session Management...\n";
        
        try {
            $testPhone = '966501234567';
            
            // Create session
            $session = \App\Models\WhatsAppSession::getOrCreateForCustomer($testPhone);
            echo "✅ Session created: {$session->session_id}\n";
            
            // Test session activity
            $session->updateActivity();
            echo "✅ Session activity updated\n";
            
            // Test flow management
            $session->setFlow('test_flow', 'test_step', ['test_data' => 'value']);
            echo "✅ Session flow set\n";
            
            // Test rate limiting
            $isLimited = $session->isRateLimited();
            echo "✅ Rate limiting check: " . ($isLimited ? 'Limited' : 'Not limited') . "\n";
            
            // Clean up
            $session->delete();
            echo "✅ Test session cleaned up\n";
            
        } catch (\Exception $e) {
            echo "❌ Session management error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    public function testIntentDetection()
    {
        echo "🎯 Testing Intent Detection...\n";
        
        $testCases = [
            // Arabic greetings
            'مرحبا' => 'greeting',
            'السلام عليكم' => 'greeting',
            'أهلا' => 'greeting',
            
            // Ticket status queries
            'حالة الجهاز' => 'ticket_status',
            'وين جهازي' => 'ticket_status',
            'جهازي' => 'ticket_status',
            
            // Business hours
            'ساعات العمل' => 'business_hours',
            'متى مفتوح' => 'business_hours',
            
            // Location
            'العنوان' => 'location',
            'الموقع' => 'location',
            'أين' => 'location',
            
            // Services
            'الخدمات' => 'services',
            'ماذا تصلحون' => 'services',
            
            // Phone numbers
            '0501234567' => 'phone_number',
            '966501234567' => 'phone_number',
            '+966501234567' => 'phone_number',
        ];
        
        $correctDetections = 0;
        $totalTests = count($testCases);
        
        foreach ($testCases as $input => $expectedIntent) {
            $detectedIntent = $this->detectIntentForTest($input);
            
            if ($detectedIntent === $expectedIntent) {
                echo "✅ '{$input}' -> {$detectedIntent}\n";
                $correctDetections++;
            } else {
                echo "❌ '{$input}' -> expected {$expectedIntent}, got {$detectedIntent}\n";
            }
        }
        
        $accuracy = ($correctDetections / $totalTests) * 100;
        echo "\n📊 Intent Detection Accuracy: {$accuracy}% ({$correctDetections}/{$totalTests})\n";
        
        echo "\n";
    }
    
    private function detectIntentForTest($content)
    {
        // Simulate the intent detection logic from WhatsAppBotService
        $content = strtolower($content);
        
        $keywords = [
            'greeting' => ['مرحبا', 'السلام عليكم', 'أهلا', 'صباح الخير', 'مساء الخير'],
            'ticket_status' => ['حالة الجهاز', 'وين جهازي', 'جهازي', 'التذكرة', 'الإصلاح'],
            'business_hours' => ['ساعات العمل', 'متى مفتوح', 'أوقات العمل', 'مواعيد'],
            'location' => ['العنوان', 'الموقع', 'أين', 'وين'],
            'services' => ['الخدمات', 'ماذا تصلحون', 'إيش تسوون'],
        ];
        
        foreach ($keywords as $intent => $intentKeywords) {
            foreach ($intentKeywords as $keyword) {
                if (str_contains($content, strtolower($keyword))) {
                    return $intent;
                }
            }
        }
        
        // Check for phone number pattern
        if (preg_match('/\b(05\d{8}|966\d{9}|\+966\d{9})\b/', $content)) {
            return 'phone_number';
        }
        
        return 'unknown';
    }
}

// Run tests if script is executed directly
if (php_sapi_name() === 'cli') {
    $tester = new WhatsAppIntegrationTest();
    $tester->runAllTests();
}
