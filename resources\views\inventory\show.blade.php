@extends('layouts.app')

@section('title', $inventory->name . ' - تفاصيل العنصر')

@push('styles')
<style>
.info-card {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.info-card-header {
    background: linear-gradient(45deg, #4e73df, #224abe);
    color: white;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e3e6f0;
}

.info-card-body {
    padding: 1.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f8f9fc;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #5a5c69;
    min-width: 150px;
}

.info-value {
    color: #3a3b45;
    text-align: left;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.875rem;
}

.status-normal { background: #d4edda; color: #155724; }
.status-low { background: #fff3cd; color: #856404; }
.status-out { background: #f8d7da; color: #721c24; }
.status-over { background: #cce5ff; color: #004085; }

.metric-card {
    background: linear-gradient(45deg, #4e73df, #224abe);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.action-buttons {
    position: sticky;
    top: 20px;
    z-index: 100;
}

.btn-action {
    margin-bottom: 0.5rem;
    width: 100%;
    text-align: right;
    border-radius: 0.35rem;
    padding: 0.75rem 1rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.timeline-item {
    position: relative;
    padding-left: 2rem;
    padding-bottom: 1rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item::after {
    content: '';
    position: absolute;
    left: 0.25rem;
    top: 0.5rem;
    width: 0.5rem;
    height: 0.5rem;
    background: #4e73df;
    border-radius: 50%;
}

.timeline-item:last-child::before {
    display: none;
}

.qr-code {
    text-align: center;
    padding: 1rem;
    background: #f8f9fc;
    border-radius: 0.35rem;
    margin: 1rem 0;
}

.stock-indicator {
    height: 8px;
    background: #e3e6f0;
    border-radius: 4px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.stock-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.alert-custom {
    border-radius: 0.35rem;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .info-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .info-label {
        margin-bottom: 0.25rem;
        min-width: auto;
    }

    .info-value {
        text-align: right;
    }

    .action-buttons {
        position: static;
        margin-top: 2rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ $inventory->name }}</h1>
            <p class="text-muted">
                <i class="fas fa-barcode me-1"></i>{{ $inventory->sku }}
                @if($inventory->brand)
                    <span class="mx-2">|</span>
                    <i class="fas fa-tag me-1"></i>{{ $inventory->brand->name }}
                @endif
            </p>
        </div>
        <div>
            <a href="{{ route('inventory.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للمخزون
            </a>
        </div>
    </div>

    <!-- Stock Status Alert -->
    @if($inventory->current_stock <= $inventory->minimum_stock)
        <div class="alert alert-warning alert-custom">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> المخزون منخفض! الكمية الحالية ({{ $inventory->current_stock }}) أقل من الحد الأدنى ({{ $inventory->minimum_stock }}).
        </div>
    @elseif($inventory->current_stock == 0)
        <div class="alert alert-danger alert-custom">
            <i class="fas fa-times-circle me-2"></i>
            <strong>تحذير:</strong> نفد المخزون! لا توجد كمية متاحة من هذا العنصر.
        </div>
    @endif

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="info-card">
                <div class="info-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        المعلومات الأساسية
                    </h5>
                </div>
                <div class="info-card-body">
                    <div class="info-item">
                        <span class="info-label">اسم العنصر:</span>
                        <span class="info-value">{{ $inventory->name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">رمز المنتج (SKU):</span>
                        <span class="info-value">
                            <code class="bg-light px-2 py-1 rounded">{{ $inventory->sku }}</code>
                        </span>
                    </div>
                    @if($inventory->barcode)
                    <div class="info-item">
                        <span class="info-label">الباركود:</span>
                        <span class="info-value">
                            <code class="bg-light px-2 py-1 rounded">{{ $inventory->barcode }}</code>
                        </span>
                    </div>
                    @endif
                    <div class="info-item">
                        <span class="info-label">الفئة:</span>
                        <span class="info-value">
                            <span class="badge rounded-pill" style="background-color: {{ $inventory->category->color ?? '#6B7280' }}; color: white;">
                                @if($inventory->category->icon)
                                    <i class="{{ $inventory->category->icon }} me-1"></i>
                                @endif
                                {{ $inventory->category->name }}
                            </span>
                        </span>
                    </div>
                    @if($inventory->brand)
                    <div class="info-item">
                        <span class="info-label">الماركة:</span>
                        <span class="info-value">{{ $inventory->brand->name }}</span>
                    </div>
                    @endif
                    @if($inventory->model_compatibility)
                    <div class="info-item">
                        <span class="info-label">توافق الموديل:</span>
                        <span class="info-value">{{ $inventory->model_compatibility }}</span>
                    </div>
                    @endif
                    @if($inventory->description)
                    <div class="info-item">
                        <span class="info-label">الوصف:</span>
                        <span class="info-value">{{ $inventory->description }}</span>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Stock Information -->
            <div class="info-card">
                <div class="info-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-boxes me-2"></i>
                        معلومات المخزون
                    </h5>
                </div>
                <div class="info-card-body">
                    <div class="info-item">
                        <span class="info-label">المخزون الحالي:</span>
                        <span class="info-value">
                            <span class="badge bg-info text-white fs-6 px-3 py-2">
                                @arabicNumber($inventory->current_stock) {{ $inventory->unit_of_measure }}
                            </span>
                        </span>
                    </div>

                    <!-- Stock Progress Bar -->
                    <div class="info-item">
                        <span class="info-label">مستوى المخزون:</span>
                        <div class="info-value" style="width: 200px;">
                            <div class="stock-indicator">
                                @php
                                    $percentage = $inventory->maximum_stock > 0 ?
                                        min(($inventory->current_stock / $inventory->maximum_stock) * 100, 100) : 0;
                                    $color = $inventory->current_stock <= $inventory->minimum_stock ? '#dc3545' :
                                            ($inventory->current_stock >= $inventory->maximum_stock ? '#007bff' : '#28a745');
                                @endphp
                                <div class="stock-fill"
                                     style="width: {{ $percentage }}%; background-color: {{ $color }};"></div>
                            </div>
                            <small class="text-muted">
                                {{ number_format($percentage, 1) }}% من السعة القصوى
                            </small>
                        </div>
                    </div>

                    <div class="info-item">
                        <span class="info-label">الحد الأدنى:</span>
                        <span class="info-value">@arabicNumber($inventory->minimum_stock) {{ $inventory->unit_of_measure }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الحد الأقصى:</span>
                        <span class="info-value">@arabicNumber($inventory->maximum_stock) {{ $inventory->unit_of_measure }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">كمية إعادة الطلب:</span>
                        <span class="info-value">@arabicNumber($inventory->reorder_quantity) {{ $inventory->unit_of_measure }}</span>
                    </div>
                    @if($inventory->location)
                    <div class="info-item">
                        <span class="info-label">الموقع:</span>
                        <span class="info-value">
                            <i class="fas fa-map-marker-alt text-info me-1"></i>{{ $inventory->location }}
                        </span>
                    </div>
                    @endif
                    @if($inventory->warehouse_section)
                    <div class="info-item">
                        <span class="info-label">قسم المستودع:</span>
                        <span class="info-value">{{ $inventory->warehouse_section }}</span>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Pricing Information -->
            <div class="info-card">
                <div class="info-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>
                        معلومات الأسعار
                    </h5>
                </div>
                <div class="info-card-body">
                    <div class="info-item">
                        <span class="info-label">سعر التكلفة:</span>
                        <span class="info-value fw-bold">@arabicCurrency($inventory->cost_price)</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">سعر البيع:</span>
                        <span class="info-value fw-bold text-success">@arabicCurrency($inventory->selling_price)</span>
                    </div>
                    @if($inventory->wholesale_price)
                    <div class="info-item">
                        <span class="info-label">سعر الجملة:</span>
                        <span class="info-value fw-bold text-info">@arabicCurrency($inventory->wholesale_price)</span>
                    </div>
                    @endif
                    <div class="info-item">
                        <span class="info-label">نسبة الربح:</span>
                        <span class="info-value">
                            <span class="badge bg-success">+@arabicPercentage($inventory->markup_percentage)</span>
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الربح للوحدة:</span>
                        <span class="info-value fw-bold text-success">
                            @arabicCurrency($inventory->selling_price - $inventory->cost_price)
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Action Buttons -->
            <div class="action-buttons">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">الإجراءات</h6>
                    </div>
                    <div class="card-body">
                        <a href="{{ route('inventory.edit', $inventory) }}" class="btn btn-success btn-action">
                            <i class="fas fa-edit me-2"></i>تعديل العنصر
                        </a>
                        <a href="{{ route('inventory.adjust-stock', $inventory) }}" class="btn btn-warning btn-action">
                            <i class="fas fa-balance-scale me-2"></i>تعديل المخزون
                        </a>
                        <button type="button" class="btn btn-info btn-action" onclick="showMovements()">
                            <i class="fas fa-history me-2"></i>سجل الحركات
                        </button>
                        <button type="button" class="btn btn-secondary btn-action" onclick="printLabel()">
                            <i class="fas fa-print me-2"></i>طباعة ملصق
                        </button>
                        <button type="button" class="btn btn-danger btn-action" onclick="confirmDelete()">
                            <i class="fas fa-trash me-2"></i>حذف العنصر
                        </button>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row">
                <div class="col-6">
                    <div class="metric-card bg-primary">
                        <div class="metric-value">@arabicCurrency($stockStats['total_value'])</div>
                        <div class="metric-label">قيمة المخزون</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="metric-card bg-success">
                        <div class="metric-value">@arabicCurrency($stockStats['potential_revenue'])</div>
                        <div class="metric-label">الإيرادات المحتملة</div>
                    </div>
                </div>
            </div>

            <!-- Status Card -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">حالة العنصر</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <span class="badge {{ $inventory->stock_status_badge_class }} status-badge">
                            {{ $inventory->stock_status_display }}
                        </span>
                    </div>

                    @if($inventory->isExpiringSoon())
                        <div class="alert alert-warning alert-custom">
                            <i class="fas fa-clock me-2"></i>
                            ينتهي خلال {{ $inventory->days_until_expiry }} أيام
                        </div>
                    @endif

                    <div class="small text-muted">
                        <div><strong>تم الإنشاء:</strong> {{ $inventory->created_at->format('Y-m-d H:i') }}</div>
                        <div><strong>آخر تحديث:</strong> {{ $inventory->updated_at->format('Y-m-d H:i') }}</div>
                        @if($inventory->createdBy)
                            <div><strong>بواسطة:</strong> {{ $inventory->createdBy->name }}</div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Movements Section -->
    @if($recentMovements->count() > 0)
    <div class="row mt-4">
        <div class="col-12">
            <div class="info-card">
                <div class="info-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        آخر الحركات
                    </h5>
                </div>
                <div class="info-card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                    <th>الكمية</th>
                                    <th>المخزون بعد الحركة</th>
                                    <th>الملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentMovements as $movement)
                                <tr>
                                    <td>{{ $movement->movement_date->format('Y-m-d H:i') }}</td>
                                    <td>
                                        <span class="badge bg-{{ $movement->direction === 'in' ? 'success' : 'danger' }}">
                                            {{ $movement->movement_type_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-{{ $movement->direction === 'in' ? 'success' : 'danger' }}">
                                            {{ $movement->direction === 'in' ? '+' : '-' }}@arabicNumber($movement->quantity)
                                        </span>
                                    </td>
                                    <td>@arabicNumber($movement->stock_after)</td>
                                    <td>{{ $movement->notes ?? '-' }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-outline-primary" onclick="showAllMovements()">
                            <i class="fas fa-list me-2"></i>عرض جميع الحركات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Technical Information -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="info-card">
                <div class="info-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        المعلومات التقنية
                    </h5>
                </div>
                <div class="info-card-body">
                    @if($inventory->part_number)
                    <div class="info-item">
                        <span class="info-label">رقم القطعة:</span>
                        <span class="info-value">{{ $inventory->part_number }}</span>
                    </div>
                    @endif
                    @if($inventory->oem_number)
                    <div class="info-item">
                        <span class="info-label">رقم OEM:</span>
                        <span class="info-value">{{ $inventory->oem_number }}</span>
                    </div>
                    @endif
                    <div class="info-item">
                        <span class="info-label">الحالة:</span>
                        <span class="info-value">
                            @switch($inventory->condition)
                                @case('new')
                                    <span class="badge bg-success">جديد</span>
                                    @break
                                @case('refurbished')
                                    <span class="badge bg-warning">مجدد</span>
                                    @break
                                @case('used')
                                    <span class="badge bg-secondary">مستعمل</span>
                                    @break
                            @endswitch
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الضمان:</span>
                        <span class="info-value">
                            @if($inventory->warranty_months > 0)
                                @arabicNumber($inventory->warranty_months) شهر
                            @else
                                بدون ضمان
                            @endif
                        </span>
                    </div>
                    @if($inventory->expiry_date)
                    <div class="info-item">
                        <span class="info-label">تاريخ الانتهاء:</span>
                        <span class="info-value">{{ $inventory->expiry_date->format('Y-m-d') }}</span>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="info-card">
                <div class="info-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-truck me-2"></i>
                        معلومات المورد
                    </h5>
                </div>
                <div class="info-card-body">
                    @if($inventory->primarySupplier)
                    <div class="info-item">
                        <span class="info-label">المورد الأساسي:</span>
                        <span class="info-value">{{ $inventory->primarySupplier->name }}</span>
                    </div>
                    @if($inventory->primarySupplier->phone_number)
                    <div class="info-item">
                        <span class="info-label">هاتف المورد:</span>
                        <span class="info-value">
                            <a href="tel:{{ $inventory->primarySupplier->phone_number }}" class="text-decoration-none">
                                {{ $inventory->primarySupplier->phone_number }}
                            </a>
                        </span>
                    </div>
                    @endif
                    @endif
                    <div class="info-item">
                        <span class="info-label">مدة التوريد:</span>
                        <span class="info-value">@arabicNumber($inventory->lead_time_days) أيام</span>
                    </div>
                    @if($inventory->storage_conditions)
                    <div class="info-item">
                        <span class="info-label">شروط التخزين:</span>
                        <span class="info-value">{{ $inventory->storage_conditions }}</span>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Settings and Notes -->
    @if($inventory->notes || $inventory->is_serialized || $inventory->allow_backorder || $inventory->track_expiry)
    <div class="row mt-4">
        <div class="col-12">
            <div class="info-card">
                <div class="info-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        الإعدادات والملاحظات
                    </h5>
                </div>
                <div class="info-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <span class="info-label">العنصر نشط:</span>
                                <span class="info-value">
                                    <span class="badge bg-{{ $inventory->is_active ? 'success' : 'danger' }}">
                                        {{ $inventory->is_active ? 'نعم' : 'لا' }}
                                    </span>
                                </span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">يتطلب رقم تسلسلي:</span>
                                <span class="info-value">
                                    <span class="badge bg-{{ $inventory->is_serialized ? 'success' : 'secondary' }}">
                                        {{ $inventory->is_serialized ? 'نعم' : 'لا' }}
                                    </span>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <span class="info-label">السماح بالطلب المسبق:</span>
                                <span class="info-value">
                                    <span class="badge bg-{{ $inventory->allow_backorder ? 'success' : 'secondary' }}">
                                        {{ $inventory->allow_backorder ? 'نعم' : 'لا' }}
                                    </span>
                                </span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">تتبع تاريخ الانتهاء:</span>
                                <span class="info-value">
                                    <span class="badge bg-{{ $inventory->track_expiry ? 'success' : 'secondary' }}">
                                        {{ $inventory->track_expiry ? 'نعم' : 'لا' }}
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                    @if($inventory->notes)
                    <div class="info-item">
                        <span class="info-label">الملاحظات:</span>
                        <span class="info-value">{{ $inventory->notes }}</span>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<!-- Movements Modal -->
<div class="modal fade" id="movementsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">سجل حركات المخزون - {{ $inventory->name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="movementsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function showMovements() {
    const modal = new bootstrap.Modal(document.getElementById('movementsModal'));
    modal.show();

    // Load movements data via AJAX
    fetch(`/inventory/{{ $inventory->id }}/movements`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('movementsContent').innerHTML = data.html;
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('movementsContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    خطأ في تحميل البيانات
                </div>
            `;
        });
}

function showAllMovements() {
    window.location.href = `/inventory/{{ $inventory->id }}/movements`;
}

function printLabel() {
    // Generate and print barcode label
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>ملصق - {{ $inventory->name }}</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                    .label { border: 2px solid #000; padding: 20px; display: inline-block; }
                    .name { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
                    .sku { font-size: 14px; margin-bottom: 10px; }
                    .barcode { font-family: 'Courier New', monospace; font-size: 24px; letter-spacing: 2px; }
                </style>
            </head>
            <body>
                <div class="label">
                    <div class="name">{{ $inventory->name }}</div>
                    <div class="sku">SKU: {{ $inventory->sku }}</div>
                    @if($inventory->barcode)
                    <div class="barcode">{{ $inventory->barcode }}</div>
                    @endif
                    <div style="margin-top: 10px; font-size: 12px;">
                        {{ $inventory->category->name }}
                        @if($inventory->brand) - {{ $inventory->brand->name }} @endif
                    </div>
                </div>
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

function confirmDelete() {
    if (confirm('هل أنت متأكد من حذف العنصر "{{ $inventory->name }}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.')) {
        // Create a form to submit delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/inventory/{{ $inventory->id }}';

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        form.appendChild(csrfToken);

        // Add method override for DELETE
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        form.appendChild(methodField);

        // Submit form
        document.body.appendChild(form);
        form.submit();
    }
}

// Auto-refresh stock status every 30 seconds
setInterval(function() {
    fetch('/inventory/{{ $inventory->id }}/status')
        .then(response => response.json())
        .then(data => {
            // Update stock display if changed
            if (data.current_stock !== {{ $inventory->current_stock }}) {
                location.reload();
            }
        })
        .catch(error => console.error('Status check error:', error));
}, 30000);
</script>
@endpush
@endsection
