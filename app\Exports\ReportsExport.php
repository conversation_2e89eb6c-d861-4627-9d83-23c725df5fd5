<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class ReportsExport implements FromArray, WithHeadings, WithStyles, ShouldAutoSize
{
    protected $data;
    protected $type;

    public function __construct(array $data, string $type = 'general')
    {
        $this->data = $data;
        $this->type = $type;
    }

    /**
     * Return the data array
     */
    public function array(): array
    {
        return $this->data;
    }

    /**
     * Define the headings based on report type
     */
    public function headings(): array
    {
        switch ($this->type) {
            case 'dashboard':
                return [
                    'Metric',
                    'Value',
                    'Description',
                ];
            case 'customer-analytics':
                return [
                    'Customer Name',
                    'Phone Number',
                    'Total Tickets',
                    'Total Spent',
                    'Average per Ticket',
                    'Last Visit',
                ];
            case 'business-intelligence':
                return [
                    'Brand',
                    'Total Tickets',
                    'Total Revenue',
                    'Average Revenue',
                    'Performance Score',
                ];
            default:
                return [
                    'Item',
                    'Value',
                    'Details',
                ];
        }
    }

    /**
     * Apply styles to the worksheet
     */
    public function styles(Worksheet $sheet)
    {
        $headerColor = match($this->type) {
            'dashboard' => '4472C4',
            'customer-analytics' => '28A745',
            'business-intelligence' => 'FD7E14',
            default => '6C757D',
        };

        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => $headerColor],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            // Style all cells
            'A:Z' => [
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
        ];
    }
}
