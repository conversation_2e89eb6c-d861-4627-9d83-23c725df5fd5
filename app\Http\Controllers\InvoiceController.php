<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Customer;
use App\Models\RepairTicket;
use App\Models\InventoryItem;
use App\Models\InvoiceSetting;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class InvoiceController extends Controller
{
    /**
     * Display a listing of invoices.
     */
    public function index(Request $request): View
    {
        $query = Invoice::with(['customer', 'repairTicket', 'createdBy'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_phone', 'like', "%{$search}%")
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('phone_number', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('invoice_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('invoice_date', '<=', $request->date_to);
        }

        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        $invoices = $query->paginate(20);

        // Statistics
        $stats = [
            'total_invoices' => Invoice::count(),
            'draft_invoices' => Invoice::where('status', 'draft')->count(),
            'sent_invoices' => Invoice::where('status', 'sent')->count(),
            'paid_invoices' => Invoice::where('payment_status', 'paid')->count(),
            'overdue_invoices' => Invoice::where('payment_status', 'overdue')->count(),
            'total_amount' => Invoice::sum('total_amount'),
            'paid_amount' => Invoice::sum('paid_amount'),
            'pending_amount' => Invoice::where('payment_status', '!=', 'paid')->sum('total_amount') - Invoice::where('payment_status', '!=', 'paid')->sum('paid_amount'),
        ];

        $customers = Customer::orderBy('name')->get();

        return view('invoices.index', compact('invoices', 'stats', 'customers'));
    }

    /**
     * Show the form for creating a new invoice.
     */
    public function create(Request $request): View
    {
        $customers = Customer::orderBy('name')->get();
        // Get completed repair tickets without invoices
        $completedStatusId = \App\Models\RepairStatus::where('name', 'مكتمل')
            ->orWhere('name', 'completed')
            ->orWhere('is_final', true)
            ->first()?->id;

        $repairTickets = RepairTicket::with('customer')
            ->when($completedStatusId, function($query) use ($completedStatusId) {
                $query->where('repair_status_id', $completedStatusId);
            })
            ->whereDoesntHave('invoice')
            ->orderBy('created_at', 'desc')
            ->get();

        $inventoryItems = InventoryItem::with(['category', 'brand'])
            ->where('is_active', true)
            ->where('current_stock', '>', 0)
            ->orderBy('name')
            ->get();

        $settings = InvoiceSetting::getSettings();

        // Pre-select repair ticket if provided
        $selectedRepairTicket = null;
        if ($request->filled('repair_ticket_id')) {
            $selectedRepairTicket = RepairTicket::with('customer')->find($request->repair_ticket_id);
        }

        return view('invoices.create', compact(
            'customers',
            'repairTickets',
            'inventoryItems',
            'settings',
            'selectedRepairTicket'
        ));
    }

    /**
     * Store a newly created invoice.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'repair_ticket_id' => 'nullable|exists:repair_tickets,id',
            'invoice_date' => 'required|date',
            'due_date' => 'required|date|after_or_equal:invoice_date',
            'tax_rate' => 'required|numeric|min:0|max:100',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
            'terms_conditions' => 'nullable|string|max:2000',
            'items' => 'required|array|min:1',
            'items.*.item_type' => 'required|in:service,part,labor,other',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.item_description' => 'nullable|string|max:500',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.inventory_item_id' => 'nullable|exists:inventory_items,id',
            'items.*.is_taxable' => 'boolean',
        ]);

        DB::beginTransaction();
        try {
            // Get customer details
            $customer = Customer::findOrFail($validated['customer_id']);

            // Generate invoice number
            $invoiceNumber = $this->generateInvoiceNumber();

            // Get company details
            $companyDetails = InvoiceSetting::getCompanyDetails();

            // Create invoice
            $invoice = Invoice::create([
                'invoice_number' => $invoiceNumber,
                'customer_id' => $validated['customer_id'],
                'repair_ticket_id' => $validated['repair_ticket_id'],
                'invoice_date' => $validated['invoice_date'],
                'due_date' => $validated['due_date'],
                'tax_rate' => $validated['tax_rate'],
                'discount_percentage' => $validated['discount_percentage'] ?? 0,
                'discount_amount' => $validated['discount_amount'] ?? 0,
                'notes' => $validated['notes'],
                'terms_conditions' => $validated['terms_conditions'] ?? InvoiceSetting::getInvoiceTerms(),
                'status' => 'draft',
                'payment_status' => 'pending',
                'customer_name' => $customer->name,
                'customer_phone' => $customer->phone_number,
                'customer_email' => $customer->email,
                'customer_address' => $customer->address,
                'company_name' => $companyDetails['name'],
                'company_phone' => $companyDetails['phone'],
                'company_email' => $companyDetails['email'],
                'company_address' => $companyDetails['address'],
                'company_tax_number' => $companyDetails['tax_number'],
                'currency' => 'SAR',
                'created_by' => Auth::id(),
            ]);

            // Add invoice items
            $subtotal = 0;
            foreach ($validated['items'] as $index => $itemData) {
                $totalPrice = $itemData['quantity'] * $itemData['unit_price'];
                $subtotal += $totalPrice;

                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'item_type' => $itemData['item_type'],
                    'item_name' => $itemData['item_name'],
                    'item_description' => $itemData['item_description'],
                    'inventory_item_id' => $itemData['inventory_item_id'],
                    'quantity' => $itemData['quantity'],
                    'unit_of_measure' => $itemData['unit_of_measure'] ?? 'قطعة',
                    'unit_price' => $itemData['unit_price'],
                    'total_price' => $totalPrice,
                    'tax_rate' => $itemData['is_taxable'] ? $validated['tax_rate'] : 0,
                    'is_taxable' => $itemData['is_taxable'] ?? true,
                    'sort_order' => $index + 1,
                ]);

                // Update inventory if item is from inventory
                if ($itemData['inventory_item_id'] ?? null) {
                    $inventoryItem = InventoryItem::find($itemData['inventory_item_id']);
                    if ($inventoryItem) {
                        $inventoryItem->decrement('current_stock', $itemData['quantity']);

                        // Create inventory movement record
                        \App\Models\InventoryMovement::create([
                            'inventory_item_id' => $inventoryItem->id,
                            'type' => 'sale',
                            'quantity' => -$itemData['quantity'], // Negative for outgoing
                            'stock_before' => $inventoryItem->current_stock + $itemData['quantity'],
                            'stock_after' => $inventoryItem->current_stock,
                            'unit_cost' => $inventoryItem->cost_price,
                            'total_cost' => $inventoryItem->cost_price * $itemData['quantity'],
                            'reference_type' => 'invoice',
                            'reference_id' => $invoice->id,
                            'reference_number' => $invoice->invoice_number,
                            'notes' => 'بيع - فاتورة رقم ' . $invoice->invoice_number,
                            'created_by' => Auth::id(),
                        ]);
                    }
                }
            }

            // Calculate totals
            $this->calculateInvoiceTotals($invoice, $subtotal);

            // If created from repair ticket, update the ticket
            if ($invoice->repair_ticket_id) {
                $repairTicket = RepairTicket::find($invoice->repair_ticket_id);
                if ($repairTicket) {
                    // Update repair ticket final cost if not set
                    if (!$repairTicket->final_cost) {
                        $repairTicket->update(['final_cost' => $invoice->total_amount]);
                    }

                    // Mark as completed if not already
                    $completedStatus = \App\Models\RepairStatus::where('name', 'مكتمل')->first();
                    if ($completedStatus && $repairTicket->repair_status_id !== $completedStatus->id) {
                        $repairTicket->update([
                            'repair_status_id' => $completedStatus->id,
                            'completed_date' => now()
                        ]);
                    }
                }
            }

            DB::commit();

            return redirect()->route('invoices.show', $invoice)
                ->with('success', 'تم إنشاء الفاتورة بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء الفاتورة: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified invoice.
     */
    public function show(Invoice $invoice): View
    {
        $invoice->load(['customer', 'repairTicket', 'items.inventoryItem', 'payments.createdBy', 'createdBy']);

        return view('invoices.show', compact('invoice'));
    }

    /**
     * Show the form for editing the specified invoice.
     */
    public function edit(Invoice $invoice): View
    {
        if (!$invoice->canEdit()) {
            abort(403, 'لا يمكن تعديل هذه الفاتورة');
        }

        $customers = Customer::orderBy('name')->get();
        $inventoryItems = InventoryItem::with(['category', 'brand'])
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $invoice->load(['items.inventoryItem']);

        return view('invoices.edit', compact('invoice', 'customers', 'inventoryItems'));
    }

    /**
     * Update the specified invoice.
     */
    public function update(Request $request, Invoice $invoice): RedirectResponse
    {
        if (!$invoice->canEdit()) {
            return back()->with('error', 'لا يمكن تعديل هذه الفاتورة');
        }

        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'invoice_date' => 'required|date',
            'due_date' => 'required|date|after_or_equal:invoice_date',
            'tax_rate' => 'required|numeric|min:0|max:100',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
            'terms_conditions' => 'nullable|string|max:2000',
            'items' => 'required|array|min:1',
            'items.*.item_type' => 'required|in:service,part,labor,other',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.item_description' => 'nullable|string|max:500',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.inventory_item_id' => 'nullable|exists:inventory_items,id',
            'items.*.is_taxable' => 'boolean',
        ]);

        DB::beginTransaction();
        try {
            // Get customer details
            $customer = Customer::findOrFail($validated['customer_id']);

            // Update invoice
            $invoice->update([
                'customer_id' => $validated['customer_id'],
                'invoice_date' => $validated['invoice_date'],
                'due_date' => $validated['due_date'],
                'tax_rate' => $validated['tax_rate'],
                'discount_percentage' => $validated['discount_percentage'] ?? 0,
                'discount_amount' => $validated['discount_amount'] ?? 0,
                'notes' => $validated['notes'],
                'terms_conditions' => $validated['terms_conditions'],
                'customer_name' => $customer->name,
                'customer_phone' => $customer->phone_number,
                'customer_email' => $customer->email,
                'customer_address' => $customer->address,
            ]);

            // Delete existing items
            $invoice->items()->delete();

            // Add updated items
            $subtotal = 0;
            foreach ($validated['items'] as $index => $itemData) {
                $totalPrice = $itemData['quantity'] * $itemData['unit_price'];
                $subtotal += $totalPrice;

                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'item_type' => $itemData['item_type'],
                    'item_name' => $itemData['item_name'],
                    'item_description' => $itemData['item_description'],
                    'inventory_item_id' => $itemData['inventory_item_id'],
                    'quantity' => $itemData['quantity'],
                    'unit_of_measure' => $itemData['unit_of_measure'] ?? 'قطعة',
                    'unit_price' => $itemData['unit_price'],
                    'total_price' => $totalPrice,
                    'tax_rate' => $itemData['is_taxable'] ? $validated['tax_rate'] : 0,
                    'is_taxable' => $itemData['is_taxable'] ?? true,
                    'sort_order' => $index + 1,
                ]);
            }

            // Recalculate totals
            $this->calculateInvoiceTotals($invoice, $subtotal);

            DB::commit();

            return redirect()->route('invoices.show', $invoice)
                ->with('success', 'تم تحديث الفاتورة بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث الفاتورة: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified invoice.
     */
    public function destroy(Invoice $invoice): RedirectResponse
    {
        if (!$invoice->canDelete()) {
            return back()->with('error', 'لا يمكن حذف هذه الفاتورة');
        }

        try {
            $invoice->delete();
            return redirect()->route('invoices.index')
                ->with('success', 'تم حذف الفاتورة بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء حذف الفاتورة');
        }
    }

    /**
     * Send invoice to customer.
     */
    public function send(Invoice $invoice): RedirectResponse
    {
        if (!$invoice->canSend()) {
            return back()->with('error', 'لا يمكن إرسال هذه الفاتورة');
        }

        try {
            $invoice->update([
                'status' => 'sent',
                'sent_at' => now()
            ]);

            // TODO: Implement actual sending logic (WhatsApp/SMS/Email)

            return back()->with('success', 'تم إرسال الفاتورة بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء إرسال الفاتورة');
        }
    }

    /**
     * Generate PDF for invoice.
     */
    public function pdf(Invoice $invoice)
    {
        $invoice->load(['customer', 'items.inventoryItem', 'payments']);

        // TODO: Implement PDF generation
        return view('invoices.pdf', compact('invoice'));
    }

    /**
     * Print invoice.
     */
    public function print(Invoice $invoice): View
    {
        $invoice->load(['customer', 'items.inventoryItem', 'payments']);

        return view('invoices.print', compact('invoice'));
    }

    /**
     * Generate unique invoice number.
     */
    private function generateInvoiceNumber(): string
    {
        $settings = InvoiceSetting::getSettings();
        $prefix = $settings['invoice_prefix'] ?? 'INV';
        $length = $settings['invoice_number_length'] ?? 6;

        $lastInvoice = Invoice::latest('id')->first();
        $nextNumber = $lastInvoice ? $lastInvoice->id + 1 : 1;

        return $prefix . '-' . str_pad($nextNumber, $length, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate invoice totals.
     */
    private function calculateInvoiceTotals(Invoice $invoice, float $subtotal): void
    {
        $discountAmount = $invoice->discount_amount;
        if ($invoice->discount_percentage > 0) {
            $discountAmount = $subtotal * ($invoice->discount_percentage / 100);
        }

        $taxableAmount = $subtotal - $discountAmount;
        $taxAmount = $taxableAmount * ($invoice->tax_rate / 100);
        $totalAmount = $subtotal - $discountAmount + $taxAmount;

        $invoice->update([
            'subtotal' => $subtotal,
            'discount_amount' => $discountAmount,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
        ]);
    }

    /**
     * Get invoice statistics for dashboard.
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'today' => [
                'invoices_count' => Invoice::whereDate('invoice_date', today())->count(),
                'total_amount' => Invoice::whereDate('invoice_date', today())->sum('total_amount'),
                'paid_amount' => Invoice::whereDate('invoice_date', today())->sum('paid_amount'),
            ],
            'this_month' => [
                'invoices_count' => Invoice::whereMonth('invoice_date', now()->month)->count(),
                'total_amount' => Invoice::whereMonth('invoice_date', now()->month)->sum('total_amount'),
                'paid_amount' => Invoice::whereMonth('invoice_date', now()->month)->sum('paid_amount'),
            ],
            'overdue' => [
                'count' => Invoice::where('due_date', '<', now())
                    ->whereNotIn('payment_status', ['paid'])
                    ->count(),
                'amount' => Invoice::where('due_date', '<', now())
                    ->whereNotIn('payment_status', ['paid'])
                    ->sum('total_amount'),
            ]
        ];

        return response()->json($stats);
    }
}
