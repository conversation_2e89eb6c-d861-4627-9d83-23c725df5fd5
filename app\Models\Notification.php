<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Notification extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'customer_id',
        'repair_ticket_id',
        'type',
        'category',
        'recipient_phone',
        'recipient_email',
        'subject',
        'message',
        'template_data',
        'status',
        'scheduled_at',
        'sent_at',
        'delivered_at',
        'read_at',
        'error_message',
        'external_id',
        'cost',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'template_data' => 'array',
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'read_at' => 'datetime',
        'cost' => 'decimal:4',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the customer that owns the notification.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the repair ticket associated with the notification.
     */
    public function repairTicket(): BelongsTo
    {
        return $this->belongsTo(RepairTicket::class);
    }

    /**
     * Get the user who created the notification.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for pending notifications.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for sent notifications.
     */
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    /**
     * Scope for failed notifications.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for scheduled notifications.
     */
    public function scopeScheduled($query)
    {
        return $query->whereNotNull('scheduled_at')
                    ->where('scheduled_at', '>', now())
                    ->where('status', 'pending');
    }

    /**
     * Scope for due notifications (scheduled time has passed).
     */
    public function scopeDue($query)
    {
        return $query->where('status', 'pending')
                    ->where(function ($q) {
                        $q->whereNull('scheduled_at')
                          ->orWhere('scheduled_at', '<=', now());
                    });
    }

    /**
     * Scope for notifications by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for notifications by category.
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Mark notification as sent.
     */
    public function markAsSent($externalId = null)
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
            'external_id' => $externalId,
        ]);
    }

    /**
     * Mark notification as delivered.
     */
    public function markAsDelivered()
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    /**
     * Mark notification as failed.
     */
    public function markAsFailed($errorMessage = null)
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead()
    {
        $this->update([
            'status' => 'read',
            'read_at' => now(),
        ]);
    }

    /**
     * Check if notification is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->scheduled_at && 
               $this->scheduled_at->isPast() && 
               $this->status === 'pending';
    }

    /**
     * Get formatted recipient based on type.
     */
    public function getFormattedRecipientAttribute(): string
    {
        return match($this->type) {
            'email' => $this->recipient_email ?? 'N/A',
            'sms', 'whatsapp' => $this->recipient_phone ?? 'N/A',
            default => 'N/A'
        };
    }

    /**
     * Get status badge class for UI.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'pending' => 'bg-warning',
            'sent' => 'bg-info',
            'delivered' => 'bg-success',
            'failed' => 'bg-danger',
            'read' => 'bg-primary',
            default => 'bg-secondary'
        };
    }

    /**
     * Get category display name.
     */
    public function getCategoryDisplayAttribute(): string
    {
        return match($this->category) {
            'status_update' => __('app.notifications.status_update'),
            'appointment_reminder' => __('app.notifications.appointment_reminder'),
            'pickup_ready' => __('app.notifications.pickup_ready'),
            'payment_reminder' => __('app.notifications.payment_reminder'),
            'satisfaction_survey' => __('app.notifications.satisfaction_survey'),
            'general' => __('app.notifications.general'),
            default => $this->category
        };
    }

    /**
     * Get type display name.
     */
    public function getTypeDisplayAttribute(): string
    {
        return match($this->type) {
            'sms' => __('app.notifications.sms'),
            'whatsapp' => __('app.notifications.whatsapp'),
            'email' => __('app.notifications.email'),
            default => $this->type
        };
    }
}
