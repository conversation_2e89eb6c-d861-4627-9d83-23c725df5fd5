<!-- Checkout Modal -->
<div class="modal fade" id="checkoutModal" tabindex="-1" aria-labelledby="checkoutModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="checkoutModalLabel">
                    <i class="bi bi-credit-card"></i> {{ __('app.pos.checkout') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="checkoutForm">
                    @csrf
                    
                    <!-- Customer Selection -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <label for="customerSelect" class="form-label">{{ __('app.pos.customer') }}</label>
                            <select class="form-select" id="customerSelect" name="customer_id">
                                <option value="">{{ __('app.pos.walk_in_customer') }}</option>
                                @foreach($customers as $customer)
                                    <option value="{{ $customer->id }}">{{ $customer->name }} - {{ $customer->phone_number }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-outline-primary w-100" data-bs-toggle="modal" data-bs-target="#newCustomerModal">
                                <i class="bi bi-person-plus"></i> {{ __('app.pos.new_customer') }}
                            </button>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">{{ __('app.pos.order_summary') }}</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>{{ __('app.pos.item') }}</th>
                                            <th class="text-center">{{ __('app.pos.quantity') }}</th>
                                            <th class="text-end">{{ __('app.pos.price') }}</th>
                                            <th class="text-end">{{ __('app.pos.total') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody id="checkoutItems">
                                        <!-- Items will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Totals -->
                            <div class="border-top pt-3">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="mb-2">
                                            <label for="discountInput" class="form-label">{{ __('app.pos.discount') }}</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="discountInput" name="discount_amount" min="0" step="0.01" value="0">
                                                <span class="input-group-text">{{ __('app.currency') }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-end">
                                            <div class="mb-1">
                                                <span class="text-muted">{{ __('app.pos.subtotal') }}:</span>
                                                <span id="checkoutSubtotal" class="fw-bold">0.00 {{ __('app.currency') }}</span>
                                            </div>
                                            <div class="mb-1">
                                                <span class="text-muted">{{ __('app.pos.tax') }}:</span>
                                                <span id="checkoutTax" class="fw-bold">0.00 {{ __('app.currency') }}</span>
                                            </div>
                                            <div class="mb-1">
                                                <span class="text-muted">{{ __('app.pos.discount') }}:</span>
                                                <span id="checkoutDiscount" class="fw-bold">0.00 {{ __('app.currency') }}</span>
                                            </div>
                                            <div class="border-top pt-2">
                                                <span class="h5">{{ __('app.pos.total') }}:</span>
                                                <span id="checkoutTotal" class="h5 text-primary fw-bold">0.00 {{ __('app.currency') }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div class="mb-4">
                        <label class="form-label">{{ __('app.pos.payment_method') }}</label>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment_method" id="paymentCash" value="cash" checked>
                                    <label class="form-check-label" for="paymentCash">
                                        <i class="bi bi-cash-coin text-success"></i> {{ __('app.pos.cash') }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment_method" id="paymentCard" value="card">
                                    <label class="form-check-label" for="paymentCard">
                                        <i class="bi bi-credit-card text-primary"></i> {{ __('app.pos.card') }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment_method" id="paymentBank" value="bank_transfer">
                                    <label class="form-check-label" for="paymentBank">
                                        <i class="bi bi-bank text-info"></i> {{ __('app.pos.bank_transfer') }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment_method" id="paymentMobile" value="mobile_payment">
                                    <label class="form-check-label" for="paymentMobile">
                                        <i class="bi bi-phone text-warning"></i> {{ __('app.pos.mobile_payment') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cash Payment Details -->
                    <div id="cashPaymentDetails" class="mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="amountReceived" class="form-label">{{ __('app.pos.amount_received') }}</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="amountReceived" name="amount_received" min="0" step="0.01">
                                    <span class="input-group-text">{{ __('app.currency') }}</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">{{ __('app.pos.change') }}</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="changeAmount" readonly>
                                    <span class="input-group-text">{{ __('app.currency') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Card Payment Details -->
                    <div id="cardPaymentDetails" class="mb-4" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="cardReference" class="form-label">{{ __('app.pos.card_reference') }}</label>
                                <input type="text" class="form-control" id="cardReference" name="card_reference">
                            </div>
                            <div class="col-md-6">
                                <label for="cardType" class="form-label">{{ __('app.pos.card_type') }}</label>
                                <select class="form-select" id="cardType" name="card_type">
                                    <option value="visa">Visa</option>
                                    <option value="mastercard">Mastercard</option>
                                    <option value="mada">مدى</option>
                                    <option value="other">{{ __('app.pos.other') }}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Bank Transfer Details -->
                    <div id="bankPaymentDetails" class="mb-4" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="bankReference" class="form-label">{{ __('app.pos.transfer_reference') }}</label>
                                <input type="text" class="form-control" id="bankReference" name="bank_reference">
                            </div>
                            <div class="col-md-6">
                                <label for="bankName" class="form-label">{{ __('app.pos.bank_name') }}</label>
                                <input type="text" class="form-control" id="bankName" name="bank_name">
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Payment Details -->
                    <div id="mobilePaymentDetails" class="mb-4" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="mobileReference" class="form-label">{{ __('app.pos.mobile_reference') }}</label>
                                <input type="text" class="form-control" id="mobileReference" name="mobile_reference">
                            </div>
                            <div class="col-md-6">
                                <label for="mobileProvider" class="form-label">{{ __('app.pos.mobile_provider') }}</label>
                                <select class="form-select" id="mobileProvider" name="mobile_provider">
                                    <option value="stc_pay">STC Pay</option>
                                    <option value="apple_pay">Apple Pay</option>
                                    <option value="samsung_pay">Samsung Pay</option>
                                    <option value="other">{{ __('app.pos.other') }}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="mb-4">
                        <label for="transactionNotes" class="form-label">{{ __('app.pos.notes') }}</label>
                        <textarea class="form-control" id="transactionNotes" name="notes" rows="2" placeholder="{{ __('app.pos.transaction_notes_placeholder') }}"></textarea>
                    </div>

                    <!-- Receipt Options -->
                    <div class="mb-4">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="printReceipt" name="print_receipt" checked>
                            <label class="form-check-label" for="printReceipt">
                                {{ __('app.pos.print_receipt') }}
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="emailReceipt" name="email_receipt">
                            <label class="form-check-label" for="emailReceipt">
                                {{ __('app.pos.email_receipt') }}
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="smsReceipt" name="sms_receipt">
                            <label class="form-check-label" for="smsReceipt">
                                {{ __('app.pos.sms_receipt') }}
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> {{ __('app.cancel') }}
                </button>
                <button type="button" class="btn btn-success" id="processPaymentBtn">
                    <i class="bi bi-check-circle"></i> {{ __('app.pos.process_payment') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- New Customer Modal -->
<div class="modal fade" id="newCustomerModal" tabindex="-1" aria-labelledby="newCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newCustomerModalLabel">
                    <i class="bi bi-person-plus"></i> {{ __('app.pos.new_customer') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="newCustomerForm">
                    @csrf
                    <div class="mb-3">
                        <label for="customerName" class="form-label">{{ __('app.customers.name') }} *</label>
                        <input type="text" class="form-control" id="customerName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="customerPhone" class="form-label">{{ __('app.customers.phone_number') }} *</label>
                        <input type="tel" class="form-control" id="customerPhone" name="phone_number" required>
                    </div>
                    <div class="mb-3">
                        <label for="customerEmail" class="form-label">{{ __('app.customers.email') }}</label>
                        <input type="email" class="form-control" id="customerEmail" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="customerAddress" class="form-label">{{ __('app.customers.address') }}</label>
                        <textarea class="form-control" id="customerAddress" name="address" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('app.cancel') }}</button>
                <button type="button" class="btn btn-primary" id="saveCustomerBtn">
                    <i class="bi bi-check"></i> {{ __('app.save') }}
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Checkout Modal Styles */
.modal-lg {
    max-width: 900px;
}

.form-check-label {
    cursor: pointer;
    font-weight: 500;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

#checkoutItems tr:hover {
    background-color: #f8f9fa;
}

.input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

/* Payment method specific styles */
.payment-details {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-top: 10px;
}

/* Change amount styling */
#changeAmount {
    background-color: #e7f3ff;
    font-weight: bold;
    color: #0d6efd;
}

#changeAmount.negative {
    background-color: #f8d7da;
    color: #721c24;
}

/* Receipt options */
.form-check-input:checked + .form-check-label {
    color: #0d6efd;
    font-weight: 600;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .modal-lg {
        max-width: 95%;
        margin: 10px auto;
    }
    
    .modal-body {
        padding: 15px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* RTL Adjustments */
body[dir="rtl"] .form-check-label {
    text-align: right;
}

body[dir="rtl"] .input-group-text {
    border-radius: 0.375rem 0 0 0.375rem;
}

body[dir="rtl"] .form-control {
    border-radius: 0 0.375rem 0.375rem 0;
}
</style>
