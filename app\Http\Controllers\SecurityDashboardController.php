<?php

namespace App\Http\Controllers;

use App\Models\SecurityLog;
use App\Models\User;
use App\Services\SecurityService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class SecurityDashboardController extends Controller
{
    protected SecurityService $securityService;

    public function __construct(SecurityService $securityService)
    {
        $this->middleware('auth');
        $this->middleware('permission:settings.logs');
        $this->securityService = $securityService;
    }

    /**
     * Display security dashboard.
     */
    public function index(): View
    {
        $dashboardData = $this->securityService->getSecurityDashboardData();
        
        // Get security metrics for charts
        $securityMetrics = $this->getSecurityMetrics();
        
        // Get recent high-severity events
        $criticalEvents = SecurityLog::getBySeverity('critical', 10);
        $highSeverityEvents = SecurityLog::getBySeverity('high', 20);
        
        // Get user activity summary
        $userActivitySummary = $this->getUserActivitySummary();

        return view('admin.security-dashboard', compact(
            'dashboardData',
            'securityMetrics',
            'criticalEvents',
            'highSeverityEvents',
            'userActivitySummary'
        ));
    }

    /**
     * Get security logs with filtering.
     */
    public function getLogs(Request $request): JsonResponse
    {
        $query = SecurityLog::with('user');

        // Apply filters
        if ($request->filled('severity')) {
            $query->where('severity', $request->severity);
        }

        if ($request->filled('action')) {
            $query->where('action', 'like', '%' . $request->action . '%');
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('ip_address')) {
            $query->where('ip_address', $request->ip_address);
        }

        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to);
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Paginate results
        $perPage = $request->get('per_page', 50);
        $logs = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $logs
        ]);
    }

    /**
     * Get security metrics for dashboard charts.
     */
    public function getSecurityMetrics(): array
    {
        $days = 30;
        $startDate = now()->subDays($days);

        // Daily login attempts (success vs failed)
        $dailyLogins = SecurityLog::where('action', 'login_attempt')
            ->where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, status, COUNT(*) as count')
            ->groupBy('date', 'status')
            ->orderBy('date')
            ->get()
            ->groupBy('date');

        // Permission denials by day
        $dailyPermissionDenials = SecurityLog::where('action', 'permission_denied')
            ->where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');

        // Security violations by severity
        $violationsBySeverity = SecurityLog::where('created_at', '>=', $startDate)
            ->selectRaw('severity, COUNT(*) as count')
            ->groupBy('severity')
            ->pluck('count', 'severity');

        // Top IP addresses with failed attempts
        $topFailedIps = SecurityLog::where('action', 'login_attempt')
            ->where('status', 'failed')
            ->where('created_at', '>=', $startDate)
            ->selectRaw('ip_address, COUNT(*) as count')
            ->groupBy('ip_address')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->pluck('count', 'ip_address');

        // Most active users
        $mostActiveUsers = SecurityLog::with('user')
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('user_id')
            ->selectRaw('user_id, COUNT(*) as count')
            ->groupBy('user_id')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($log) {
                return [
                    'user_name' => $log->user ? $log->user->name : 'Unknown',
                    'count' => $log->count
                ];
            });

        return [
            'daily_logins' => $dailyLogins,
            'daily_permission_denials' => $dailyPermissionDenials,
            'violations_by_severity' => $violationsBySeverity,
            'top_failed_ips' => $topFailedIps,
            'most_active_users' => $mostActiveUsers,
        ];
    }

    /**
     * Get user activity summary.
     */
    public function getUserActivitySummary(): array
    {
        $activeUsers = User::where('last_login_at', '>=', now()->subDays(30))
            ->orderBy('last_login_at', 'desc')
            ->limit(20)
            ->get()
            ->map(function ($user) {
                $summary = SecurityLog::getUserActivitySummary($user->id, 30);
                return [
                    'user' => $user,
                    'summary' => $summary
                ];
            });

        return $activeUsers->toArray();
    }

    /**
     * Block IP address.
     */
    public function blockIp(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ip_address' => 'required|ip',
            'duration' => 'required|integer|min:1|max:10080', // max 1 week
            'reason' => 'required|string|max:500'
        ]);

        try {
            $this->securityService->blockIp(
                $validated['ip_address'],
                $validated['duration']
            );

            SecurityLog::logSecurityViolation('manual_ip_block', [
                'ip' => $validated['ip_address'],
                'duration_minutes' => $validated['duration'],
                'reason' => $validated['reason'],
                'blocked_by' => auth()->user()->name
            ]);

            return response()->json([
                'success' => true,
                'message' => 'IP address blocked successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to block IP: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Unblock IP address.
     */
    public function unblockIp(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ip_address' => 'required|ip'
        ]);

        try {
            $cacheKey = "blocked_ip_{$validated['ip_address']}";
            Cache::forget($cacheKey);

            SecurityLog::logEvent(
                'ip_unblocked',
                null,
                null,
                [
                    'ip' => $validated['ip_address'],
                    'unblocked_by' => auth()->user()->name
                ],
                SecurityLog::SEVERITY_MEDIUM,
                SecurityLog::STATUS_SUCCESS
            );

            return response()->json([
                'success' => true,
                'message' => 'IP address unblocked successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to unblock IP: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Force user logout.
     */
    public function forceLogout(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'reason' => 'required|string|max:500'
        ]);

        try {
            $user = User::findOrFail($validated['user_id']);
            
            // In a real implementation, you would invalidate all user sessions
            // This is a simplified version
            $user->update(['remember_token' => null]);

            SecurityLog::logEvent(
                'forced_logout',
                'user',
                $user->id,
                [
                    'target_user' => $user->name,
                    'reason' => $validated['reason'],
                    'forced_by' => auth()->user()->name
                ],
                SecurityLog::SEVERITY_HIGH,
                SecurityLog::STATUS_SUCCESS
            );

            return response()->json([
                'success' => true,
                'message' => 'User logged out successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to logout user: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get security alerts.
     */
    public function getSecurityAlerts(): JsonResponse
    {
        $alerts = [];

        // Check for multiple failed logins from same IP
        $suspiciousIps = SecurityLog::where('action', 'login_attempt')
            ->where('status', 'failed')
            ->where('created_at', '>=', now()->subHours(1))
            ->selectRaw('ip_address, COUNT(*) as count')
            ->groupBy('ip_address')
            ->having('count', '>=', 3)
            ->get();

        foreach ($suspiciousIps as $ip) {
            $alerts[] = [
                'type' => 'suspicious_ip',
                'severity' => 'high',
                'message' => "Multiple failed login attempts from IP: {$ip->ip_address} ({$ip->count} attempts)",
                'data' => ['ip' => $ip->ip_address, 'count' => $ip->count]
            ];
        }

        // Check for users with unusual activity
        $unusualActivity = SecurityLog::where('created_at', '>=', now()->subHours(1))
            ->whereNotNull('user_id')
            ->selectRaw('user_id, COUNT(*) as count')
            ->groupBy('user_id')
            ->having('count', '>=', 100)
            ->with('user')
            ->get();

        foreach ($unusualActivity as $activity) {
            $alerts[] = [
                'type' => 'unusual_activity',
                'severity' => 'medium',
                'message' => "Unusual activity detected for user: {$activity->user->name} ({$activity->count} actions in 1 hour)",
                'data' => ['user_id' => $activity->user_id, 'count' => $activity->count]
            ];
        }

        // Check for recent critical events
        $criticalEvents = SecurityLog::where('severity', 'critical')
            ->where('created_at', '>=', now()->subHours(24))
            ->count();

        if ($criticalEvents > 0) {
            $alerts[] = [
                'type' => 'critical_events',
                'severity' => 'critical',
                'message' => "{$criticalEvents} critical security events in the last 24 hours",
                'data' => ['count' => $criticalEvents]
            ];
        }

        return response()->json([
            'success' => true,
            'alerts' => $alerts,
            'count' => count($alerts)
        ]);
    }

    /**
     * Export security logs.
     */
    public function exportLogs(Request $request): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $query = SecurityLog::with('user');

        // Apply same filters as getLogs method
        if ($request->filled('severity')) {
            $query->where('severity', $request->severity);
        }

        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to);
        }

        $filename = 'security_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';

        return response()->streamDownload(function () use ($query) {
            $handle = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($handle, [
                'ID', 'Date/Time', 'User', 'Action', 'Resource Type', 'Resource ID',
                'IP Address', 'Severity', 'Status', 'Details'
            ]);

            // Stream data in chunks
            $query->chunk(1000, function ($logs) use ($handle) {
                foreach ($logs as $log) {
                    fputcsv($handle, [
                        $log->id,
                        $log->created_at->format('Y-m-d H:i:s'),
                        $log->user ? $log->user->name : 'System',
                        $log->action,
                        $log->resource_type,
                        $log->resource_id,
                        $log->ip_address,
                        $log->severity,
                        $log->status,
                        json_encode($log->details)
                    ]);
                }
            });

            fclose($handle);
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\""
        ]);
    }
}
