@extends('layouts.app')

@section('title', __('app.notifications.title'))

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ __('app.notifications.title') }}</h1>
            <p class="text-muted">{{ __('app.manage_and_track_notifications') }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('notifications.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> {{ __('app.notifications.send_notification') }}
            </a>
            <button type="button" class="btn btn-outline-info" onclick="processScheduled()">
                <i class="fas fa-clock"></i> {{ __('app.process_scheduled') }}
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@arabicNumber($stats['total'])</h4>
                            <small>{{ __('app.notifications.total_notifications') }}</small>
                        </div>
                        <i class="fas fa-bell display-6"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@arabicNumber($stats['pending'])</h4>
                            <small>{{ __('app.notifications.pending_notifications') }}</small>
                        </div>
                        <i class="fas fa-clock display-6"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@arabicNumber($stats['sent'])</h4>
                            <small>{{ __('app.notifications.sent_notifications') }}</small>
                        </div>
                        <i class="fas fa-paper-plane display-6"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@arabicNumber($stats['failed'])</h4>
                            <small>{{ __('app.notifications.failed_notifications') }}</small>
                        </div>
                        <i class="fas fa-exclamation-triangle display-6"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@arabicNumber($stats['today'])</h4>
                            <small>{{ __('app.notifications.notifications_today') }}</small>
                        </div>
                        <i class="fas fa-calendar-day display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('notifications.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">{{ __('app.search') }}</label>
                    <input type="text" name="search" class="form-control" 
                           value="{{ request('search') }}" 
                           placeholder="{{ __('app.search_notifications') }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">{{ __('app.notifications.type') }}</label>
                    <select name="type" class="form-select">
                        <option value="">{{ __('app.all') }}</option>
                        <option value="sms" {{ request('type') === 'sms' ? 'selected' : '' }}>
                            {{ __('app.notifications.sms') }}
                        </option>
                        <option value="whatsapp" {{ request('type') === 'whatsapp' ? 'selected' : '' }}>
                            {{ __('app.notifications.whatsapp') }}
                        </option>
                        <option value="email" {{ request('type') === 'email' ? 'selected' : '' }}>
                            {{ __('app.notifications.email') }}
                        </option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">{{ __('app.notifications.category') }}</label>
                    <select name="category" class="form-select">
                        <option value="">{{ __('app.all') }}</option>
                        <option value="status_update" {{ request('category') === 'status_update' ? 'selected' : '' }}>
                            {{ __('app.notifications.status_update') }}
                        </option>
                        <option value="pickup_ready" {{ request('category') === 'pickup_ready' ? 'selected' : '' }}>
                            {{ __('app.notifications.pickup_ready') }}
                        </option>
                        <option value="appointment_reminder" {{ request('category') === 'appointment_reminder' ? 'selected' : '' }}>
                            {{ __('app.notifications.appointment_reminder') }}
                        </option>
                        <option value="payment_reminder" {{ request('category') === 'payment_reminder' ? 'selected' : '' }}>
                            {{ __('app.notifications.payment_reminder') }}
                        </option>
                        <option value="satisfaction_survey" {{ request('category') === 'satisfaction_survey' ? 'selected' : '' }}>
                            {{ __('app.notifications.satisfaction_survey') }}
                        </option>
                        <option value="general" {{ request('category') === 'general' ? 'selected' : '' }}>
                            {{ __('app.notifications.general') }}
                        </option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">{{ __('app.notifications.status') }}</label>
                    <select name="status" class="form-select">
                        <option value="">{{ __('app.all') }}</option>
                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>
                            {{ __('app.notifications.pending') }}
                        </option>
                        <option value="sent" {{ request('status') === 'sent' ? 'selected' : '' }}>
                            {{ __('app.notifications.sent') }}
                        </option>
                        <option value="delivered" {{ request('status') === 'delivered' ? 'selected' : '' }}>
                            {{ __('app.notifications.delivered') }}
                        </option>
                        <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>
                            {{ __('app.notifications.failed') }}
                        </option>
                        <option value="read" {{ request('status') === 'read' ? 'selected' : '' }}>
                            {{ __('app.notifications.read') }}
                        </option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">{{ __('app.from_date') }}</label>
                    <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Notifications Table -->
    <div class="card">
        <div class="card-body">
            @if($notifications->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{{ __('app.notifications.recipient') }}</th>
                                <th>{{ __('app.notifications.type') }}</th>
                                <th>{{ __('app.notifications.category') }}</th>
                                <th>{{ __('app.notifications.message') }}</th>
                                <th>{{ __('app.notifications.status') }}</th>
                                <th>{{ __('app.created_at') }}</th>
                                <th>{{ __('app.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($notifications as $notification)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $notification->customer->name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ $notification->formatted_recipient }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-{{ $notification->type === 'sms' ? 'sms' : ($notification->type === 'whatsapp' ? 'whatsapp' : 'envelope') }}"></i>
                                            {{ $notification->type_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $notification->category_display }}</span>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;" title="{{ $notification->message }}">
                                            {{ $notification->message }}
                                        </div>
                                        @if($notification->repairTicket)
                                            <small class="text-muted">
                                                <i class="fas fa-ticket-alt"></i> {{ $notification->repairTicket->ticket_number }}
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge {{ $notification->status_badge_class }}">
                                            {{ __('app.notifications.' . $notification->status) }}
                                        </span>
                                        @if($notification->cost > 0)
                                            <br><small class="text-muted">@arabicCurrency($notification->cost)</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div>{{ $notification->created_at->toArabicDateTimeString() }}</div>
                                        @if($notification->sent_at)
                                            <small class="text-success">
                                                <i class="fas fa-check"></i> {{ $notification->sent_at->toArabicDateTimeString() }}
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('notifications.show', $notification) }}" 
                                               class="btn btn-outline-primary" title="{{ __('app.view') }}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if($notification->status === 'failed')
                                                <form method="POST" action="{{ route('notifications.resend', $notification) }}" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-outline-warning" 
                                                            title="{{ __('app.notifications.resend') }}"
                                                            onclick="return confirm('{{ __('app.confirm_resend') }}')">
                                                        <i class="fas fa-redo"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $notifications->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{{ __('app.notifications.no_notifications') }}</h5>
                    <p class="text-muted">{{ __('app.no_notifications_found') }}</p>
                    <a href="{{ route('notifications.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {{ __('app.notifications.send_notification') }}
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
function processScheduled() {
    if (confirm('{{ __("app.confirm_process_scheduled") }}')) {
        fetch('{{ route("notifications.process-scheduled") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('{{ __("app.error_occurred") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ __("app.error_occurred") }}');
        });
    }
}
</script>
@endsection
