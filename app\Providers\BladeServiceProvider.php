<?php

namespace App\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\ServiceProvider;

class BladeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Permission checking directives
        Blade::if('permission', function ($permission) {
            return Auth::check() && Auth::user()->hasPermission($permission);
        });

        Blade::if('anypermission', function (...$permissions) {
            return Auth::check() && Auth::user()->hasAnyPermission($permissions);
        });

        Blade::if('allpermissions', function (...$permissions) {
            return Auth::check() && Auth::user()->hasAllPermissions($permissions);
        });

        // Role checking directives
        Blade::if('role', function ($role) {
            return Auth::check() && Auth::user()->hasRole($role);
        });

        Blade::if('anyrole', function (...$roles) {
            if (!Auth::check()) return false;
            
            foreach ($roles as $role) {
                if (Auth::user()->hasRole($role)) {
                    return true;
                }
            }
            return false;
        });

        // Admin checking directive
        Blade::if('admin', function () {
            return Auth::check() && Auth::user()->isAdmin();
        });

        // User management checking directive
        Blade::if('canmanage', function ($user) {
            return Auth::check() && Auth::user()->canManageUser($user);
        });

        // Security level checking directive
        Blade::if('minlevel', function ($level) {
            return Auth::check() && Auth::user()->getRoleLevel() >= $level;
        });

        // Custom permission with fallback directive
        Blade::directive('permissionOr', function ($expression) {
            return "<?php if(auth()->check() && auth()->user()->hasPermission({$expression})): ?>";
        });

        Blade::directive('endpermissionOr', function () {
            return '<?php else: ?>';
        });

        Blade::directive('endpermission', function () {
            return '<?php endif; ?>';
        });

        // User info directives
        Blade::directive('username', function () {
            return "<?php echo auth()->check() ? auth()->user()->name : 'Guest'; ?>";
        });

        Blade::directive('userrole', function () {
            return "<?php echo auth()->check() ? __( 'app.roles.' . auth()->user()->role) : ''; ?>";
        });

        Blade::directive('userroleBadge', function () {
            return "<?php if(auth()->check()): 
                \$role = auth()->user()->userRole;
                \$color = \$role ? \$role->color : '#6c757d';
                \$name = \$role ? \$role->name : auth()->user()->role;
                echo '<span class=\"badge\" style=\"background-color: ' . \$color . '\">' . \$name . '</span>';
            endif; ?>";
        });
    }
}
