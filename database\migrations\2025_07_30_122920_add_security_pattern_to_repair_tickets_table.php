<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('repair_tickets', function (Blueprint $table) {
            // First, rename the existing column to device_pattern (boolean)
            $table->renameColumn('device_pattern_password', 'device_pattern_temp');
        });

        Schema::table('repair_tickets', function (Blueprint $table) {
            // Add the new boolean device_pattern column
            $table->boolean('device_pattern')->default(false)->after('device_model')
                  ->comment('Whether device has a security pattern/password');

            // Add the new encrypted security_pattern column
            $table->text('security_pattern')->nullable()->after('device_pattern')
                  ->comment('Encrypted device security pattern/password for repair access');
        });

        Schema::table('repair_tickets', function (Blueprint $table) {
            // Drop the temporary column
            $table->dropColumn('device_pattern_temp');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('repair_tickets', function (Blueprint $table) {
            // Add back the original column
            $table->string('device_pattern_password')->nullable()->after('device_model');
        });

        Schema::table('repair_tickets', function (Blueprint $table) {
            // Drop the new columns
            $table->dropColumn(['device_pattern', 'security_pattern']);
        });
    }
};
