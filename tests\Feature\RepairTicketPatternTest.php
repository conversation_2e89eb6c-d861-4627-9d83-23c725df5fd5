<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Customer;
use App\Models\Brand;
use App\Models\RepairStatus;
use App\Models\DeviceCondition;
use App\Models\RepairTicket;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class RepairTicketPatternTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $customer;
    protected $brand;
    protected $repairStatus;
    protected $deviceCondition;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->customer = Customer::factory()->create();
        $this->brand = Brand::factory()->create();
        $this->repairStatus = RepairStatus::factory()->create();
        $this->deviceCondition = DeviceCondition::factory()->create();
    }

    /**
     * Test creating repair ticket with text password only
     */
    public function test_can_create_ticket_with_text_password()
    {
        $this->actingAs($this->user);

        $ticketData = [
            'customer_id' => $this->customer->id,
            'brand_id' => $this->brand->id,
            'repair_status_id' => $this->repairStatus->id,
            'device_condition_id' => $this->deviceCondition->id,
            'device_model' => 'iPhone 13',
            'reported_problem' => 'Screen cracked',
            'technician_comments' => 'Initial assessment',
            'received_date' => now(),
            'pattern_type' => 'text',
            'security_pattern' => 'password123'
        ];

        $response = $this->post(route('repair-tickets.store'), $ticketData);

        $response->assertRedirect();
        $this->assertDatabaseHas('repair_tickets', [
            'customer_id' => $this->customer->id,
            'pattern_type' => 'text'
        ]);

        $ticket = RepairTicket::latest()->first();
        $this->assertEquals('password123', $ticket->security_pattern);
        $this->assertNull($ticket->visual_pattern);
        $this->assertTrue($ticket->hasSecurityPattern());
        $this->assertFalse($ticket->hasVisualPattern());
    }

    /**
     * Test creating repair ticket with visual pattern only
     */
    public function test_can_create_ticket_with_visual_pattern()
    {
        $this->actingAs($this->user);

        $ticketData = [
            'customer_id' => $this->customer->id,
            'brand_id' => $this->brand->id,
            'repair_status_id' => $this->repairStatus->id,
            'device_condition_id' => $this->deviceCondition->id,
            'device_model' => 'Samsung Galaxy S21',
            'reported_problem' => 'Battery drain',
            'technician_comments' => 'Check battery health',
            'received_date' => now(),
            'pattern_type' => 'visual',
            'visual_pattern' => '1-2-3-6-9'
        ];

        $response = $this->post(route('repair-tickets.store'), $ticketData);

        $response->assertRedirect();
        $this->assertDatabaseHas('repair_tickets', [
            'customer_id' => $this->customer->id,
            'pattern_type' => 'visual'
        ]);

        $ticket = RepairTicket::latest()->first();
        $this->assertEquals('1-2-3-6-9', $ticket->visual_pattern);
        $this->assertNull($ticket->security_pattern);
        $this->assertFalse($ticket->hasSecurityPattern());
        $this->assertTrue($ticket->hasVisualPattern());
    }

    /**
     * Test creating repair ticket with both text and visual patterns
     */
    public function test_can_create_ticket_with_both_patterns()
    {
        $this->actingAs($this->user);

        $ticketData = [
            'customer_id' => $this->customer->id,
            'brand_id' => $this->brand->id,
            'repair_status_id' => $this->repairStatus->id,
            'device_condition_id' => $this->deviceCondition->id,
            'device_model' => 'Google Pixel 6',
            'reported_problem' => 'Software issues',
            'technician_comments' => 'Factory reset needed',
            'received_date' => now(),
            'pattern_type' => 'both',
            'security_pattern' => 'mypassword',
            'visual_pattern' => '1-4-7-8-9'
        ];

        $response = $this->post(route('repair-tickets.store'), $ticketData);

        $response->assertRedirect();
        $ticket = RepairTicket::latest()->first();

        $this->assertEquals('both', $ticket->pattern_type);
        $this->assertEquals('mypassword', $ticket->security_pattern);
        $this->assertEquals('1-4-7-8-9', $ticket->visual_pattern);
        $this->assertTrue($ticket->hasSecurityPattern());
        $this->assertTrue($ticket->hasVisualPattern());
        $this->assertTrue($ticket->hasAnySecurityPattern());
    }

    /**
     * Test creating repair ticket with no security pattern
     */
    public function test_can_create_ticket_with_no_pattern()
    {
        $this->actingAs($this->user);

        $ticketData = [
            'customer_id' => $this->customer->id,
            'brand_id' => $this->brand->id,
            'repair_status_id' => $this->repairStatus->id,
            'device_condition_id' => $this->deviceCondition->id,
            'device_model' => 'OnePlus 9',
            'reported_problem' => 'Camera not working',
            'technician_comments' => 'Hardware issue',
            'received_date' => now(),
            'pattern_type' => 'none'
        ];

        $response = $this->post(route('repair-tickets.store'), $ticketData);

        $response->assertRedirect();
        $ticket = RepairTicket::latest()->first();

        $this->assertEquals('none', $ticket->pattern_type);
        $this->assertNull($ticket->security_pattern);
        $this->assertNull($ticket->visual_pattern);
        $this->assertFalse($ticket->hasSecurityPattern());
        $this->assertFalse($ticket->hasVisualPattern());
        $this->assertFalse($ticket->hasAnySecurityPattern());
    }

    /**
     * Test validation fails for invalid visual pattern
     */
    public function test_validation_fails_for_invalid_visual_pattern()
    {
        $this->actingAs($this->user);

        $ticketData = [
            'customer_id' => $this->customer->id,
            'brand_id' => $this->brand->id,
            'repair_status_id' => $this->repairStatus->id,
            'device_condition_id' => $this->deviceCondition->id,
            'device_model' => 'iPhone 12',
            'reported_problem' => 'Screen issue',
            'received_date' => now(),
            'pattern_type' => 'visual',
            'visual_pattern' => '1-2-3' // Too few dots
        ];

        $response = $this->post(route('repair-tickets.store'), $ticketData);

        $response->assertSessionHasErrors(['visual_pattern']);
        $this->assertDatabaseMissing('repair_tickets', [
            'customer_id' => $this->customer->id,
            'device_model' => 'iPhone 12'
        ]);
    }

    /**
     * Test updating repair ticket patterns
     */
    public function test_can_update_ticket_patterns()
    {
        $this->actingAs($this->user);

        $ticket = RepairTicket::factory()->create([
            'pattern_type' => 'text',
            'security_pattern' => 'oldpassword'
        ]);

        $updateData = [
            'customer_id' => $ticket->customer_id,
            'brand_id' => $ticket->brand_id,
            'repair_status_id' => $ticket->repair_status_id,
            'device_condition_id' => $ticket->device_condition_id,
            'device_model' => $ticket->device_model,
            'reported_problem' => $ticket->reported_problem,
            'received_date' => $ticket->received_date->format('Y-m-d'),
            'pattern_type' => 'both',
            'security_pattern' => 'newpassword',
            'visual_pattern' => '1-2-3-6-9'
        ];

        $response = $this->put(route('repair-tickets.update', $ticket), $updateData);

        $response->assertRedirect();
        $ticket->refresh();

        $this->assertEquals('both', $ticket->pattern_type);
        $this->assertEquals('newpassword', $ticket->security_pattern);
        $this->assertEquals('1-2-3-6-9', $ticket->visual_pattern);
    }

    /**
     * Test pattern encryption and decryption
     */
    public function test_patterns_are_encrypted_in_database()
    {
        $ticket = RepairTicket::factory()->create([
            'pattern_type' => 'both',
            'security_pattern' => 'testpassword',
            'visual_pattern' => '1-2-3-6-9'
        ]);

        // Check that raw database values are encrypted
        $rawTicket = \DB::table('repair_tickets')->where('id', $ticket->id)->first();
        $this->assertNotEquals('testpassword', $rawTicket->security_pattern);
        $this->assertNotEquals('1-2-3-6-9', $rawTicket->visual_pattern);

        // Check that model decrypts correctly
        $this->assertEquals('testpassword', $ticket->security_pattern);
        $this->assertEquals('1-2-3-6-9', $ticket->visual_pattern);
    }

    /**
     * Test pattern masking for display
     */
    public function test_patterns_are_masked_for_display()
    {
        $ticket = RepairTicket::factory()->create([
            'pattern_type' => 'both',
            'security_pattern' => 'testpassword123',
            'visual_pattern' => '1-2-3-6-9-8-7'
        ]);

        $maskedText = $ticket->getMaskedSecurityPattern();
        $maskedVisual = $ticket->getMaskedVisualPattern();

        $this->assertStringContainsString('*', $maskedText);
        $this->assertStringNotContainsString('testpassword123', $maskedText);

        $this->assertStringContainsString('*', $maskedVisual);
        $this->assertStringNotContainsString('1-2-3-6-9-8-7', $maskedVisual);
    }

    /**
     * Test pattern display methods
     */
    public function test_pattern_display_methods()
    {
        $ticket = RepairTicket::factory()->create([
            'pattern_type' => 'visual',
            'visual_pattern' => '1-2-3-6-9'
        ]);

        $patternArray = $ticket->getVisualPatternArray();
        $this->assertEquals([1, 2, 3, 6, 9], $patternArray);

        $displayText = $ticket->getSecurityPatternDisplay();
        $this->assertStringContainsString('نمط بصري', $displayText); // Arabic for "Visual Pattern"
    }

    /**
     * Test ticket show page displays patterns correctly
     */
    public function test_ticket_show_page_displays_patterns()
    {
        $this->actingAs($this->user);

        $ticket = RepairTicket::factory()->create([
            'pattern_type' => 'both',
            'security_pattern' => 'testpass',
            'visual_pattern' => '1-2-3-6-9'
        ]);

        $response = $this->get(route('repair-tickets.show', $ticket));

        $response->assertStatus(200);
        $response->assertSee('Security Pattern Information');
        $response->assertSee('كلمة مرور + نمط بصري'); // Arabic for "Text + Visual"
        $response->assertDontSee('testpass'); // Should be masked
        $response->assertDontSee('1-2-3-6-9'); // Should be masked
    }

    /**
     * Test pattern selector component is included in forms
     */
    public function test_create_form_includes_pattern_selector()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('repair-tickets.create'));

        $response->assertStatus(200);
        $response->assertSee('pattern-lock.css');
        $response->assertSee('pattern-lock.js');
        $response->assertSee('نوع كلمة المرور/النمط'); // Arabic text
    }

    /**
     * Test edit form includes pattern selector with existing data
     */
    public function test_edit_form_includes_pattern_selector_with_data()
    {
        $this->actingAs($this->user);

        $ticket = RepairTicket::factory()->create([
            'pattern_type' => 'text',
            'security_pattern' => 'existingpass'
        ]);

        $response = $this->get(route('repair-tickets.edit', $ticket));

        $response->assertStatus(200);
        $response->assertSee('pattern-lock.css');
        $response->assertSee('pattern-lock.js');
        $response->assertSee('نوع كلمة المرور/النمط'); // Arabic text
    }
}
