<?php

namespace App\Observers;

use App\Models\RepairTicket;
use App\Services\NotificationService;
use App\Services\SmsService;
use App\Services\WhatsAppService;
use Illuminate\Support\Facades\Log;

class RepairTicketObserver
{
    protected NotificationService $notificationService;
    protected SmsService $smsService;
    protected WhatsAppService $whatsappService;

    public function __construct(
        NotificationService $notificationService,
        SmsService $smsService,
        WhatsAppService $whatsappService
    ) {
        $this->notificationService = $notificationService;
        $this->smsService = $smsService;
        $this->whatsappService = $whatsappService;
    }

    /**
     * Handle the RepairTicket "updated" event.
     */
    public function updated(RepairTicket $repairTicket): void
    {
        // Check if repair status has changed
        if ($repairTicket->isDirty('repair_status_id')) {
            $this->handleStatusChange($repairTicket);
        }

        // Check if ticket is marked as completed
        if ($repairTicket->isDirty('completed_date') && $repairTicket->completed_date) {
            $this->handleTicketCompleted($repairTicket);
        }
    }

    /**
     * Handle repair status change.
     */
    private function handleStatusChange(RepairTicket $repairTicket): void
    {
        try {
            $oldStatusId = $repairTicket->getOriginal('repair_status_id');
            $oldStatus = $oldStatusId ? \App\Models\RepairStatus::find($oldStatusId)?->name : 'غير محدد';
            $newStatus = $repairTicket->repairStatus->name;

            // Send status update notification
            $this->notificationService->sendStatusUpdate($repairTicket, $oldStatus, $newStatus);

            // Send SMS notification
            $this->sendStatusUpdateSms($repairTicket, $newStatus);

            // Send WhatsApp notification
            $this->sendWhatsAppStatusUpdate($repairTicket, $newStatus);

            Log::info('Status update notification sent', [
                'ticket_id' => $repairTicket->id,
                'customer_id' => $repairTicket->customer_id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send status update notification', [
                'ticket_id' => $repairTicket->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle ticket completion.
     */
    private function handleTicketCompleted(RepairTicket $repairTicket): void
    {
        try {
            // Check if the status is final (completed)
            if ($repairTicket->repairStatus->is_final) {
                // Send pickup ready notification
                $this->notificationService->sendPickupReady($repairTicket);

                Log::info('Pickup ready notification sent', [
                    'ticket_id' => $repairTicket->id,
                    'customer_id' => $repairTicket->customer_id,
                ]);

                // Schedule satisfaction survey for 1 day after completion
                $this->scheduleSatisfactionSurvey($repairTicket);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send pickup ready notification', [
                'ticket_id' => $repairTicket->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Schedule satisfaction survey.
     */
    private function scheduleSatisfactionSurvey(RepairTicket $repairTicket): void
    {
        try {
            $scheduledAt = now()->addDay()->format('Y-m-d H:i:s');

            $this->notificationService->sendFromTemplate(
                'satisfaction-survey',
                $repairTicket->customer,
                [],
                $repairTicket,
                $scheduledAt
            );

            Log::info('Satisfaction survey scheduled', [
                'ticket_id' => $repairTicket->id,
                'customer_id' => $repairTicket->customer_id,
                'scheduled_at' => $scheduledAt,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to schedule satisfaction survey', [
                'ticket_id' => $repairTicket->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send SMS for status updates
     */
    private function sendStatusUpdateSms(RepairTicket $ticket, string $newStatus): void
    {
        try {
            // Don't send SMS for initial status
            if ($newStatus === 'مستلم' || $newStatus === 'Received') {
                return;
            }

            $customerName = $ticket->customer->name;
            $ticketNumber = $ticket->ticket_number;

            $message = "عزيزي {$customerName}،\n";
            $message .= "تم تحديث حالة جهازك\n";
            $message .= "رقم التذكرة: {$ticketNumber}\n";
            $message .= "الحالة الجديدة: {$newStatus}\n";

            // Add specific messages based on status
            if ($ticket->repairStatus->is_final) {
                $message .= "\n🎉 جهازك جاهز للاستلام!\n";
                $message .= "يرجى زيارة الورشة لاستلام جهازك\n";
                $message .= "ساعات العمل: 9ص - 10م\n";
            } elseif (str_contains(strtolower($newStatus), 'قيد')) {
                $message .= "\nجاري العمل على إصلاح جهازك\n";
                $message .= "سنبلغك فور الانتهاء\n";
            }

            $message .= "\nورشة إصلاح NJ\n";
            $message .= "للاستفسار: " . config('app.phone', '0501234567');

            $this->smsService->sendSms($ticket->customer->phone_number, $message);

        } catch (\Exception $e) {
            Log::error('Failed to send status update SMS', [
                'ticket_id' => $ticket->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send WhatsApp status update notification
     */
    private function sendWhatsAppStatusUpdate(RepairTicket $ticket, string $newStatus): void
    {
        try {
            // Don't send WhatsApp for initial status
            if ($newStatus === 'مستلم' || $newStatus === 'Received') {
                return;
            }

            $customerName = $ticket->customer->name;
            $ticketNumber = $ticket->ticket_number;
            $statusIcon = $this->getStatusIcon($newStatus);

            $message = "{$statusIcon} *تحديث حالة جهازك*\n\n";
            $message .= "عزيزي {$customerName}،\n";
            $message .= "تم تحديث حالة جهازك\n\n";
            $message .= "🎫 رقم التذكرة: *{$ticketNumber}*\n";
            $message .= "📱 الجهاز: *{$ticket->brand->name} {$ticket->device_model}*\n";
            $message .= "{$statusIcon} الحالة الجديدة: *{$newStatus}*\n\n";

            // Add specific messages based on status
            if ($ticket->repairStatus->is_final) {
                $message .= "🎉 *جهازك جاهز للاستلام!*\n";
                $message .= "يرجى زيارة الورشة لاستلام جهازك\n";
                $message .= "📍 العنوان: " . config('whatsapp.business_address') . "\n";
                $message .= "🕐 ساعات العمل: " . config('whatsapp.business_hours') . "\n\n";
            } elseif (str_contains(strtolower($newStatus), 'قيد')) {
                $message .= "🔧 جاري العمل على إصلاح جهازك\n";
                $message .= "سنبلغك فور الانتهاء\n\n";
            } elseif (str_contains(strtolower($newStatus), 'انتظار')) {
                $message .= "⏳ جهازك في انتظار قطع الغيار\n";
                $message .= "سنبلغك عند وصولها\n\n";
            }

            $message .= "📞 للاستفسار: " . config('whatsapp.business_phone') . "\n";
            $message .= "💬 أو راسلنا هنا على واتساب";

            $this->whatsappService->sendTextMessage($ticket->customer->phone_number, $message);

        } catch (\Exception $e) {
            Log::error('Failed to send WhatsApp status update', [
                'ticket_id' => $ticket->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get status icon for WhatsApp messages
     */
    private function getStatusIcon(string $status): string
    {
        $icons = [
            'مستلم' => '📥',
            'قيد التشخيص' => '🔍',
            'في انتظار قطع الغيار' => '⏳',
            'قيد الإصلاح' => '🔧',
            'مكتمل' => '✅',
            'جاهز للاستلام' => '🎉',
            'مُسلم' => '📤',
            'ملغي' => '❌',
        ];

        return $icons[$status] ?? '📋';
    }
}
