<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Customer;
use App\Models\RepairTicket;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CustomerTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_create_a_customer()
    {
        $customer = Customer::factory()->create([
            'name' => 'John Doe',
            'phone_number' => '+1234567890',
            'email' => '<EMAIL>'
        ]);

        $this->assertInstanceOf(Customer::class, $customer);
        $this->assertDatabaseHas('customers', [
            'name' => '<PERSON>',
            'phone_number' => '+1234567890',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function it_has_many_repair_tickets()
    {
        $customer = Customer::factory()->create();
        $tickets = RepairTicket::factory()->count(3)->create([
            'customer_id' => $customer->id
        ]);

        $this->assertCount(3, $customer->repairTickets);
        $this->assertInstanceOf(RepairTicket::class, $customer->repairTickets->first());
    }

    /** @test */
    public function it_can_calculate_total_spent()
    {
        $customer = Customer::factory()->create();
        
        RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'final_cost' => 100.00
        ]);
        
        RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'final_cost' => 150.50
        ]);
        
        RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'final_cost' => null // Should be ignored
        ]);

        $this->assertEquals(250.50, $customer->totalSpent());
    }

    /** @test */
    public function it_can_calculate_average_ticket_cost()
    {
        $customer = Customer::factory()->create();
        
        RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'final_cost' => 100.00
        ]);
        
        RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'final_cost' => 200.00
        ]);

        $this->assertEquals(150.00, $customer->averageTicketCost());
    }

    /** @test */
    public function it_returns_zero_average_when_no_tickets_with_cost()
    {
        $customer = Customer::factory()->create();
        
        RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'final_cost' => null
        ]);

        $this->assertEquals(0, $customer->averageTicketCost());
    }

    /** @test */
    public function it_can_get_last_visit_date()
    {
        $customer = Customer::factory()->create();
        
        $oldTicket = RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'received_date' => now()->subDays(10)
        ]);
        
        $recentTicket = RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'received_date' => now()->subDays(2)
        ]);

        $lastVisit = $customer->lastVisit();
        $this->assertEquals($recentTicket->received_date->toDateString(), $lastVisit->toDateString());
    }

    /** @test */
    public function it_returns_null_for_last_visit_when_no_tickets()
    {
        $customer = Customer::factory()->create();
        $this->assertNull($customer->lastVisit());
    }

    /** @test */
    public function it_can_search_customers()
    {
        Customer::factory()->create([
            'name' => 'John Doe',
            'phone_number' => '+1234567890',
            'email' => '<EMAIL>'
        ]);
        
        Customer::factory()->create([
            'name' => 'Jane Smith',
            'phone_number' => '+0987654321',
            'email' => '<EMAIL>'
        ]);

        // Search by name
        $results = Customer::search('John')->get();
        $this->assertCount(1, $results);
        $this->assertEquals('John Doe', $results->first()->name);

        // Search by phone
        $results = Customer::search('0987654321')->get();
        $this->assertCount(1, $results);
        $this->assertEquals('Jane Smith', $results->first()->name);

        // Search by email
        $results = Customer::search('<EMAIL>')->get();
        $this->assertCount(1, $results);
        $this->assertEquals('John Doe', $results->first()->name);
    }

    /** @test */
    public function it_can_get_active_customers()
    {
        // Customer with recent tickets
        $activeCustomer = Customer::factory()->create();
        RepairTicket::factory()->create([
            'customer_id' => $activeCustomer->id,
            'received_date' => now()->subDays(15) // Within 30 days
        ]);

        // Customer with old tickets
        $inactiveCustomer = Customer::factory()->create();
        RepairTicket::factory()->create([
            'customer_id' => $inactiveCustomer->id,
            'received_date' => now()->subDays(45) // More than 30 days
        ]);

        // Customer with no tickets
        $noTicketsCustomer = Customer::factory()->create();

        $activeCustomers = Customer::active()->get();
        $this->assertCount(1, $activeCustomers);
        $this->assertEquals($activeCustomer->id, $activeCustomers->first()->id);
    }

    /** @test */
    public function it_validates_required_fields()
    {
        $this->expectException(\Illuminate\Database\QueryException::class);
        
        Customer::create([
            'phone_number' => '+1234567890',
            'email' => '<EMAIL>'
            // Missing required 'name' field
        ]);
    }

    /** @test */
    public function it_can_format_phone_number()
    {
        $customer = Customer::factory()->create([
            'phone_number' => '1234567890'
        ]);

        // Assuming we have a phone formatting method
        $formatted = $customer->getFormattedPhoneAttribute();
        $this->assertNotEmpty($formatted);
    }

    /** @test */
    public function it_can_get_full_name_with_phone()
    {
        $customer = Customer::factory()->create([
            'name' => 'John Doe',
            'phone_number' => '+1234567890'
        ]);

        $fullInfo = $customer->getFullInfoAttribute();
        $this->assertStringContainsString('John Doe', $fullInfo);
        $this->assertStringContainsString('+1234567890', $fullInfo);
    }

    /** @test */
    public function it_can_count_pending_tickets()
    {
        $customer = Customer::factory()->create();
        $pendingStatus = \App\Models\RepairStatus::factory()->create(['name' => 'Pending']);
        $completedStatus = \App\Models\RepairStatus::factory()->create(['name' => 'Completed']);

        RepairTicket::factory()->count(2)->create([
            'customer_id' => $customer->id,
            'repair_status_id' => $pendingStatus->id
        ]);

        RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'repair_status_id' => $completedStatus->id
        ]);

        $this->assertEquals(2, $customer->pendingTicketsCount());
    }

    /** @test */
    public function it_can_count_completed_tickets()
    {
        $customer = Customer::factory()->create();
        $pendingStatus = \App\Models\RepairStatus::factory()->create(['name' => 'Pending']);
        $completedStatus = \App\Models\RepairStatus::factory()->create(['name' => 'Completed']);

        RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'repair_status_id' => $pendingStatus->id
        ]);

        RepairTicket::factory()->count(3)->create([
            'customer_id' => $customer->id,
            'repair_status_id' => $completedStatus->id
        ]);

        $this->assertEquals(3, $customer->completedTicketsCount());
    }

    /** @test */
    public function it_can_determine_if_customer_is_vip()
    {
        $customer = Customer::factory()->create();
        
        // Create tickets with high total value
        RepairTicket::factory()->count(5)->create([
            'customer_id' => $customer->id,
            'final_cost' => 200.00
        ]);

        // Assuming VIP threshold is $500
        $this->assertTrue($customer->isVip());
    }

    /** @test */
    public function it_can_get_customer_loyalty_level()
    {
        $customer = Customer::factory()->create();
        
        RepairTicket::factory()->count(10)->create([
            'customer_id' => $customer->id
        ]);

        $loyaltyLevel = $customer->getLoyaltyLevel();
        $this->assertNotEmpty($loyaltyLevel);
        $this->assertContains($loyaltyLevel, ['Bronze', 'Silver', 'Gold', 'Platinum']);
    }

    /** @test */
    public function it_can_get_recent_tickets()
    {
        $customer = Customer::factory()->create();
        
        // Recent tickets
        RepairTicket::factory()->count(3)->create([
            'customer_id' => $customer->id,
            'received_date' => now()->subDays(5)
        ]);
        
        // Old tickets
        RepairTicket::factory()->count(2)->create([
            'customer_id' => $customer->id,
            'received_date' => now()->subDays(35)
        ]);

        $recentTickets = $customer->recentTickets();
        $this->assertCount(3, $recentTickets);
    }

    /** @test */
    public function it_can_get_customer_statistics()
    {
        $customer = Customer::factory()->create();
        
        RepairTicket::factory()->count(5)->create([
            'customer_id' => $customer->id,
            'final_cost' => 100.00
        ]);

        $stats = $customer->getStatistics();
        
        $this->assertArrayHasKey('total_tickets', $stats);
        $this->assertArrayHasKey('total_spent', $stats);
        $this->assertArrayHasKey('average_cost', $stats);
        $this->assertArrayHasKey('last_visit', $stats);
        
        $this->assertEquals(5, $stats['total_tickets']);
        $this->assertEquals(500.00, $stats['total_spent']);
        $this->assertEquals(100.00, $stats['average_cost']);
    }
}
