<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PosTransactionItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'transaction_id',
        'inventory_item_id',
        'item_name',
        'item_sku',
        'barcode',
        'quantity',
        'unit_of_measure',
        'unit_price',
        'total_price',
        'cost_price',
        'discount_percentage',
        'discount_amount',
        'is_taxable',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'is_taxable' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->calculateTotal();
        });
    }

    /**
     * Get the transaction that owns this item.
     */
    public function transaction(): BelongsTo
    {
        return $this->belongsTo(PosTransaction::class, 'transaction_id');
    }

    /**
     * Get the inventory item.
     */
    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(InventoryItem::class);
    }

    /**
     * Calculate total price.
     */
    public function calculateTotal(): void
    {
        $this->total_price = ($this->quantity * $this->unit_price) - $this->discount_amount;
    }

    /**
     * Get net unit price after discount.
     */
    public function getNetUnitPriceAttribute(): float
    {
        $discountPerUnit = $this->quantity > 0 ? $this->discount_amount / $this->quantity : 0;
        return $this->unit_price - $discountPerUnit;
    }

    /**
     * Get profit amount for this item.
     */
    public function getProfitAmountAttribute(): float
    {
        if (!$this->cost_price) {
            return 0;
        }

        $totalCost = $this->quantity * $this->cost_price;
        return $this->total_price - $totalCost;
    }

    /**
     * Get profit margin percentage.
     */
    public function getProfitMarginAttribute(): float
    {
        if ($this->total_price <= 0) {
            return 0;
        }

        return ($this->profit_amount / $this->total_price) * 100;
    }

    /**
     * Apply discount percentage.
     */
    public function applyDiscountPercentage(float $percentage): void
    {
        $this->discount_percentage = $percentage;
        $this->discount_amount = ($this->quantity * $this->unit_price) * ($percentage / 100);
        $this->calculateTotal();
    }

    /**
     * Apply discount amount.
     */
    public function applyDiscountAmount(float $amount): void
    {
        $this->discount_amount = $amount;
        $subtotal = $this->quantity * $this->unit_price;
        $this->discount_percentage = $subtotal > 0 ? ($amount / $subtotal) * 100 : 0;
        $this->calculateTotal();
    }

    /**
     * Update quantity.
     */
    public function updateQuantity(float $newQuantity): void
    {
        $oldQuantity = $this->quantity;
        $this->quantity = $newQuantity;
        
        // Recalculate discount amount if percentage was applied
        if ($this->discount_percentage > 0) {
            $this->discount_amount = ($this->quantity * $this->unit_price) * ($this->discount_percentage / 100);
        }
        
        $this->calculateTotal();
        $this->save();

        // Update inventory if linked
        if ($this->inventory_item_id && $this->inventoryItem) {
            $quantityDifference = $newQuantity - $oldQuantity;
            if ($quantityDifference != 0) {
                $this->inventoryItem->updateStock(-$quantityDifference, 'pos_adjustment', [
                    'transaction_id' => $this->transaction_id,
                    'transaction_number' => $this->transaction->transaction_number,
                    'notes' => "POS Quantity Adjustment - Transaction {$this->transaction->transaction_number}"
                ]);
            }
        }
    }

    /**
     * Create from inventory item.
     */
    public static function createFromInventoryItem(InventoryItem $inventoryItem, float $quantity = 1): array
    {
        return [
            'inventory_item_id' => $inventoryItem->id,
            'item_name' => $inventoryItem->name,
            'item_sku' => $inventoryItem->sku,
            'barcode' => $inventoryItem->barcode,
            'quantity' => $quantity,
            'unit_of_measure' => $inventoryItem->unit_of_measure,
            'unit_price' => $inventoryItem->selling_price,
            'cost_price' => $inventoryItem->cost_price,
            'is_taxable' => true,
        ];
    }

    /**
     * Create from barcode scan.
     */
    public static function createFromBarcode(string $barcode, float $quantity = 1): ?array
    {
        // First check barcode inventory
        $barcodeItem = BarcodeInventory::where('barcode', $barcode)
            ->where('is_active', true)
            ->first();

        if ($barcodeItem && $barcodeItem->inventoryItem) {
            return static::createFromInventoryItem($barcodeItem->inventoryItem, $quantity);
        }

        // Then check inventory items directly
        $inventoryItem = InventoryItem::where('barcode', $barcode)
            ->where('is_active', true)
            ->first();

        if ($inventoryItem) {
            return static::createFromInventoryItem($inventoryItem, $quantity);
        }

        return null;
    }

    /**
     * Create manual item (not linked to inventory).
     */
    public static function createManualItem(string $name, float $price, float $quantity = 1): array
    {
        return [
            'inventory_item_id' => null,
            'item_name' => $name,
            'item_sku' => null,
            'barcode' => null,
            'quantity' => $quantity,
            'unit_of_measure' => 'piece',
            'unit_price' => $price,
            'cost_price' => null,
            'is_taxable' => true,
        ];
    }

    /**
     * Check if item has sufficient stock.
     */
    public function hasSufficientStock(): bool
    {
        if (!$this->inventoryItem) {
            return true; // Manual items don't have stock constraints
        }

        return $this->inventoryItem->current_stock >= $this->quantity;
    }

    /**
     * Get available stock for this item.
     */
    public function getAvailableStockAttribute(): ?int
    {
        return $this->inventoryItem?->current_stock;
    }

    /**
     * Check if item is out of stock.
     */
    public function isOutOfStock(): bool
    {
        return $this->inventoryItem && $this->inventoryItem->current_stock <= 0;
    }

    /**
     * Check if item is low stock.
     */
    public function isLowStock(): bool
    {
        return $this->inventoryItem && 
               $this->inventoryItem->current_stock <= $this->inventoryItem->minimum_stock;
    }

    /**
     * Get stock status display.
     */
    public function getStockStatusAttribute(): string
    {
        if (!$this->inventoryItem) {
            return app()->getLocale() === 'ar' ? 'غير مرتبط بالمخزون' : 'Not linked to inventory';
        }

        if ($this->isOutOfStock()) {
            return app()->getLocale() === 'ar' ? 'نفد المخزون' : 'Out of Stock';
        }

        if ($this->isLowStock()) {
            return app()->getLocale() === 'ar' ? 'مخزون منخفض' : 'Low Stock';
        }

        return app()->getLocale() === 'ar' ? 'متوفر' : 'In Stock';
    }

    /**
     * Get stock status color for UI.
     */
    public function getStockStatusColorAttribute(): string
    {
        if (!$this->inventoryItem) {
            return 'secondary';
        }

        if ($this->isOutOfStock()) {
            return 'danger';
        }

        if ($this->isLowStock()) {
            return 'warning';
        }

        return 'success';
    }
}
