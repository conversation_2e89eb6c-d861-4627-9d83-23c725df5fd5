<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WhatsAppSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'customer_phone',
        'customer_id',
        'status',
        'current_flow',
        'current_step',
        'flow_data',
        'context',
        'last_intent',
        'last_message',
        'last_activity_at',
        'preferred_language',
        'notifications_enabled',
        'user_preferences',
        'started_at',
        'expires_at',
        'message_count',
        'auto_responses_count',
        'last_message_at',
        'messages_in_current_minute',
        'rate_limit_reset_at',
    ];

    protected $casts = [
        'flow_data' => 'array',
        'context' => 'array',
        'user_preferences' => 'array',
        'notifications_enabled' => 'boolean',
        'last_activity_at' => 'datetime',
        'started_at' => 'datetime',
        'expires_at' => 'datetime',
        'last_message_at' => 'datetime',
        'rate_limit_reset_at' => 'datetime',
    ];

    /**
     * Get the customer that owns the session.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the messages for this session.
     */
    public function messages(): HasMany
    {
        return $this->hasMany(WhatsAppMessage::class, 'session_id', 'session_id');
    }

    /**
     * Scope for active sessions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope for expired sessions.
     */
    public function scopeExpired($query)
    {
        return $query->where(function ($q) {
            $q->where('status', 'expired')
              ->orWhere('expires_at', '<=', now());
        });
    }

    /**
     * Scope for sessions by flow.
     */
    public function scopeByFlow($query, $flow)
    {
        return $query->where('current_flow', $flow);
    }

    /**
     * Check if session is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && $this->expires_at > now();
    }

    /**
     * Check if session is expired.
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired' || $this->expires_at <= now();
    }

    /**
     * Check if rate limit is exceeded.
     */
    public function isRateLimited(): bool
    {
        if ($this->rate_limit_reset_at && $this->rate_limit_reset_at <= now()) {
            $this->resetRateLimit();
            return false;
        }

        $limit = config('whatsapp.rate_limit_per_customer', 10);
        return $this->messages_in_current_minute >= $limit;
    }

    /**
     * Increment message count for rate limiting.
     */
    public function incrementMessageCount(): void
    {
        $now = now();
        
        // Reset counter if it's a new minute
        if (!$this->rate_limit_reset_at || $this->rate_limit_reset_at <= $now) {
            $this->messages_in_current_minute = 1;
            $this->rate_limit_reset_at = $now->addMinute();
        } else {
            $this->messages_in_current_minute++;
        }

        $this->message_count++;
        $this->last_message_at = $now;
        $this->last_activity_at = $now;
        $this->save();
    }

    /**
     * Reset rate limit counter.
     */
    public function resetRateLimit(): void
    {
        $this->update([
            'messages_in_current_minute' => 0,
            'rate_limit_reset_at' => now()->addMinute(),
        ]);
    }

    /**
     * Update session activity.
     */
    public function updateActivity(): void
    {
        $this->update([
            'last_activity_at' => now(),
            'expires_at' => now()->addMinutes(config('whatsapp.session_timeout', 30)),
        ]);
    }

    /**
     * Set current flow and step.
     */
    public function setFlow(string $flow, string $step = null, array $data = []): void
    {
        $this->update([
            'current_flow' => $flow,
            'current_step' => $step,
            'flow_data' => array_merge($this->flow_data ?? [], $data),
        ]);
    }

    /**
     * Get flow data value.
     */
    public function getFlowData(string $key, $default = null)
    {
        return data_get($this->flow_data, $key, $default);
    }

    /**
     * Set flow data value.
     */
    public function setFlowData(string $key, $value): void
    {
        $flowData = $this->flow_data ?? [];
        data_set($flowData, $key, $value);
        
        $this->update(['flow_data' => $flowData]);
    }

    /**
     * Add to context.
     */
    public function addContext(string $key, $value): void
    {
        $context = $this->context ?? [];
        $context[$key] = $value;
        
        $this->update(['context' => $context]);
    }

    /**
     * Get context value.
     */
    public function getContext(string $key, $default = null)
    {
        return data_get($this->context, $key, $default);
    }

    /**
     * Clear context.
     */
    public function clearContext(): void
    {
        $this->update(['context' => []]);
    }

    /**
     * Complete the session.
     */
    public function complete(): void
    {
        $this->update([
            'status' => 'completed',
            'current_flow' => null,
            'current_step' => null,
        ]);
    }

    /**
     * Expire the session.
     */
    public function expire(): void
    {
        $this->update([
            'status' => 'expired',
            'expires_at' => now(),
        ]);
    }

    /**
     * Abandon the session.
     */
    public function abandon(): void
    {
        $this->update([
            'status' => 'abandoned',
        ]);
    }

    /**
     * Create or get session for customer.
     */
    public static function getOrCreateForCustomer(string $customerPhone): self
    {
        $session = static::where('customer_phone', $customerPhone)
                         ->active()
                         ->first();

        if (!$session) {
            $customer = Customer::where('phone_number', 'like', '%' . substr($customerPhone, -9))
                               ->first();

            $session = static::create([
                'session_id' => uniqid('whatsapp_', true),
                'customer_phone' => $customerPhone,
                'customer_id' => $customer?->id,
                'status' => 'active',
                'preferred_language' => 'ar',
                'started_at' => now(),
                'expires_at' => now()->addMinutes(config('whatsapp.session_timeout', 30)),
                'last_activity_at' => now(),
            ]);
        }

        return $session;
    }
}
