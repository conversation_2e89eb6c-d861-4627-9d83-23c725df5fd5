<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0d6efd">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="NJ Repair">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/images/icons/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/images/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/images/icons/icon-192x192.png">

    <title>{{ __('app.app_name') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    @if(app()->getLocale() == 'ar')
        <!-- Arabic Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    @else
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    @endif

    <!-- Bootstrap CSS -->
    @if(app()->getLocale() == 'ar')
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    @else
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    @endif
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{{ asset('css/custom.css') }}" rel="stylesheet">
    <link href="{{ asset('css/mobile-responsive.css') }}" rel="stylesheet">
    @if(app()->getLocale() == 'ar')
        <link href="{{ asset('css/rtl.css') }}" rel="stylesheet">
    @endif

    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: 600;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .status-badge {
            font-size: 0.875rem;
        }
        .search-form {
            max-width: 400px;
        }
    </style>

    @if(app()->getLocale() == 'ar')
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            text-align: right;
        }
        .navbar-brand {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
        }
        .dropdown-menu {
            text-align: right;
        }
        .alert {
            text-align: right;
        }
        .form-control, .form-select {
            text-align: right;
        }
    </style>
    @endif

    @stack('styles')
</head>
<body>
    <div id="app">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="{{ route('dashboard') }}">
                    <i class="bi bi-tools"></i> {{ __('app.app_name') }}
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('dashboard') }}">
                                <i class="bi bi-house"></i> {{ __('app.nav.dashboard') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('repair-tickets.index') }}">
                                <i class="bi bi-ticket"></i> {{ __('app.nav.repair_tickets') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('customers.index') }}">
                                <i class="bi bi-people"></i> {{ __('app.nav.customers') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('invoices.index') }}">
                                <i class="fas fa-file-invoice"></i> الفواتير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('payments.index') }}">
                                <i class="fas fa-credit-card"></i> المدفوعات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('inventory.index') }}">
                                <i class="fas fa-boxes"></i> المخزون
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-graph-up"></i> {{ __('app.nav.reports') }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('reports.dashboard') }}">
                                    <i class="bi bi-speedometer2"></i> {{ __('app.nav.dashboard') }}
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('reports.customer-analytics') }}">
                                    <i class="bi bi-person-lines-fill"></i> {{ __('app.reports.customer_analytics') }}
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('reports.business-intelligence') }}">
                                    <i class="bi bi-graph-up-arrow"></i> {{ __('app.reports.business_intelligence') }}
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ route('reports.financial') }}">
                                    <i class="fas fa-chart-line"></i> التقارير المالية
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('reports.revenue') }}">
                                    <i class="fas fa-money-bill-wave"></i> تقرير الإيرادات
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('reports.outstanding') }}">
                                    <i class="fas fa-file-invoice-dollar"></i> الفواتير المعلقة
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('notifications.index') }}">
                                <i class="bi bi-bell"></i> {{ __('app.notifications.title') }}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('inventory.index') }}">
                                <i class="bi bi-box-seam"></i> {{ __('app.inventory.title') }}
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-gear"></i> {{ __('app.nav.settings') }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('brands.index') }}">{{ __('app.brands.title') }}</a></li>
                                <li><a class="dropdown-item" href="{{ route('repair-statuses.index') }}">{{ __('app.repair_statuses.title') }}</a></li>
                                <li><a class="dropdown-item" href="{{ route('device-conditions.index') }}">{{ __('app.device_conditions.title') }}</a></li>
                            </ul>
                        </li>
                    </ul>

                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle"></i> {{ Auth::user()->name ?? __('app.nav.user') }}
                            </a>
                            <ul class="dropdown-menu">
                                @if(Route::has('profile.edit'))
                                    <li><a class="dropdown-item" href="{{ route('profile.edit') }}">{{ __('app.nav.profile') }}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                @endif
                                @if(Route::has('logout'))
                                    <li>
                                        <form method="POST" action="{{ route('logout') }}">
                                            @csrf
                                            <button type="submit" class="dropdown-item">{{ __('app.nav.logout') }}</button>
                                        </form>
                                    </li>
                                @else
                                    <li><a class="dropdown-item" href="#" onclick="alert('Authentication not yet configured')">{{ __('app.nav.logout') }}</a></li>
                                @endif
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container-fluid">
            <div class="row">
                <main class="col-12">
                    <!-- Alerts -->
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if ($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- Page Content -->
                    @yield('content')
                </main>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="{{ asset('js/notifications.js') }}"></script>
    <script src="{{ asset('js/ui-enhancements.js') }}"></script>
    <script src="{{ asset('js/dashboard-enhancements.js') }}"></script>
    <script src="{{ asset('js/theme-manager.js') }}"></script>

    <!-- PWA Installation Script -->
    <script>
        // Register Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;

            // Show install button
            const installBtn = document.createElement('button');
            installBtn.className = 'btn btn-primary btn-sm position-fixed';
            installBtn.style.cssText = 'bottom: 20px; right: 20px; z-index: 1000; border-radius: 50px;';
            installBtn.innerHTML = '<i class="bi bi-download"></i> تثبيت التطبيق';
            installBtn.onclick = installPWA;
            document.body.appendChild(installBtn);
        });

        function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    }
                    deferredPrompt = null;
                    // Remove install button
                    const installBtn = document.querySelector('button[onclick="installPWA()"]');
                    if (installBtn) installBtn.remove();
                });
            }
        }

        // Handle app installed
        window.addEventListener('appinstalled', (evt) => {
            console.log('PWA was installed');
            // Remove install button if still visible
            const installBtn = document.querySelector('button[onclick="installPWA()"]');
            if (installBtn) installBtn.remove();
        });
    </script>

    @stack('scripts')
</body>
</html>
