/**
 * POS System JavaScript
 * Handles all POS functionality including cart management, checkout, and receipt generation
 */

class POSSystem {
    constructor() {
        this.cart = [];
        this.settings = this.loadSettings();
        this.currentTransaction = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadProducts();
        this.setupKeyboardShortcuts();
        this.setupBarcodeScanner();
        this.updateCartDisplay();
    }

    bindEvents() {
        // Product selection
        $(document).on('click', '.product-card:not(.out-of-stock)', (e) => {
            const productId = $(e.currentTarget).data('product-id');
            this.addToCart(productId);
        });

        // Category filtering
        $(document).on('click', '.category-tab', (e) => {
            const category = $(e.currentTarget).data('category');
            this.filterByCategory(category);
            $('.category-tab').removeClass('active');
            $(e.currentTarget).addClass('active');
        });

        // Search functionality
        $('#barcodeInput').on('input', (e) => {
            const query = $(e.target).val();
            this.searchProducts(query);
        });

        // Category filter dropdown
        $('#categoryFilter').on('change', (e) => {
            const category = $(e.target).val();
            this.filterByCategory(category);
        });

        // Cart quantity controls
        $(document).on('click', '.quantity-btn', (e) => {
            const action = $(e.currentTarget).data('action');
            const itemId = $(e.currentTarget).closest('.cart-item').data('item-id');
            this.updateQuantity(itemId, action);
        });

        // Remove item from cart
        $(document).on('click', '.remove-item', (e) => {
            const itemId = $(e.currentTarget).closest('.cart-item').data('item-id');
            this.removeFromCart(itemId);
        });

        // Payment method selection
        $(document).on('click', '.payment-method', (e) => {
            $('.payment-method').removeClass('active');
            $(e.currentTarget).addClass('active');
        });

        // Clear cart
        $('#clearCartBtn').on('click', () => {
            this.clearCart();
        });

        // Checkout
        $('#checkoutBtn').on('click', () => {
            this.openCheckout();
        });

        // Process payment
        $('#processPaymentBtn').on('click', () => {
            this.processPayment();
        });

        // Print receipt
        $('#printReceiptBtn').on('click', () => {
            this.printReceipt();
        });

        // Settings
        $('#saveSettingsBtn').on('click', () => {
            this.saveSettings();
        });

        // Fullscreen toggle
        $('#fullscreenBtn').on('click', () => {
            this.toggleFullscreen();
        });

        // Payment method change in checkout
        $('input[name="payment_method"]').on('change', (e) => {
            this.showPaymentDetails($(e.target).val());
        });

        // Amount received calculation
        $('#amountReceived').on('input', () => {
            this.calculateChange();
        });

        // Discount calculation
        $('#discountInput').on('input', () => {
            this.updateCheckoutTotals();
        });
    }

    setupKeyboardShortcuts() {
        $(document).on('keydown', (e) => {
            // F1 - Focus search
            if (e.key === 'F1') {
                e.preventDefault();
                $('#barcodeInput').focus();
            }
            // F2 - New customer
            else if (e.key === 'F2') {
                e.preventDefault();
                $('#newCustomerModal').modal('show');
            }
            // F3 - Clear cart
            else if (e.key === 'F3') {
                e.preventDefault();
                this.clearCart();
            }
            // F4 - Checkout
            else if (e.key === 'F4') {
                e.preventDefault();
                if (this.cart.length > 0) {
                    this.openCheckout();
                }
            }
            // F5 - Refresh products
            else if (e.key === 'F5') {
                e.preventDefault();
                this.loadProducts();
            }
            // F11 - Fullscreen
            else if (e.key === 'F11') {
                e.preventDefault();
                this.toggleFullscreen();
            }
            // Enter on barcode input
            else if (e.key === 'Enter' && $(e.target).is('#barcodeInput')) {
                e.preventDefault();
                const barcode = $(e.target).val();
                if (barcode) {
                    this.addByBarcode(barcode);
                    $(e.target).val('').focus();
                }
            }
        });
    }

    setupBarcodeScanner() {
        let barcodeBuffer = '';
        let barcodeTimeout;

        $(document).on('keypress', (e) => {
            // Only process if not in input field
            if ($(e.target).is('input, textarea, select')) return;

            // Add character to buffer
            barcodeBuffer += e.key;

            // Clear existing timeout
            clearTimeout(barcodeTimeout);

            // Set new timeout
            barcodeTimeout = setTimeout(() => {
                if (barcodeBuffer.length > 3) { // Minimum barcode length
                    this.addByBarcode(barcodeBuffer);
                    if (this.settings.soundEnabled) {
                        this.playSound('scan');
                    }
                }
                barcodeBuffer = '';
            }, 100);
        });
    }

    addToCart(productId, quantity = 1) {
        const product = this.getProductById(productId);
        if (!product || product.current_stock <= 0) {
            this.showAlert('Product not available or out of stock', 'warning');
            return;
        }

        const existingItem = this.cart.find(item => item.id === productId);
        
        if (existingItem) {
            if (existingItem.quantity + quantity <= product.current_stock) {
                existingItem.quantity += quantity;
            } else {
                this.showAlert('Insufficient stock', 'warning');
                return;
            }
        } else {
            this.cart.push({
                id: productId,
                name: product.name,
                price: parseFloat(product.selling_price),
                quantity: quantity,
                stock: product.current_stock,
                barcode: product.barcode
            });
        }

        this.updateCartDisplay();
        if (this.settings.soundEnabled) {
            this.playSound('add');
        }
    }

    addByBarcode(barcode) {
        const product = this.getProductByBarcode(barcode);
        if (product) {
            this.addToCart(product.id);
        } else {
            this.showAlert('Product not found', 'warning');
            if (this.settings.soundEnabled) {
                this.playSound('error');
            }
        }
    }

    removeFromCart(productId) {
        this.cart = this.cart.filter(item => item.id !== productId);
        this.updateCartDisplay();
    }

    updateQuantity(productId, action) {
        const item = this.cart.find(item => item.id === productId);
        if (!item) return;

        if (action === 'increase') {
            if (item.quantity < item.stock) {
                item.quantity++;
            } else {
                this.showAlert('Insufficient stock', 'warning');
                return;
            }
        } else if (action === 'decrease') {
            if (item.quantity > 1) {
                item.quantity--;
            } else {
                this.removeFromCart(productId);
                return;
            }
        }

        this.updateCartDisplay();
    }

    clearCart() {
        if (this.cart.length === 0) return;
        
        if (confirm('Are you sure you want to clear the cart?')) {
            this.cart = [];
            this.updateCartDisplay();
        }
    }

    updateCartDisplay() {
        const cartItems = $('#cartItems');
        const cartCount = $('#cartCount');
        
        if (this.cart.length === 0) {
            cartItems.html(`
                <div class="text-center text-muted p-4">
                    <i class="bi bi-cart display-4 opacity-50"></i>
                    <p class="mt-2">Empty cart</p>
                </div>
            `);
            cartCount.text('0');
            $('#checkoutBtn').prop('disabled', true);
        } else {
            let itemsHtml = '';
            this.cart.forEach(item => {
                itemsHtml += `
                    <div class="cart-item" data-item-id="${item.id}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="flex-grow-1">
                                <div class="fw-bold">${item.name}</div>
                                <div class="text-muted small">${item.price.toFixed(2)} SAR each</div>
                            </div>
                            <div class="quantity-controls">
                                <button class="quantity-btn" data-action="decrease">-</button>
                                <input type="number" class="quantity-input" value="${item.quantity}" min="1" max="${item.stock}" readonly>
                                <button class="quantity-btn" data-action="increase">+</button>
                                <button class="btn btn-sm btn-outline-danger ms-2 remove-item">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="text-end mt-1">
                            <strong>${(item.price * item.quantity).toFixed(2)} SAR</strong>
                        </div>
                    </div>
                `;
            });
            
            cartItems.html(itemsHtml);
            cartCount.text(this.cart.reduce((sum, item) => sum + item.quantity, 0));
            $('#checkoutBtn').prop('disabled', false);
        }

        this.updateTotals();
    }

    updateTotals() {
        const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const taxRate = this.settings.taxRate / 100;
        const taxAmount = subtotal * taxRate;
        const total = subtotal + taxAmount;

        $('#subtotal').text(subtotal.toFixed(2) + ' SAR');
        $('#taxAmount').text(taxAmount.toFixed(2) + ' SAR');
        $('#totalAmount').text(total.toFixed(2) + ' SAR');
    }

    openCheckout() {
        if (this.cart.length === 0) return;

        // Populate checkout modal
        this.populateCheckoutItems();
        this.updateCheckoutTotals();
        $('#checkoutModal').modal('show');
    }

    populateCheckoutItems() {
        const checkoutItems = $('#checkoutItems');
        let itemsHtml = '';

        this.cart.forEach(item => {
            itemsHtml += `
                <tr>
                    <td>${item.name}</td>
                    <td class="text-center">${item.quantity}</td>
                    <td class="text-end">${item.price.toFixed(2)} SAR</td>
                    <td class="text-end">${(item.price * item.quantity).toFixed(2)} SAR</td>
                </tr>
            `;
        });

        checkoutItems.html(itemsHtml);
    }

    updateCheckoutTotals() {
        const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const discount = parseFloat($('#discountInput').val()) || 0;
        const taxableAmount = subtotal - discount;
        const taxRate = this.settings.taxRate / 100;
        const taxAmount = taxableAmount * taxRate;
        const total = taxableAmount + taxAmount;

        $('#checkoutSubtotal').text(subtotal.toFixed(2) + ' SAR');
        $('#checkoutDiscount').text(discount.toFixed(2) + ' SAR');
        $('#checkoutTax').text(taxAmount.toFixed(2) + ' SAR');
        $('#checkoutTotal').text(total.toFixed(2) + ' SAR');

        // Update amount received field
        $('#amountReceived').val(total.toFixed(2));
        this.calculateChange();
    }

    showPaymentDetails(method) {
        // Hide all payment details
        $('#cashPaymentDetails, #cardPaymentDetails, #bankPaymentDetails, #mobilePaymentDetails').hide();
        
        // Show relevant payment details
        switch(method) {
            case 'cash':
                $('#cashPaymentDetails').show();
                break;
            case 'card':
                $('#cardPaymentDetails').show();
                break;
            case 'bank_transfer':
                $('#bankPaymentDetails').show();
                break;
            case 'mobile_payment':
                $('#mobilePaymentDetails').show();
                break;
        }
    }

    calculateChange() {
        const total = parseFloat($('#checkoutTotal').text().replace(' SAR', ''));
        const received = parseFloat($('#amountReceived').val()) || 0;
        const change = received - total;
        
        $('#changeAmount').val(change.toFixed(2));
        
        if (change < 0) {
            $('#changeAmount').addClass('negative');
        } else {
            $('#changeAmount').removeClass('negative');
        }
    }

    processPayment() {
        // Validate form
        if (!this.validateCheckoutForm()) return;

        // Show loading
        $('#processPaymentBtn').prop('disabled', true).html('<i class="bi bi-hourglass-split"></i> Processing...');

        // Simulate payment processing
        setTimeout(() => {
            this.completeTransaction();
        }, 2000);
    }

    validateCheckoutForm() {
        const paymentMethod = $('input[name="payment_method"]:checked').val();
        const total = parseFloat($('#checkoutTotal').text().replace(' SAR', ''));

        if (paymentMethod === 'cash') {
            const received = parseFloat($('#amountReceived').val()) || 0;
            if (received < total) {
                this.showAlert('Insufficient amount received', 'error');
                return false;
            }
        }

        return true;
    }

    completeTransaction() {
        // Generate transaction data
        this.currentTransaction = {
            id: 'POS-' + Date.now(),
            items: [...this.cart],
            subtotal: this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
            discount: parseFloat($('#discountInput').val()) || 0,
            tax: 0,
            total: 0,
            paymentMethod: $('input[name="payment_method"]:checked').val(),
            customer: $('#customerSelect option:selected').text(),
            cashier: 'Current User',
            timestamp: new Date()
        };

        // Calculate totals
        const taxableAmount = this.currentTransaction.subtotal - this.currentTransaction.discount;
        this.currentTransaction.tax = taxableAmount * (this.settings.taxRate / 100);
        this.currentTransaction.total = taxableAmount + this.currentTransaction.tax;

        // Clear cart and close checkout
        this.cart = [];
        this.updateCartDisplay();
        $('#checkoutModal').modal('hide');
        
        // Reset checkout form
        $('#processPaymentBtn').prop('disabled', false).html('<i class="bi bi-check-circle"></i> Process Payment');

        // Show receipt
        this.showReceipt();

        // Play success sound
        if (this.settings.soundEnabled) {
            this.playSound('success');
        }

        this.showAlert('Transaction completed successfully!', 'success');
    }

    showReceipt() {
        if (!this.currentTransaction) return;

        // Populate receipt data
        $('#receiptNumber').text(this.currentTransaction.id);
        $('#receiptDateTime').text(this.currentTransaction.timestamp.toLocaleString());
        $('#receiptCustomer').text(this.currentTransaction.customer);
        $('#receiptCashier').text(this.currentTransaction.cashier);

        // Populate items
        let itemsHtml = '';
        this.currentTransaction.items.forEach(item => {
            itemsHtml += `
                <tr>
                    <td>${item.name}</td>
                    <td class="text-center">${item.quantity}</td>
                    <td class="text-end">${item.price.toFixed(2)}</td>
                    <td class="text-end">${(item.price * item.quantity).toFixed(2)}</td>
                </tr>
            `;
        });
        $('#receiptItemsTable').html(itemsHtml);

        // Populate totals
        $('#receiptSubtotal').text(this.currentTransaction.subtotal.toFixed(2) + ' SAR');
        $('#receiptTaxAmount').text(this.currentTransaction.tax.toFixed(2) + ' SAR');
        $('#receiptTotalAmount').text(this.currentTransaction.total.toFixed(2) + ' SAR');

        if (this.currentTransaction.discount > 0) {
            $('#receiptDiscountAmount').text(this.currentTransaction.discount.toFixed(2) + ' SAR');
            $('#receiptDiscountRow').show();
        }

        // Payment method
        const paymentMethods = {
            'cash': 'Cash',
            'card': 'Card',
            'bank_transfer': 'Bank Transfer',
            'mobile_payment': 'Mobile Payment'
        };
        $('#receiptPaymentMethod').text(paymentMethods[this.currentTransaction.paymentMethod]);

        // Show receipt modal
        $('#receiptModal').modal('show');

        // Auto print if enabled
        if (this.settings.autoPrintReceipt) {
            setTimeout(() => this.printReceipt(), 1000);
        }
    }

    printReceipt() {
        window.print();
        $('#printReceiptBtn').addClass('btn-success-animation');
        setTimeout(() => {
            $('#printReceiptBtn').removeClass('btn-success-animation');
        }, 600);
    }

    // Utility methods
    getProductById(id) {
        return window.posProducts?.find(p => p.id == id);
    }

    getProductByBarcode(barcode) {
        return window.posProducts?.find(p => p.barcode === barcode);
    }

    filterByCategory(categoryId) {
        const products = $('.product-card');
        
        if (!categoryId) {
            products.show();
        } else {
            products.each(function() {
                const productCategory = $(this).data('category');
                if (productCategory == categoryId) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }
    }

    searchProducts(query) {
        if (!query) {
            $('.product-card').show();
            return;
        }

        const searchTerm = query.toLowerCase();
        $('.product-card').each(function() {
            const name = $(this).data('name');
            const barcode = $(this).data('barcode');
            
            if (name.includes(searchTerm) || (barcode && barcode.includes(searchTerm))) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
            $('#fullscreenBtn').html('<i class="bi bi-fullscreen-exit"></i> Exit Fullscreen');
        } else {
            document.exitFullscreen();
            $('#fullscreenBtn').html('<i class="bi bi-fullscreen"></i> Fullscreen');
        }
    }

    playSound(type) {
        // Simple beep sounds for different actions
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        const frequencies = {
            'add': 800,
            'scan': 1000,
            'success': 600,
            'error': 300
        };

        oscillator.frequency.value = frequencies[type] || 500;
        oscillator.type = 'sine';
        
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    }

    showAlert(message, type = 'info') {
        // Create toast notification
        const toast = $(`
            <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `);

        // Add to toast container (create if doesn't exist)
        if (!$('#toastContainer').length) {
            $('body').append('<div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3"></div>');
        }

        $('#toastContainer').append(toast);
        const bsToast = new bootstrap.Toast(toast[0]);
        bsToast.show();

        // Remove after hiding
        toast.on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }

    loadSettings() {
        const defaultSettings = {
            taxRate: 15,
            currency: 'SAR',
            autoTax: true,
            soundEnabled: true,
            barcodeAutoAdd: true,
            autoPrintReceipt: true,
            showItemCodes: false,
            receiptCopies: 1
        };

        const saved = localStorage.getItem('posSettings');
        return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
    }

    saveSettings() {
        const settings = {
            taxRate: parseFloat($('#taxRate').val()),
            currency: $('#currency').val(),
            autoTax: $('#autoTax').is(':checked'),
            soundEnabled: $('#soundEnabled').is(':checked'),
            barcodeAutoAdd: $('#barcodeAutoAdd').is(':checked'),
            autoPrintReceipt: $('#autoPrintReceipt').is(':checked'),
            showItemCodes: $('#showItemCodes').is(':checked'),
            receiptCopies: parseInt($('#receiptCopies').val())
        };

        localStorage.setItem('posSettings', JSON.stringify(settings));
        this.settings = settings;
        $('#settingsModal').modal('hide');
        this.showAlert('Settings saved successfully!', 'success');
    }

    loadProducts() {
        // This would typically load from an API
        // For now, we'll use the products already loaded in the page
        console.log('Products loaded');
    }
}

// Initialize POS system when document is ready
$(document).ready(function() {
    window.pos = new POSSystem();
});
