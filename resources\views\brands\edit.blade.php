@extends('layouts.app')

@section('title', 'تعديل الماركة - ' . $brand->name)

@push('styles')
<style>
.brand-form-header {
    background: linear-gradient(45deg, #1cc88a, #17a673);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    padding: 2rem;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #1cc88a;
    box-shadow: 0 0 0 0.2rem rgba(28, 200, 138, 0.25);
}

.required-field::after {
    content: " *";
    color: #e74a3b;
    font-weight: bold;
}

.btn-submit {
    background: linear-gradient(45deg, #1cc88a, #17a673);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 0.35rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.btn-submit:hover {
    background: linear-gradient(45deg, #17a673, #138f5a);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    color: white;
}

.help-text {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.form-check {
    padding: 1rem;
    background: #f8f9fc;
    border-radius: 0.35rem;
    border: 1px solid #e3e6f0;
}

.form-check-input:checked {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.alert-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    border: none;
    color: white;
    border-radius: 0.35rem;
}

@media (max-width: 768px) {
    .brand-form-header {
        padding: 1rem;
    }
    
    .form-section {
        padding: 1.5rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="brand-form-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">تعديل الماركة</h1>
                <p class="mb-0 opacity-75">{{ $brand->name }}</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('brands.show', $brand) }}" class="btn btn-light">
                    <i class="fas fa-eye me-2"></i>عرض الماركة
                </a>
                <a href="{{ route('brands.index') }}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للماركات
                </a>
            </div>
        </div>
    </div>

    <!-- Warning if brand has tickets -->
    @if($brand->repairTickets()->count() > 0)
        <div class="alert alert-warning mb-4">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> هذه الماركة مرتبطة بـ {{ $brand->repairTickets()->count() }} بطاقة إصلاح. 
            تعديل الاسم سيؤثر على جميع البطاقات المرتبطة.
        </div>
    @endif

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="form-section">
                <form method="POST" action="{{ route('brands.update', $brand) }}">
                    @csrf
                    @method('PUT')

                    <div class="mb-3">
                        <label for="name" class="form-label required-field">اسم الماركة</label>
                        <input type="text"
                               class="form-control @error('name') is-invalid @enderror"
                               id="name"
                               name="name"
                               value="{{ old('name', $brand->name) }}"
                               required
                               autofocus
                               placeholder="مثال: Apple، Samsung، Huawei">
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">أدخل اسم الماركة بوضوح</div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control @error('description') is-invalid @enderror"
                                  id="description"
                                  name="description"
                                  rows="3"
                                  placeholder="وصف مختصر عن الماركة (اختياري)">{{ old('description', $brand->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">معلومات إضافية عن الماركة</div>
                    </div>

                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input"
                                   type="checkbox"
                                   id="is_active"
                                   name="is_active"
                                   value="1"
                                   {{ old('is_active', $brand->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                <strong>الماركة نشطة</strong>
                                <div class="help-text">الماركات النشطة فقط تظهر في قوائم الاختيار</div>
                            </label>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn-submit">
                            <i class="fas fa-save me-2"></i>حفظ التعديلات
                        </button>
                        <a href="{{ route('brands.show', $brand) }}" class="btn btn-secondary ms-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة النموذج قبل الإرسال
    document.querySelector('form').addEventListener('submit', function(e) {
        const name = document.getElementById('name').value;
        
        if (!name.trim()) {
            e.preventDefault();
            alert('يرجى إدخال اسم الماركة');
            return false;
        }
        
        if (name.length < 2) {
            e.preventDefault();
            alert('اسم الماركة يجب أن يكون أكثر من حرفين');
            return false;
        }
        
        return true;
    });

    // تنسيق النص أثناء الكتابة
    document.getElementById('name').addEventListener('input', function(e) {
        // إزالة المسافات الزائدة
        e.target.value = e.target.value.replace(/\s+/g, ' ').trim();
    });

    // تحذير عند إلغاء تفعيل ماركة لها بطاقات
    const isActiveCheckbox = document.getElementById('is_active');
    const hasTickets = {{ $brand->repairTickets()->count() }};
    
    if (hasTickets > 0) {
        isActiveCheckbox.addEventListener('change', function() {
            if (!this.checked) {
                if (!confirm('تحذير: إلغاء تفعيل هذه الماركة سيخفيها من قوائم الاختيار في بطاقات الإصلاح الجديدة. هل أنت متأكد؟')) {
                    this.checked = true;
                }
            }
        });
    }
});
</script>
@endpush
@endsection
