<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add sales analytics fields to invoices table
        Schema::table('invoices', function (Blueprint $table) {
            $table->string('sales_channel')->default('repair_shop')->after('currency')
                  ->comment('Channel: repair_shop, pos, online, phone');
            $table->decimal('cost_of_goods', 10, 2)->nullable()->after('total_amount')
                  ->comment('Total cost of parts/services for profit calculation');
            $table->decimal('profit_amount', 10, 2)->nullable()->after('cost_of_goods')
                  ->comment('Calculated profit amount');
            $table->decimal('profit_margin_percentage', 5, 2)->nullable()->after('profit_amount')
                  ->comment('Profit margin percentage');
            $table->string('payment_terms')->nullable()->after('due_date')
                  ->comment('Payment terms: immediate, net_15, net_30, etc.');
            $table->json('sales_metrics')->nullable()->after('profit_margin_percentage')
                  ->comment('Additional sales metrics and analytics data');
            $table->timestamp('first_payment_date')->nullable()->after('paid_date')
                  ->comment('Date of first payment received');
            $table->integer('days_to_payment')->nullable()->after('first_payment_date')
                  ->comment('Days between invoice and first payment');
        });

        // Create sales analytics summary table
        Schema::create('sales_analytics', function (Blueprint $table) {
            $table->id();
            $table->date('analytics_date');
            $table->string('period_type')->comment('daily, weekly, monthly, yearly');
            $table->decimal('total_sales', 12, 2)->default(0);
            $table->decimal('total_cost', 12, 2)->default(0);
            $table->decimal('total_profit', 12, 2)->default(0);
            $table->decimal('profit_margin', 5, 2)->default(0);
            $table->integer('invoices_count')->default(0);
            $table->integer('customers_count')->default(0);
            $table->decimal('average_invoice_value', 10, 2)->default(0);
            $table->json('sales_by_channel')->nullable();
            $table->json('sales_by_category')->nullable();
            $table->json('payment_methods_breakdown')->nullable();
            $table->json('top_selling_items')->nullable();
            $table->timestamps();

            $table->unique(['analytics_date', 'period_type']);
            $table->index(['analytics_date', 'period_type']);
        });

        // Create payment method configurations table
        Schema::create('payment_method_configs', function (Blueprint $table) {
            $table->id();
            $table->string('method_code')->unique();
            $table->string('method_name_ar');
            $table->string('method_name_en');
            $table->string('icon_class')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('requires_reference')->default(false);
            $table->boolean('requires_card_info')->default(false);
            $table->boolean('requires_bank_info')->default(false);
            $table->decimal('processing_fee_percentage', 5, 2)->default(0);
            $table->decimal('processing_fee_fixed', 8, 2)->default(0);
            $table->integer('settlement_days')->default(0);
            $table->json('additional_fields')->nullable();
            $table->json('validation_rules')->nullable();
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        // Create customer payment history summary table
        Schema::create('customer_payment_summaries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->decimal('total_invoiced', 12, 2)->default(0);
            $table->decimal('total_paid', 12, 2)->default(0);
            $table->decimal('outstanding_balance', 12, 2)->default(0);
            $table->integer('total_invoices')->default(0);
            $table->integer('paid_invoices')->default(0);
            $table->integer('overdue_invoices')->default(0);
            $table->decimal('average_payment_days', 5, 1)->default(0);
            $table->string('payment_behavior')->default('new')
                  ->comment('excellent, good, average, poor, new');
            $table->decimal('credit_limit', 10, 2)->nullable();
            $table->boolean('credit_hold')->default(false);
            $table->date('last_payment_date')->nullable();
            $table->date('last_invoice_date')->nullable();
            $table->timestamps();

            $table->index('customer_id');
            $table->index('payment_behavior');
            $table->index('credit_hold');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_payment_summaries');
        Schema::dropIfExists('payment_method_configs');
        Schema::dropIfExists('sales_analytics');
        
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropColumn([
                'sales_channel',
                'cost_of_goods',
                'profit_amount',
                'profit_margin_percentage',
                'payment_terms',
                'sales_metrics',
                'first_payment_date',
                'days_to_payment'
            ]);
        });
    }
};
