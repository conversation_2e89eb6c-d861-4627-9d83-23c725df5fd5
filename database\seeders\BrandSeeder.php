<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $brands = [
            // Mobile Phone Brands
            ['name' => 'آبل', 'description' => 'أجهزة آيفون وآيباد'],
            ['name' => 'سامسونج', 'description' => 'هواتف وأجهزة جالاكسي الذكية'],
            ['name' => 'هواوي', 'description' => 'هواتف وأجهزة هواوي الذكية'],
            ['name' => 'شاومي', 'description' => 'هواتف مي وريدمي الذكية'],
            ['name' => 'ون بلس', 'description' => 'هواتف ون بلس الذكية'],
            ['name' => 'جوجل', 'description' => 'هواتف بيكسل الذكية'],
            ['name' => 'سوني', 'description' => 'هواتف إكسبيريا الذكية'],
            ['name' => 'إل جي', 'description' => 'هواتف إل جي الذكية'],
            ['name' => 'موتورولا', 'description' => 'هواتف موتو الذكية'],
            ['name' => 'نوكيا', 'description' => 'هواتف نوكيا الذكية'],
            ['name' => 'أوبو', 'description' => 'هواتف أوبو الذكية'],
            ['name' => 'فيفو', 'description' => 'هواتف فيفو الذكية'],
            ['name' => 'ريلمي', 'description' => 'هواتف ريلمي الذكية'],

            // Computer Brands
            ['name' => 'ديل', 'description' => 'أجهزة كمبيوتر محمولة ومكتبية من ديل'],
            ['name' => 'إتش بي', 'description' => 'أجهزة كمبيوتر محمولة ومكتبية من إتش بي'],
            ['name' => 'لينوفو', 'description' => 'أجهزة ثينك باد وآيديا باد المحمولة'],
            ['name' => 'أسوس', 'description' => 'أجهزة كمبيوتر محمولة ومكتبية من أسوس'],
            ['name' => 'أيسر', 'description' => 'أجهزة كمبيوتر محمولة ومكتبية من أيسر'],
            ['name' => 'إم إس آي', 'description' => 'أجهزة كمبيوتر محمولة للألعاب من إم إس آي'],
            ['name' => 'توشيبا', 'description' => 'أجهزة كمبيوتر محمولة من توشيبا'],
            ['name' => 'مايكروسوفت', 'description' => 'أجهزة سيرفس'],
            ['name' => 'كمبيوتر مخصص', 'description' => 'أجهزة كمبيوتر مجمعة حسب الطلب'],
        ];

        foreach ($brands as $brand) {
            DB::table('brands')->insert([
                'name' => $brand['name'],
                'slug' => Str::slug($brand['name']),
                'description' => $brand['description'],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
