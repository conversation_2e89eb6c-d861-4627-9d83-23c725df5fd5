@extends('layouts.app')

@section('title', __('app.repair_statuses.title'))

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ __('app.repair_statuses.title') }}</h1>
            <p class="text-muted mb-0">{{ __('app.repair_statuses.manage_description') }}</p>
        </div>
        <a href="{{ route('repair-statuses.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>{{ __('app.repair_statuses.create') }}
        </a>
    </div>

    <!-- Success Message -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Repair Statuses Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">{{ __('app.repair_statuses.list') }}</h5>
        </div>
        <div class="card-body">
            @if($repairStatuses->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{{ __('app.repair_statuses.name') }}</th>
                                <th>{{ __('app.repair_statuses.description') }}</th>
                                <th>{{ __('app.repair_statuses.color') }}</th>
                                <th>{{ __('app.common.sort_order') }}</th>
                                <th>{{ __('app.repair_statuses.is_final') }}</th>
                                <th>{{ __('app.common.status') }}</th>
                                <th>{{ __('app.common.tickets_count') }}</th>
                                <th>{{ __('app.common.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($repairStatuses as $status)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="badge me-2" style="background-color: {{ $status->color }}; color: white;">
                                                {{ $status->name }}
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ $status->description ?? __('app.common.no_description') }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="color-preview me-2" style="width: 20px; height: 20px; background-color: {{ $status->color }}; border-radius: 3px; border: 1px solid #dee2e6;"></div>
                                            <code>{{ $status->color }}</code>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $status->sort_order }}</span>
                                    </td>
                                    <td>
                                        @if($status->is_final)
                                            <span class="badge bg-success">{{ __('app.common.yes') }}</span>
                                        @else
                                            <span class="badge bg-secondary">{{ __('app.common.no') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($status->is_active)
                                            <span class="badge bg-success">{{ __('app.common.active') }}</span>
                                        @else
                                            <span class="badge bg-danger">{{ __('app.common.inactive') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $status->repair_tickets_count ?? 0 }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <!-- Quick View Button -->
                                            <a href="{{ route('repair-statuses.show', $status) }}" class="btn btn-sm btn-outline-info" title="{{ __('app.common.view') }}">
                                                <i class="fas fa-eye"></i>
                                            </a>

                                            <!-- Quick Edit Button -->
                                            <a href="{{ route('repair-statuses.edit', $status) }}" class="btn btn-sm btn-outline-primary" title="{{ __('app.common.edit') }}">
                                                <i class="fas fa-edit"></i>
                                            </a>

                                            <!-- Actions Dropdown -->
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('repair-statuses.show', $status) }}">
                                                            <i class="fas fa-eye me-2"></i>{{ __('app.common.view') }}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('repair-statuses.edit', $status) }}">
                                                            <i class="fas fa-edit me-2"></i>{{ __('app.common.edit') }}
                                                        </a>
                                                    </li>
                                                    @if($status->repair_tickets_count > 0)
                                                        <li>
                                                            <a class="dropdown-item" href="{{ route('repair-tickets.index', ['status' => $status->id]) }}">
                                                                <i class="fas fa-list me-2"></i>{{ __('app.common.view_tickets') }} ({{ $status->repair_tickets_count }})
                                                            </a>
                                                        </li>
                                                    @endif
                                                    <li><hr class="dropdown-divider"></li>
                                                    @if($status->repair_tickets_count == 0)
                                                        <li>
                                                            <form action="{{ route('repair-statuses.destroy', $status) }}" method="POST" class="d-inline" onsubmit="return confirm('{{ __('app.repair_statuses.confirm_delete') }}')">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="dropdown-item text-danger">
                                                                    <i class="fas fa-trash me-2"></i>{{ __('app.common.delete') }}
                                                                </button>
                                                            </form>
                                                        </li>
                                                    @else
                                                        <li>
                                                            <span class="dropdown-item text-muted" title="{{ __('app.common.cannot_delete_has_tickets') }}">
                                                                <i class="fas fa-trash me-2"></i>{{ __('app.common.delete') }} ({{ __('app.common.disabled') }})
                                                            </span>
                                                        </li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{{ __('app.repair_statuses.no_statuses') }}</h5>
                    <p class="text-muted">{{ __('app.repair_statuses.create_first_status') }}</p>
                    <a href="{{ route('repair-statuses.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{{ __('app.repair_statuses.create') }}
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
