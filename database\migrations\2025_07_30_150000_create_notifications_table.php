<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('repair_ticket_id')->nullable()->constrained()->onDelete('cascade');
            $table->enum('type', ['sms', 'whatsapp', 'email'])->default('sms');
            $table->enum('category', [
                'status_update',
                'appointment_reminder', 
                'pickup_ready',
                'payment_reminder',
                'satisfaction_survey',
                'general'
            ])->default('general');
            $table->string('recipient_phone')->nullable();
            $table->string('recipient_email')->nullable();
            $table->string('subject')->nullable();
            $table->text('message');
            $table->json('template_data')->nullable(); // Store dynamic data for templates
            $table->enum('status', ['pending', 'sent', 'delivered', 'failed', 'read'])->default('pending');
            $table->timestamp('scheduled_at')->nullable(); // For scheduled notifications
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->text('error_message')->nullable(); // Store error details if failed
            $table->string('external_id')->nullable(); // SMS provider message ID
            $table->decimal('cost', 8, 4)->nullable(); // Cost per message
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();

            // Indexes for better performance
            $table->index(['customer_id', 'type']);
            $table->index(['repair_ticket_id', 'category']);
            $table->index(['status', 'scheduled_at']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
