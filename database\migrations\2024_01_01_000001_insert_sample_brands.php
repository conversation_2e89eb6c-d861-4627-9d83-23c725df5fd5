<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $brands = [
            ['name' => 'آبل', 'description' => 'أجهزة آيفون وآيباد', 'is_active' => true],
            ['name' => 'سامسونج', 'description' => 'هواتف وأجهزة جالاكسي الذكية', 'is_active' => true],
            ['name' => 'هواوي', 'description' => 'هواتف وأجهزة هواوي الذكية', 'is_active' => true],
            ['name' => 'شاومي', 'description' => 'هواتف مي وريدمي الذكية', 'is_active' => true],
            ['name' => 'ون بلس', 'description' => 'هواتف ون بلس الذكية', 'is_active' => true],
            ['name' => 'جوجل', 'description' => 'هواتف بيكسل الذكية', 'is_active' => true],
            ['name' => 'سوني', 'description' => 'هواتف إكسبيريا الذكية', 'is_active' => true],
            ['name' => 'إل جي', 'description' => 'هواتف إل جي الذكية', 'is_active' => false],
            ['name' => 'موتورولا', 'description' => 'هواتف موتو الذكية', 'is_active' => true],
            ['name' => 'نوكيا', 'description' => 'هواتف نوكيا الذكية', 'is_active' => true],
        ];

        foreach ($brands as $brand) {
            DB::table('brands')->insert([
                'name' => $brand['name'],
                'slug' => \Illuminate\Support\Str::slug($brand['name']),
                'description' => $brand['description'],
                'is_active' => $brand['is_active'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('brands')->whereIn('name', [
            'آبل', 'سامسونج', 'هواوي', 'شاومي', 'ون بلس', 
            'جوجل', 'سوني', 'إل جي', 'موتورولا', 'نوكيا'
        ])->delete();
    }
};
