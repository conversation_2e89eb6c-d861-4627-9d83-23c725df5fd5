/**
 * NJ Repair Shop - Notification System
 * Enhanced toast notifications with animations and icons
 */

class NotificationSystem {
    constructor() {
        this.container = null;
        this.init();
    }

    init() {
        // Create notification container if it doesn't exist
        if (!document.getElementById('notification-container')) {
            this.container = document.createElement('div');
            this.container.id = 'notification-container';
            this.container.className = 'position-fixed top-0 end-0 p-3';
            this.container.style.zIndex = '9999';
            document.body.appendChild(this.container);
        } else {
            this.container = document.getElementById('notification-container');
        }
    }

    show(message, type = 'info', duration = 5000) {
        const toast = this.createToast(message, type, duration);
        this.container.appendChild(toast);
        
        // Show toast with animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // Auto hide after duration
        setTimeout(() => {
            this.hide(toast);
        }, duration);

        return toast;
    }

    createToast(message, type, duration) {
        const toastId = 'toast-' + Date.now();
        const icons = {
            success: 'bi-check-circle-fill',
            error: 'bi-exclamation-triangle-fill',
            warning: 'bi-exclamation-circle-fill',
            info: 'bi-info-circle-fill'
        };

        const colors = {
            success: 'text-bg-success',
            error: 'text-bg-danger',
            warning: 'text-bg-warning',
            info: 'text-bg-info'
        };

        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = `toast align-items-center ${colors[type] || colors.info} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body d-flex align-items-center">
                    <i class="bi ${icons[type] || icons.info} me-2"></i>
                    <span>${message}</span>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" 
                        data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

        // Add click to close functionality
        const closeBtn = toast.querySelector('.btn-close');
        closeBtn.addEventListener('click', () => {
            this.hide(toast);
        });

        return toast;
    }

    hide(toast) {
        toast.classList.remove('show');
        toast.classList.add('hiding');
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    success(message, duration = 5000) {
        return this.show(message, 'success', duration);
    }

    error(message, duration = 7000) {
        return this.show(message, 'error', duration);
    }

    warning(message, duration = 6000) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration = 5000) {
        return this.show(message, 'info', duration);
    }

    // Show loading notification
    loading(message = 'Loading...') {
        const toast = this.createLoadingToast(message);
        this.container.appendChild(toast);
        
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        return toast;
    }

    createLoadingToast(message) {
        const toastId = 'loading-toast-' + Date.now();
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = 'toast align-items-center text-bg-primary border-0';
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span>${message}</span>
                </div>
            </div>
        `;

        return toast;
    }

    // Clear all notifications
    clearAll() {
        const toasts = this.container.querySelectorAll('.toast');
        toasts.forEach(toast => {
            this.hide(toast);
        });
    }
}

// Initialize notification system
const notifications = new NotificationSystem();

// Global functions for easy access
window.showNotification = (message, type, duration) => notifications.show(message, type, duration);
window.showSuccess = (message, duration) => notifications.success(message, duration);
window.showError = (message, duration) => notifications.error(message, duration);
window.showWarning = (message, duration) => notifications.warning(message, duration);
window.showInfo = (message, duration) => notifications.info(message, duration);
window.showLoading = (message) => notifications.loading(message);
window.clearNotifications = () => notifications.clearAll();

// Auto-show notifications from Laravel session
document.addEventListener('DOMContentLoaded', function() {
    // Check for Laravel flash messages
    const flashMessages = {
        success: document.querySelector('meta[name="flash-success"]'),
        error: document.querySelector('meta[name="flash-error"]'),
        warning: document.querySelector('meta[name="flash-warning"]'),
        info: document.querySelector('meta[name="flash-info"]')
    };

    Object.keys(flashMessages).forEach(type => {
        const meta = flashMessages[type];
        if (meta && meta.content) {
            notifications[type](meta.content);
        }
    });
});

// Enhanced form submission with loading states
function enhanceFormSubmission() {
    const forms = document.querySelectorAll('form[data-enhance="true"]');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
                submitBtn.disabled = true;
                
                // Re-enable after 10 seconds as fallback
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 10000);
            }
        });
    });
}

// Initialize enhanced forms when DOM is ready
document.addEventListener('DOMContentLoaded', enhanceFormSubmission);

// AJAX helper with notifications
window.ajaxRequest = function(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    };

    const mergedOptions = { ...defaultOptions, ...options };
    
    // Show loading notification for non-GET requests
    let loadingToast = null;
    if (mergedOptions.method !== 'GET') {
        loadingToast = notifications.loading('Processing request...');
    }

    return fetch(url, mergedOptions)
        .then(response => {
            // Hide loading notification
            if (loadingToast) {
                notifications.hide(loadingToast);
            }

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Show success notification if message provided
            if (data.message) {
                notifications.success(data.message);
            }
            return data;
        })
        .catch(error => {
            // Hide loading notification
            if (loadingToast) {
                notifications.hide(loadingToast);
            }
            
            // Show error notification
            notifications.error(error.message || 'An error occurred while processing your request.');
            throw error;
        });
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationSystem;
}
