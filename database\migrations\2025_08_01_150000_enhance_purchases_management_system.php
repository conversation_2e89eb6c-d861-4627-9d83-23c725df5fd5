<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Enhance suppliers table with performance tracking
        Schema::table('suppliers', function (Blueprint $table) {
            $table->decimal('average_delivery_days', 5, 1)->default(0)->after('current_balance')
                  ->comment('Average delivery time in days');
            $table->decimal('on_time_delivery_rate', 5, 2)->default(0)->after('average_delivery_days')
                  ->comment('Percentage of on-time deliveries');
            $table->decimal('quality_rating', 3, 2)->default(0)->after('on_time_delivery_rate')
                  ->comment('Quality rating out of 5');
            $table->integer('total_orders')->default(0)->after('quality_rating')
                  ->comment('Total number of orders placed');
            $table->integer('completed_orders')->default(0)->after('total_orders')
                  ->comment('Number of completed orders');
            $table->decimal('total_purchase_amount', 12, 2)->default(0)->after('completed_orders')
                  ->comment('Total amount of all purchases');
            $table->date('last_order_date')->nullable()->after('total_purchase_amount')
                  ->comment('Date of last order placed');
            $table->date('last_delivery_date')->nullable()->after('last_order_date')
                  ->comment('Date of last delivery received');
            $table->json('performance_metrics')->nullable()->after('last_delivery_date')
                  ->comment('Additional performance metrics');
            $table->enum('preferred_status', ['preferred', 'standard', 'blacklisted'])->default('standard')->after('performance_metrics')
                  ->comment('Supplier preference status');
            $table->text('blacklist_reason')->nullable()->after('preferred_status')
                  ->comment('Reason for blacklisting if applicable');
        });

        // Enhance purchase orders table with advanced tracking
        Schema::table('purchase_orders', function (Blueprint $table) {
            $table->string('reference_number')->nullable()->after('po_number')
                  ->comment('Supplier reference number');
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal')->after('status')
                  ->comment('Order priority level');
            $table->decimal('estimated_cost', 10, 2)->nullable()->after('total_amount')
                  ->comment('Initial estimated cost');
            $table->decimal('actual_cost', 10, 2)->nullable()->after('estimated_cost')
                  ->comment('Final actual cost');
            $table->decimal('cost_variance', 10, 2)->nullable()->after('actual_cost')
                  ->comment('Difference between estimated and actual cost');
            $table->integer('delivery_days_planned')->nullable()->after('expected_delivery_date')
                  ->comment('Planned delivery time in days');
            $table->integer('delivery_days_actual')->nullable()->after('delivery_days_planned')
                  ->comment('Actual delivery time in days');
            $table->boolean('delivered_on_time')->nullable()->after('delivery_days_actual')
                  ->comment('Whether delivery was on time');
            $table->decimal('quality_score', 3, 2)->nullable()->after('delivered_on_time')
                  ->comment('Overall quality score for this order');
            $table->json('quality_issues')->nullable()->after('quality_score')
                  ->comment('Quality issues encountered');
            $table->string('delivery_method')->nullable()->after('delivery_address')
                  ->comment('Delivery method used');
            $table->decimal('delivery_cost', 8, 2)->nullable()->after('delivery_method')
                  ->comment('Actual delivery cost');
            $table->json('tracking_info')->nullable()->after('delivery_cost')
                  ->comment('Delivery tracking information');
            $table->timestamp('last_status_update')->nullable()->after('approved_at')
                  ->comment('Last time status was updated');
            $table->foreignId('last_updated_by')->nullable()->after('last_status_update')->constrained('users')->onDelete('set null')
                  ->comment('User who last updated the order');
        });

        // Create supplier performance history table
        Schema::create('supplier_performance_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');
            $table->date('evaluation_date');
            $table->decimal('delivery_performance', 5, 2)->default(0)->comment('On-time delivery percentage');
            $table->decimal('quality_rating', 3, 2)->default(0)->comment('Quality rating out of 5');
            $table->decimal('price_competitiveness', 3, 2)->default(0)->comment('Price competitiveness rating');
            $table->decimal('communication_rating', 3, 2)->default(0)->comment('Communication quality rating');
            $table->decimal('overall_rating', 3, 2)->default(0)->comment('Overall supplier rating');
            $table->integer('orders_evaluated')->default(0)->comment('Number of orders in this evaluation');
            $table->decimal('total_order_value', 12, 2)->default(0)->comment('Total value of orders evaluated');
            $table->json('strengths')->nullable()->comment('Supplier strengths');
            $table->json('weaknesses')->nullable()->comment('Areas for improvement');
            $table->text('notes')->nullable()->comment('Additional evaluation notes');
            $table->foreignId('evaluated_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();

            $table->index(['supplier_id', 'evaluation_date']);
            $table->index('evaluation_date');
        });

        // Create inventory reorder rules table
        Schema::create('inventory_reorder_rules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('inventory_item_id')->constrained()->onDelete('cascade');
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');
            $table->integer('reorder_point')->comment('Stock level that triggers reorder');
            $table->integer('reorder_quantity')->comment('Quantity to order when reordering');
            $table->integer('maximum_stock')->nullable()->comment('Maximum stock level to maintain');
            $table->integer('lead_time_days')->default(7)->comment('Expected lead time in days');
            $table->decimal('unit_cost', 10, 2)->comment('Expected unit cost from this supplier');
            $table->integer('minimum_order_quantity')->default(1)->comment('Minimum quantity that can be ordered');
            $table->boolean('is_active')->default(true)->comment('Whether this rule is active');
            $table->boolean('auto_reorder')->default(false)->comment('Whether to automatically create POs');
            $table->date('last_reorder_date')->nullable()->comment('Date of last reorder');
            $table->integer('reorder_frequency_days')->nullable()->comment('How often to check for reorder');
            $table->json('seasonal_adjustments')->nullable()->comment('Seasonal quantity adjustments');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->unique(['inventory_item_id', 'supplier_id']);
            $table->index(['reorder_point', 'is_active']);
            $table->index('auto_reorder');
        });

        // Create purchase analytics table
        Schema::create('purchase_analytics', function (Blueprint $table) {
            $table->id();
            $table->date('analytics_date');
            $table->string('period_type')->comment('daily, weekly, monthly, yearly');
            $table->decimal('total_purchase_amount', 12, 2)->default(0);
            $table->decimal('total_paid_amount', 12, 2)->default(0);
            $table->decimal('outstanding_amount', 12, 2)->default(0);
            $table->integer('orders_placed')->default(0);
            $table->integer('orders_completed')->default(0);
            $table->integer('orders_cancelled')->default(0);
            $table->decimal('average_order_value', 10, 2)->default(0);
            $table->decimal('average_delivery_time', 5, 1)->default(0);
            $table->decimal('on_time_delivery_rate', 5, 2)->default(0);
            $table->integer('unique_suppliers')->default(0);
            $table->json('top_suppliers')->nullable()->comment('Top suppliers by volume');
            $table->json('top_categories')->nullable()->comment('Top categories by spending');
            $table->json('cost_savings')->nullable()->comment('Cost savings achieved');
            $table->json('quality_metrics')->nullable()->comment('Quality-related metrics');
            $table->timestamps();

            $table->unique(['analytics_date', 'period_type']);
            $table->index(['analytics_date', 'period_type']);
        });

        // Create supplier contracts table
        Schema::create('supplier_contracts', function (Blueprint $table) {
            $table->id();
            $table->string('contract_number')->unique();
            $table->foreignId('supplier_id')->constrained()->onDelete('restrict');
            $table->string('contract_type')->comment('framework, fixed_price, volume_discount, etc.');
            $table->date('start_date');
            $table->date('end_date');
            $table->enum('status', ['draft', 'active', 'expired', 'terminated', 'renewed'])->default('draft');
            $table->decimal('contract_value', 12, 2)->nullable()->comment('Total contract value if applicable');
            $table->decimal('minimum_spend', 10, 2)->nullable()->comment('Minimum spend commitment');
            $table->decimal('maximum_spend', 10, 2)->nullable()->comment('Maximum spend limit');
            $table->json('discount_tiers')->nullable()->comment('Volume discount tiers');
            $table->json('payment_terms')->nullable()->comment('Payment terms and conditions');
            $table->json('delivery_terms')->nullable()->comment('Delivery terms and SLAs');
            $table->json('quality_requirements')->nullable()->comment('Quality standards and requirements');
            $table->json('performance_kpis')->nullable()->comment('Key performance indicators');
            $table->text('terms_conditions')->nullable();
            $table->json('attachments')->nullable()->comment('Contract documents');
            $table->boolean('auto_renew')->default(false)->comment('Whether contract auto-renews');
            $table->integer('renewal_notice_days')->nullable()->comment('Days notice required for renewal');
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();

            $table->index(['supplier_id', 'status']);
            $table->index(['start_date', 'end_date']);
            $table->index('status');
        });

        // Create purchase requisitions table (internal requests before PO)
        Schema::create('purchase_requisitions', function (Blueprint $table) {
            $table->id();
            $table->string('requisition_number')->unique();
            $table->foreignId('requested_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('department_id')->nullable()->constrained()->onDelete('set null');
            $table->date('requested_date');
            $table->date('required_date')->comment('When items are needed');
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            $table->enum('status', ['draft', 'submitted', 'approved', 'rejected', 'converted', 'cancelled'])->default('draft');
            $table->text('justification')->comment('Business justification for purchase');
            $table->decimal('estimated_total', 10, 2)->default(0);
            $table->json('approval_workflow')->nullable()->comment('Approval workflow status');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('purchase_order_id')->nullable()->constrained()->onDelete('set null');
            $table->text('rejection_reason')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['requested_by', 'status']);
            $table->index(['required_date', 'priority']);
            $table->index('status');
        });

        // Create purchase requisition items table
        Schema::create('purchase_requisition_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('purchase_requisition_id')->constrained()->onDelete('cascade');
            $table->foreignId('inventory_item_id')->nullable()->constrained()->onDelete('set null');
            $table->string('item_name');
            $table->text('item_description')->nullable();
            $table->string('item_specification')->nullable();
            $table->integer('quantity_requested');
            $table->string('unit_of_measure')->default('piece');
            $table->decimal('estimated_unit_cost', 10, 2)->nullable();
            $table->decimal('estimated_total_cost', 10, 2)->nullable();
            $table->foreignId('preferred_supplier_id')->nullable()->constrained('suppliers')->onDelete('set null');
            $table->text('justification')->nullable();
            $table->json('attachments')->nullable()->comment('Specifications, quotes, etc.');
            $table->timestamps();

            $table->index('purchase_requisition_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_requisition_items');
        Schema::dropIfExists('purchase_requisitions');
        Schema::dropIfExists('supplier_contracts');
        Schema::dropIfExists('purchase_analytics');
        Schema::dropIfExists('inventory_reorder_rules');
        Schema::dropIfExists('supplier_performance_history');

        Schema::table('purchase_orders', function (Blueprint $table) {
            $table->dropForeign(['last_updated_by']);
            $table->dropColumn([
                'reference_number',
                'priority',
                'estimated_cost',
                'actual_cost',
                'cost_variance',
                'delivery_days_planned',
                'delivery_days_actual',
                'delivered_on_time',
                'quality_score',
                'quality_issues',
                'delivery_method',
                'delivery_cost',
                'tracking_info',
                'last_status_update',
                'last_updated_by'
            ]);
        });

        Schema::table('suppliers', function (Blueprint $table) {
            $table->dropColumn([
                'average_delivery_days',
                'on_time_delivery_rate',
                'quality_rating',
                'total_orders',
                'completed_orders',
                'total_purchase_amount',
                'last_order_date',
                'last_delivery_date',
                'performance_metrics',
                'preferred_status',
                'blacklist_reason'
            ]);
        });
    }
};
