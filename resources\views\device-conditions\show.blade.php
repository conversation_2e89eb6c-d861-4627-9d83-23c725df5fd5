@extends('layouts.app')

@section('title', $deviceCondition->name)

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ $deviceCondition->name }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('device-conditions.index') }}">{{ __('app.device_conditions.title') }}</a></li>
                    <li class="breadcrumb-item active">{{ $deviceCondition->name }}</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="{{ route('device-conditions.edit', $deviceCondition) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>{{ __('app.common.edit') }}
            </a>
            <a href="{{ route('device-conditions.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>{{ __('app.common.back') }}
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Condition Details -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.device_conditions.condition_details') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.device_conditions.name') }}</label>
                                <div class="fw-bold">{{ $deviceCondition->name }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.common.slug') }}</label>
                                <div class="fw-bold"><code>{{ $deviceCondition->slug }}</code></div>
                            </div>
                        </div>
                    </div>

                    @if($deviceCondition->description)
                        <div class="mb-3">
                            <label class="form-label text-muted">{{ __('app.device_conditions.description') }}</label>
                            <div class="fw-bold">{{ $deviceCondition->description }}</div>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.common.sort_order') }}</label>
                                <div class="fw-bold">
                                    <span class="badge bg-secondary">{{ $deviceCondition->sort_order }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.common.status') }}</label>
                                <div class="fw-bold">
                                    @if($deviceCondition->is_active)
                                        <span class="badge bg-success">{{ __('app.common.active') }}</span>
                                    @else
                                        <span class="badge bg-danger">{{ __('app.common.inactive') }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.common.created_at') }}</label>
                                <div class="fw-bold">{{ $deviceCondition->created_at->format('Y-m-d H:i') }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.common.updated_at') }}</label>
                                <div class="fw-bold">{{ $deviceCondition->updated_at->format('Y-m-d H:i') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Tickets -->
            @if($deviceCondition->repairTickets && $deviceCondition->repairTickets->count() > 0)
                <div class="card mt-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">{{ __('app.common.related_tickets') }}</h5>
                        <span class="badge bg-primary">{{ $deviceCondition->repairTickets->count() }}</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{{ __('app.repair_tickets.ticket_number') }}</th>
                                        <th>{{ __('app.customers.customer') }}</th>
                                        <th>{{ __('app.repair_tickets.device') }}</th>
                                        <th>{{ __('app.common.received_date') }}</th>
                                        <th>{{ __('app.common.actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($deviceCondition->repairTickets->take(10) as $ticket)
                                        <tr>
                                            <td>
                                                <a href="{{ route('repair-tickets.show', $ticket) }}" class="text-decoration-none">
                                                    {{ $ticket->ticket_number }}
                                                </a>
                                            </td>
                                            <td>{{ $ticket->customer->name ?? __('app.common.unknown') }}</td>
                                            <td>{{ $ticket->brand->name ?? '' }} {{ $ticket->device_model }}</td>
                                            <td>{{ $ticket->received_date->format('Y-m-d') }}</td>
                                            <td>
                                                <a href="{{ route('repair-tickets.show', $ticket) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @if($deviceCondition->repairTickets->count() > 10)
                            <div class="text-center mt-3">
                                <a href="{{ route('repair-tickets.index', ['condition' => $deviceCondition->id]) }}" class="btn btn-outline-primary">
                                    {{ __('app.common.view_all_tickets') }} ({{ $deviceCondition->repairTickets->count() }})
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Usage Statistics -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.common.usage_statistics') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12">
                            <div class="mb-3">
                                <h3 class="text-primary mb-0">{{ $deviceCondition->repair_tickets_count ?? 0 }}</h3>
                                <small class="text-muted">{{ __('app.common.total_tickets') }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.common.actions') }}</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('device-conditions.edit', $deviceCondition) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>{{ __('app.common.edit') }}
                        </a>
                        
                        @if(($deviceCondition->repair_tickets_count ?? 0) == 0)
                            <form action="{{ route('device-conditions.destroy', $deviceCondition) }}" method="POST" onsubmit="return confirm('{{ __('app.device_conditions.confirm_delete') }}')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="fas fa-trash me-2"></i>{{ __('app.common.delete') }}
                                </button>
                            </form>
                        @else
                            <button type="button" class="btn btn-danger" disabled title="{{ __('app.common.cannot_delete_has_tickets') }}">
                                <i class="fas fa-trash me-2"></i>{{ __('app.common.delete') }}
                            </button>
                            <small class="text-muted">{{ __('app.device_conditions.cannot_delete_explanation') }}</small>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
