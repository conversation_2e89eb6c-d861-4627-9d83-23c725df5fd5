<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class PosTerminal extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'terminal_code',
        'terminal_name',
        'location',
        'is_active',
        'configuration',
        'assigned_user_id',
        'last_activity_at',
        'cash_drawer_balance',
        'opening_balance',
        'shift_started_at',
        'shift_opened_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_active' => 'boolean',
        'configuration' => 'array',
        'last_activity_at' => 'datetime',
        'cash_drawer_balance' => 'decimal:2',
        'opening_balance' => 'decimal:2',
        'shift_started_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the assigned user.
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_user_id');
    }

    /**
     * Get the user who opened the current shift.
     */
    public function shiftOpenedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'shift_opened_by');
    }

    /**
     * Get all transactions for this terminal.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(PosTransaction::class, 'terminal_id');
    }

    /**
     * Get all sessions for this terminal.
     */
    public function sessions(): HasMany
    {
        return $this->hasMany(PosSession::class, 'terminal_id');
    }

    /**
     * Get the current active session.
     */
    public function currentSession(): HasOne
    {
        return $this->hasOne(PosSession::class, 'terminal_id')->where('status', 'active');
    }

    /**
     * Get cash drawer operations.
     */
    public function cashDrawerOperations(): HasMany
    {
        return $this->hasMany(CashDrawerOperation::class, 'terminal_id');
    }

    /**
     * Generate unique terminal code.
     */
    public static function generateTerminalCode(): string
    {
        $lastTerminal = static::latest('id')->first();
        $nextNumber = $lastTerminal ? $lastTerminal->id + 1 : 1;

        return 'POS-' . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Check if terminal has an active shift.
     */
    public function hasActiveShift(): bool
    {
        return $this->shift_started_at !== null && $this->currentSession !== null;
    }

    /**
     * Open a new shift.
     */
    public function openShift(User $user, float $openingCash = 0): PosSession
    {
        if ($this->hasActiveShift()) {
            throw new \Exception('Terminal already has an active shift');
        }

        $session = $this->sessions()->create([
            'session_number' => PosSession::generateSessionNumber(),
            'user_id' => $user->id,
            'started_at' => now(),
            'opening_cash' => $openingCash,
            'status' => 'active'
        ]);

        $this->update([
            'shift_started_at' => now(),
            'shift_opened_by' => $user->id,
            'opening_balance' => $openingCash,
            'cash_drawer_balance' => $openingCash
        ]);

        // Record cash drawer operation
        $this->cashDrawerOperations()->create([
            'operation_type' => 'open_shift',
            'amount' => $openingCash,
            'balance_before' => 0,
            'balance_after' => $openingCash,
            'reason' => 'Shift opened',
            'performed_by' => $user->id,
            'operation_date' => now()
        ]);

        return $session;
    }

    /**
     * Close the current shift.
     */
    public function closeShift(User $user, float $closingCash, array $paymentSummary = []): PosSession
    {
        $session = $this->currentSession;
        
        if (!$session) {
            throw new \Exception('No active shift to close');
        }

        $expectedCash = $this->opening_balance + $session->total_cash;
        $cashDifference = $closingCash - $expectedCash;

        $session->update([
            'ended_at' => now(),
            'closing_cash' => $closingCash,
            'expected_cash' => $expectedCash,
            'cash_difference' => $cashDifference,
            'payment_summary' => $paymentSummary,
            'status' => 'closed'
        ]);

        $this->update([
            'shift_started_at' => null,
            'shift_opened_by' => null,
            'cash_drawer_balance' => $closingCash
        ]);

        // Record cash drawer operation
        $this->cashDrawerOperations()->create([
            'operation_type' => 'close_shift',
            'amount' => $closingCash,
            'balance_before' => $this->cash_drawer_balance,
            'balance_after' => $closingCash,
            'reason' => 'Shift closed',
            'performed_by' => $user->id,
            'operation_date' => now(),
            'notes' => $cashDifference != 0 ? "Cash difference: {$cashDifference}" : null
        ]);

        return $session;
    }

    /**
     * Add cash to drawer.
     */
    public function addCash(User $user, float $amount, string $reason = null): void
    {
        $balanceBefore = $this->cash_drawer_balance;
        $balanceAfter = $balanceBefore + $amount;

        $this->update(['cash_drawer_balance' => $balanceAfter]);

        $this->cashDrawerOperations()->create([
            'operation_type' => 'cash_in',
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'reason' => $reason ?? 'Cash added',
            'performed_by' => $user->id,
            'operation_date' => now()
        ]);
    }

    /**
     * Remove cash from drawer.
     */
    public function removeCash(User $user, float $amount, string $reason = null): void
    {
        $balanceBefore = $this->cash_drawer_balance;
        $balanceAfter = $balanceBefore - $amount;

        if ($balanceAfter < 0) {
            throw new \Exception('Insufficient cash in drawer');
        }

        $this->update(['cash_drawer_balance' => $balanceAfter]);

        $this->cashDrawerOperations()->create([
            'operation_type' => 'cash_out',
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'reason' => $reason ?? 'Cash removed',
            'performed_by' => $user->id,
            'operation_date' => now()
        ]);
    }

    /**
     * Update last activity timestamp.
     */
    public function updateActivity(): void
    {
        $this->update(['last_activity_at' => now()]);
    }

    /**
     * Get today's sales summary.
     */
    public function getTodaysSalesSummary(): array
    {
        $transactions = $this->transactions()
            ->whereDate('transaction_date', today())
            ->where('status', 'completed')
            ->with('payments')
            ->get();

        $totalSales = $transactions->sum('total_amount');
        $transactionCount = $transactions->count();
        $averageTransaction = $transactionCount > 0 ? $totalSales / $transactionCount : 0;

        $paymentBreakdown = [];
        foreach ($transactions as $transaction) {
            foreach ($transaction->payments as $payment) {
                $method = $payment->payment_method;
                if (!isset($paymentBreakdown[$method])) {
                    $paymentBreakdown[$method] = 0;
                }
                $paymentBreakdown[$method] += $payment->amount;
            }
        }

        return [
            'total_sales' => $totalSales,
            'transaction_count' => $transactionCount,
            'average_transaction' => $averageTransaction,
            'payment_breakdown' => $paymentBreakdown
        ];
    }

    /**
     * Scope for active terminals.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for terminals with active shifts.
     */
    public function scopeWithActiveShift($query)
    {
        return $query->whereNotNull('shift_started_at');
    }

    /**
     * Get configuration value.
     */
    public function getConfigValue(string $key, $default = null)
    {
        return data_get($this->configuration, $key, $default);
    }

    /**
     * Set configuration value.
     */
    public function setConfigValue(string $key, $value): void
    {
        $config = $this->configuration ?? [];
        data_set($config, $key, $value);
        $this->update(['configuration' => $config]);
    }

    /**
     * Check if terminal is online (active in last 5 minutes).
     */
    public function isOnline(): bool
    {
        return $this->last_activity_at && $this->last_activity_at->gt(now()->subMinutes(5));
    }

    /**
     * Get status display.
     */
    public function getStatusDisplayAttribute(): string
    {
        if (!$this->is_active) {
            return app()->getLocale() === 'ar' ? 'غير نشط' : 'Inactive';
        }

        if ($this->hasActiveShift()) {
            return app()->getLocale() === 'ar' ? 'نشط - وردية مفتوحة' : 'Active - Shift Open';
        }

        if ($this->isOnline()) {
            return app()->getLocale() === 'ar' ? 'متصل' : 'Online';
        }

        return app()->getLocale() === 'ar' ? 'غير متصل' : 'Offline';
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        if (!$this->is_active) {
            return 'secondary';
        }

        if ($this->hasActiveShift()) {
            return 'success';
        }

        if ($this->isOnline()) {
            return 'primary';
        }

        return 'warning';
    }
}
