<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Customer;
use App\Models\Brand;
use App\Models\RepairStatus;
use App\Models\DeviceCondition;
use App\Models\RepairTicket;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class RepairTicketControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $customer;
    protected $brand;
    protected $status;
    protected $condition;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->customer = Customer::factory()->create();
        $this->brand = Brand::factory()->create();
        $this->status = RepairStatus::factory()->create(['name' => 'Pending']);
        $this->condition = DeviceCondition::factory()->create();
    }

    /** @test */
    public function authenticated_user_can_view_tickets_index()
    {
        $this->actingAs($this->user);

        RepairTicket::factory()->count(5)->create();

        $response = $this->get(route('repair-tickets.index'));

        $response->assertStatus(200);
        $response->assertViewIs('repair-tickets.index');
        $response->assertViewHas('repairTickets');
    }

    /** @test */
    public function guest_cannot_view_tickets_index()
    {
        $response = $this->get(route('repair-tickets.index'));

        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function user_can_search_tickets()
    {
        $this->actingAs($this->user);

        $customer = Customer::factory()->create(['name' => 'John Doe']);
        $ticket = RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'device_model' => 'iPhone 13'
        ]);

        RepairTicket::factory()->count(3)->create(); // Other tickets

        $response = $this->get(route('repair-tickets.index', ['search' => 'John']));

        $response->assertStatus(200);
        $response->assertSee($ticket->ticket_number);
        $response->assertSee('John Doe');
    }

    /** @test */
    public function user_can_filter_tickets_by_status()
    {
        $this->actingAs($this->user);

        $pendingStatus = RepairStatus::factory()->create(['name' => 'Pending']);
        $completedStatus = RepairStatus::factory()->create(['name' => 'Completed']);

        $pendingTicket = RepairTicket::factory()->create(['repair_status_id' => $pendingStatus->id]);
        $completedTicket = RepairTicket::factory()->create(['repair_status_id' => $completedStatus->id]);

        $response = $this->get(route('repair-tickets.index', ['status' => 'Pending']));

        $response->assertStatus(200);
        $response->assertSee($pendingTicket->ticket_number);
        $response->assertDontSee($completedTicket->ticket_number);
    }

    /** @test */
    public function user_can_view_create_ticket_form()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('repair-tickets.create'));

        $response->assertStatus(200);
        $response->assertViewIs('repair-tickets.create');
        $response->assertViewHas(['customers', 'brands', 'repairStatuses', 'deviceConditions', 'technicians']);
    }

    /** @test */
    public function user_can_create_new_ticket()
    {
        $this->actingAs($this->user);

        $ticketData = [
            'customer_id' => $this->customer->id,
            'brand_id' => $this->brand->id,
            'device_model' => 'iPhone 13 Pro',
            'reported_problem' => 'Screen is cracked and not responding',
            'device_pattern' => true,
            'security_pattern' => '123456',
            'device_condition_id' => $this->condition->id,
            'repair_status_id' => $this->status->id,
            'priority' => 'medium',
            'received_date' => now()->format('Y-m-d H:i'),
            'initial_cost' => 150.00
        ];

        $response = $this->post(route('repair-tickets.store'), $ticketData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('repair_tickets', [
            'customer_id' => $this->customer->id,
            'device_model' => 'iPhone 13 Pro',
            'reported_problem' => 'Screen is cracked and not responding'
        ]);

        // Check that security pattern is encrypted
        $ticket = RepairTicket::latest()->first();
        $this->assertEquals('123456', $ticket->security_pattern);
        $this->assertTrue($ticket->hasSecurityPattern());
    }

    /** @test */
    public function ticket_creation_validates_required_fields()
    {
        $this->actingAs($this->user);

        $response = $this->post(route('repair-tickets.store'), []);

        $response->assertSessionHasErrors([
            'customer_id',
            'brand_id',
            'device_model',
            'reported_problem',
            'repair_status_id',
            'received_date'
        ]);
    }

    /** @test */
    public function user_can_view_single_ticket()
    {
        $this->actingAs($this->user);

        $ticket = RepairTicket::factory()->create([
            'customer_id' => $this->customer->id,
            'brand_id' => $this->brand->id,
            'repair_status_id' => $this->status->id
        ]);

        $response = $this->get(route('repair-tickets.show', $ticket));

        $response->assertStatus(200);
        $response->assertViewIs('repair-tickets.show');
        $response->assertViewHas('repairTicket');
        $response->assertSee($ticket->ticket_number);
        $response->assertSee($ticket->customer->name);
    }

    /** @test */
    public function user_can_view_edit_ticket_form()
    {
        $this->actingAs($this->user);

        $ticket = RepairTicket::factory()->create();

        $response = $this->get(route('repair-tickets.edit', $ticket));

        $response->assertStatus(200);
        $response->assertViewIs('repair-tickets.edit');
        $response->assertViewHas('repairTicket');
    }

    /** @test */
    public function user_can_update_ticket()
    {
        $this->actingAs($this->user);

        $ticket = RepairTicket::factory()->create([
            'device_model' => 'iPhone 12',
            'technician_comments' => 'Initial assessment'
        ]);

        $updateData = [
            'customer_id' => $ticket->customer_id,
            'brand_id' => $ticket->brand_id,
            'device_model' => 'iPhone 13 Pro',
            'reported_problem' => $ticket->reported_problem,
            'device_pattern' => $ticket->device_pattern,
            'device_condition_id' => $ticket->device_condition_id,
            'repair_status_id' => $ticket->repair_status_id,
            'priority' => $ticket->priority,
            'received_date' => $ticket->received_date->format('Y-m-d H:i'),
            'technician_comments' => 'Updated assessment - needs new screen',
            'final_cost' => 175.00
        ];

        $response = $this->put(route('repair-tickets.update', $ticket), $updateData);

        $response->assertRedirect(route('repair-tickets.show', $ticket));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('repair_tickets', [
            'id' => $ticket->id,
            'device_model' => 'iPhone 13 Pro',
            'technician_comments' => 'Updated assessment - needs new screen',
            'final_cost' => 175.00
        ]);
    }

    /** @test */
    public function user_can_delete_ticket()
    {
        $this->actingAs($this->user);

        $ticket = RepairTicket::factory()->create();

        $response = $this->delete(route('repair-tickets.destroy', $ticket));

        $response->assertRedirect(route('repair-tickets.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseMissing('repair_tickets', [
            'id' => $ticket->id
        ]);
    }

    /** @test */
    public function user_can_assign_ticket_to_technician()
    {
        $this->actingAs($this->user);

        $technician = User::factory()->create();
        $ticket = RepairTicket::factory()->create(['assigned_to' => null]);

        $response = $this->patch(route('repair-tickets.assign', $ticket), [
            'assigned_to' => $technician->id
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('repair_tickets', [
            'id' => $ticket->id,
            'assigned_to' => $technician->id
        ]);
    }

    /** @test */
    public function user_can_update_ticket_status()
    {
        $this->actingAs($this->user);

        $newStatus = RepairStatus::factory()->create(['name' => 'In Progress']);
        $ticket = RepairTicket::factory()->create();

        $response = $this->patch(route('repair-tickets.update-status', $ticket), [
            'repair_status_id' => $newStatus->id
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('repair_tickets', [
            'id' => $ticket->id,
            'repair_status_id' => $newStatus->id
        ]);
    }

    /** @test */
    public function user_can_perform_bulk_actions()
    {
        $this->actingAs($this->user);

        $tickets = RepairTicket::factory()->count(3)->create();
        $newStatus = RepairStatus::factory()->create(['name' => 'Completed']);

        $response = $this->post(route('repair-tickets.bulk-action'), [
            'tickets' => $tickets->pluck('id')->toArray(),
            'action' => 'update_status',
            'repair_status_id' => $newStatus->id
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        foreach ($tickets as $ticket) {
            $this->assertDatabaseHas('repair_tickets', [
                'id' => $ticket->id,
                'repair_status_id' => $newStatus->id
            ]);
        }
    }

    /** @test */
    public function user_can_get_customer_autocomplete_data()
    {
        $this->actingAs($this->user);

        Customer::factory()->create(['name' => 'John Doe', 'phone_number' => '+1234567890']);
        Customer::factory()->create(['name' => 'Jane Smith', 'phone_number' => '+0987654321']);

        $response = $this->get(route('repair-tickets.customers-autocomplete', ['term' => 'John']));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => ['id', 'name', 'phone_number']
        ]);

        $data = $response->json();
        $this->assertCount(1, $data);
        $this->assertEquals('John Doe', $data[0]['name']);
    }

    /** @test */
    public function tickets_are_paginated()
    {
        $this->actingAs($this->user);

        RepairTicket::factory()->count(25)->create();

        $response = $this->get(route('repair-tickets.index'));

        $response->assertStatus(200);
        $response->assertViewHas('repairTickets');

        $tickets = $response->viewData('repairTickets');
        $this->assertEquals(15, $tickets->perPage()); // Assuming 15 per page
    }

    /** @test */
    public function user_can_export_tickets_to_excel()
    {
        $this->actingAs($this->user);

        RepairTicket::factory()->count(5)->create();

        $response = $this->get(route('print-export.tickets.export-excel'));

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    }

    /** @test */
    public function user_can_print_ticket_pdf()
    {
        $this->actingAs($this->user);

        $ticket = RepairTicket::factory()->create();

        $response = $this->get(route('print-export.ticket.print', $ticket));

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/pdf');
    }

    /** @test */
    public function ticket_number_is_automatically_generated()
    {
        $this->actingAs($this->user);

        $ticketData = [
            'customer_id' => $this->customer->id,
            'brand_id' => $this->brand->id,
            'device_model' => 'iPhone 13',
            'reported_problem' => 'Screen cracked',
            'repair_status_id' => $this->status->id,
            'received_date' => now()->format('Y-m-d H:i')
        ];

        $response = $this->post(route('repair-tickets.store'), $ticketData);

        $ticket = RepairTicket::latest()->first();
        $this->assertNotNull($ticket->ticket_number);
        $this->assertStringStartsWith('NJ', $ticket->ticket_number);
    }

    /** @test */
    public function user_cannot_create_ticket_with_invalid_customer()
    {
        $this->actingAs($this->user);

        $ticketData = [
            'customer_id' => 99999, // Non-existent customer
            'brand_id' => $this->brand->id,
            'device_model' => 'iPhone 13',
            'reported_problem' => 'Screen cracked',
            'repair_status_id' => $this->status->id,
            'received_date' => now()->format('Y-m-d H:i')
        ];

        $response = $this->post(route('repair-tickets.store'), $ticketData);

        $response->assertSessionHasErrors('customer_id');
    }
}
