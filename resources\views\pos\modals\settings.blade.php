<!-- Settings Modal -->
<div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-secondary text-white">
                <h5 class="modal-title" id="settingsModalLabel">
                    <i class="bi bi-gear"></i> {{ __('app.pos.settings') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- General Settings -->
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-sliders"></i> {{ __('app.pos.general_settings') }}
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="taxRate" class="form-label">{{ __('app.pos.tax_rate') }} (%)</label>
                                    <input type="number" class="form-control" id="taxRate" value="15" min="0" max="100" step="0.01">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="currency" class="form-label">{{ __('app.pos.currency') }}</label>
                                    <select class="form-select" id="currency">
                                        <option value="SAR" selected>{{ __('app.pos.saudi_riyal') }} (SAR)</option>
                                        <option value="USD">{{ __('app.pos.us_dollar') }} (USD)</option>
                                        <option value="EUR">{{ __('app.pos.euro') }} (EUR)</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="autoTax" checked>
                                        <label class="form-check-label" for="autoTax">
                                            {{ __('app.pos.auto_calculate_tax') }}
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="soundEnabled" checked>
                                        <label class="form-check-label" for="soundEnabled">
                                            {{ __('app.pos.enable_sounds') }}
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="barcodeAutoAdd" checked>
                                        <label class="form-check-label" for="barcodeAutoAdd">
                                            {{ __('app.pos.auto_add_barcode_items') }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Receipt Settings -->
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-receipt"></i> {{ __('app.pos.receipt_settings') }}
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="receiptHeader" class="form-label">{{ __('app.pos.receipt_header') }}</label>
                                    <textarea class="form-control" id="receiptHeader" rows="3">{{ config('app.name') }}
{{ __('app.pos.receipt_header_default') }}</textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="receiptFooter" class="form-label">{{ __('app.pos.receipt_footer') }}</label>
                                    <textarea class="form-control" id="receiptFooter" rows="2">{{ __('app.pos.receipt_footer_default') }}</textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="autoPrintReceipt" checked>
                                        <label class="form-check-label" for="autoPrintReceipt">
                                            {{ __('app.pos.auto_print_receipt') }}
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="showItemCodes">
                                        <label class="form-check-label" for="showItemCodes">
                                            {{ __('app.pos.show_item_codes') }}
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="receiptCopies" class="form-label">{{ __('app.pos.receipt_copies') }}</label>
                                    <select class="form-select" id="receiptCopies">
                                        <option value="1" selected>1 {{ __('app.pos.copy') }}</option>
                                        <option value="2">2 {{ __('app.pos.copies') }}</option>
                                        <option value="3">3 {{ __('app.pos.copies') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <!-- Display Settings -->
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-display"></i> {{ __('app.pos.display_settings') }}
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="gridColumns" class="form-label">{{ __('app.pos.product_grid_columns') }}</label>
                                    <select class="form-select" id="gridColumns">
                                        <option value="3">3 {{ __('app.pos.columns') }}</option>
                                        <option value="4" selected>4 {{ __('app.pos.columns') }}</option>
                                        <option value="5">5 {{ __('app.pos.columns') }}</option>
                                        <option value="6">6 {{ __('app.pos.columns') }}</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="fontSize" class="form-label">{{ __('app.pos.font_size') }}</label>
                                    <select class="form-select" id="fontSize">
                                        <option value="small">{{ __('app.pos.small') }}</option>
                                        <option value="medium" selected>{{ __('app.pos.medium') }}</option>
                                        <option value="large">{{ __('app.pos.large') }}</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="showProductImages" checked>
                                        <label class="form-check-label" for="showProductImages">
                                            {{ __('app.pos.show_product_images') }}
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="showStockLevels" checked>
                                        <label class="form-check-label" for="showStockLevels">
                                            {{ __('app.pos.show_stock_levels') }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Barcode Scanner Settings -->
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-upc-scan"></i> {{ __('app.pos.barcode_scanner') }}
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="barcodeEnabled" checked>
                                        <label class="form-check-label" for="barcodeEnabled">
                                            {{ __('app.pos.enable_barcode_scanner') }}
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="scannerPrefix" class="form-label">{{ __('app.pos.scanner_prefix') }}</label>
                                    <input type="text" class="form-control" id="scannerPrefix" placeholder="{{ __('app.pos.optional') }}">
                                    <div class="form-text">{{ __('app.pos.scanner_prefix_help') }}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="scannerSuffix" class="form-label">{{ __('app.pos.scanner_suffix') }}</label>
                                    <input type="text" class="form-control" id="scannerSuffix" placeholder="{{ __('app.pos.optional') }}">
                                    <div class="form-text">{{ __('app.pos.scanner_suffix_help') }}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <button type="button" class="btn btn-outline-primary w-100" id="testScannerBtn">
                                        <i class="bi bi-camera"></i> {{ __('app.pos.test_scanner') }}
                                    </button>
                                </div>
                                
                                <div class="alert alert-info">
                                    <small>
                                        <i class="bi bi-info-circle"></i>
                                        {{ __('app.pos.scanner_help_text') }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Keyboard Shortcuts -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-keyboard"></i> {{ __('app.pos.keyboard_shortcuts') }}
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-2">
                                            <kbd>F1</kbd> - {{ __('app.pos.focus_search') }}
                                        </div>
                                        <div class="mb-2">
                                            <kbd>F2</kbd> - {{ __('app.pos.new_customer') }}
                                        </div>
                                        <div class="mb-2">
                                            <kbd>F3</kbd> - {{ __('app.pos.clear_cart') }}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-2">
                                            <kbd>F4</kbd> - {{ __('app.pos.checkout') }}
                                        </div>
                                        <div class="mb-2">
                                            <kbd>F5</kbd> - {{ __('app.pos.refresh_products') }}
                                        </div>
                                        <div class="mb-2">
                                            <kbd>F11</kbd> - {{ __('app.pos.fullscreen') }}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-2">
                                            <kbd>Ctrl</kbd> + <kbd>+</kbd> - {{ __('app.pos.add_quantity') }}
                                        </div>
                                        <div class="mb-2">
                                            <kbd>Ctrl</kbd> + <kbd>-</kbd> - {{ __('app.pos.reduce_quantity') }}
                                        </div>
                                        <div class="mb-2">
                                            <kbd>Del</kbd> - {{ __('app.pos.remove_item') }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> {{ __('app.cancel') }}
                </button>
                <button type="button" class="btn btn-primary" id="saveSettingsBtn">
                    <i class="bi bi-check-circle"></i> {{ __('app.save') }}
                </button>
                <button type="button" class="btn btn-outline-danger" id="resetSettingsBtn">
                    <i class="bi bi-arrow-clockwise"></i> {{ __('app.pos.reset_defaults') }}
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Settings Modal Styles */
.card-header h6 {
    color: #495057;
    font-weight: 600;
}

.form-check-label {
    font-weight: 500;
    cursor: pointer;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

kbd {
    background-color: #212529;
    color: #fff;
    padding: 0.2rem 0.4rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    font-family: monospace;
}

.alert-info {
    background-color: #e7f3ff;
    border-color: #b8daff;
    color: #0c5460;
}

/* Card hover effects */
.card {
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Form controls styling */
.form-control:focus,
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Button styling */
#testScannerBtn:hover {
    background-color: #0d6efd;
    color: white;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .modal-lg {
        max-width: 95%;
        margin: 10px auto;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .row .col-md-4,
    .row .col-md-6 {
        margin-bottom: 15px;
    }
    
    kbd {
        font-size: 0.75rem;
        padding: 0.1rem 0.3rem;
    }
}

/* RTL Adjustments */
body[dir="rtl"] .form-check-label {
    text-align: right;
}

body[dir="rtl"] .form-text {
    text-align: right;
}

body[dir="rtl"] kbd {
    font-family: 'Cairo', monospace;
}
</style>
