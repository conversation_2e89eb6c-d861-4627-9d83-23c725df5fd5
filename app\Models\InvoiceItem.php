<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Casts\Attribute;

class InvoiceItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_id',
        'item_type',
        'item_name',
        'item_description',
        'item_sku',
        'inventory_item_id',
        'quantity',
        'unit_of_measure',
        'unit_price',
        'total_price',
        'discount_percentage',
        'discount_amount',
        'net_amount',
        'tax_rate',
        'tax_amount',
        'is_taxable',
        'notes',
        'sort_order'
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'is_taxable' => 'boolean'
    ];

    // العلاقات
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(InventoryItem::class);
    }

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    // Accessors
    protected function itemTypeDisplay(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->item_type) {
                'service' => 'خدمة',
                'part' => 'قطعة غيار',
                'labor' => 'عمالة',
                'other' => 'أخرى',
                default => $this->item_type
            }
        );
    }

    protected function itemTypeIcon(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->item_type) {
                'service' => 'fas fa-tools',
                'part' => 'fas fa-cog',
                'labor' => 'fas fa-user-hard-hat',
                'other' => 'fas fa-box',
                default => 'fas fa-box'
            }
        );
    }

    // Methods
    public function calculateTotal(): void
    {
        $this->total_price = $this->quantity * $this->unit_price;
    }

    public function updateInventory(): void
    {
        if ($this->item_type === 'part' && $this->inventoryItem) {
            // خصم الكمية من المخزون
            $this->inventoryItem->updateStock(-$this->quantity, 'sale', [
                'invoice_id' => $this->invoice_id,
                'notes' => "بيع - فاتورة رقم {$this->invoice->invoice_number}"
            ]);
        }
    }

    public function restoreInventory(): void
    {
        if ($this->item_type === 'part' && $this->inventoryItem) {
            // إرجاع الكمية للمخزون
            $this->inventoryItem->updateStock($this->quantity, 'return', [
                'invoice_id' => $this->invoice_id,
                'notes' => "إرجاع - إلغاء فاتورة رقم {$this->invoice->invoice_number}"
            ]);
        }
    }
}
