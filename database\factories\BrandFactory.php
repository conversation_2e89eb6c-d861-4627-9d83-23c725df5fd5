<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Brand>
 */
class BrandFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->randomElement([
                'Apple', 'Samsung', 'Huawei', 'Xiaomi', 'OnePlus',
                'Google', 'Sony', 'LG', 'Motorola', 'Nokia',
                'Oppo', 'Vivo', 'Realme', 'Honor', 'Nothing',
                'Asus', 'HTC', '<PERSON><PERSON><PERSON>', 'Alcatel', 'TCL'
            ]),
            'description' => $this->faker->optional(0.6)->sentence(),
        ];
    }
}
