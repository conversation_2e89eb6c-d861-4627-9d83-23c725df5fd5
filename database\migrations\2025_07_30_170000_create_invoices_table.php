<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number')->unique(); // Invoice number
            $table->foreignId('customer_id')->constrained()->onDelete('restrict');
            $table->foreignId('repair_ticket_id')->nullable()->constrained()->onDelete('set null');
            
            // Invoice dates
            $table->date('invoice_date'); // Invoice issue date
            $table->date('due_date'); // Payment due date
            
            // Financial information
            $table->decimal('subtotal', 10, 2)->default(0); // Subtotal before tax
            $table->decimal('tax_rate', 5, 2)->default(15); // Tax rate (VAT 15%)
            $table->decimal('tax_amount', 10, 2)->default(0); // Tax amount
            $table->decimal('discount_percentage', 5, 2)->default(0); // Discount percentage
            $table->decimal('discount_amount', 10, 2)->default(0); // Discount amount
            $table->decimal('total_amount', 10, 2)->default(0); // Total amount
            
            // Payment information
            $table->enum('payment_status', ['pending', 'partial', 'paid', 'overdue', 'cancelled'])->default('pending');
            $table->decimal('paid_amount', 10, 2)->default(0); // Amount paid so far
            $table->date('paid_date')->nullable(); // Date when fully paid
            
            // Payment method tracking
            $table->json('payment_methods')->nullable(); // Array of payment methods used
            
            // Invoice status
            $table->enum('status', ['draft', 'sent', 'viewed', 'paid', 'cancelled'])->default('draft');
            
            // Customer information snapshot (at time of invoice)
            $table->string('customer_name'); // Customer name at time of invoice
            $table->string('customer_phone'); // Customer phone at time of invoice
            $table->string('customer_email')->nullable(); // Customer email at time of invoice
            $table->text('customer_address')->nullable(); // Customer address at time of invoice
            $table->string('customer_tax_number')->nullable(); // Customer tax number if applicable
            
            // Company information (for invoice header)
            $table->string('company_name')->default('ورشة NJ للإصلاح');
            $table->string('company_phone')->default('0501234567');
            $table->string('company_email')->nullable();
            $table->text('company_address')->nullable();
            $table->string('company_tax_number')->nullable();
            $table->string('company_commercial_register')->nullable();
            
            // Additional information
            $table->text('notes')->nullable(); // Invoice notes
            $table->text('terms_conditions')->nullable(); // Terms and conditions
            $table->string('currency', 3)->default('SAR'); // Currency code
            
            // Tracking
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamp('sent_at')->nullable(); // When invoice was sent to customer
            $table->timestamp('viewed_at')->nullable(); // When customer viewed invoice
            $table->timestamps();

            // Indexes for better performance
            $table->index('invoice_number');
            $table->index('customer_id');
            $table->index('repair_ticket_id');
            $table->index('invoice_date');
            $table->index('due_date');
            $table->index('payment_status');
            $table->index('status');
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
