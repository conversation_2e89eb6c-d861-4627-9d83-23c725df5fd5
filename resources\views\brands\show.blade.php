@extends('layouts.app')

@section('title', 'عرض الماركة - ' . $brand->name)

@push('styles')
<style>
.brand-header {
    background: linear-gradient(45deg, #6f42c1, #e83e8c);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.info-card {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    margin-bottom: 1.5rem;
}

.info-card-header {
    background: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
}

.info-card-body {
    padding: 1.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #5a5c69;
}

.info-value {
    color: #2c3e50;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-weight: 600;
    font-size: 0.875rem;
}

.status-active {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.status-inactive {
    background: linear-gradient(45deg, #6c757d, #495057);
    color: white;
}

.tickets-table {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    overflow: hidden;
}

.table th {
    background: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .brand-header {
        padding: 1rem;
    }
    
    .info-card-body {
        padding: 1rem;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="brand-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">{{ $brand->name }}</h1>
                <p class="mb-0 opacity-75">تفاصيل الماركة ومعلوماتها</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('brands.edit', $brand) }}" class="btn btn-light">
                    <i class="fas fa-edit me-2"></i>تعديل
                </a>
                <a href="{{ route('brands.index') }}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للماركات
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Brand Information -->
        <div class="col-md-6">
            <div class="info-card">
                <div class="info-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات الماركة
                    </h5>
                </div>
                <div class="info-card-body">
                    <div class="info-item">
                        <span class="info-label">اسم الماركة:</span>
                        <span class="info-value">{{ $brand->name }}</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">الحالة:</span>
                        <span class="status-badge {{ $brand->is_active ? 'status-active' : 'status-inactive' }}">
                            {{ $brand->is_active ? 'نشط' : 'غير نشط' }}
                        </span>
                    </div>
                    
                    @if($brand->description)
                    <div class="info-item">
                        <span class="info-label">الوصف:</span>
                        <span class="info-value">{{ $brand->description }}</span>
                    </div>
                    @endif
                    
                    <div class="info-item">
                        <span class="info-label">تاريخ الإنشاء:</span>
                        <span class="info-value">{{ $brand->created_at->format('Y-m-d H:i') }}</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">آخر تحديث:</span>
                        <span class="info-value">{{ $brand->updated_at->format('Y-m-d H:i') }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="col-md-6">
            <div class="info-card">
                <div class="info-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>الإحصائيات
                    </h5>
                </div>
                <div class="info-card-body">
                    <div class="info-item">
                        <span class="info-label">إجمالي بطاقات الإصلاح:</span>
                        <span class="info-value">
                            <span class="badge bg-primary">{{ $brand->repairTickets()->count() }}</span>
                        </span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">البطاقات المكتملة:</span>
                        <span class="info-value">
                            <span class="badge bg-success">{{ $brand->repairTickets()->whereHas('repairStatus', function($q) { $q->where('is_final', true); })->count() }}</span>
                        </span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">البطاقات قيد التنفيذ:</span>
                        <span class="info-value">
                            <span class="badge bg-warning">{{ $brand->repairTickets()->whereHas('repairStatus', function($q) { $q->where('is_final', false); })->count() }}</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Repair Tickets -->
    <div class="tickets-table">
        <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
            <h5 class="mb-0">
                <i class="fas fa-ticket-alt me-2"></i>بطاقات الإصلاح الحديثة
            </h5>
            @if($brand->repairTickets()->count() > 5)
                <a href="{{ route('repair-tickets.index', ['brand' => $brand->id]) }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            @endif
        </div>
        
        @if($brand->repairTickets()->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>رقم البطاقة</th>
                            <th>العميل</th>
                            <th>موديل الجهاز</th>
                            <th>الحالة</th>
                            <th>تاريخ الاستلام</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($brand->repairTickets()->latest()->take(5)->get() as $ticket)
                            <tr>
                                <td>
                                    <a href="{{ route('repair-tickets.show', $ticket) }}" class="text-decoration-none fw-bold">
                                        {{ $ticket->ticket_number }}
                                    </a>
                                </td>
                                <td>{{ $ticket->customer->name }}</td>
                                <td>{{ $ticket->device_model }}</td>
                                <td>
                                    <span class="badge" style="background-color: {{ $ticket->repairStatus->color }}; color: white;">
                                        {{ $ticket->repairStatus->name }}
                                    </span>
                                </td>
                                <td>{{ $ticket->received_date->format('Y-m-d') }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="empty-state">
                <i class="fas fa-ticket-alt"></i>
                <h5>لا توجد بطاقات إصلاح</h5>
                <p>لم يتم إنشاء أي بطاقات إصلاح لهذه الماركة بعد.</p>
            </div>
        @endif
    </div>
</div>
@endsection
