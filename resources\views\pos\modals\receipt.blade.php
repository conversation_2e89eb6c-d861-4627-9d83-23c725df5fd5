<!-- Receipt Modal -->
<div class="modal fade" id="receiptModal" tabindex="-1" aria-labelledby="receiptModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="receiptModalLabel">
                    <i class="bi bi-receipt"></i> {{ __('app.pos.receipt') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <!-- Receipt Content -->
                <div id="receiptContent" class="receipt-container">
                    <div class="receipt-header text-center">
                        <h3 class="business-name">{{ config('app.name') }}</h3>
                        <div class="business-info">
                            <div>{{ __('app.pos.receipt_header_default') }}</div>
                            <div>{{ __('app.pos.phone') }}: {{ config('app.phone', '+966501234567') }}</div>
                            <div>{{ __('app.pos.email') }}: {{ config('app.email', '<EMAIL>') }}</div>
                        </div>
                        <hr class="receipt-divider">
                    </div>
                    
                    <div class="receipt-body">
                        <!-- Transaction Info -->
                        <div class="transaction-info mb-3">
                            <div class="row">
                                <div class="col-6">
                                    <strong>{{ __('app.pos.receipt_number') }}:</strong><br>
                                    <span id="receiptNumber">#POS-000001</span>
                                </div>
                                <div class="col-6 text-end">
                                    <strong>{{ __('app.pos.date_time') }}:</strong><br>
                                    <span id="receiptDateTime">{{ now()->format('Y-m-d H:i') }}</span>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-6">
                                    <strong>{{ __('app.pos.cashier') }}:</strong><br>
                                    <span id="receiptCashier">{{ auth()->user()->name }}</span>
                                </div>
                                <div class="col-6 text-end">
                                    <strong>{{ __('app.pos.customer') }}:</strong><br>
                                    <span id="receiptCustomer">{{ __('app.pos.walk_in_customer') }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="receipt-divider">
                        
                        <!-- Items Table -->
                        <div class="receipt-items">
                            <table class="table table-sm receipt-table">
                                <thead>
                                    <tr>
                                        <th>{{ __('app.pos.item') }}</th>
                                        <th class="text-center">{{ __('app.pos.qty') }}</th>
                                        <th class="text-end">{{ __('app.pos.price') }}</th>
                                        <th class="text-end">{{ __('app.pos.total') }}</th>
                                    </tr>
                                </thead>
                                <tbody id="receiptItemsTable">
                                    <!-- Items will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                        
                        <hr class="receipt-divider">
                        
                        <!-- Totals -->
                        <div class="receipt-totals">
                            <div class="row mb-1">
                                <div class="col-8">{{ __('app.pos.subtotal') }}:</div>
                                <div class="col-4 text-end" id="receiptSubtotal">0.00 {{ __('app.currency') }}</div>
                            </div>
                            <div class="row mb-1">
                                <div class="col-8">{{ __('app.pos.tax') }} (<span id="receiptTaxRate">15</span>%):</div>
                                <div class="col-4 text-end" id="receiptTaxAmount">0.00 {{ __('app.currency') }}</div>
                            </div>
                            <div class="row mb-1" id="receiptDiscountRow" style="display: none;">
                                <div class="col-8">{{ __('app.pos.discount') }}:</div>
                                <div class="col-4 text-end" id="receiptDiscountAmount">0.00 {{ __('app.currency') }}</div>
                            </div>
                            <hr class="receipt-divider">
                            <div class="row total-row">
                                <div class="col-8"><strong>{{ __('app.pos.total') }}:</strong></div>
                                <div class="col-4 text-end"><strong id="receiptTotalAmount">0.00 {{ __('app.currency') }}</strong></div>
                            </div>
                        </div>
                        
                        <hr class="receipt-divider">
                        
                        <!-- Payment Info -->
                        <div class="payment-info">
                            <div class="row mb-1">
                                <div class="col-8">{{ __('app.pos.payment_method') }}:</div>
                                <div class="col-4 text-end" id="receiptPaymentMethod">{{ __('app.pos.cash') }}</div>
                            </div>
                            <div class="row mb-1" id="receiptAmountReceivedRow">
                                <div class="col-8">{{ __('app.pos.amount_received') }}:</div>
                                <div class="col-4 text-end" id="receiptAmountReceived">0.00 {{ __('app.currency') }}</div>
                            </div>
                            <div class="row mb-1" id="receiptChangeRow">
                                <div class="col-8">{{ __('app.pos.change') }}:</div>
                                <div class="col-4 text-end" id="receiptChange">0.00 {{ __('app.currency') }}</div>
                            </div>
                            <div class="row mb-1" id="receiptReferenceRow" style="display: none;">
                                <div class="col-8">{{ __('app.pos.reference') }}:</div>
                                <div class="col-4 text-end" id="receiptReference">-</div>
                            </div>
                        </div>
                        
                        <hr class="receipt-divider">
                        
                        <!-- Notes -->
                        <div class="receipt-notes" id="receiptNotesSection" style="display: none;">
                            <strong>{{ __('app.pos.notes') }}:</strong><br>
                            <span id="receiptNotes"></span>
                            <hr class="receipt-divider">
                        </div>
                        
                        <!-- Footer -->
                        <div class="receipt-footer text-center">
                            <div class="thank-you">{{ __('app.pos.thank_you') }}</div>
                            <div class="return-policy">{{ __('app.pos.receipt_footer_default') }}</div>
                            
                            <!-- QR Code for digital receipt -->
                            <div class="qr-code mt-3" id="receiptQRCode" style="display: none;">
                                <div class="text-center">
                                    <div id="qrCodeContainer"></div>
                                    <small>{{ __('app.pos.scan_for_digital_receipt') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> {{ __('app.close') }}
                </button>
                <button type="button" class="btn btn-info" id="emailReceiptBtn">
                    <i class="bi bi-envelope"></i> {{ __('app.pos.email_receipt') }}
                </button>
                <button type="button" class="btn btn-warning" id="smsReceiptBtn">
                    <i class="bi bi-phone"></i> {{ __('app.pos.sms_receipt') }}
                </button>
                <button type="button" class="btn btn-primary" id="printReceiptBtn">
                    <i class="bi bi-printer"></i> {{ __('app.pos.print_receipt') }}
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Receipt Styles */
.receipt-container {
    background: white;
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
    max-width: 400px;
    margin: 0 auto;
}

.receipt-header {
    margin-bottom: 20px;
}

.business-name {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

.business-info {
    font-size: 12px;
    color: #666;
    line-height: 1.3;
}

.receipt-divider {
    border: none;
    border-top: 1px dashed #333;
    margin: 10px 0;
}

.transaction-info {
    font-size: 12px;
}

.receipt-table {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    margin-bottom: 0;
}

.receipt-table th {
    border-bottom: 1px solid #333;
    padding: 5px 2px;
    font-weight: bold;
    background: none;
}

.receipt-table td {
    border: none;
    padding: 3px 2px;
    vertical-align: top;
}

.receipt-table .item-name {
    font-weight: bold;
}

.receipt-table .item-code {
    font-size: 10px;
    color: #666;
}

.receipt-totals {
    font-size: 13px;
}

.total-row {
    font-size: 16px;
    font-weight: bold;
}

.payment-info {
    font-size: 12px;
}

.receipt-footer {
    margin-top: 20px;
    font-size: 11px;
}

.thank-you {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

.return-policy {
    color: #666;
    line-height: 1.3;
}

.qr-code {
    margin-top: 15px;
}

#qrCodeContainer {
    display: inline-block;
    margin-bottom: 5px;
}

/* Print Styles */
@media print {
    .modal-header,
    .modal-footer {
        display: none !important;
    }
    
    .modal-content {
        border: none;
        box-shadow: none;
    }
    
    .receipt-container {
        padding: 0;
        max-width: none;
        width: 80mm; /* Thermal printer width */
    }
    
    .receipt-table {
        font-size: 10px;
    }
    
    .business-name {
        font-size: 18px;
    }
    
    .total-row {
        font-size: 14px;
    }
    
    .thank-you {
        font-size: 14px;
    }
    
    /* Hide elements that shouldn't print */
    .btn,
    .modal-backdrop {
        display: none !important;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .modal-lg {
        max-width: 95%;
        margin: 10px auto;
    }
    
    .receipt-container {
        padding: 15px;
        font-size: 13px;
    }
    
    .business-name {
        font-size: 20px;
    }
    
    .receipt-table {
        font-size: 11px;
    }
    
    .total-row {
        font-size: 14px;
    }
}

/* RTL Adjustments */
body[dir="rtl"] .receipt-container {
    text-align: right;
    font-family: 'Cairo', monospace;
}

body[dir="rtl"] .receipt-table th,
body[dir="rtl"] .receipt-table td {
    text-align: right;
}

body[dir="rtl"] .receipt-table th:last-child,
body[dir="rtl"] .receipt-table td:last-child {
    text-align: left; /* Keep numbers left-aligned */
}

body[dir="rtl"] .text-end {
    text-align: left !important;
}

body[dir="rtl"] .text-center {
    text-align: center !important;
}

/* Receipt Animation */
.receipt-container {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Button hover effects */
.modal-footer .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Success animation for print button */
.btn-success-animation {
    animation: successPulse 0.6s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); background-color: #28a745; }
    100% { transform: scale(1); }
}
</style>

<!-- QR Code Library -->
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
