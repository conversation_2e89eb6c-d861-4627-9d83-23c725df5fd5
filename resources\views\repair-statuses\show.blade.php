@extends('layouts.app')

@section('title', $repairStatus->name)

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <span class="badge me-2" style="background-color: {{ $repairStatus->color }}; color: white;">
                    {{ $repairStatus->name }}
                </span>
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('repair-statuses.index') }}">{{ __('app.repair_statuses.title') }}</a></li>
                    <li class="breadcrumb-item active">{{ $repairStatus->name }}</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="{{ route('repair-statuses.edit', $repairStatus) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>{{ __('app.common.edit') }}
            </a>
            <a href="{{ route('repair-statuses.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>{{ __('app.common.back') }}
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Status Details -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.repair_statuses.status_details') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.repair_statuses.name') }}</label>
                                <div class="fw-bold">{{ $repairStatus->name }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.common.slug') }}</label>
                                <div class="fw-bold"><code>{{ $repairStatus->slug }}</code></div>
                            </div>
                        </div>
                    </div>

                    @if($repairStatus->description)
                        <div class="mb-3">
                            <label class="form-label text-muted">{{ __('app.repair_statuses.description') }}</label>
                            <div class="fw-bold">{{ $repairStatus->description }}</div>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.repair_statuses.color') }}</label>
                                <div class="d-flex align-items-center">
                                    <div class="color-preview me-2" style="width: 30px; height: 30px; background-color: {{ $repairStatus->color }}; border-radius: 5px; border: 1px solid #dee2e6;"></div>
                                    <code class="fw-bold">{{ $repairStatus->color }}</code>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.common.sort_order') }}</label>
                                <div class="fw-bold">
                                    <span class="badge bg-secondary">{{ $repairStatus->sort_order }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.common.status') }}</label>
                                <div class="fw-bold">
                                    @if($repairStatus->is_active)
                                        <span class="badge bg-success">{{ __('app.common.active') }}</span>
                                    @else
                                        <span class="badge bg-danger">{{ __('app.common.inactive') }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.repair_statuses.is_final') }}</label>
                                <div class="fw-bold">
                                    @if($repairStatus->is_final)
                                        <span class="badge bg-success">{{ __('app.common.yes') }}</span>
                                        <small class="text-muted d-block">{{ __('app.repair_statuses.final_status_help') }}</small>
                                    @else
                                        <span class="badge bg-secondary">{{ __('app.common.no') }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.common.created_at') }}</label>
                                <div class="fw-bold">{{ $repairStatus->created_at->format('Y-m-d H:i') }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.common.updated_at') }}</label>
                                <div class="fw-bold">{{ $repairStatus->updated_at->format('Y-m-d H:i') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Tickets -->
            @if($repairStatus->repairTickets && $repairStatus->repairTickets->count() > 0)
                <div class="card mt-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">{{ __('app.common.related_tickets') }}</h5>
                        <span class="badge bg-primary">{{ $repairStatus->repairTickets->count() }}</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{{ __('app.repair_tickets.ticket_number') }}</th>
                                        <th>{{ __('app.customers.customer') }}</th>
                                        <th>{{ __('app.repair_tickets.device') }}</th>
                                        <th>{{ __('app.common.received_date') }}</th>
                                        <th>{{ __('app.common.actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($repairStatus->repairTickets->take(10) as $ticket)
                                        <tr>
                                            <td>
                                                <a href="{{ route('repair-tickets.show', $ticket) }}" class="text-decoration-none">
                                                    {{ $ticket->ticket_number }}
                                                </a>
                                            </td>
                                            <td>{{ $ticket->customer->name ?? __('app.common.unknown') }}</td>
                                            <td>{{ $ticket->brand->name ?? '' }} {{ $ticket->device_model }}</td>
                                            <td>{{ $ticket->received_date->format('Y-m-d') }}</td>
                                            <td>
                                                <a href="{{ route('repair-tickets.show', $ticket) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @if($repairStatus->repairTickets->count() > 10)
                            <div class="text-center mt-3">
                                <a href="{{ route('repair-tickets.index', ['status' => $repairStatus->id]) }}" class="btn btn-outline-primary">
                                    {{ __('app.common.view_all_tickets') }} ({{ $repairStatus->repairTickets->count() }})
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Usage Statistics -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.common.usage_statistics') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12">
                            <div class="mb-3">
                                <h3 class="text-primary mb-0">{{ $repairStatus->repair_tickets_count ?? 0 }}</h3>
                                <small class="text-muted">{{ __('app.common.total_tickets') }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.common.actions') }}</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('repair-statuses.edit', $repairStatus) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>{{ __('app.common.edit') }}
                        </a>
                        
                        @if(($repairStatus->repair_tickets_count ?? 0) == 0)
                            <form action="{{ route('repair-statuses.destroy', $repairStatus) }}" method="POST" onsubmit="return confirm('{{ __('app.repair_statuses.confirm_delete') }}')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="fas fa-trash me-2"></i>{{ __('app.common.delete') }}
                                </button>
                            </form>
                        @else
                            <button type="button" class="btn btn-danger" disabled title="{{ __('app.common.cannot_delete_has_tickets') }}">
                                <i class="fas fa-trash me-2"></i>{{ __('app.common.delete') }}
                            </button>
                            <small class="text-muted">{{ __('app.repair_statuses.cannot_delete_explanation') }}</small>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
