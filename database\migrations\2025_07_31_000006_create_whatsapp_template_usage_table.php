<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_template_usage', function (Blueprint $table) {
            $table->id();
            
            // Template and message relationship
            $table->foreignId('template_id')->constrained('whatsapp_message_templates')->onDelete('cascade');
            $table->foreignId('message_id')->nullable()->constrained('whatsapp_messages')->onDelete('set null');
            
            // Customer information
            $table->string('customer_phone', 20);
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('set null');
            
            // Usage context
            $table->string('trigger_type')->nullable(); // 'manual', 'auto', 'scheduled', 'webhook'
            $table->string('trigger_source')->nullable(); // 'dashboard', 'api', 'bot', 'system'
            $table->unsignedBigInteger('triggered_by')->nullable(); // User who triggered
            $table->foreign('triggered_by')->references('id')->on('users')->onDelete('set null');
            
            // Template data used
            $table->json('variables_used')->nullable(); // Actual variable values used
            $table->text('final_message')->nullable(); // Final rendered message
            
            // Delivery tracking
            $table->enum('status', ['sent', 'delivered', 'read', 'failed'])->default('sent');
            $table->string('whatsapp_message_id')->nullable();
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->text('error_message')->nullable();
            
            // Performance metrics
            $table->integer('delivery_time_seconds')->nullable(); // Time to deliver
            $table->boolean('customer_responded')->default(false);
            $table->timestamp('customer_response_at')->nullable();
            $table->integer('response_time_seconds')->nullable();
            
            // Business context
            $table->unsignedBigInteger('repair_ticket_id')->nullable();
            $table->foreign('repair_ticket_id')->references('id')->on('repair_tickets')->onDelete('set null');
            $table->string('business_event')->nullable(); // 'status_update', 'appointment', 'payment_reminder'
            
            // Cost tracking (for paid templates)
            $table->decimal('cost', 8, 4)->default(0); // Cost in local currency
            $table->string('currency', 3)->default('SAR');
            
            $table->timestamps();
            
            // Indexes for analytics
            $table->index(['template_id', 'created_at']);
            $table->index(['customer_phone', 'created_at']);
            $table->index(['status', 'created_at']);
            $table->index(['trigger_type', 'created_at']);
            $table->index(['business_event', 'created_at']);
            $table->index('sent_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_template_usage');
    }
};
