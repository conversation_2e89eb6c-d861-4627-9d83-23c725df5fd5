#!/bin/bash

# Production Server Setup Script for NJ Repair Shop
# Run this script on Ubuntu 20.04+ server

set -e

echo "🚀 Starting NJ Repair Shop Production Setup..."

# Update system
echo "📦 Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install required packages
echo "📦 Installing required packages..."
sudo apt install -y nginx mysql-server php8.2-fpm php8.2-mysql php8.2-xml php8.2-mbstring \
    php8.2-curl php8.2-zip php8.2-gd php8.2-intl php8.2-bcmath php8.2-redis \
    redis-server certbot python3-certbot-nginx unzip git supervisor

# Install Composer
echo "📦 Installing Composer..."
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
sudo chmod +x /usr/local/bin/composer

# Install Node.js and npm
echo "📦 Installing Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Create application user
echo "👤 Creating application user..."
sudo useradd -m -s /bin/bash njrepair
sudo usermod -aG www-data njrepair

# Create application directory
echo "📁 Setting up application directory..."
sudo mkdir -p /var/www/njrepair
sudo chown njrepair:www-data /var/www/njrepair
sudo chmod 755 /var/www/njrepair

# Configure MySQL
echo "🗄️ Configuring MySQL..."
sudo mysql -e "CREATE DATABASE njrepair_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
sudo mysql -e "CREATE USER 'njrepair'@'localhost' IDENTIFIED BY '$(openssl rand -base64 32)';"
sudo mysql -e "GRANT ALL PRIVILEGES ON njrepair_production.* TO 'njrepair'@'localhost';"
sudo mysql -e "FLUSH PRIVILEGES;"

# Configure Redis
echo "🔴 Configuring Redis..."
sudo systemctl enable redis-server
sudo systemctl start redis-server

# Configure PHP-FPM
echo "🐘 Configuring PHP-FPM..."
sudo tee /etc/php/8.2/fpm/pool.d/njrepair.conf > /dev/null <<EOF
[njrepair]
user = njrepair
group = www-data
listen = /run/php/php8.2-fpm-njrepair.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500

php_admin_value[disable_functions] = exec,passthru,shell_exec,system
php_admin_flag[allow_url_fopen] = off
php_admin_value[memory_limit] = 256M
php_admin_value[max_execution_time] = 300
php_admin_value[upload_max_filesize] = 10M
php_admin_value[post_max_size] = 10M
EOF

sudo systemctl restart php8.2-fpm

# Configure Nginx
echo "🌐 Configuring Nginx..."
sudo tee /etc/nginx/sites-available/njrepair > /dev/null <<EOF
server {
    listen 80;
    server_name njrepair.com www.njrepair.com;
    root /var/www/njrepair/public;
    index index.php index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone \$binary_remote_addr zone=api:10m rate=30r/m;

    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/run/php/php8.2-fpm-njrepair.sock;
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location /login {
        limit_req zone=login burst=3 nodelay;
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    location /api/ {
        limit_req zone=api burst=10 nodelay;
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

sudo ln -sf /etc/nginx/sites-available/njrepair /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl restart nginx

# Configure firewall
echo "🔥 Configuring firewall..."
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw --force enable

# Setup SSL with Let's Encrypt
echo "🔒 Setting up SSL certificate..."
read -p "Enter your domain name (e.g., njrepair.com): " DOMAIN
sudo certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN

# Setup automatic SSL renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -

# Configure log rotation
echo "📝 Configuring log rotation..."
sudo tee /etc/logrotate.d/njrepair > /dev/null <<EOF
/var/www/njrepair/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 njrepair www-data
}
EOF

echo "✅ Production server setup completed!"
echo "📋 Next steps:"
echo "1. Deploy your Laravel application to /var/www/njrepair"
echo "2. Configure environment variables"
echo "3. Run database migrations"
echo "4. Set up monitoring and backups"
echo ""
echo "🔐 Database credentials:"
echo "Database: njrepair_production"
echo "Username: njrepair"
echo "Password: Check /root/.mysql_credentials"
