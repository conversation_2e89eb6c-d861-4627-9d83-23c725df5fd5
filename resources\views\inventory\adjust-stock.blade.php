@extends('layouts.app')

@section('title', 'تعديل المخزون - ' . $inventory->name)

@push('styles')
<style>
.adjustment-card {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    margin-bottom: 1.5rem;
}

.adjustment-header {
    background: linear-gradient(45deg, #f6c23e, #dda20a);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
    margin: -1px -1px 0 -1px;
}

.adjustment-body {
    padding: 1.5rem;
}

.current-stock-display {
    background: linear-gradient(45deg, #4e73df, #224abe);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1.5rem;
}

.stock-value {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stock-label {
    font-size: 1rem;
    opacity: 0.9;
}

.adjustment-type-card {
    border: 2px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.adjustment-type-card:hover {
    border-color: #f6c23e;
    background-color: #fffbf0;
}

.adjustment-type-card.selected {
    border-color: #f6c23e;
    background-color: #fff3cd;
}

.adjustment-type-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.quantity-input-group {
    position: relative;
}

.quantity-display {
    font-size: 1.5rem;
    font-weight: bold;
    text-align: center;
    padding: 1rem;
    border: 2px solid #e3e6f0;
    border-radius: 0.5rem;
    background: #f8f9fc;
}

.btn-adjustment {
    width: 100%;
    padding: 0.75rem;
    font-weight: 600;
    border-radius: 0.35rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.btn-adjustment:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-increase {
    background: linear-gradient(45deg, #1cc88a, #17a673);
    border: none;
    color: white;
}

.btn-increase:hover {
    background: linear-gradient(45deg, #17a673, #138d5f);
    color: white;
}

.btn-decrease {
    background: linear-gradient(45deg, #e74a3b, #c0392b);
    border: none;
    color: white;
}

.btn-decrease:hover {
    background: linear-gradient(45deg, #c0392b, #a93226);
    color: white;
}

.btn-set {
    background: linear-gradient(45deg, #f6c23e, #dda20a);
    border: none;
    color: white;
}

.btn-set:hover {
    background: linear-gradient(45deg, #dda20a, #c69500);
    color: white;
}

.preview-section {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
}

.preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e3e6f0;
}

.preview-item:last-child {
    border-bottom: none;
}

.preview-label {
    font-weight: 600;
    color: #5a5c69;
}

.preview-value {
    font-weight: bold;
}

.preview-value.positive {
    color: #1cc88a;
}

.preview-value.negative {
    color: #e74a3b;
}

.preview-value.neutral {
    color: #f6c23e;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #f6c23e;
    box-shadow: 0 0 0 0.2rem rgba(246, 194, 62, 0.25);
}

.required-field::after {
    content: " *";
    color: #e74a3b;
    font-weight: bold;
}

.help-text {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.alert {
    border-radius: 0.35rem;
    border: none;
    padding: 1rem 1.5rem;
}

.quick-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.quick-btn {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #d1d3e2;
    background: white;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-btn:hover {
    background: #f8f9fc;
    border-color: #f6c23e;
}

.quick-btn.active {
    background: #f6c23e;
    color: white;
    border-color: #f6c23e;
}

@media (max-width: 768px) {
    .adjustment-body {
        padding: 1rem;
    }

    .stock-value {
        font-size: 2rem;
    }

    .quick-buttons {
        flex-direction: column;
    }

    .btn-adjustment {
        margin-bottom: 1rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">تعديل المخزون</h1>
            <p class="text-muted">{{ $inventory->name }} - {{ $inventory->sku }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('inventory.show', $inventory) }}" class="btn btn-info">
                <i class="fas fa-eye me-2"></i>عرض العنصر
            </a>
            <a href="{{ route('inventory.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للمخزون
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Current Stock Display -->
        <div class="col-lg-4">
            <div class="current-stock-display">
                <div class="stock-value" id="currentStockDisplay">
                    @arabicNumber($inventory->current_stock)
                </div>
                <div class="stock-label">{{ $inventory->unit_of_measure }}</div>
                <div class="stock-label">المخزون الحالي</div>
            </div>

            <!-- Stock Limits -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">حدود المخزون</h6>
                </div>
                <div class="card-body">
                    <div class="preview-item">
                        <span class="preview-label">الحد الأدنى:</span>
                        <span class="preview-value">@arabicNumber($inventory->minimum_stock)</span>
                    </div>
                    <div class="preview-item">
                        <span class="preview-label">الحد الأقصى:</span>
                        <span class="preview-value">@arabicNumber($inventory->maximum_stock)</span>
                    </div>
                    <div class="preview-item">
                        <span class="preview-label">كمية إعادة الطلب:</span>
                        <span class="preview-value">@arabicNumber($inventory->reorder_quantity)</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <button type="button" class="btn btn-increase btn-adjustment" onclick="setAdjustmentType('increase', {{ $inventory->reorder_quantity }})">
                        <i class="fas fa-plus me-2"></i>إضافة كمية إعادة الطلب
                    </button>
                    <button type="button" class="btn btn-set btn-adjustment" onclick="setAdjustmentType('set', {{ $inventory->minimum_stock }})">
                        <i class="fas fa-equals me-2"></i>تعيين للحد الأدنى
                    </button>
                    <button type="button" class="btn btn-set btn-adjustment" onclick="setAdjustmentType('set', {{ $inventory->maximum_stock }})">
                        <i class="fas fa-equals me-2"></i>تعيين للحد الأقصى
                    </button>
                </div>
            </div>
        </div>

        <!-- Adjustment Form -->
        <div class="col-lg-8">
            <form action="{{ route('inventory.process-adjustment', $inventory) }}" method="POST" id="adjustmentForm">
                @csrf

                <!-- Adjustment Type Selection -->
                <div class="adjustment-card">
                    <div class="adjustment-header">
                        <h5 class="mb-0">
                            <i class="fas fa-balance-scale me-2"></i>
                            نوع التعديل
                        </h5>
                    </div>
                    <div class="adjustment-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="adjustment-type-card" onclick="selectAdjustmentType('increase')">
                                    <div class="text-center">
                                        <div class="adjustment-type-icon text-success">
                                            <i class="fas fa-plus-circle"></i>
                                        </div>
                                        <h6>زيادة المخزون</h6>
                                        <small class="text-muted">إضافة كمية جديدة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="adjustment-type-card" onclick="selectAdjustmentType('decrease')">
                                    <div class="text-center">
                                        <div class="adjustment-type-icon text-danger">
                                            <i class="fas fa-minus-circle"></i>
                                        </div>
                                        <h6>تقليل المخزون</h6>
                                        <small class="text-muted">خصم كمية موجودة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="adjustment-type-card" onclick="selectAdjustmentType('set')">
                                    <div class="text-center">
                                        <div class="adjustment-type-icon text-warning">
                                            <i class="fas fa-equals"></i>
                                        </div>
                                        <h6>تحديد المخزون</h6>
                                        <small class="text-muted">تعيين كمية محددة</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="adjustment_type" id="adjustment_type" required>
                        @error('adjustment_type')
                            <div class="text-danger mt-2">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Quantity Input -->
                <div class="adjustment-card">
                    <div class="adjustment-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            تفاصيل التعديل
                        </h5>
                    </div>
                    <div class="adjustment-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="quantity" class="form-label required-field">الكمية</label>
                                <div class="quantity-input-group">
                                    <input type="number"
                                           class="form-control @error('quantity') is-invalid @enderror"
                                           id="quantity"
                                           name="quantity"
                                           value="{{ old('quantity') }}"
                                           min="1"
                                           placeholder="أدخل الكمية"
                                           required>
                                    @error('quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Quick Quantity Buttons -->
                                <div class="quick-buttons mt-2">
                                    <button type="button" class="quick-btn" onclick="setQuantity(1)">1</button>
                                    <button type="button" class="quick-btn" onclick="setQuantity(5)">5</button>
                                    <button type="button" class="quick-btn" onclick="setQuantity(10)">10</button>
                                    <button type="button" class="quick-btn" onclick="setQuantity({{ $inventory->reorder_quantity }})">{{ $inventory->reorder_quantity }}</button>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="reason" class="form-label required-field">سبب التعديل</label>
                                <select class="form-select @error('reason') is-invalid @enderror"
                                        id="reason"
                                        name="reason"
                                        required>
                                    <option value="">اختر السبب</option>
                                    <option value="استلام بضاعة جديدة" {{ old('reason') == 'استلام بضاعة جديدة' ? 'selected' : '' }}>استلام بضاعة جديدة</option>
                                    <option value="إرجاع من عميل" {{ old('reason') == 'إرجاع من عميل' ? 'selected' : '' }}>إرجاع من عميل</option>
                                    <option value="تلف أو عطل" {{ old('reason') == 'تلف أو عطل' ? 'selected' : '' }}>تلف أو عطل</option>
                                    <option value="فقدان" {{ old('reason') == 'فقدان' ? 'selected' : '' }}>فقدان</option>
                                    <option value="جرد دوري" {{ old('reason') == 'جرد دوري' ? 'selected' : '' }}>جرد دوري</option>
                                    <option value="تصحيح خطأ" {{ old('reason') == 'تصحيح خطأ' ? 'selected' : '' }}>تصحيح خطأ</option>
                                    <option value="نقل بين مستودعات" {{ old('reason') == 'نقل بين مستودعات' ? 'selected' : '' }}>نقل بين مستودعات</option>
                                    <option value="أخرى" {{ old('reason') == 'أخرى' ? 'selected' : '' }}>أخرى</option>
                                </select>
                                @error('reason')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="unit_cost" class="form-label">تكلفة الوحدة (اختياري)</label>
                                <div class="input-group">
                                    <input type="number"
                                           class="form-control @error('unit_cost') is-invalid @enderror"
                                           id="unit_cost"
                                           name="unit_cost"
                                           value="{{ old('unit_cost', $inventory->cost_price) }}"
                                           step="0.01"
                                           min="0"
                                           placeholder="تكلفة الوحدة">
                                    <span class="input-group-text">ريال</span>
                                </div>
                                @error('unit_cost')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="help-text">لحساب التكلفة الإجمالية للحركة</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">التكلفة الإجمالية</label>
                                <div class="input-group">
                                    <input type="text"
                                           class="form-control"
                                           id="total_cost_display"
                                           readonly
                                           placeholder="سيتم الحساب تلقائياً">
                                    <span class="input-group-text">ريال</span>
                                </div>
                                <div class="help-text">يتم حسابها تلقائياً</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات إضافية</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror"
                                      id="notes"
                                      name="notes"
                                      rows="3"
                                      placeholder="أي ملاحظات أو تفاصيل إضافية">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Preview Section -->
                <div class="preview-section" id="previewSection" style="display: none;">
                    <h6 class="mb-3">
                        <i class="fas fa-eye me-2"></i>معاينة التعديل
                    </h6>
                    <div class="preview-item">
                        <span class="preview-label">المخزون الحالي:</span>
                        <span class="preview-value" id="currentStockPreview">@arabicNumber($inventory->current_stock)</span>
                    </div>
                    <div class="preview-item">
                        <span class="preview-label">نوع التعديل:</span>
                        <span class="preview-value" id="adjustmentTypePreview">-</span>
                    </div>
                    <div class="preview-item">
                        <span class="preview-label">الكمية:</span>
                        <span class="preview-value" id="quantityPreview">-</span>
                    </div>
                    <div class="preview-item">
                        <span class="preview-label">المخزون بعد التعديل:</span>
                        <span class="preview-value" id="newStockPreview">-</span>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-warning btn-lg">
                        <i class="fas fa-balance-scale me-2"></i>تطبيق التعديل
                    </button>
                    <a href="{{ route('inventory.show', $inventory) }}" class="btn btn-secondary btn-lg ms-2">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const currentStock = {{ $inventory->current_stock }};
    let selectedAdjustmentType = '';

    // Update preview when inputs change
    document.getElementById('quantity').addEventListener('input', updatePreview);
    document.getElementById('unit_cost').addEventListener('input', calculateTotalCost);

    function selectAdjustmentType(type) {
        selectedAdjustmentType = type;
        document.getElementById('adjustment_type').value = type;

        // Update UI
        document.querySelectorAll('.adjustment-type-card').forEach(card => {
            card.classList.remove('selected');
        });

        event.target.closest('.adjustment-type-card').classList.add('selected');

        updatePreview();
    }

    function setAdjustmentType(type, quantity) {
        selectAdjustmentType(type);
        document.getElementById('quantity').value = quantity;
        updatePreview();
    }

    function setQuantity(quantity) {
        document.getElementById('quantity').value = quantity;

        // Update quick buttons
        document.querySelectorAll('.quick-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');

        updatePreview();
        calculateTotalCost();
    }

    function updatePreview() {
        const quantity = parseInt(document.getElementById('quantity').value) || 0;
        const previewSection = document.getElementById('previewSection');

        if (selectedAdjustmentType && quantity > 0) {
            previewSection.style.display = 'block';

            let newStock = currentStock;
            let adjustmentText = '';
            let quantityDisplay = '';
            let previewClass = 'neutral';

            switch (selectedAdjustmentType) {
                case 'increase':
                    newStock = currentStock + quantity;
                    adjustmentText = 'زيادة المخزون';
                    quantityDisplay = '+' + quantity;
                    previewClass = 'positive';
                    break;
                case 'decrease':
                    newStock = Math.max(0, currentStock - quantity);
                    adjustmentText = 'تقليل المخزون';
                    quantityDisplay = '-' + quantity;
                    previewClass = 'negative';
                    break;
                case 'set':
                    newStock = quantity;
                    adjustmentText = 'تحديد المخزون';
                    quantityDisplay = quantity + ' (تحديد)';
                    previewClass = 'neutral';
                    break;
            }

            document.getElementById('adjustmentTypePreview').textContent = adjustmentText;
            document.getElementById('quantityPreview').textContent = quantityDisplay;
            document.getElementById('quantityPreview').className = 'preview-value ' + previewClass;
            document.getElementById('newStockPreview').textContent = newStock;
            document.getElementById('newStockPreview').className = 'preview-value ' +
                (newStock > currentStock ? 'positive' : newStock < currentStock ? 'negative' : 'neutral');

            // Update current stock display
            document.getElementById('currentStockDisplay').textContent = newStock;

            // Validate stock levels
            validateStockLevels(newStock);
        } else {
            previewSection.style.display = 'none';
            document.getElementById('currentStockDisplay').textContent = currentStock;
        }
    }

    function calculateTotalCost() {
        const quantity = parseInt(document.getElementById('quantity').value) || 0;
        const unitCost = parseFloat(document.getElementById('unit_cost').value) || 0;

        if (quantity > 0 && unitCost > 0) {
            const totalCost = quantity * unitCost;
            document.getElementById('total_cost_display').value = totalCost.toFixed(2);
        } else {
            document.getElementById('total_cost_display').value = '';
        }
    }

    function validateStockLevels(newStock) {
        const minStock = {{ $inventory->minimum_stock }};
        const maxStock = {{ $inventory->maximum_stock }};

        // Remove existing alerts
        const existingAlert = document.querySelector('.stock-level-alert');
        if (existingAlert) {
            existingAlert.remove();
        }

        let alertClass = '';
        let alertMessage = '';

        if (newStock < 0) {
            alertClass = 'alert-danger';
            alertMessage = 'تحذير: لا يمكن أن يكون المخزون أقل من صفر!';
        } else if (newStock === 0) {
            alertClass = 'alert-warning';
            alertMessage = 'تنبيه: سيصبح المخزون صفر (نفد المخزون).';
        } else if (newStock < minStock) {
            alertClass = 'alert-warning';
            alertMessage = `تنبيه: المخزون الجديد (${newStock}) أقل من الحد الأدنى (${minStock}).`;
        } else if (newStock > maxStock) {
            alertClass = 'alert-info';
            alertMessage = `ملاحظة: المخزون الجديد (${newStock}) أكبر من الحد الأقصى (${maxStock}).`;
        }

        if (alertMessage) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass} stock-level-alert mt-3`;
            alertDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${alertMessage}`;
            document.getElementById('previewSection').appendChild(alertDiv);
        }
    }

    // Form validation
    document.getElementById('adjustmentForm').addEventListener('submit', function(e) {
        if (!selectedAdjustmentType) {
            e.preventDefault();
            alert('يرجى اختيار نوع التعديل');
            return false;
        }

        const quantity = parseInt(document.getElementById('quantity').value) || 0;
        if (quantity <= 0) {
            e.preventDefault();
            alert('يرجى إدخال كمية صحيحة');
            return false;
        }

        const reason = document.getElementById('reason').value;
        if (!reason) {
            e.preventDefault();
            alert('يرجى اختيار سبب التعديل');
            return false;
        }

        // Calculate new stock for final validation
        let newStock = currentStock;
        switch (selectedAdjustmentType) {
            case 'increase':
                newStock = currentStock + quantity;
                break;
            case 'decrease':
                newStock = currentStock - quantity;
                if (newStock < 0) {
                    e.preventDefault();
                    alert('لا يمكن تقليل المخزون إلى أقل من صفر');
                    return false;
                }
                break;
            case 'set':
                newStock = quantity;
                break;
        }

        // Confirm the adjustment
        const confirmMessage = `هل أنت متأكد من تطبيق التعديل؟\n\n` +
                              `النوع: ${document.getElementById('adjustmentTypePreview').textContent}\n` +
                              `الكمية: ${document.getElementById('quantityPreview').textContent}\n` +
                              `المخزون الحالي: ${currentStock}\n` +
                              `المخزون الجديد: ${newStock}\n` +
                              `السبب: ${reason}`;

        if (!confirm(confirmMessage)) {
            e.preventDefault();
            return false;
        }

        return true;
    });

    // Make functions global
    window.selectAdjustmentType = selectAdjustmentType;
    window.setAdjustmentType = setAdjustmentType;
    window.setQuantity = setQuantity;

    // Initial calculation
    calculateTotalCost();
});
</script>
@endpush
@endsection
