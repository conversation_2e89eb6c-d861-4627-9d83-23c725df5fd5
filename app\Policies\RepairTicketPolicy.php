<?php

namespace App\Policies;

use App\Models\RepairTicket;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class RepairTicketPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->canManageTickets();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, RepairTicket $repairTicket): bool
    {
        // Admin and Manager can view all tickets
        if ($user->isManager()) {
            return true;
        }

        // Technicians can only view tickets assigned to them or created by them
        if ($user->isTechnician()) {
            return $repairTicket->assigned_technician_id === $user->id || 
                   $repairTicket->created_by === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->canManageTickets();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, RepairTicket $repairTicket): bool
    {
        // Admin and Manager can update all tickets
        if ($user->isManager()) {
            return true;
        }

        // Technicians can only update tickets assigned to them
        if ($user->isTechnician()) {
            return $repairTicket->assigned_technician_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, RepairTicket $repairTicket): bool
    {
        // Only Admin and Manager can delete tickets
        return $user->isManager();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, RepairTicket $repairTicket): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, RepairTicket $repairTicket): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can assign technicians.
     */
    public function assignTechnician(User $user): bool
    {
        return $user->isManager();
    }

    /**
     * Determine whether the user can perform bulk operations.
     */
    public function bulkAction(User $user): bool
    {
        return $user->isManager();
    }
}
