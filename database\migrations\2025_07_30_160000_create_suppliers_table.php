<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('suppliers', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Supplier name
            $table->string('company_name')->nullable(); // Company name if different
            $table->string('contact_person')->nullable(); // Contact person name
            $table->string('phone_number'); // Primary phone
            $table->string('phone_number_2')->nullable(); // Secondary phone
            $table->string('email')->nullable(); // Email address
            $table->text('address')->nullable(); // Full address
            $table->string('city')->nullable(); // City
            $table->string('country')->default('Saudi Arabia'); // Country
            $table->string('tax_number')->nullable(); // Tax registration number
            $table->string('commercial_register')->nullable(); // Commercial register number
            $table->enum('payment_terms', ['cash', 'credit_7', 'credit_15', 'credit_30', 'credit_60', 'credit_90'])->default('cash'); // Payment terms
            $table->decimal('credit_limit', 10, 2)->default(0); // Credit limit
            $table->decimal('current_balance', 10, 2)->default(0); // Current outstanding balance
            $table->text('notes')->nullable(); // Additional notes
            $table->boolean('is_active')->default(true); // Active status
            $table->json('contact_info')->nullable(); // Additional contact methods (WhatsApp, etc.)
            $table->timestamps();

            // Indexes for better performance
            $table->index('name');
            $table->index('phone_number');
            $table->index('email');
            $table->index('is_active');
            $table->index('city');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('suppliers');
    }
};
