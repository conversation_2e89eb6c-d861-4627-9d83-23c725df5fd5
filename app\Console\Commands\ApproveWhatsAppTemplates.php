<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\WhatsAppMessageTemplate;
use App\Models\User;

class ApproveWhatsAppTemplates extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'whatsapp:approve-templates 
                            {--all : Approve all pending templates}
                            {--template= : Approve specific template by ID}
                            {--user= : User ID to approve as (defaults to first admin)}';

    /**
     * The console command description.
     */
    protected $description = 'Approve WhatsApp message templates';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('📋 WhatsApp Template Approval Tool');

        try {
            // Get approver user
            $userId = $this->option('user');
            $user = $userId ? User::find($userId) : User::first();
            
            if (!$user) {
                $this->error('❌ No user found for approval');
                return Command::FAILURE;
            }

            if ($this->option('all')) {
                return $this->approveAllTemplates($user);
            }

            if ($this->option('template')) {
                return $this->approveSpecificTemplate($this->option('template'), $user);
            }

            // Interactive mode
            return $this->interactiveApproval($user);

        } catch (\Exception $e) {
            $this->error('❌ Failed to approve templates: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Approve all pending templates
     */
    protected function approveAllTemplates(User $user): int
    {
        $pendingTemplates = WhatsAppMessageTemplate::where('status', 'PENDING')->get();
        $draftTemplates = WhatsAppMessageTemplate::where('status', 'DRAFT')->get();
        
        $allTemplates = $pendingTemplates->merge($draftTemplates);

        if ($allTemplates->isEmpty()) {
            $this->info('✅ No templates need approval');
            return Command::SUCCESS;
        }

        $this->info("Found {$allTemplates->count()} templates to approve:");
        
        foreach ($allTemplates as $template) {
            $this->line("  • {$template->display_name_ar} ({$template->status})");
        }

        if (!$this->confirm('Approve all these templates?', true)) {
            $this->info('Operation cancelled');
            return Command::SUCCESS;
        }

        $approved = 0;
        foreach ($allTemplates as $template) {
            try {
                $template->approve($user, 'Auto-approved via command line');
                $approved++;
                $this->info("✅ Approved: {$template->display_name_ar}");
            } catch (\Exception $e) {
                $this->error("❌ Failed to approve {$template->display_name_ar}: {$e->getMessage()}");
            }
        }

        $this->info("🎉 Approved {$approved} out of {$allTemplates->count()} templates");
        return Command::SUCCESS;
    }

    /**
     * Approve specific template
     */
    protected function approveSpecificTemplate(int $templateId, User $user): int
    {
        $template = WhatsAppMessageTemplate::find($templateId);

        if (!$template) {
            $this->error("❌ Template with ID {$templateId} not found");
            return Command::FAILURE;
        }

        if ($template->status === 'APPROVED') {
            $this->info("✅ Template '{$template->display_name_ar}' is already approved");
            return Command::SUCCESS;
        }

        $this->info("Template: {$template->display_name_ar}");
        $this->info("Status: {$template->status}");
        $this->info("Category: {$template->category->name_ar}");
        
        if ($this->confirm('Approve this template?', true)) {
            $notes = $this->ask('Approval notes (optional)');
            
            try {
                $template->approve($user, $notes);
                $this->info("✅ Template '{$template->display_name_ar}' approved successfully");
                return Command::SUCCESS;
            } catch (\Exception $e) {
                $this->error("❌ Failed to approve template: {$e->getMessage()}");
                return Command::FAILURE;
            }
        }

        $this->info('Operation cancelled');
        return Command::SUCCESS;
    }

    /**
     * Interactive approval process
     */
    protected function interactiveApproval(User $user): int
    {
        $templates = WhatsAppMessageTemplate::whereIn('status', ['DRAFT', 'PENDING'])
            ->with('category')
            ->get();

        if ($templates->isEmpty()) {
            $this->info('✅ No templates need approval');
            return Command::SUCCESS;
        }

        $this->info("Found {$templates->count()} templates needing approval:");
        $this->newLine();

        foreach ($templates as $index => $template) {
            $this->info("Template #" . ($index + 1));
            $this->line("  Name: {$template->display_name_ar}");
            $this->line("  Category: {$template->category->name_ar}");
            $this->line("  Status: {$template->status}");
            $this->line("  Language: {$template->language}");
            
            if ($template->description_ar) {
                $this->line("  Description: {$template->description_ar}");
            }

            $this->newLine();
            $this->line("Body Text Preview:");
            $this->line("  " . substr($template->body_text, 0, 100) . "...");
            $this->newLine();

            $action = $this->choice(
                'What would you like to do with this template?',
                ['approve', 'reject', 'skip', 'quit'],
                'approve'
            );

            switch ($action) {
                case 'approve':
                    $notes = $this->ask('Approval notes (optional)');
                    try {
                        $template->approve($user, $notes);
                        $this->info("✅ Approved: {$template->display_name_ar}");
                    } catch (\Exception $e) {
                        $this->error("❌ Failed to approve: {$e->getMessage()}");
                    }
                    break;

                case 'reject':
                    $reason = $this->ask('Rejection reason (required)');
                    if ($reason) {
                        try {
                            $template->reject($reason, $user);
                            $this->warn("❌ Rejected: {$template->display_name_ar}");
                        } catch (\Exception $e) {
                            $this->error("❌ Failed to reject: {$e->getMessage()}");
                        }
                    } else {
                        $this->warn('Rejection reason is required. Skipping...');
                    }
                    break;

                case 'skip':
                    $this->info("⏭️  Skipped: {$template->display_name_ar}");
                    break;

                case 'quit':
                    $this->info('👋 Approval process ended');
                    return Command::SUCCESS;
            }

            $this->newLine();
        }

        $this->info('🎉 All templates processed!');
        return Command::SUCCESS;
    }
}
