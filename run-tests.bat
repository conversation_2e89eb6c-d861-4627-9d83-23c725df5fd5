@echo off
echo ========================================
echo NJ Repair Shop - Test Suite Runner
echo ========================================
echo.

REM Check if vendor directory exists
if not exist "vendor" (
    echo Error: Vendor directory not found. Please run 'composer install' first.
    pause
    exit /b 1
)

REM Check if .env.testing exists, create if not
if not exist ".env.testing" (
    echo Creating .env.testing file...
    copy .env .env.testing
    echo APP_ENV=testing >> .env.testing
    echo DB_CONNECTION=sqlite >> .env.testing
    echo DB_DATABASE=:memory: >> .env.testing
    echo.
)

echo Setting up test environment...
php artisan config:clear --env=testing
php artisan cache:clear --env=testing

echo.
echo ========================================
echo Running Unit Tests
echo ========================================
php artisan test --testsuite=Unit --stop-on-failure

if %ERRORLEVEL% neq 0 (
    echo.
    echo Unit tests failed! Stopping execution.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Running Feature Tests
echo ========================================
php artisan test --testsuite=Feature --stop-on-failure

if %ERRORLEVEL% neq 0 (
    echo.
    echo Feature tests failed! Stopping execution.
    pause
    exit /b 1
)

echo.
echo ========================================
echo All Tests Completed Successfully!
echo ========================================
echo.
echo Test Summary:
php artisan test --testsuite=All --coverage-text

echo.
echo Tests completed. Press any key to exit.
pause
