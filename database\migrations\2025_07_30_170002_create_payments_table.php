<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->string('payment_number')->unique(); // Payment reference number
            $table->foreignId('invoice_id')->constrained()->onDelete('restrict');
            $table->foreignId('customer_id')->constrained()->onDelete('restrict');
            
            // Payment information
            $table->decimal('amount', 10, 2); // Payment amount
            $table->date('payment_date'); // Date of payment
            $table->enum('payment_method', [
                'cash',           // نقداً
                'card',           // بطاقة ائتمان/خصم
                'bank_transfer',  // تحويل بنكي
                'check',          // شيك
                'mobile_payment', // دفع عبر الجوال (STC Pay, Apple Pay, etc.)
                'installment',    // قسط
                'other'           // أخرى
            ]);
            
            // Payment method details
            $table->string('payment_reference')->nullable(); // Reference number from payment provider
            $table->string('card_last_four')->nullable(); // Last 4 digits of card
            $table->string('bank_name')->nullable(); // Bank name for transfers/checks
            $table->string('check_number')->nullable(); // Check number
            $table->json('payment_details')->nullable(); // Additional payment details
            
            // Status and verification
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled', 'refunded'])->default('completed');
            $table->boolean('is_verified')->default(false); // Whether payment is verified
            $table->timestamp('verified_at')->nullable(); // When payment was verified
            $table->foreignId('verified_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Refund information
            $table->decimal('refunded_amount', 10, 2)->default(0); // Amount refunded
            $table->timestamp('refunded_at')->nullable(); // When refund was processed
            $table->text('refund_reason')->nullable(); // Reason for refund
            
            // Additional information
            $table->text('notes')->nullable(); // Payment notes
            $table->string('receipt_number')->nullable(); // Receipt number
            $table->json('attachments')->nullable(); // Array of attachment file paths
            
            // Tracking
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();

            // Indexes
            $table->index('payment_number');
            $table->index('invoice_id');
            $table->index('customer_id');
            $table->index('payment_date');
            $table->index('payment_method');
            $table->index('status');
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
