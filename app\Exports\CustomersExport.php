<?php

namespace App\Exports;

use App\Models\Customer;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class CustomersExport implements FromQuery, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $filters;

    public function __construct(array $filters = [])
    {
        $this->filters = $filters;
    }

    /**
     * Query for the export
     */
    public function query()
    {
        $query = Customer::withCount('repairTickets')
            ->with(['repairTickets' => function ($query) {
                $query->select('customer_id', 'final_cost')
                    ->whereNotNull('final_cost');
            }]);

        // Apply filters
        if (!empty($this->filters['search'])) {
            $query->where(function ($q) {
                $search = $this->filters['search'];
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if (!empty($this->filters['start_date']) && !empty($this->filters['end_date'])) {
            $query->whereBetween('created_at', [
                $this->filters['start_date'],
                $this->filters['end_date']
            ]);
        }

        return $query->orderBy('name');
    }

    /**
     * Define the headings for the Excel file
     */
    public function headings(): array
    {
        return [
            'Customer Name',
            'Phone Number',
            'Email',
            'Address',
            'Total Tickets',
            'Total Spent',
            'Average per Ticket',
            'Registration Date',
            'Last Updated',
        ];
    }

    /**
     * Map the data for each row
     */
    public function map($customer): array
    {
        $totalSpent = $customer->repairTickets->sum('final_cost');
        $avgPerTicket = $customer->repair_tickets_count > 0 ? $totalSpent / $customer->repair_tickets_count : 0;

        return [
            $customer->name,
            $customer->phone_number,
            $customer->email ?? 'N/A',
            $customer->address ?? 'N/A',
            $customer->repair_tickets_count,
            $totalSpent > 0 ? '$' . number_format($totalSpent, 2) : '$0.00',
            $avgPerTicket > 0 ? '$' . number_format($avgPerTicket, 2) : '$0.00',
            $customer->created_at->format('Y-m-d H:i'),
            $customer->updated_at->format('Y-m-d H:i'),
        ];
    }

    /**
     * Apply styles to the worksheet
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '28A745'],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            // Style all cells
            'A:I' => [
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
        ];
    }
}
