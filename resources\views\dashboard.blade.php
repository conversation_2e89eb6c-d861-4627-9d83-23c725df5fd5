@extends('layouts.app')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="bi bi-house"></i> {{ __('app.dashboard.title') }}
            </h1>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card animate-on-scroll" data-bs-toggle="tooltip" data-bs-placement="top" title="Total number of registered customers">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="card-title display-6 mb-1">@arabicNumber($stats['total_customers'])</h4>
                            <p class="card-text mb-0 opacity-75">{{ __('app.dashboard.total_customers') }}</p>
                            <small class="text-white-50">
                                <i class="bi bi-arrow-up"></i> +@arabicNumber($stats['new_customers_this_month'] ?? 0) {{ __('app.dashboard.new_customers_this_month') }}
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people display-4 opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">@arabicNumber($stats['total_tickets'])</h4>
                            <p class="card-text">{{ __('app.dashboard.total_tickets') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-ticket display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">@arabicNumber($stats['pending_tickets'])</h4>
                            <p class="card-text">{{ __('app.dashboard.pending_tickets') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-clock display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">@arabicNumber($stats['completed_today'])</h4>
                            <p class="card-text">{{ __('app.dashboard.completed_today') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Tickets -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history"></i> {{ __('app.dashboard.recent_tickets') }}
                        </h5>
                        <a href="{{ route('repair-tickets.index') }}" class="btn btn-outline-primary btn-sm">
                            {{ __('app.dashboard.view_all') }}
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if($recentTickets->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>{{ __('app.dashboard.ticket_number') }}</th>
                                        <th>{{ __('app.dashboard.customer') }}</th>
                                        <th>{{ __('app.dashboard.device') }}</th>
                                        <th>{{ __('app.dashboard.status') }}</th>
                                        <th>{{ __('app.dashboard.created') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentTickets as $ticket)
                                        <tr>
                                            <td>
                                                <a href="{{ route('repair-tickets.show', $ticket) }}" class="text-decoration-none">
                                                    <strong>{{ $ticket->ticket_number }}</strong>
                                                </a>
                                            </td>
                                            <td>{{ $ticket->customer->name }}</td>
                                            <td>{{ $ticket->brand->name }} {{ $ticket->device_model }}</td>
                                            <td>
                                                <span class="badge" style="background-color: {{ $ticket->repairStatus->color }}">
                                                    {{ $ticket->repairStatus->name }}
                                                </span>
                                            </td>
                                            <td>
                                                <small>{{ $ticket->created_at->diffForHumans() }}</small>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="bi bi-ticket display-1 text-muted"></i>
                            <h4 class="mt-3">{{ __('app.dashboard.no_tickets_yet') }}</h4>
                            <p class="text-muted">{{ __('app.dashboard.create_first_ticket') }}</p>
                            <a href="{{ route('repair-tickets.create') }}" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> {{ __('app.dashboard.create_ticket') }}
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Tickets by Status -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pie-chart"></i> {{ __('app.dashboard.tickets_by_status') }}
                    </h5>
                </div>
                <div class="card-body">
                    @if($ticketsByStatus->count() > 0)
                        @foreach($ticketsByStatus as $status)
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="badge me-2" style="background-color: {{ $status->color }}">
                                        &nbsp;
                                    </span>
                                    <span>{{ $status->name }}</span>
                                </div>
                                <span class="badge bg-secondary">{{ $status->repair_tickets_count }}</span>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-3">
                            <i class="bi bi-pie-chart display-4 text-muted"></i>
                            <p class="text-muted mt-2">{{ __('app.dashboard.no_status_data') }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightning"></i> {{ __('app.dashboard.quick_actions') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ route('repair-tickets.create') }}" class="btn btn-success w-100 mb-2">
                                <i class="bi bi-plus-circle"></i> {{ __('app.dashboard.new_repair_ticket') }}
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('customers.create') }}" class="btn btn-primary w-100 mb-2">
                                <i class="bi bi-person-plus"></i> {{ __('app.dashboard.add_customer') }}
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('customers.index') }}" class="btn btn-outline-primary w-100 mb-2">
                                <i class="bi bi-people"></i> {{ __('app.dashboard.view_customers') }}
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('repair-tickets.index') }}" class="btn btn-outline-secondary w-100 mb-2">
                                <i class="bi bi-ticket"></i> {{ __('app.dashboard.all_tickets') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
