<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_webhooks', function (Blueprint $table) {
            $table->id();
            
            // Webhook identification
            $table->string('webhook_id')->unique()->nullable(); // WhatsApp webhook ID
            $table->string('event_type')->index(); // message, status, etc.
            $table->string('object_type')->index(); // whatsapp_business_account
            
            // Request information
            $table->string('method', 10)->default('POST'); // HTTP method
            $table->text('url'); // Webhook URL
            $table->json('headers')->nullable(); // Request headers
            $table->longText('payload'); // Raw webhook payload
            $table->string('signature')->nullable(); // Webhook signature for verification
            
            // Processing status
            $table->enum('status', ['pending', 'processing', 'processed', 'failed', 'ignored'])->default('pending')->index();
            $table->text('error_message')->nullable();
            $table->integer('retry_count')->default(0);
            $table->timestamp('processed_at')->nullable();
            
            // Related entities
            $table->string('phone_number', 20)->nullable()->index(); // Phone number involved
            $table->string('whatsapp_message_id')->nullable()->index(); // Related message ID
            $table->foreignId('message_id')->nullable()->constrained('whatsapp_messages')->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            
            // Webhook content analysis
            $table->json('parsed_data')->nullable(); // Parsed webhook data
            $table->string('message_type')->nullable(); // text, template, media, etc.
            $table->string('message_status')->nullable(); // sent, delivered, read, failed
            $table->boolean('is_duplicate')->default(false); // Duplicate webhook detection
            
            // Performance metrics
            $table->integer('processing_time_ms')->nullable(); // Processing time in milliseconds
            $table->timestamp('received_at')->nullable(); // When webhook was received
            $table->string('user_agent')->nullable(); // WhatsApp user agent
            $table->ipAddress('ip_address')->nullable(); // Source IP address
            
            // Business context
            $table->string('business_context')->nullable(); // repair_update, payment_reminder, etc.
            $table->json('business_data')->nullable(); // Related business entity data
            $table->boolean('requires_action')->default(false); // Needs manual intervention
            $table->text('action_notes')->nullable(); // Notes for required actions
            
            // Webhook validation
            $table->boolean('signature_valid')->nullable(); // Signature validation result
            $table->boolean('timestamp_valid')->nullable(); // Timestamp validation result
            $table->string('validation_errors')->nullable(); // Validation error details
            
            // Debugging and monitoring
            $table->json('debug_info')->nullable(); // Debug information
            $table->string('trace_id')->nullable(); // Request tracing ID
            $table->boolean('is_test')->default(false); // Test webhook
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['event_type', 'status', 'created_at']);
            $table->index(['phone_number', 'created_at']);
            $table->index(['status', 'retry_count', 'created_at']);
            $table->index(['message_type', 'message_status', 'created_at']);
            $table->index(['business_context', 'created_at']);
            $table->index(['requires_action', 'created_at']);
            $table->index(['is_duplicate', 'created_at']);
            $table->index(['signature_valid', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_webhooks');
    }
};
