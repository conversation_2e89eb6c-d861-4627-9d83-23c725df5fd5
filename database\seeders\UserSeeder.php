<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin',
            'phone_number' => '+966501234567',
            'hire_date' => now()->subMonths(12),
            'hourly_rate' => 50.00,
            'is_active' => true,
            'preferred_language' => 'ar',
            'email_verified_at' => now(),
        ]);

        // Create manager user
        User::create([
            'name' => 'أحمد المدير',
            'email' => '<EMAIL>',
            'password' => Hash::make('manager123'),
            'role' => 'manager',
            'phone_number' => '+966501234568',
            'hire_date' => now()->subMonths(6),
            'hourly_rate' => 35.00,
            'is_active' => true,
            'preferred_language' => 'ar',
            'email_verified_at' => now(),
        ]);

        // Create technician user
        User::create([
            'name' => 'محمد الفني',
            'email' => '<EMAIL>',
            'password' => Hash::make('tech123'),
            'role' => 'technician',
            'phone_number' => '+966501234569',
            'hire_date' => now()->subMonths(3),
            'hourly_rate' => 25.00,
            'is_active' => true,
            'preferred_language' => 'ar',
            'email_verified_at' => now(),
        ]);

        // Create receptionist user
        User::create([
            'name' => 'فاطمة الاستقبال',
            'email' => '<EMAIL>',
            'password' => Hash::make('reception123'),
            'role' => 'receptionist',
            'phone_number' => '+966501234570',
            'hire_date' => now()->subMonths(1),
            'hourly_rate' => 20.00,
            'is_active' => true,
            'preferred_language' => 'ar',
            'email_verified_at' => now(),
        ]);
    }
}
