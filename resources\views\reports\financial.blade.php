@extends('layouts.app')

@section('title', 'التقارير المالية')

@push('styles')
<style>
.financial-header {
    background: linear-gradient(45deg, #6f42c1, #5a32a3);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(45deg, #6f42c1, #5a32a3);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.875rem;
    text-transform: uppercase;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 600;
}

.stat-change.positive {
    color: #28a745;
}

.stat-change.negative {
    color: #dc3545;
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.3;
    float: left;
    margin-top: -0.5rem;
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.report-card {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    overflow: hidden;
    transition: all 0.2s ease;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.report-card-header {
    background: linear-gradient(45deg, #6f42c1, #5a32a3);
    color: white;
    padding: 1rem 1.5rem;
}

.report-card-body {
    padding: 1.5rem;
}

.report-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.report-title {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.report-description {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.btn-report {
    background: linear-gradient(45deg, #6f42c1, #5a32a3);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.35rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    width: 100%;
    justify-content: center;
}

.btn-report:hover {
    background: linear-gradient(45deg, #5a32a3, #4c2a85);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    color: white;
    text-decoration: none;
}

.quick-stats {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.quick-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.quick-stat-item {
    text-align: center;
    padding: 1rem;
    background: white;
    border-radius: 0.35rem;
    border: 1px solid #e3e6f0;
}

.quick-stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.quick-stat-label {
    font-size: 0.875rem;
    color: #6c757d;
}

.chart-container {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.chart-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e3e6f0;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: bold;
    color: #5a5c69;
}

@media (max-width: 768px) {
    .financial-header {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .reports-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="financial-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">التقارير المالية</h1>
                <p class="mb-0 opacity-75">تحليل شامل للأداء المالي للورشة</p>
            </div>
            <div>
                <button class="btn btn-light" onclick="refreshStats()">
                    <i class="fas fa-sync-alt me-2"></i>تحديث البيانات
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid" id="statsGrid">
        <div class="stat-card">
            <div class="stat-icon text-success">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-value text-success" id="todayRevenue">0.00 ريال</div>
            <div class="stat-label">إيرادات اليوم</div>
            <div class="stat-change positive" id="todayRevenueChange">+0%</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-primary">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-value text-primary" id="monthRevenue">0.00 ريال</div>
            <div class="stat-label">إيرادات الشهر</div>
            <div class="stat-change positive" id="monthRevenueChange">+0%</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-value text-warning" id="outstandingAmount">0.00 ريال</div>
            <div class="stat-label">مبالغ معلقة</div>
            <div class="stat-change" id="outstandingCount">0 فاتورة</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-danger">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-value text-danger" id="overdueAmount">0.00 ريال</div>
            <div class="stat-label">مبالغ متأخرة</div>
            <div class="stat-change" id="overdueCount">0 فاتورة</div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="quick-stats">
        <h5 class="mb-3">إحصائيات سريعة</h5>
        <div class="quick-stats-grid" id="quickStatsGrid">
            <div class="quick-stat-item">
                <div class="quick-stat-value text-info" id="todayInvoices">0</div>
                <div class="quick-stat-label">فواتير اليوم</div>
            </div>
            <div class="quick-stat-item">
                <div class="quick-stat-value text-success" id="todayPayments">0</div>
                <div class="quick-stat-label">مدفوعات اليوم</div>
            </div>
            <div class="quick-stat-item">
                <div class="quick-stat-value text-primary" id="monthInvoices">0</div>
                <div class="quick-stat-label">فواتير الشهر</div>
            </div>
            <div class="quick-stat-item">
                <div class="quick-stat-value text-warning" id="yearRevenue">0.00 ريال</div>
                <div class="quick-stat-label">إيرادات السنة</div>
            </div>
        </div>
    </div>

    <!-- Reports Grid -->
    <div class="reports-grid">
        <div class="report-card">
            <div class="report-card-header">
                <div class="report-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
            </div>
            <div class="report-card-body">
                <div class="report-title">تقرير الإيرادات</div>
                <div class="report-description">
                    تحليل مفصل للإيرادات اليومية والشهرية والسنوية مع مقارنات زمنية وتحليل طرق الدفع
                </div>
                <a href="{{ route('reports.revenue') }}" class="btn-report">
                    <i class="fas fa-chart-bar"></i>عرض التقرير
                </a>
            </div>
        </div>

        <div class="report-card">
            <div class="report-card-header">
                <div class="report-icon">
                    <i class="fas fa-file-invoice-dollar"></i>
                </div>
            </div>
            <div class="report-card-body">
                <div class="report-title">الفواتير المعلقة</div>
                <div class="report-description">
                    قائمة شاملة بالفواتير غير المدفوعة والمتأخرة مع تفاصيل العملاء ومواعيد الاستحقاق
                </div>
                <a href="{{ route('reports.outstanding') }}" class="btn-report">
                    <i class="fas fa-file-invoice-dollar"></i>عرض التقرير
                </a>
            </div>
        </div>

        <div class="report-card">
            <div class="report-card-header">
                <div class="report-icon">
                    <i class="fas fa-calculator"></i>
                </div>
            </div>
            <div class="report-card-body">
                <div class="report-title">الأرباح والخسائر</div>
                <div class="report-description">
                    تحليل مالي شامل للأرباح والخسائر مع حساب هوامش الربح وتكلفة البضاعة المباعة
                </div>
                <a href="{{ route('reports.profit-loss') }}" class="btn-report">
                    <i class="fas fa-calculator"></i>عرض التقرير
                </a>
            </div>
        </div>

        <div class="report-card">
            <div class="report-card-header">
                <div class="report-icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>
            <div class="report-card-body">
                <div class="report-title">تحليل العملاء</div>
                <div class="report-description">
                    تقرير مفصل عن أداء العملاء وقيمة المبيعات لكل عميل والمبالغ المستحقة
                </div>
                <a href="{{ route('reports.customers') }}" class="btn-report">
                    <i class="fas fa-users"></i>عرض التقرير
                </a>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadFinancialStats();
});

function loadFinancialStats() {
    fetch('{{ route("reports.financial-stats") }}')
        .then(response => response.json())
        .then(data => {
            updateStats(data);
        })
        .catch(error => {
            console.error('Error loading financial stats:', error);
        });
}

function updateStats(data) {
    // Today stats
    document.getElementById('todayRevenue').textContent = formatCurrency(data.today.revenue);
    document.getElementById('todayInvoices').textContent = data.today.invoices;
    document.getElementById('todayPayments').textContent = data.today.payments;
    
    // Month stats
    document.getElementById('monthRevenue').textContent = formatCurrency(data.this_month.revenue);
    document.getElementById('monthInvoices').textContent = data.this_month.invoices;
    
    // Year stats
    document.getElementById('yearRevenue').textContent = formatCurrency(data.this_year.revenue);
    
    // Outstanding and overdue
    document.getElementById('outstandingAmount').textContent = formatCurrency(data.outstanding.amount);
    document.getElementById('outstandingCount').textContent = data.outstanding.count + ' فاتورة';
    document.getElementById('overdueAmount').textContent = formatCurrency(data.overdue.amount);
    document.getElementById('overdueCount').textContent = data.overdue.count + ' فاتورة';
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount || 0);
}

function refreshStats() {
    const button = event.target.closest('button');
    const icon = button.querySelector('i');
    
    icon.classList.add('fa-spin');
    button.disabled = true;
    
    loadFinancialStats();
    
    setTimeout(() => {
        icon.classList.remove('fa-spin');
        button.disabled = false;
    }, 1000);
}
</script>
@endpush
@endsection
