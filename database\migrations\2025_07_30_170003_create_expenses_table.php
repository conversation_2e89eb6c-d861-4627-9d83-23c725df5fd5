<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expenses', function (Blueprint $table) {
            $table->id();
            $table->string('expense_number')->unique(); // Expense reference number
            
            // Expense information
            $table->string('title'); // Expense title
            $table->text('description')->nullable(); // Expense description
            $table->decimal('amount', 10, 2); // Expense amount
            $table->date('expense_date'); // Date of expense
            
            // Category and classification
            $table->enum('category', [
                'rent',              // إيجار
                'utilities',         // مرافق (كهرباء، ماء، إنترنت)
                'supplies',          // مستلزمات
                'equipment',         // معدات
                'maintenance',       // صيانة
                'marketing',         // تسويق
                'transportation',    // مواصلات
                'professional_fees', // رسوم مهنية
                'insurance',         // تأمين
                'taxes',             // ضرائب ورسوم
                'salaries',          // رواتب
                'inventory',         // مخزون
                'other'              // أخرى
            ]);
            
            // Payment information
            $table->enum('payment_method', [
                'cash',
                'card',
                'bank_transfer',
                'check',
                'mobile_payment',
                'other'
            ]);
            $table->string('payment_reference')->nullable(); // Payment reference
            
            // Supplier/Vendor information
            $table->foreignId('supplier_id')->nullable()->constrained()->onDelete('set null');
            $table->string('vendor_name')->nullable(); // Vendor name if not in suppliers
            $table->string('vendor_phone')->nullable(); // Vendor contact
            $table->string('vendor_tax_number')->nullable(); // Vendor tax number
            
            // Tax information
            $table->boolean('is_taxable')->default(true); // Whether expense is taxable
            $table->decimal('tax_rate', 5, 2)->default(15); // Tax rate
            $table->decimal('tax_amount', 10, 2)->default(0); // Tax amount
            $table->decimal('net_amount', 10, 2); // Net amount after tax
            
            // Reference information
            $table->string('reference_type')->nullable(); // Type of reference (purchase_order, repair_ticket, etc.)
            $table->unsignedBigInteger('reference_id')->nullable(); // ID of the reference
            $table->string('reference_number')->nullable(); // Human-readable reference
            
            // Status and approval
            $table->enum('status', ['draft', 'pending_approval', 'approved', 'paid', 'cancelled'])->default('draft');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            
            // Receipt and documentation
            $table->string('receipt_number')->nullable(); // Receipt/invoice number
            $table->json('attachments')->nullable(); // Array of attachment file paths
            $table->boolean('has_receipt')->default(false); // Whether receipt is available
            
            // Recurring expense information
            $table->boolean('is_recurring')->default(false); // Whether this is a recurring expense
            $table->enum('recurring_frequency', ['monthly', 'quarterly', 'yearly'])->nullable();
            $table->date('next_due_date')->nullable(); // Next due date for recurring expenses
            
            // Additional information
            $table->text('notes')->nullable(); // Additional notes
            $table->boolean('is_reimbursable')->default(false); // Whether expense is reimbursable
            $table->decimal('reimbursed_amount', 10, 2)->default(0); // Amount reimbursed
            
            // Tracking
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();

            // Indexes
            $table->index('expense_number');
            $table->index('expense_date');
            $table->index('category');
            $table->index('payment_method');
            $table->index('supplier_id');
            $table->index('status');
            $table->index(['reference_type', 'reference_id']);
            $table->index('created_by');
            $table->index('is_recurring');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expenses');
    }
};
