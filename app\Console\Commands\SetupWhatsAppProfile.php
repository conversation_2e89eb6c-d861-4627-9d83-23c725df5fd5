<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\WhatsAppService;

class SetupWhatsAppProfile extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'whatsapp:setup-profile 
                            {--interactive : Interactive setup mode}
                            {--optimize : Optimize profile automatically}';

    /**
     * The console command description.
     */
    protected $description = 'Setup and optimize WhatsApp Business profile';

    protected WhatsAppService $whatsappService;

    /**
     * Create a new command instance.
     */
    public function __construct(WhatsAppService $whatsappService)
    {
        parent::__construct();
        $this->whatsappService = $whatsappService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🏢 Setting up WhatsApp Business Profile...');

        try {
            if ($this->option('interactive')) {
                return $this->interactiveSetup();
            }

            if ($this->option('optimize')) {
                return $this->optimizeProfile();
            }

            // Default: Show current profile and options
            return $this->showProfileStatus();

        } catch (\Exception $e) {
            $this->error('❌ Failed to setup profile: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Interactive profile setup
     */
    protected function interactiveSetup(): int
    {
        $this->info('📝 Interactive WhatsApp Business Profile Setup');
        $this->newLine();

        // Get current profile
        $currentProfile = $this->whatsappService->getBusinessProfile();
        
        if ($currentProfile) {
            $this->info('Current Profile:');
            $this->displayProfile($currentProfile);
            $this->newLine();
        }

        // Collect profile information
        $profileData = [];

        $profileData['about'] = $this->ask(
            'Business description (about)', 
            $currentProfile['about'] ?? config('whatsapp.business_name') . ' - خدمة عملاء 24/7'
        );

        $profileData['address'] = $this->ask(
            'Business address', 
            $currentProfile['address'] ?? config('whatsapp.business_address')
        );

        $profileData['description'] = $this->ask(
            'Detailed description', 
            $currentProfile['description'] ?? 'ورشة متخصصة في إصلاح جميع أنواع الأجهزة الإلكترونية'
        );

        $profileData['email'] = $this->ask(
            'Business email', 
            $currentProfile['email'] ?? config('mail.from.address')
        );

        $website = $this->ask(
            'Business website', 
            $currentProfile['websites'][0] ?? config('whatsapp.business_website')
        );
        
        if ($website) {
            $profileData['websites'] = [$website];
        }

        // Confirm and update
        $this->newLine();
        $this->info('Profile to be updated:');
        foreach ($profileData as $key => $value) {
            $displayValue = is_array($value) ? implode(', ', $value) : $value;
            $this->line("  {$key}: {$displayValue}");
        }

        if ($this->confirm('Update WhatsApp Business Profile?', true)) {
            $success = $this->whatsappService->updateBusinessProfile($profileData);
            
            if ($success) {
                $this->info('✅ Profile updated successfully!');
                
                // Setup business hours
                if ($this->confirm('Would you like to setup business hours?', true)) {
                    $this->setupBusinessHours();
                }
                
                return Command::SUCCESS;
            } else {
                $this->error('❌ Failed to update profile');
                return Command::FAILURE;
            }
        }

        return Command::SUCCESS;
    }

    /**
     * Optimize profile automatically
     */
    protected function optimizeProfile(): int
    {
        $this->info('🚀 Optimizing WhatsApp Business Profile...');

        $result = $this->whatsappService->optimizeBusinessProfile();

        if ($result['success']) {
            $this->info('✅ ' . $result['message']);
            
            $this->newLine();
            $this->info('Optimized Profile Data:');
            foreach ($result['profile_data'] as $key => $value) {
                $displayValue = is_array($value) ? implode(', ', $value) : $value;
                $this->line("  {$key}: {$displayValue}");
            }

            // Setup business hours
            $this->info('⏰ Setting up business hours...');
            $this->setupDefaultBusinessHours();

            return Command::SUCCESS;
        } else {
            $this->error('❌ ' . $result['message']);
            return Command::FAILURE;
        }
    }

    /**
     * Show current profile status
     */
    protected function showProfileStatus(): int
    {
        $this->info('📊 Current WhatsApp Business Profile Status');
        $this->newLine();

        $profile = $this->whatsappService->getBusinessProfile();

        if ($profile) {
            $this->displayProfile($profile);
        } else {
            $this->warn('⚠️  No profile found or unable to retrieve profile');
        }

        $this->newLine();
        $this->info('Available Commands:');
        $this->line('  php artisan whatsapp:setup-profile --interactive  # Interactive setup');
        $this->line('  php artisan whatsapp:setup-profile --optimize     # Auto-optimize');

        return Command::SUCCESS;
    }

    /**
     * Display profile information
     */
    protected function displayProfile(array $profile): void
    {
        $fields = [
            'about' => 'About',
            'address' => 'Address', 
            'description' => 'Description',
            'email' => 'Email',
            'websites' => 'Websites',
            'vertical' => 'Business Category',
            'profile_picture_url' => 'Profile Picture'
        ];

        foreach ($fields as $key => $label) {
            if (isset($profile[$key])) {
                $value = $profile[$key];
                if (is_array($value)) {
                    $value = implode(', ', $value);
                }
                $this->line("  {$label}: {$value}");
            }
        }
    }

    /**
     * Setup business hours interactively
     */
    protected function setupBusinessHours(): void
    {
        $this->info('⏰ Setting up business hours...');
        
        $days = [
            'sunday' => 'الأحد',
            'monday' => 'الاثنين', 
            'tuesday' => 'الثلاثاء',
            'wednesday' => 'الأربعاء',
            'thursday' => 'الخميس',
            'friday' => 'الجمعة',
            'saturday' => 'السبت'
        ];

        $hours = [];

        foreach ($days as $day => $arabicDay) {
            $isOpen = $this->confirm("Open on {$arabicDay} ({$day})?", true);
            
            if ($isOpen) {
                $openTime = $this->ask("Opening time for {$arabicDay} (HH:MM)", '09:00');
                $closeTime = $this->ask("Closing time for {$arabicDay} (HH:MM)", '22:00');
                
                $hours[$day] = [
                    'is_open' => true,
                    'open_time' => $openTime,
                    'close_time' => $closeTime
                ];
            } else {
                $hours[$day] = ['is_open' => false];
            }
        }

        $success = $this->whatsappService->setBusinessHours($hours);
        
        if ($success) {
            $this->info('✅ Business hours updated successfully!');
        } else {
            $this->error('❌ Failed to update business hours');
        }
    }

    /**
     * Setup default business hours
     */
    protected function setupDefaultBusinessHours(): void
    {
        $defaultHours = [
            'sunday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '22:00'],
            'monday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '22:00'],
            'tuesday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '22:00'],
            'wednesday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '22:00'],
            'thursday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '22:00'],
            'friday' => ['is_open' => true, 'open_time' => '14:00', 'close_time' => '22:00'],
            'saturday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '22:00'],
        ];

        $success = $this->whatsappService->setBusinessHours($defaultHours);
        
        if ($success) {
            $this->info('✅ Default business hours set successfully!');
        } else {
            $this->warn('⚠️  Could not set business hours automatically');
        }
    }
}
