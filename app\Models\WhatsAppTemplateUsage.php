<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WhatsAppTemplateUsage extends Model
{
    use HasFactory;

    protected $table = 'whatsapp_template_usage';

    protected $fillable = [
        'template_id',
        'message_id',
        'customer_phone',
        'customer_id',
        'trigger_type',
        'trigger_source',
        'triggered_by',
        'variables_used',
        'final_message',
        'status',
        'whatsapp_message_id',
        'sent_at',
        'delivered_at',
        'read_at',
        'error_message',
        'delivery_time_seconds',
        'customer_responded',
        'customer_response_at',
        'response_time_seconds',
        'repair_ticket_id',
        'business_event',
        'cost',
        'currency',
    ];

    protected $casts = [
        'variables_used' => 'array',
        'customer_responded' => 'boolean',
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'read_at' => 'datetime',
        'customer_response_at' => 'datetime',
        'cost' => 'decimal:4',
    ];

    /**
     * Get the template that was used.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(WhatsAppMessageTemplate::class, 'template_id');
    }

    /**
     * Get the WhatsApp message.
     */
    public function message(): BelongsTo
    {
        return $this->belongsTo(WhatsAppMessage::class, 'message_id');
    }

    /**
     * Get the customer.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * Get the user who triggered the message.
     */
    public function triggeredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'triggered_by');
    }

    /**
     * Get the repair ticket.
     */
    public function repairTicket(): BelongsTo
    {
        return $this->belongsTo(RepairTicket::class, 'repair_ticket_id');
    }

    /**
     * Scope for successful deliveries.
     */
    public function scopeDelivered($query)
    {
        return $query->whereIn('status', ['delivered', 'read']);
    }

    /**
     * Scope for failed deliveries.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for messages with customer responses.
     */
    public function scopeWithResponse($query)
    {
        return $query->where('customer_responded', true);
    }

    /**
     * Scope by trigger type.
     */
    public function scopeByTrigger($query, $triggerType)
    {
        return $query->where('trigger_type', $triggerType);
    }

    /**
     * Scope by business event.
     */
    public function scopeByEvent($query, $event)
    {
        return $query->where('business_event', $event);
    }

    /**
     * Check if message was delivered.
     */
    public function isDelivered(): bool
    {
        return in_array($this->status, ['delivered', 'read']);
    }

    /**
     * Check if message was read.
     */
    public function isRead(): bool
    {
        return $this->status === 'read';
    }

    /**
     * Check if message failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Mark as delivered.
     */
    public function markAsDelivered(): void
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
            'delivery_time_seconds' => $this->sent_at ? now()->diffInSeconds($this->sent_at) : null,
        ]);
    }

    /**
     * Mark as read.
     */
    public function markAsRead(): void
    {
        $this->update([
            'status' => 'read',
            'read_at' => now(),
        ]);
    }

    /**
     * Mark as failed.
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Record customer response.
     */
    public function recordCustomerResponse(): void
    {
        $responseTime = $this->sent_at ? now()->diffInSeconds($this->sent_at) : null;

        $this->update([
            'customer_responded' => true,
            'customer_response_at' => now(),
            'response_time_seconds' => $responseTime,
        ]);
    }

    /**
     * Get delivery time in human readable format.
     */
    public function getDeliveryTimeHumanAttribute(): ?string
    {
        if (!$this->delivery_time_seconds) {
            return null;
        }

        $seconds = $this->delivery_time_seconds;

        if ($seconds < 60) {
            return "{$seconds} ثانية";
        } elseif ($seconds < 3600) {
            $minutes = round($seconds / 60);
            return "{$minutes} دقيقة";
        } else {
            $hours = round($seconds / 3600, 1);
            return "{$hours} ساعة";
        }
    }

    /**
     * Get response time in human readable format.
     */
    public function getResponseTimeHumanAttribute(): ?string
    {
        if (!$this->response_time_seconds) {
            return null;
        }

        $seconds = $this->response_time_seconds;

        if ($seconds < 60) {
            return "{$seconds} ثانية";
        } elseif ($seconds < 3600) {
            $minutes = round($seconds / 60);
            return "{$minutes} دقيقة";
        } else {
            $hours = round($seconds / 3600, 1);
            return "{$hours} ساعة";
        }
    }

    /**
     * Get status display name.
     */
    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            'sent' => 'مُرسل',
            'delivered' => 'مُسلم',
            'read' => 'مقروء',
            'failed' => 'فشل',
            default => $this->status,
        };
    }

    /**
     * Get trigger type display name.
     */
    public function getTriggerDisplayAttribute(): string
    {
        return match($this->trigger_type) {
            'manual' => 'يدوي',
            'auto' => 'تلقائي',
            'scheduled' => 'مجدول',
            'webhook' => 'ويب هوك',
            default => $this->trigger_type ?? 'غير محدد',
        };
    }

    /**
     * Get business event display name.
     */
    public function getEventDisplayAttribute(): string
    {
        return match($this->business_event) {
            'status_update' => 'تحديث الحالة',
            'appointment' => 'موعد',
            'payment_reminder' => 'تذكير دفع',
            'pickup_ready' => 'جاهز للاستلام',
            'welcome' => 'ترحيب',
            'marketing' => 'تسويق',
            default => $this->business_event ?? 'عام',
        };
    }

    /**
     * Calculate analytics for a template.
     */
    public static function getTemplateAnalytics(int $templateId, array $dateRange = null): array
    {
        $query = static::where('template_id', $templateId);

        if ($dateRange) {
            $query->whereBetween('created_at', $dateRange);
        }

        $total = $query->count();
        $delivered = $query->clone()->delivered()->count();
        $failed = $query->clone()->failed()->count();
        $withResponse = $query->clone()->withResponse()->count();

        $avgDeliveryTime = $query->clone()
            ->whereNotNull('delivery_time_seconds')
            ->avg('delivery_time_seconds');

        $avgResponseTime = $query->clone()
            ->whereNotNull('response_time_seconds')
            ->avg('response_time_seconds');

        return [
            'total_sent' => $total,
            'delivered' => $delivered,
            'failed' => $failed,
            'delivery_rate' => $total > 0 ? round(($delivered / $total) * 100, 2) : 0,
            'response_rate' => $delivered > 0 ? round(($withResponse / $delivered) * 100, 2) : 0,
            'avg_delivery_time' => $avgDeliveryTime ? round($avgDeliveryTime) : null,
            'avg_response_time' => $avgResponseTime ? round($avgResponseTime) : null,
        ];
    }
}
