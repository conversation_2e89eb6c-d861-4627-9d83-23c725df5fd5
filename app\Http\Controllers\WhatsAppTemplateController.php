<?php

namespace App\Http\Controllers;

use App\Models\WhatsAppMessageTemplate;
use App\Models\WhatsAppTemplateCategory;
use App\Services\WhatsAppTemplateService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class WhatsAppTemplateController extends Controller
{
    protected WhatsAppTemplateService $templateService;

    public function __construct(WhatsAppTemplateService $templateService)
    {
        $this->templateService = $templateService;
    }

    /**
     * Display template management dashboard
     */
    public function index(Request $request): View
    {
        $query = WhatsAppMessageTemplate::with(['category', 'creator', 'approver']);

        // Apply filters
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('language')) {
            $query->where('language', $request->language);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('display_name', 'like', "%{$search}%")
                  ->orWhere('display_name_ar', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('description_ar', 'like', "%{$search}%");
            });
        }

        $templates = $query->orderBy('created_at', 'desc')->paginate(15);
        $categories = WhatsAppTemplateCategory::active()->ordered()->get();

        // Get statistics
        $stats = [
            'total' => WhatsAppMessageTemplate::count(),
            'approved' => WhatsAppMessageTemplate::where('status', 'APPROVED')->count(),
            'pending' => WhatsAppMessageTemplate::where('status', 'PENDING')->count(),
            'rejected' => WhatsAppMessageTemplate::where('status', 'REJECTED')->count(),
            'active' => WhatsAppMessageTemplate::where('is_active', true)->count(),
        ];

        return view('whatsapp.templates.index', compact('templates', 'categories', 'stats'));
    }

    /**
     * Show template creation form
     */
    public function create(): View
    {
        $categories = WhatsAppTemplateCategory::active()->ordered()->get();
        return view('whatsapp.templates.create', compact('categories'));
    }

    /**
     * Store new template
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:whatsapp_message_templates',
            'display_name' => 'required|string|max:255',
            'display_name_ar' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'category_id' => 'required|exists:whatsapp_template_categories,id',
            'language' => 'required|in:ar,en',
            'header_text' => 'nullable|string|max:60',
            'body_text' => 'required|string|max:1024',
            'footer_text' => 'nullable|string|max:60',
            'buttons' => 'nullable|array|max:3',
            'buttons.*.type' => 'required_with:buttons|in:QUICK_REPLY,PHONE_NUMBER,URL',
            'buttons.*.text' => 'required_with:buttons|string|max:25',
            'variables' => 'nullable|array',
            'sample_values' => 'nullable|array',
            'has_media' => 'boolean',
            'media_type' => 'nullable|in:image,document,video',
            'auto_send_enabled' => 'boolean',
            'requires_opt_in' => 'boolean',
        ]);

        $validated['created_by'] = auth()->id();
        $validated['whatsapp_name'] = str_replace('-', '_', \Str::slug($validated['name'], '_'));

        $template = WhatsAppMessageTemplate::create($validated);

        return redirect()->route('whatsapp.templates.show', $template)
                        ->with('success', 'تم إنشاء القالب بنجاح');
    }

    /**
     * Show template details
     */
    public function show(WhatsAppMessageTemplate $template): View
    {
        $template->load(['category', 'creator', 'approver', 'usageRecords' => function ($query) {
            $query->latest()->limit(10);
        }]);

        // Get analytics
        $analytics = $this->templateService->getTemplateAnalytics($template->id);

        // Get recent usage
        $recentUsage = $template->usageRecords()
            ->with(['customer', 'repairTicket'])
            ->latest()
            ->limit(10)
            ->get();

        return view('whatsapp.templates.show', compact('template', 'analytics', 'recentUsage'));
    }

    /**
     * Show template edit form
     */
    public function edit(WhatsAppMessageTemplate $template): View
    {
        $categories = WhatsAppTemplateCategory::active()->ordered()->get();
        return view('whatsapp.templates.edit', compact('template', 'categories'));
    }

    /**
     * Update template
     */
    public function update(Request $request, WhatsAppMessageTemplate $template): RedirectResponse
    {
        // Only allow editing if template is not approved
        if ($template->status === 'APPROVED') {
            return back()->with('error', 'لا يمكن تعديل القوالب المعتمدة');
        }

        $validated = $request->validate([
            'display_name' => 'required|string|max:255',
            'display_name_ar' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'category_id' => 'required|exists:whatsapp_template_categories,id',
            'language' => 'required|in:ar,en',
            'header_text' => 'nullable|string|max:60',
            'body_text' => 'required|string|max:1024',
            'footer_text' => 'nullable|string|max:60',
            'buttons' => 'nullable|array|max:3',
            'buttons.*.type' => 'required_with:buttons|in:QUICK_REPLY,PHONE_NUMBER,URL',
            'buttons.*.text' => 'required_with:buttons|string|max:25',
            'variables' => 'nullable|array',
            'sample_values' => 'nullable|array',
            'has_media' => 'boolean',
            'media_type' => 'nullable|in:image,document,video',
            'auto_send_enabled' => 'boolean',
            'requires_opt_in' => 'boolean',
            'is_active' => 'boolean',
        ]);

        $template->update($validated);

        return redirect()->route('whatsapp.templates.show', $template)
                        ->with('success', 'تم تحديث القالب بنجاح');
    }

    /**
     * Delete template
     */
    public function destroy(WhatsAppMessageTemplate $template): RedirectResponse
    {
        // Only allow deletion if template is not approved or has no usage
        if ($template->status === 'APPROVED' && $template->usage_count > 0) {
            return back()->with('error', 'لا يمكن حذف القوالب المعتمدة المستخدمة');
        }

        $template->delete();

        return redirect()->route('whatsapp.templates.index')
                        ->with('success', 'تم حذف القالب بنجاح');
    }

    /**
     * Submit template for approval
     */
    public function submitForApproval(WhatsAppMessageTemplate $template): RedirectResponse
    {
        if ($template->status !== 'DRAFT') {
            return back()->with('error', 'يمكن تقديم المسودات فقط للموافقة');
        }

        $template->submitForApproval();

        return back()->with('success', 'تم تقديم القالب للموافقة');
    }

    /**
     * Approve template
     */
    public function approve(Request $request, WhatsAppMessageTemplate $template): RedirectResponse
    {
        $request->validate([
            'approval_notes' => 'nullable|string|max:500'
        ]);

        $template->approve(auth()->user(), $request->approval_notes);

        return back()->with('success', 'تم اعتماد القالب بنجاح');
    }

    /**
     * Reject template
     */
    public function reject(Request $request, WhatsAppMessageTemplate $template): RedirectResponse
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:500'
        ]);

        $template->reject($request->rejection_reason, auth()->user());

        return back()->with('success', 'تم رفض القالب');
    }

    /**
     * Submit template to WhatsApp
     */
    public function submitToWhatsApp(WhatsAppMessageTemplate $template): RedirectResponse
    {
        if (!$template->isApproved()) {
            return back()->with('error', 'يجب اعتماد القالب أولاً');
        }

        $result = $this->templateService->submitToWhatsApp($template);

        if ($result['success']) {
            return back()->with('success', 'تم إرسال القالب إلى واتساب للمراجعة');
        } else {
            return back()->with('error', 'فشل في إرسال القالب: ' . $result['error']);
        }
    }

    /**
     * Preview template
     */
    public function preview(Request $request, WhatsAppMessageTemplate $template)
    {
        $variables = $request->input('variables', []);
        $rendered = $template->render($variables);

        return response()->json([
            'success' => true,
            'preview' => $rendered
        ]);
    }

    /**
     * Test send template
     */
    public function testSend(Request $request, WhatsAppMessageTemplate $template): RedirectResponse
    {
        $request->validate([
            'test_phone' => 'required|string',
            'test_variables' => 'nullable|array'
        ]);

        if (!$template->canBeUsed()) {
            return back()->with('error', 'القالب غير متاح للاستخدام');
        }

        $result = $this->templateService->sendTemplate(
            $template,
            $request->test_phone,
            $request->test_variables ?? [],
            [
                'trigger_type' => 'manual',
                'trigger_source' => 'dashboard',
                'triggered_by' => auth()->id(),
                'business_event' => 'test'
            ]
        );

        if ($result['success']) {
            return back()->with('success', 'تم إرسال القالب للاختبار بنجاح');
        } else {
            return back()->with('error', 'فشل في إرسال القالب: ' . $result['error']);
        }
    }

    /**
     * Get template analytics data
     */
    public function analytics(Request $request, WhatsAppMessageTemplate $template)
    {
        $dateRange = null;
        if ($request->filled('start_date') && $request->filled('end_date')) {
            $dateRange = [$request->start_date, $request->end_date];
        }

        $analytics = $this->templateService->getTemplateAnalytics($template->id, $dateRange);

        return response()->json($analytics);
    }

    /**
     * Duplicate template
     */
    public function duplicate(WhatsAppMessageTemplate $template): RedirectResponse
    {
        $newTemplate = $template->replicate();
        $newTemplate->name = $template->name . '_copy_' . time();
        $newTemplate->whatsapp_name = $template->whatsapp_name . '_copy_' . time();
        $newTemplate->display_name = $template->display_name . ' (نسخة)';
        $newTemplate->display_name_ar = $template->display_name_ar . ' (نسخة)';
        $newTemplate->status = 'DRAFT';
        $newTemplate->whatsapp_template_id = null;
        $newTemplate->created_by = auth()->id();
        $newTemplate->approved_by = null;
        $newTemplate->approved_at = null;
        $newTemplate->usage_count = 0;
        $newTemplate->success_count = 0;
        $newTemplate->failure_count = 0;
        $newTemplate->success_rate = 0;
        $newTemplate->last_used_at = null;
        $newTemplate->save();

        return redirect()->route('whatsapp.templates.edit', $newTemplate)
                        ->with('success', 'تم نسخ القالب بنجاح');
    }
}
