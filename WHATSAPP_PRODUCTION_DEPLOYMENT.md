# 🚀 WhatsApp Business API - Production Deployment Guide

This guide provides step-by-step instructions for deploying WhatsApp Business API integration to production for the NJ Repair Shop system.

## 📋 Pre-Deployment Checklist

### ✅ Meta Business Account Requirements
- [ ] Meta Business Account verified and approved
- [ ] WhatsApp Business Account created and verified
- [ ] Business phone number verified with WhatsApp
- [ ] All required business documents submitted
- [ ] App review completed and approved by Meta

### ✅ Technical Requirements
- [ ] Production server with PHP 8.1+
- [ ] SSL certificate installed and valid
- [ ] Domain name configured and accessible
- [ ] Database server configured (MySQL/PostgreSQL)
- [ ] Redis server for queue processing
- [ ] Supervisor for queue worker management
- [ ] Backup system configured

### ✅ Application Requirements
- [ ] All WhatsApp message templates approved by Meta
- [ ] Environment variables configured
- [ ] Database migrations completed
- [ ] Queue system configured and tested
- [ ] Webhook endpoint tested and verified
- [ ] Error handling and logging implemented
- [ ] Rate limiting configured

---

## 🔧 Production Server Setup

### 1. Server Configuration

#### System Requirements
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y nginx mysql-server redis-server supervisor curl git unzip

# Install PHP 8.1 and extensions
sudo apt install -y php8.1-fpm php8.1-mysql php8.1-redis php8.1-curl php8.1-json php8.1-mbstring php8.1-xml php8.1-zip
```

#### Nginx Configuration
```nginx
# /etc/nginx/sites-available/njrepair.com
server {
    listen 80;
    server_name njrepair.com www.njrepair.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name njrepair.com www.njrepair.com;
    root /var/www/njrepair/public;
    index index.php;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/njrepair.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/njrepair.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # WhatsApp Webhook specific configuration
    location /api/whatsapp/webhook {
        try_files $uri $uri/ /index.php?$query_string;
        
        # Increase timeout for webhook processing
        proxy_read_timeout 300;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
        
        # Buffer settings for large webhook payloads
        client_max_body_size 10M;
        client_body_buffer_size 128k;
    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Increase timeout for long-running processes
        fastcgi_read_timeout 300;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

### 2. SSL Certificate Setup

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d njrepair.com -d www.njrepair.com

# Set up automatic renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. Database Configuration

```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p
```

```sql
CREATE DATABASE njrepair_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'njrepair_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON njrepair_production.* TO 'njrepair_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 4. Redis Configuration

```bash
# Configure Redis
sudo nano /etc/redis/redis.conf

# Set password
requirepass your_redis_password_here

# Restart Redis
sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

---

## 📱 WhatsApp Business API Production Setup

### 1. Meta Developer Console Configuration

#### Update Webhook URL
```
1. Go to Meta Developer Console
2. Navigate to your WhatsApp app
3. Go to WhatsApp > Configuration
4. Update Callback URL to: https://njrepair.com/api/whatsapp/webhook
5. Update Verify Token (use production token)
6. Save configuration
```

#### Production Access Token
```
1. Go to WhatsApp > Getting Started
2. Generate System User Access Token
3. Select required permissions:
   - whatsapp_business_messaging
   - whatsapp_business_management
   - business_management
4. Set token expiration to "Never"
5. Copy token securely
```

### 2. Environment Configuration

Create production `.env` file:
```env
# Application
APP_NAME="NJ Repair Shop"
APP_ENV=production
APP_KEY=base64:your_app_key_here
APP_DEBUG=false
APP_URL=https://njrepair.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=njrepair_production
DB_USERNAME=njrepair_user
DB_PASSWORD=secure_database_password

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=your_redis_password_here
REDIS_PORT=6379

# Queue
QUEUE_CONNECTION=redis
QUEUE_FAILED_DRIVER=database

# WhatsApp Business API - Production
WHATSAPP_API_VERSION=v18.0
WHATSAPP_ACCESS_TOKEN=your_production_access_token
WHATSAPP_PHONE_NUMBER_ID=your_production_phone_number_id
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_secure_production_verify_token
WHATSAPP_APP_ID=your_app_id
WHATSAPP_APP_SECRET=your_app_secret

# WhatsApp Features - Production Settings
WHATSAPP_ENABLED=true
WHATSAPP_FALLBACK_TO_SMS=true
WHATSAPP_RATE_LIMIT_PER_MINUTE=80
WHATSAPP_TEMPLATE_APPROVAL_REQUIRED=true
WHATSAPP_DEFAULT_LANGUAGE=ar

# Security
WHATSAPP_WEBHOOK_SECRET=your_production_webhook_secret
WHATSAPP_WEBHOOK_SIGNATURE_VALIDATION=true

# Logging
WHATSAPP_LOGGING_ENABLED=true
WHATSAPP_LOG_LEVEL=info
WHATSAPP_LOG_WEBHOOKS=true
WHATSAPP_LOG_API_CALLS=true

# Business Hours
WHATSAPP_BUSINESS_HOURS_ENABLED=true
WHATSAPP_BUSINESS_TIMEZONE=Asia/Riyadh

# Error Handling
WHATSAPP_ERROR_NOTIFICATION_ENABLED=true
WHATSAPP_ERROR_THRESHOLD=10

# Mail Configuration (for error notifications)
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="NJ Repair Shop"
```

### 3. Application Deployment

```bash
# Clone repository
cd /var/www
sudo git clone https://github.com/your-repo/njrepair.git
cd njrepair

# Set permissions
sudo chown -R www-data:www-data /var/www/njrepair
sudo chmod -R 755 /var/www/njrepair
sudo chmod -R 775 /var/www/njrepair/storage
sudo chmod -R 775 /var/www/njrepair/bootstrap/cache

# Install dependencies
composer install --optimize-autoloader --no-dev

# Set up environment
cp .env.production .env
php artisan key:generate

# Run migrations
php artisan migrate --force

# Seed WhatsApp templates
php artisan db:seed --class=WhatsAppTemplateSeeder --force

# Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Set up storage link
php artisan storage:link
```

---

## 🔄 Queue Worker Configuration

### 1. Supervisor Configuration

Create supervisor configuration:
```bash
sudo nano /etc/supervisor/conf.d/njrepair-worker.conf
```

```ini
[program:njrepair-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/njrepair/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600 --queue=whatsapp,default
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/njrepair/storage/logs/worker.log
stopwaitsecs=3600

[program:njrepair-scheduler]
process_name=%(program_name)s
command=php /var/www/njrepair/artisan schedule:work
autostart=true
autorestart=true
user=www-data
redirect_stderr=true
stdout_logfile=/var/www/njrepair/storage/logs/scheduler.log
```

```bash
# Update supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start njrepair-worker:*
sudo supervisorctl start njrepair-scheduler:*
```

### 2. Horizon Configuration (Alternative)

```bash
# Install Horizon
composer require laravel/horizon

# Publish Horizon assets
php artisan horizon:install

# Configure Horizon
php artisan horizon:publish
```

Edit `config/horizon.php` for production:
```php
'environments' => [
    'production' => [
        'supervisor-1' => [
            'connection' => 'redis',
            'queue' => ['whatsapp', 'default'],
            'balance' => 'auto',
            'processes' => 3,
            'tries' => 3,
            'timeout' => 300,
        ],
    ],
],
```

---

## 🧪 Production Testing

### 1. Pre-Launch Testing

```bash
# Test WhatsApp setup
php artisan whatsapp:setup --test

# Test API connectivity
php artisan whatsapp:test-connection

# Test webhook endpoint
curl -X GET "https://njrepair.com/api/whatsapp/webhook?hub.mode=subscribe&hub.challenge=test&hub.verify_token=YOUR_TOKEN"

# Test template message (use your phone number)
php artisan whatsapp:send-template +966501234567 repair_received_ar "Test Customer" "Test Device" "TEST-001"
```

### 2. Load Testing

```bash
# Install Apache Bench
sudo apt install apache2-utils

# Test webhook endpoint
ab -n 100 -c 10 https://njrepair.com/api/whatsapp/webhook

# Monitor system resources
htop
```

### 3. Security Testing

```bash
# Test SSL configuration
curl -I https://njrepair.com

# Test security headers
curl -I https://njrepair.com/api/whatsapp/webhook

# Verify webhook signature validation
# (Use Meta's webhook testing tool)
```

---

## 📊 Monitoring and Logging

### 1. Log Configuration

```bash
# Configure log rotation
sudo nano /etc/logrotate.d/njrepair
```

```
/var/www/njrepair/storage/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    notifempty
    create 644 www-data www-data
    postrotate
        /usr/bin/supervisorctl restart njrepair-worker:*
    endscript
}
```

### 2. Monitoring Setup

```bash
# Install monitoring tools
sudo apt install htop iotop nethogs

# Set up basic monitoring script
nano /usr/local/bin/njrepair-monitor.sh
```

```bash
#!/bin/bash
# Basic monitoring script for NJ Repair Shop

# Check queue workers
if ! pgrep -f "queue:work" > /dev/null; then
    echo "Queue workers not running!" | mail -s "NJ Repair Alert" <EMAIL>
    sudo supervisorctl start njrepair-worker:*
fi

# Check Redis
if ! redis-cli ping > /dev/null 2>&1; then
    echo "Redis not responding!" | mail -s "NJ Repair Alert" <EMAIL>
    sudo systemctl restart redis-server
fi

# Check disk space
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is ${DISK_USAGE}%" | mail -s "NJ Repair Alert" <EMAIL>
fi

# Check WhatsApp API connectivity
cd /var/www/njrepair
if ! php artisan whatsapp:test-connection --quiet; then
    echo "WhatsApp API connectivity issue!" | mail -s "NJ Repair Alert" <EMAIL>
fi
```

```bash
# Make script executable
chmod +x /usr/local/bin/njrepair-monitor.sh

# Add to crontab
crontab -e
# Add: */5 * * * * /usr/local/bin/njrepair-monitor.sh
```

---

## 🔒 Security Hardening

### 1. Firewall Configuration

```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. Application Security

```bash
# Set secure file permissions
find /var/www/njrepair -type f -exec chmod 644 {} \;
find /var/www/njrepair -type d -exec chmod 755 {} \;
chmod -R 775 /var/www/njrepair/storage
chmod -R 775 /var/www/njrepair/bootstrap/cache

# Secure sensitive files
chmod 600 /var/www/njrepair/.env
```

### 3. Database Security

```sql
-- Remove test databases and users
DROP DATABASE IF EXISTS test;
DELETE FROM mysql.user WHERE User='';
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');
FLUSH PRIVILEGES;
```

---

## 📋 Post-Deployment Checklist

### ✅ Immediate Post-Deployment
- [ ] Application accessible via HTTPS
- [ ] WhatsApp webhook responding correctly
- [ ] Database migrations completed
- [ ] Queue workers running
- [ ] SSL certificate valid
- [ ] Error logging working
- [ ] Test message sent successfully

### ✅ 24 Hours After Deployment
- [ ] Monitor error logs for issues
- [ ] Check queue processing performance
- [ ] Verify webhook processing
- [ ] Monitor API rate limits
- [ ] Check system resource usage
- [ ] Verify backup systems

### ✅ Weekly Monitoring
- [ ] Review WhatsApp message delivery rates
- [ ] Check template approval status
- [ ] Monitor system performance
- [ ] Review security logs
- [ ] Update SSL certificates if needed
- [ ] Check disk space and cleanup logs

---

## 🆘 Emergency Procedures

### WhatsApp Service Outage
```bash
# Enable SMS fallback
php artisan config:set whatsapp.features.fallback_to_sms true

# Notify customers via alternative channels
php artisan notify:customers --channel=sms --message="WhatsApp temporarily unavailable"
```

### Database Issues
```bash
# Switch to read-only mode
php artisan down --message="Maintenance in progress"

# Restore from backup
mysql -u root -p njrepair_production < backup.sql

# Bring application back online
php artisan up
```

### High Load Situations
```bash
# Scale queue workers
sudo supervisorctl stop njrepair-worker:*
# Edit supervisor config to increase numprocs
sudo supervisorctl start njrepair-worker:*

# Enable application caching
php artisan cache:clear
php artisan config:cache
php artisan route:cache
```

---

**🎉 Congratulations!** Your WhatsApp Business API integration is now live in production. Monitor the system closely for the first few days and be prepared to make adjustments based on real-world usage patterns.
