@extends('layouts.app')

@push('styles')
    <link href="{{ asset('css/pattern-lock.css') }}" rel="stylesheet">
@endpush

@push('scripts')
    <script src="{{ asset('js/pattern-lock.js') }}"></script>
@endpush

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="bi bi-ticket"></i> {{ $repairTicket->ticket_number }}
                        @if($repairTicket->isOverdue())
                            <span class="badge bg-warning">{{ __('app.repair_tickets.overdue') }}</span>
                        @endif
                    </h1>
                    <p class="text-muted mb-0">
                        {{ __('app.repair_tickets.created') }} {{ $repairTicket->created_at->diffForHumans() }} •
                        {{ $repairTicket->daysSinceReceived() }} {{ __('app.repair_tickets.days_since_received') }}
                    </p>
                </div>
                <div class="btn-group" role="group">
                    <a href="{{ route('repair-tickets.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> {{ __('app.back') }} {{ __('app.nav.repair_tickets') }}
                    </a>
                    <a href="{{ route('repair-tickets.edit', $repairTicket) }}" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> {{ __('app.repair_tickets.edit') }}
                    </a>
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#statusModal">
                        <i class="bi bi-arrow-repeat"></i> {{ __('app.repair_tickets.update_status') }}
                    </button>
                    @if($repairTicket->repairStatus->is_final && !$repairTicket->invoice)
                        <a href="{{ route('invoices.create', ['repair_ticket_id' => $repairTicket->id]) }}" class="btn btn-warning">
                            <i class="fas fa-file-invoice"></i> إنشاء فاتورة
                        </a>
                    @elseif($repairTicket->invoice)
                        <a href="{{ route('invoices.show', $repairTicket->invoice) }}" class="btn btn-info">
                            <i class="fas fa-file-invoice"></i> عرض الفاتورة
                        </a>
                    @endif
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="bi bi-printer"></i> {{ __('app.print') }} & {{ __('app.export') }}
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ route('print-export.ticket.preview', $repairTicket) }}" target="_blank">
                                <i class="bi bi-eye"></i> {{ __('app.repair_tickets.preview') }}
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('print-export.ticket.print', $repairTicket) }}" target="_blank">
                                <i class="bi bi-printer"></i> {{ __('app.repair_tickets.print_pdf') }}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ route('print-export.tickets.export-excel', ['ticket_ids' => [$repairTicket->id]]) }}">
                                <i class="bi bi-file-earmark-excel"></i> {{ __('app.repair_tickets.export_excel') }}
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Main Ticket Information -->
                <div class="col-md-8">
                    <!-- Customer & Device Info -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-info-circle"></i> {{ __('app.repair_tickets.information') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">{{ __('app.customers.customer') }}</h6>
                                    <p class="mb-1">
                                        <strong>
                                            <a href="{{ route('customers.show', $repairTicket->customer) }}"
                                               class="text-decoration-none">
                                                {{ $repairTicket->customer->name }}
                                            </a>
                                        </strong>
                                    </p>
                                    <p class="mb-1">
                                        <i class="bi bi-telephone"></i>
                                        <a href="tel:{{ $repairTicket->customer->phone_number }}">
                                            {{ $repairTicket->customer->phone_number }}
                                        </a>
                                    </p>
                                    @if($repairTicket->customer->email)
                                        <p class="mb-3">
                                            <i class="bi bi-envelope"></i>
                                            <a href="mailto:{{ $repairTicket->customer->email }}">
                                                {{ $repairTicket->customer->email }}
                                            </a>
                                        </p>
                                    @endif
                                </div>

                                <div class="col-md-6">
                                    <h6 class="text-primary">{{ __('app.repair_tickets.device') }}</h6>
                                    <p class="mb-1">
                                        <strong>{{ $repairTicket->brand->name }} {{ $repairTicket->device_model }}</strong>
                                    </p>
                                    <p class="mb-1">
                                        <span class="badge bg-secondary">
                                            {{ $repairTicket->deviceCondition->name }}
                                        </span>
                                    </p>
                                    {{-- Security pattern info moved to dedicated section below --}}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Problem Description -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-exclamation-triangle"></i> {{ __('app.repair_tickets.reported_problem') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">{{ $repairTicket->reported_problem }}</p>
                        </div>
                    </div>

                    <!-- Technician Comments -->
                    @if($repairTicket->technician_comments)
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-chat-text"></i> {{ __('app.repair_tickets.technician_comments') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">{{ $repairTicket->technician_comments }}</p>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Status & Timeline -->
                <div class="col-md-4">
                    <!-- Current Status -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-flag"></i> {{ __('app.repair_tickets.current_status') }}
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <span class="badge fs-6 px-3 py-2"
                                  style="background-color: {{ $repairTicket->repairStatus->color }}">
                                {{ $repairTicket->repairStatus->name }}
                            </span>
                        </div>
                    </div>

                    <!-- Important Dates -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-calendar"></i> {{ __('app.repair_tickets.important_dates') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.repair_tickets.received_date') }}</label>
                                <p class="mb-0">{{ $repairTicket->received_date->format('F j, Y') }}</p>
                            </div>

                            @if($repairTicket->estimated_completion_date)
                                <div class="mb-3">
                                    <label class="form-label text-muted">{{ __('app.repair_tickets.estimated_completion_date') }}</label>
                                    <p class="mb-0 {{ $repairTicket->isOverdue() ? 'text-warning' : '' }}">
                                        {{ $repairTicket->estimated_completion_date->format('F j, Y') }}
                                        @if($repairTicket->isOverdue())
                                            <i class="bi bi-exclamation-triangle"></i>
                                        @endif
                                    </p>
                                </div>
                            @endif

                            @if($repairTicket->completed_date)
                                <div class="mb-0">
                                    <label class="form-label text-muted">{{ __('app.repair_tickets.completed_date') }}</label>
                                    <p class="mb-0 text-success">
                                        {{ $repairTicket->completed_date->format('F j, Y') }}
                                        <i class="bi bi-check-circle"></i>
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Cost Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-currency-dollar"></i> {{ __('app.repair_tickets.cost_information') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($repairTicket->estimated_cost)
                                <div class="mb-2">
                                    <label class="form-label text-muted">{{ __('app.repair_tickets.estimated_cost') }}</label>
                                    <p class="mb-0">{{ __('app.common.currency_symbol') }}{{ number_format($repairTicket->estimated_cost, 2) }}</p>
                                </div>
                            @endif

                            @if($repairTicket->final_cost)
                                <div class="mb-0">
                                    <label class="form-label text-muted">{{ __('app.repair_tickets.final_cost') }}</label>
                                    <p class="mb-0 fw-bold text-success">
                                        {{ __('app.common.currency_symbol') }}{{ number_format($repairTicket->final_cost, 2) }}
                                    </p>
                                </div>
                            @endif

                            @if(!$repairTicket->estimated_cost && !$repairTicket->final_cost)
                                <p class="text-muted mb-0">{{ __('app.repair_tickets.no_cost_information') }}</p>
                            @endif
                        </div>
                    </div>

                    <!-- Assigned Technician -->
                    @if($repairTicket->assignedTo)
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-person-gear"></i> {{ __('app.repair_tickets.assigned_technician') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">{{ $repairTicket->assignedTo->name }}</p>
                            </div>
                        </div>
                    @endif

                    <!-- Security Pattern Information -->
                    @if($repairTicket->hasAnySecurityPattern())
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-shield-lock"></i> {{ __('app.repair_tickets.security_pattern_information') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <x-pattern-display :ticket="$repairTicket" />
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('repair-tickets.update-status', $repairTicket) }}">
                @csrf
                @method('PATCH')

                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app.repair_tickets.update_ticket_status') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <div class="mb-3">
                        <label for="repair_status_id" class="form-label">{{ __('app.repair_tickets.new_status') }}</label>
                        <select class="form-select" id="repair_status_id" name="repair_status_id" required>
                            @foreach(\App\Models\RepairStatus::active()->ordered()->get() as $status)
                                <option value="{{ $status->id }}"
                                        {{ $repairTicket->repair_status_id == $status->id ? 'selected' : '' }}
                                        style="color: {{ $status->color }}">
                                    {{ $status->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="technician_comments" class="form-label">{{ __('app.repair_tickets.update_comments') }}</label>
                        <textarea class="form-control"
                                  id="technician_comments"
                                  name="technician_comments"
                                  rows="3"
                                  placeholder="{{ __('app.repair_tickets.update_comments_placeholder') }}"></textarea>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('app.common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('app.repair_tickets.update_status') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
