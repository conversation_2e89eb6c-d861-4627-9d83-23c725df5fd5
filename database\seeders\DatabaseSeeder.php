<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed the lookup tables first
        $this->call([
            BrandSeeder::class,
            RepairStatusSeeder::class,
            DeviceConditionSeeder::class,
        ]);

        // Create a default admin user
        \App\Models\User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        // Create additional test users
        \App\Models\User::factory(5)->create();
    }
}
