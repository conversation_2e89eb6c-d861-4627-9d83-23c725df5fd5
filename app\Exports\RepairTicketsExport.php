<?php

namespace App\Exports;

use App\Models\RepairTicket;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class RepairTicketsExport implements FromQuery, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $filters;

    public function __construct(array $filters = [])
    {
        $this->filters = $filters;
    }

    /**
     * Query for the export
     */
    public function query()
    {
        $query = RepairTicket::with(['customer', 'brand', 'repairStatus', 'deviceCondition', 'assignedTo']);

        // Apply filters
        if (!empty($this->filters['status'])) {
            $query->where('repair_status_id', $this->filters['status']);
        }

        if (!empty($this->filters['brand'])) {
            $query->where('brand_id', $this->filters['brand']);
        }

        if (!empty($this->filters['technician'])) {
            if ($this->filters['technician'] === 'unassigned') {
                $query->whereNull('assigned_technician_id');
            } else {
                $query->where('assigned_technician_id', $this->filters['technician']);
            }
        }

        if (!empty($this->filters['search'])) {
            $query->search($this->filters['search']);
        }

        if (!empty($this->filters['start_date']) && !empty($this->filters['end_date'])) {
            $query->whereBetween('received_date', [
                $this->filters['start_date'],
                $this->filters['end_date']
            ]);
        }

        return $query->orderBy('received_date', 'desc');
    }

    /**
     * Define the headings for the Excel file
     */
    public function headings(): array
    {
        return [
            'Ticket Number',
            'Customer Name',
            'Phone Number',
            'Device Brand',
            'Device Model',
            'Reported Problem',
            'Device Pattern',
            'Security Pattern',
            'Device Condition',
            'Technician Comments',
            'Repair Status',
            'Assigned Technician',
            'Received Date',
            'Estimated Completion',
            'Completed Date',
            'Initial Cost',
            'Final Cost',
            'Created At',
            'Updated At',
        ];
    }

    /**
     * Map the data for each row
     */
    public function map($ticket): array
    {
        return [
            $ticket->ticket_number,
            $ticket->customer->name ?? 'N/A',
            $ticket->customer->phone_number ?? 'N/A',
            $ticket->brand->name ?? 'N/A',
            $ticket->device_model,
            $ticket->reported_problem,
            $ticket->device_pattern ? 'Yes' : 'No',
            $ticket->hasSecurityPattern() ? $ticket->getMaskedSecurityPattern() : 'No',
            $ticket->deviceCondition->name ?? 'N/A',
            $ticket->technician_comments,
            $ticket->repairStatus->name ?? 'N/A',
            $ticket->assignedTo->name ?? 'Unassigned',
            $ticket->received_date ? $ticket->received_date->format('Y-m-d H:i') : '',
            $ticket->estimated_completion_date ? $ticket->estimated_completion_date->format('Y-m-d') : '',
            $ticket->completed_date ? $ticket->completed_date->format('Y-m-d H:i') : '',
            $ticket->initial_cost ? '$' . number_format($ticket->initial_cost, 2) : '',
            $ticket->final_cost ? '$' . number_format($ticket->final_cost, 2) : '',
            $ticket->created_at->format('Y-m-d H:i'),
            $ticket->updated_at->format('Y-m-d H:i'),
        ];
    }

    /**
     * Apply styles to the worksheet
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4'],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            // Style all cells
            'A:R' => [
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
        ];
    }
}
