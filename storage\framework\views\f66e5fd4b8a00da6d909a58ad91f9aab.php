<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="bi bi-eye text-primary"></i> عرض قالب واتساب</h2>
                    <p class="text-muted"><?php echo e($template->display_name_ar); ?></p>
                </div>
                <div class="btn-group">
                    <?php if($template->status !== 'APPROVED'): ?>
                        <a href="<?php echo e(route('whatsapp.templates.edit', $template)); ?>" class="btn btn-primary">
                            <i class="bi bi-pencil"></i> تعديل
                        </a>
                    <?php endif; ?>
                    <a href="<?php echo e(route('whatsapp.templates.duplicate', $template)); ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-files"></i> نسخ
                    </a>
                    <a href="<?php echo e(route('whatsapp.templates.index')); ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Template Details -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-info-circle"></i> معلومات القالب</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>الاسم التقني:</strong></td>
                                    <td><code><?php echo e($template->name); ?></code></td>
                                </tr>
                                <tr>
                                    <td><strong>اسم واتساب:</strong></td>
                                    <td><code><?php echo e($template->whatsapp_name); ?></code></td>
                                </tr>
                                <tr>
                                    <td><strong>الاسم المعروض:</strong></td>
                                    <td><?php echo e($template->display_name_ar); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>الفئة:</strong></td>
                                    <td>
                                        <span class="badge" style="background-color: <?php echo e($template->category->color); ?>">
                                            <i class="<?php echo e($template->category->icon); ?>"></i>
                                            <?php echo e($template->category->name_ar); ?>

                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>اللغة:</strong></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo e($template->language === 'ar' ? 'عربي' : 'English'); ?>

                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        <span class="badge bg-<?php echo e($template->status_color); ?>">
                                            <?php echo e($template->status); ?>

                                        </span>
                                        <?php if($template->quality_rating !== 'UNKNOWN'): ?>
                                            <span class="badge bg-<?php echo e($template->quality_color); ?> ms-1">
                                                <?php echo e($template->quality_rating); ?>

                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>نشط:</strong></td>
                                    <td>
                                        <?php if($template->is_active): ?>
                                            <span class="badge bg-success">نعم</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">لا</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td><?php echo e($template->created_at->format('Y-m-d H:i')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>المنشئ:</strong></td>
                                    <td><?php echo e($template->creator?->name ?? 'غير محدد'); ?></td>
                                </tr>
                                <?php if($template->approved_at): ?>
                                <tr>
                                    <td><strong>تاريخ الاعتماد:</strong></td>
                                    <td><?php echo e($template->approved_at->format('Y-m-d H:i')); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>

                    <?php if($template->description_ar): ?>
                        <div class="mt-3">
                            <strong>الوصف:</strong>
                            <p class="text-muted"><?php echo e($template->description_ar); ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if($template->rejection_reason): ?>
                        <div class="alert alert-danger mt-3">
                            <strong>سبب الرفض:</strong> <?php echo e($template->rejection_reason); ?>

                        </div>
                    <?php endif; ?>

                    <?php if($template->approval_notes): ?>
                        <div class="alert alert-success mt-3">
                            <strong>ملاحظات الموافقة:</strong> <?php echo e($template->approval_notes); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Template Content -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="bi bi-chat-square-text"></i> محتوى القالب</h5>
                </div>
                <div class="card-body">
                    <?php if($template->header_text): ?>
                        <div class="mb-3">
                            <strong>نص الرأس:</strong>
                            <div class="border rounded p-2 bg-light"><?php echo e($template->header_text); ?></div>
                        </div>
                    <?php endif; ?>

                    <div class="mb-3">
                        <strong>نص الرسالة الرئيسي:</strong>
                        <div class="border rounded p-3 bg-light" style="white-space: pre-wrap;"><?php echo e($template->body_text); ?></div>
                    </div>

                    <?php if($template->footer_text): ?>
                        <div class="mb-3">
                            <strong>نص التذييل:</strong>
                            <div class="border rounded p-2 bg-light"><?php echo e($template->footer_text); ?></div>
                        </div>
                    <?php endif; ?>

                    <?php if($template->buttons): ?>
                        <div class="mb-3">
                            <strong>الأزرار:</strong>
                            <div class="mt-2">
                                <?php $__currentLoopData = $template->buttons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $button): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <button class="btn btn-outline-primary btn-sm me-2 mb-1" disabled>
                                        <?php echo e($button['text']); ?>

                                        <small class="text-muted">(<?php echo e($button['type']); ?>)</small>
                                    </button>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if($template->variables): ?>
                        <div class="mb-3">
                            <strong>المتغيرات:</strong>
                            <div class="mt-2">
                                <?php $__currentLoopData = $template->variables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $variable => $description): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="badge bg-info me-1 mb-1">
                                        {{ $variable }} - <?php echo e($description); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Usage Statistics -->
            <?php if($template->usage_count > 0): ?>
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="bi bi-graph-up"></i> إحصائيات الاستخدام</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary"><?php echo e(number_format($template->usage_count)); ?></h4>
                                <small class="text-muted">إجمالي الاستخدام</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success"><?php echo e(number_format($template->success_count)); ?></h4>
                                <small class="text-muted">نجح</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-danger"><?php echo e(number_format($template->failure_count)); ?></h4>
                                <small class="text-muted">فشل</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info"><?php echo e($template->success_rate); ?>%</h4>
                                <small class="text-muted">معدل النجاح</small>
                            </div>
                        </div>
                    </div>

                    <?php if($template->last_used_at): ?>
                        <div class="mt-3 text-center">
                            <small class="text-muted">آخر استخدام: <?php echo e($template->last_used_at->diffForHumans()); ?></small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Recent Usage -->
            <?php if($recentUsage->count() > 0): ?>
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="bi bi-clock-history"></i> الاستخدام الأخير</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                    <th>المصدر</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentUsage; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $usage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <?php echo e($usage->customer?->name ?? 'غير محدد'); ?>

                                        <br>
                                        <small class="text-muted"><?php echo e($usage->customer_phone); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo e($usage->status === 'delivered' ? 'success' : ($usage->status === 'failed' ? 'danger' : 'warning')); ?>">
                                            <?php echo e($usage->status_display); ?>

                                        </span>
                                    </td>
                                    <td><?php echo e($usage->created_at->format('Y-m-d H:i')); ?></td>
                                    <td><?php echo e($usage->trigger_display); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Actions Sidebar -->
        <div class="col-lg-4">
            <div class="card sticky-top">
                <div class="card-header">
                    <h5><i class="bi bi-gear"></i> الإجراءات</h5>
                </div>
                <div class="card-body">
                    <?php if($template->status === 'DRAFT'): ?>
                        <form method="POST" action="<?php echo e(route('whatsapp.templates.submit-approval', $template)); ?>" class="mb-3">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="btn btn-success w-100">
                                <i class="bi bi-check-circle"></i> تقديم للموافقة
                            </button>
                        </form>
                    <?php endif; ?>

                    <?php if($template->status === 'PENDING' && auth()->check() && auth()->user()->can('approve-templates')): ?>
                        <div class="mb-3">
                            <button type="button" class="btn btn-success w-100 mb-2" onclick="approveTemplate()">
                                <i class="bi bi-check-lg"></i> اعتماد القالب
                            </button>
                            <button type="button" class="btn btn-danger w-100" onclick="rejectTemplate()">
                                <i class="bi bi-x-lg"></i> رفض القالب
                            </button>
                        </div>
                    <?php endif; ?>

                    <?php if($template->isApproved() && !$template->whatsapp_template_id): ?>
                        <form method="POST" action="<?php echo e(route('whatsapp.templates.submit-whatsapp', $template)); ?>" class="mb-3">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-whatsapp"></i> إرسال لواتساب
                            </button>
                        </form>
                    <?php endif; ?>

                    <?php if($template->canBeUsed()): ?>
                        <button type="button" class="btn btn-outline-primary w-100 mb-3" onclick="testTemplate()">
                            <i class="bi bi-send"></i> اختبار الإرسال
                        </button>
                    <?php endif; ?>

                    <a href="<?php echo e(route('whatsapp.templates.duplicate', $template)); ?>" class="btn btn-outline-secondary w-100 mb-3">
                        <i class="bi bi-files"></i> نسخ القالب
                    </a>

                    <?php if($template->status !== 'APPROVED' || $template->usage_count === 0): ?>
                        <form method="POST" action="<?php echo e(route('whatsapp.templates.destroy', $template)); ?>"
                              onsubmit="return confirm('هل أنت متأكد من حذف هذا القالب؟')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="bi bi-trash"></i> حذف القالب
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Template Preview -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="bi bi-eye"></i> معاينة القالب</h5>
                </div>
                <div class="card-body">
                    <div class="whatsapp-preview border rounded p-3 bg-light">
                        <?php if($template->header_text): ?>
                            <div class="fw-bold text-primary mb-2"><?php echo e($template->header_text); ?></div>
                        <?php endif; ?>

                        <div class="mb-2" style="white-space: pre-wrap;"><?php echo e($template->body_text); ?></div>

                        <?php if($template->footer_text): ?>
                            <div class="text-muted small"><?php echo e($template->footer_text); ?></div>
                        <?php endif; ?>

                        <?php if($template->buttons): ?>
                            <div class="mt-3">
                                <?php $__currentLoopData = $template->buttons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $button): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <button class="btn btn-outline-primary btn-sm me-2 mb-1" disabled>
                                        <?php echo e($button['text']); ?>

                                    </button>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function approveTemplate() {
    const notes = prompt('ملاحظات الموافقة (اختياري):');
    if (notes !== null) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo e(route("whatsapp.templates.approve", $template)); ?>';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';

        const notesInput = document.createElement('input');
        notesInput.type = 'hidden';
        notesInput.name = 'approval_notes';
        notesInput.value = notes;

        form.appendChild(csrfToken);
        form.appendChild(notesInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function rejectTemplate() {
    const reason = prompt('سبب الرفض (مطلوب):');
    if (reason && reason.trim()) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo e(route("whatsapp.templates.reject", $template)); ?>';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';

        const reasonInput = document.createElement('input');
        reasonInput.type = 'hidden';
        reasonInput.name = 'rejection_reason';
        reasonInput.value = reason;

        form.appendChild(csrfToken);
        form.appendChild(reasonInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function testTemplate() {
    const phone = prompt('رقم الهاتف للاختبار (مثال: 966501234567):');
    if (phone && phone.trim()) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo e(route("whatsapp.templates.test-send", $template)); ?>';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';

        const phoneInput = document.createElement('input');
        phoneInput.type = 'hidden';
        phoneInput.name = 'test_phone';
        phoneInput.value = phone;

        form.appendChild(csrfToken);
        form.appendChild(phoneInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\nj\resources\views/whatsapp/templates/show.blade.php ENDPATH**/ ?>