@extends('layouts.app')

@push('styles')
    <link href="{{ asset('css/pattern-lock.css') }}" rel="stylesheet">
@endpush

@push('scripts')
    <script src="{{ asset('js/pattern-lock.js') }}"></script>
@endpush

@section('content')
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-pencil"></i> {{ __('app.repair_tickets.edit') }}: {{ $repairTicket->ticket_number }}
                        </h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="{{ route('repair-tickets.show', $repairTicket) }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> {{ __('app.back') }} {{ __('app.repair_tickets.view') }}
                            </a>
                            <a href="{{ route('repair-tickets.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-list"></i> {{ __('app.repair_tickets.list') }}
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <form method="POST" action="{{ route('repair-tickets.update', $repairTicket) }}">
                        @csrf
                        @method('PUT')

                        <!-- Customer Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-person"></i> {{ __('app.customers.information') }}
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="customer_id" class="form-label">
                                        {{ __('app.customers.customer') }} <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select @error('customer_id') is-invalid @enderror"
                                            id="customer_id"
                                            name="customer_id"
                                            required>
                                        <option value="">{{ __('app.customers.select_customer') }}</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer->id }}"
                                                    {{ (old('customer_id', $repairTicket->customer_id) == $customer->id) ? 'selected' : '' }}>
                                                {{ $customer->name }} - {{ $customer->phone_number }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('customer_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Device Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-phone"></i> {{ __('app.repair_tickets.device_information') }}
                                </h6>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="brand_id" class="form-label">
                                        {{ __('app.brands.brand') }} <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select @error('brand_id') is-invalid @enderror"
                                            id="brand_id"
                                            name="brand_id"
                                            required>
                                        <option value="">{{ __('app.brands.select_brand') }}</option>
                                        @foreach($brands as $brand)
                                            <option value="{{ $brand->id }}"
                                                    {{ (old('brand_id', $repairTicket->brand_id) == $brand->id) ? 'selected' : '' }}>
                                                {{ $brand->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('brand_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="device_model" class="form-label">
                                        {{ __('app.repair_tickets.device_model') }} <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control @error('device_model') is-invalid @enderror"
                                           id="device_model"
                                           name="device_model"
                                           value="{{ old('device_model', $repairTicket->device_model) }}"
                                           placeholder="{{ __('app.repair_tickets.device_model_placeholder') }}"
                                           required>
                                    @error('device_model')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="device_condition_id" class="form-label">
                                        {{ __('app.device_conditions.condition') }} <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select @error('device_condition_id') is-invalid @enderror"
                                            id="device_condition_id"
                                            name="device_condition_id"
                                            required>
                                        <option value="">{{ __('app.device_conditions.select_condition') }}</option>
                                        @foreach($deviceConditions as $condition)
                                            <option value="{{ $condition->id }}"
                                                    {{ (old('device_condition_id', $repairTicket->device_condition_id) == $condition->id) ? 'selected' : '' }}>
                                                {{ $condition->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('device_condition_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Problem & Security -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-exclamation-triangle"></i> {{ __('app.repair_tickets.problem_and_security') }}
                                </h6>
                            </div>

                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="reported_problem" class="form-label">
                                        {{ __('app.repair_tickets.reported_problem') }} <span class="text-danger">*</span>
                                    </label>
                                    <textarea class="form-control @error('reported_problem') is-invalid @enderror"
                                              id="reported_problem"
                                              name="reported_problem"
                                              rows="3"
                                              placeholder="{{ __('app.repair_tickets.reported_problem_placeholder') }}"
                                              required>{{ old('reported_problem', $repairTicket->reported_problem) }}</textarea>
                                    @error('reported_problem')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            {{-- Security Pattern/Password Section --}}
                            <div class="col-12">
                                <x-pattern-selector :ticket="$repairTicket" />
                            </div>
                        </div>

                        <!-- Repair Details -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-tools"></i> {{ __('app.repair_tickets.repair_details') }}
                                </h6>
                            </div>

                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="repair_status_id" class="form-label">
                                        {{ __('app.repair_statuses.status') }} <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select @error('repair_status_id') is-invalid @enderror"
                                            id="repair_status_id"
                                            name="repair_status_id"
                                            required>
                                        @foreach($repairStatuses as $status)
                                            <option value="{{ $status->id }}"
                                                    {{ (old('repair_status_id', $repairTicket->repair_status_id) == $status->id) ? 'selected' : '' }}
                                                    style="color: {{ $status->color }}">
                                                {{ $status->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('repair_status_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="received_date" class="form-label">
                                        {{ __('app.repair_tickets.received_date') }} <span class="text-danger">*</span>
                                    </label>
                                    <input type="date"
                                           class="form-control @error('received_date') is-invalid @enderror"
                                           id="received_date"
                                           name="received_date"
                                           value="{{ old('received_date', $repairTicket->received_date->format('Y-m-d')) }}"
                                           required>
                                    @error('received_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="estimated_completion_date" class="form-label">
                                        {{ __('app.repair_tickets.estimated_completion_date') }}
                                    </label>
                                    <input type="date"
                                           class="form-control @error('estimated_completion_date') is-invalid @enderror"
                                           id="estimated_completion_date"
                                           name="estimated_completion_date"
                                           value="{{ old('estimated_completion_date', $repairTicket->estimated_completion_date?->format('Y-m-d')) }}">
                                    @error('estimated_completion_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="assigned_to" class="form-label">
                                        {{ __('app.repair_tickets.assigned_technician') }}
                                    </label>
                                    <select class="form-select @error('assigned_to') is-invalid @enderror"
                                            id="assigned_to"
                                            name="assigned_to">
                                        <option value="">{{ __('app.repair_tickets.unassigned') }}</option>
                                        @foreach(\App\Models\User::orderBy('name')->get() as $user)
                                            <option value="{{ $user->id }}"
                                                    {{ (old('assigned_to', $repairTicket->assigned_to) == $user->id) ? 'selected' : '' }}>
                                                {{ $user->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('assigned_to')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Cost Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-currency-dollar"></i> {{ __('app.repair_tickets.cost_information') }}
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="estimated_cost" class="form-label">
                                        {{ __('app.repair_tickets.estimated_cost') }}
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">{{ __('app.common.currency_symbol') }}</span>
                                        <input type="number"
                                               class="form-control @error('estimated_cost') is-invalid @enderror"
                                               id="estimated_cost"
                                               name="estimated_cost"
                                               value="{{ old('estimated_cost', $repairTicket->estimated_cost) }}"
                                               step="0.01"
                                               min="0"
                                               placeholder="{{ __('app.repair_tickets.estimated_cost_placeholder') }}">
                                    </div>
                                    @error('estimated_cost')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="final_cost" class="form-label">
                                        {{ __('app.repair_tickets.final_cost') }}
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">{{ __('app.common.currency_symbol') }}</span>
                                        <input type="number"
                                               class="form-control @error('final_cost') is-invalid @enderror"
                                               id="final_cost"
                                               name="final_cost"
                                               value="{{ old('final_cost', $repairTicket->final_cost) }}"
                                               step="0.01"
                                               min="0"
                                               placeholder="{{ __('app.repair_tickets.final_cost_placeholder') }}">
                                    </div>
                                    @error('final_cost')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Technician Comments -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="technician_comments" class="form-label">
                                        {{ __('app.repair_tickets.technician_comments') }}
                                    </label>
                                    <textarea class="form-control @error('technician_comments') is-invalid @enderror"
                                              id="technician_comments"
                                              name="technician_comments"
                                              rows="4"
                                              placeholder="{{ __('app.repair_tickets.technician_comments_placeholder') }}">{{ old('technician_comments', $repairTicket->technician_comments) }}</textarea>
                                    @error('technician_comments')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <div>
                                <form method="POST"
                                      action="{{ route('repair-tickets.destroy', $repairTicket) }}"
                                      class="d-inline"
                                      onsubmit="return confirm('{{ __('app.repair_tickets.confirm_delete') }}')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger">
                                        <i class="bi bi-trash"></i> {{ __('app.repair_tickets.delete_ticket') }}
                                    </button>
                                </form>
                            </div>

                            <div class="d-flex gap-2">
                                <a href="{{ route('repair-tickets.show', $repairTicket) }}" class="btn btn-secondary">
                                    <i class="bi bi-x-circle"></i> {{ __('app.common.cancel') }}
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> {{ __('app.repair_tickets.update_ticket') }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
