@extends('layouts.app')

@section('title', 'إنشاء بطاقة إصلاح جديدة')

@push('styles')
<link href="{{ asset('css/pattern-lock.css') }}" rel="stylesheet">
<style>
.repair-ticket-header {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    margin-bottom: 1.5rem;
}

.form-section-header {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
    margin: -1px -1px 0 -1px;
}

.form-section-body {
    padding: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.required-field::after {
    content: " *";
    color: #e74a3b;
    font-weight: bold;
}

.help-text {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.btn-submit {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 0.35rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.btn-submit:hover {
    background: linear-gradient(45deg, #20c997, #17a2b8);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    color: white;
}

.alert {
    border-radius: 0.35rem;
    border: none;
    padding: 1rem 1.5rem;
}

@media (max-width: 768px) {
    .repair-ticket-header {
        padding: 1rem;
    }

    .form-section-body {
        padding: 1rem;
    }

    .btn-submit {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
</style>
@endpush

@push('scripts')
<script src="{{ asset('js/pattern-lock.js') }}"></script>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="repair-ticket-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">إنشاء بطاقة إصلاح جديدة</h1>
                <p class="mb-0 opacity-75">إضافة بطاقة إصلاح جديدة للعميل</p>
            </div>
            <div>
                <a href="{{ route('repair-tickets.index') }}" class="btn btn-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة لبطاقات الإصلاح
                </a>
            </div>
        </div>
    </div>

    <!-- Alert Info -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle me-2"></i>
        <strong>تنبيه:</strong> الحقول المميزة بعلامة (*) مطلوبة ويجب ملؤها.
    </div>

    <!-- Form -->
    <form method="POST" action="{{ route('repair-tickets.store') }}">
        @csrf

        <!-- Customer Information -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات العميل
                </h5>
            </div>
            <div class="form-section-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="customer_id" class="form-label required-field">العميل</label>
                        <select class="form-select @error('customer_id') is-invalid @enderror"
                                id="customer_id"
                                name="customer_id"
                                required>
                            <option value="">اختر العميل</option>
                            @foreach($customers as $customer)
                                <option value="{{ $customer->id }}"
                                        {{ (old('customer_id', $selectedCustomer?->id) == $customer->id) ? 'selected' : '' }}>
                                    {{ $customer->name }} - {{ $customer->phone_number }}
                                </option>
                            @endforeach
                        </select>
                        @error('customer_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">
                            <a href="{{ route('customers.create') }}" target="_blank" class="text-decoration-none">
                                <i class="fas fa-plus-circle me-1"></i>إضافة عميل جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Device Information -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-mobile-alt me-2"></i>
                    معلومات الجهاز
                </h5>
            </div>
            <div class="form-section-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="brand_id" class="form-label required-field">الماركة</label>
                        <select class="form-select @error('brand_id') is-invalid @enderror"
                                id="brand_id"
                                name="brand_id"
                                required>
                            <option value="">اختر الماركة</option>
                            @foreach($brands as $brand)
                                <option value="{{ $brand->id }}"
                                        {{ old('brand_id') == $brand->id ? 'selected' : '' }}>
                                    {{ $brand->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('brand_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="device_model" class="form-label required-field">موديل الجهاز</label>
                        <input type="text"
                               class="form-control @error('device_model') is-invalid @enderror"
                               id="device_model"
                               name="device_model"
                               value="{{ old('device_model') }}"
                               required
                               placeholder="مثال: iPhone 14 Pro، Galaxy S23">
                        @error('device_model')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="device_condition_id" class="form-label required-field">حالة الجهاز</label>
                        <select class="form-select @error('device_condition_id') is-invalid @enderror"
                                id="device_condition_id"
                                name="device_condition_id"
                                required>
                            <option value="">اختر حالة الجهاز</option>
                            @foreach($deviceConditions as $condition)
                                <option value="{{ $condition->id }}"
                                        {{ old('device_condition_id') == $condition->id ? 'selected' : '' }}>
                                    {{ $condition->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('device_condition_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Problem & Security -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    المشكلة والأمان
                </h5>
            </div>
            <div class="form-section-body">
                <div class="row">
                    <div class="col-md-8 mb-3">
                        <label for="reported_problem" class="form-label required-field">المشكلة المبلغ عنها</label>
                        <textarea class="form-control @error('reported_problem') is-invalid @enderror"
                                  id="reported_problem"
                                  name="reported_problem"
                                  rows="3"
                                  required
                                  placeholder="اوصف المشكلة بالتفصيل...">{{ old('reported_problem') }}</textarea>
                        @error('reported_problem')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">اكتب وصفاً مفصلاً للمشكلة التي يواجهها العميل</div>
                    </div>

                    {{-- Security Pattern/Password Section --}}
                    <div class="col-12">
                        <x-pattern-selector />
                    </div>
                </div>
            </div>
        </div>

        <!-- Repair Details -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    تفاصيل الإصلاح
                </h5>
            </div>
            <div class="form-section-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="repair_status_id" class="form-label required-field">الحالة الأولية</label>
                        <select class="form-select @error('repair_status_id') is-invalid @enderror"
                                id="repair_status_id"
                                name="repair_status_id"
                                required>
                            @foreach($repairStatuses as $status)
                                <option value="{{ $status->id }}"
                                        {{ old('repair_status_id', 1) == $status->id ? 'selected' : '' }}
                                        style="color: {{ $status->color }}">
                                    {{ $status->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('repair_status_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="received_date" class="form-label required-field">تاريخ الاستلام</label>
                        <input type="date"
                               class="form-control @error('received_date') is-invalid @enderror"
                               id="received_date"
                               name="received_date"
                               value="{{ old('received_date', date('Y-m-d')) }}"
                               required>
                        @error('received_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="estimated_completion_date" class="form-label">تاريخ الإنجاز المتوقع</label>
                        <input type="date"
                               class="form-control @error('estimated_completion_date') is-invalid @enderror"
                               id="estimated_completion_date"
                               name="estimated_completion_date"
                               value="{{ old('estimated_completion_date') }}">
                        @error('estimated_completion_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">سيتم تعيينه تلقائياً بعد 3 أيام من تاريخ الاستلام</div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="estimated_cost" class="form-label">التكلفة المقدرة</label>
                        <div class="input-group">
                            <input type="number"
                                   class="form-control @error('estimated_cost') is-invalid @enderror"
                                   id="estimated_cost"
                                   name="estimated_cost"
                                   value="{{ old('estimated_cost') }}"
                                   step="0.01"
                                   min="0"
                                   placeholder="0.00">
                            <span class="input-group-text">ريال</span>
                        </div>
                        @error('estimated_cost')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">التكلفة التقديرية للإصلاح</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technician Comments -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-comment me-2"></i>
                    تعليقات الفني
                </h5>
            </div>
            <div class="form-section-body">
                <div class="mb-3">
                    <label for="technician_comments" class="form-label">ملاحظات الفني</label>
                    <textarea class="form-control @error('technician_comments') is-invalid @enderror"
                              id="technician_comments"
                              name="technician_comments"
                              rows="3"
                              placeholder="التقييم الأولي، الملاحظات، أو التعليمات الخاصة...">{{ old('technician_comments') }}</textarea>
                    @error('technician_comments')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <div class="help-text">أي ملاحظات أو تعليمات خاصة للفني</div>
                </div>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="text-center mt-4">
            <button type="submit" class="btn-submit">
                <i class="fas fa-save me-2"></i>إنشاء بطاقة الإصلاح
            </button>
            <a href="{{ route('repair-tickets.index') }}" class="btn btn-secondary ms-2">
                <i class="fas fa-times me-2"></i>إلغاء
            </a>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-set estimated completion date when received date changes
    document.getElementById('received_date').addEventListener('change', function() {
        const receivedDate = new Date(this.value);
        const estimatedDate = new Date(receivedDate);
        estimatedDate.setDate(estimatedDate.getDate() + 3); // Add 3 days by default

        const estimatedInput = document.getElementById('estimated_completion_date');
        if (!estimatedInput.value) {
            estimatedInput.value = estimatedDate.toISOString().split('T')[0];
        }
    });

    // Customer selection handler
    document.getElementById('customer_id').addEventListener('change', function() {
        // Could add AJAX to load customer details if needed
        if (this.value) {
            console.log('تم اختيار العميل:', this.options[this.selectedIndex].text);
        }
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const requiredFields = document.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('is-invalid');
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }

        return true;
    });

    // Auto-focus on first input
    const firstInput = document.querySelector('#customer_id');
    if (firstInput) {
        firstInput.focus();
    }
});
</script>
@endpush
