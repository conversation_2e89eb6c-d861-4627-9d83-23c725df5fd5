<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Carbon\Carbon;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_number',
        'customer_id',
        'repair_ticket_id',
        'invoice_date',
        'due_date',
        'subtotal',
        'tax_rate',
        'tax_amount',
        'discount_percentage',
        'discount_amount',
        'total_amount',
        'payment_status',
        'paid_amount',
        'paid_date',
        'payment_methods',
        'status',
        'customer_name',
        'customer_phone',
        'customer_email',
        'customer_address',
        'customer_tax_number',
        'company_name',
        'company_phone',
        'company_email',
        'company_address',
        'company_tax_number',
        'company_commercial_register',
        'notes',
        'terms_conditions',
        'currency',
        'created_by',
        'sent_at',
        'viewed_at'
    ];

    protected $casts = [
        'invoice_date' => 'date',
        'due_date' => 'date',
        'paid_date' => 'date',
        'sent_at' => 'datetime',
        'viewed_at' => 'datetime',
        'payment_methods' => 'array',
        'subtotal' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2'
    ];

    // العلاقات
    public function repairTicket(): BelongsTo
    {
        return $this->belongsTo(RepairTicket::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors & Mutators
    protected function statusDisplay(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->status) {
                'draft' => 'مسودة',
                'sent' => 'مرسلة',
                'paid' => 'مدفوعة',
                'partially_paid' => 'مدفوعة جزئياً',
                'overdue' => 'متأخرة',
                'cancelled' => 'ملغية',
                default => $this->status
            }
        );
    }

    protected function statusColor(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->status) {
                'draft' => 'secondary',
                'sent' => 'info',
                'paid' => 'success',
                'partially_paid' => 'warning',
                'overdue' => 'danger',
                'cancelled' => 'dark',
                default => 'secondary'
            }
        );
    }

    protected function isOverdue(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->due_date < now() && $this->status !== 'paid' && $this->status !== 'cancelled'
        );
    }

    protected function daysOverdue(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->is_overdue ? now()->diffInDays($this->due_date) : 0
        );
    }

    // Scopes
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopePartiallyPaid($query)
    {
        return $query->where('status', 'partially_paid');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue')
                    ->orWhere(function($q) {
                        $q->where('due_date', '<', now())
                          ->whereNotIn('status', ['paid', 'cancelled']);
                    });
    }

    public function scopeUnpaid($query)
    {
        return $query->whereIn('status', ['sent', 'partially_paid', 'overdue']);
    }

    // Methods
    public function generateInvoiceNumber(): string
    {
        $settings = InvoiceSetting::getSettings();
        $prefix = $settings['invoice_prefix'] ?? 'INV';
        $length = $settings['invoice_number_length'] ?? 6;

        $lastInvoice = static::latest('id')->first();
        $nextNumber = $lastInvoice ? $lastInvoice->id + 1 : 1;

        return $prefix . '-' . str_pad($nextNumber, $length, '0', STR_PAD_LEFT);
    }

    public function calculateTotals(): void
    {
        $this->subtotal = $this->items->sum('total_price');
        $this->tax_amount = $this->subtotal * ($this->tax_rate / 100);
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        $this->remaining_amount = $this->total_amount - $this->paid_amount;

        $this->updateStatus();
    }

    public function updateStatus(): void
    {
        if ($this->paid_amount >= $this->total_amount) {
            $this->status = 'paid';
        } elseif ($this->paid_amount > 0) {
            $this->status = 'partially_paid';
        } elseif ($this->is_overdue) {
            $this->status = 'overdue';
        }
    }

    public function addPayment(float $amount, string $method, array $details = []): Payment
    {
        $payment = $this->payments()->create([
            'payment_number' => Payment::generatePaymentNumber(),
            'customer_id' => $this->customer_id,
            'amount' => $amount,
            'payment_method' => $method,
            'payment_date' => now(),
            'received_by' => auth()->id(),
            'payment_details' => $details
        ]);

        $this->paid_amount += $amount;
        $this->calculateTotals();
        $this->save();

        return $payment;
    }

    public function canEdit(): bool
    {
        return in_array($this->status, ['draft', 'sent']);
    }

    public function canDelete(): bool
    {
        return $this->status === 'draft' && $this->payments->isEmpty();
    }

    public function canSend(): bool
    {
        return in_array($this->status, ['draft']) && $this->items->isNotEmpty();
    }
}
