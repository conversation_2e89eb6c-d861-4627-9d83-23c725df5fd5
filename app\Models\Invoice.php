<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Events\InvoiceCreated;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Carbon\Carbon;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_number',
        'customer_id',
        'repair_ticket_id',
        'invoice_date',
        'due_date',
        'subtotal',
        'tax_rate',
        'tax_amount',
        'discount_percentage',
        'discount_amount',
        'total_amount',
        'payment_status',
        'paid_amount',
        'paid_date',
        'payment_methods',
        'status',
        'customer_name',
        'customer_phone',
        'customer_email',
        'customer_address',
        'customer_tax_number',
        'company_name',
        'company_phone',
        'company_email',
        'company_address',
        'company_tax_number',
        'company_commercial_register',
        'notes',
        'terms_conditions',
        'currency',
        'created_by',
        'sent_at',
        'viewed_at',
        // New sales management fields
        'sales_channel',
        'cost_of_goods',
        'profit_amount',
        'profit_margin_percentage',
        'payment_terms',
        'sales_metrics',
        'first_payment_date',
        'days_to_payment'
    ];

    protected $casts = [
        'invoice_date' => 'date',
        'due_date' => 'date',
        'paid_date' => 'date',
        'sent_at' => 'datetime',
        'viewed_at' => 'datetime',
        'payment_methods' => 'array',
        'subtotal' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        // New sales management fields
        'cost_of_goods' => 'decimal:2',
        'profit_amount' => 'decimal:2',
        'profit_margin_percentage' => 'decimal:2',
        'sales_metrics' => 'array',
        'first_payment_date' => 'datetime'
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::created(function ($invoice) {
            // Dispatch invoice created event for WhatsApp notifications
            InvoiceCreated::dispatch($invoice);
        });
    }

    // العلاقات
    public function repairTicket(): BelongsTo
    {
        return $this->belongsTo(RepairTicket::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors & Mutators
    protected function statusDisplay(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->status) {
                'draft' => 'مسودة',
                'sent' => 'مرسلة',
                'paid' => 'مدفوعة',
                'partially_paid' => 'مدفوعة جزئياً',
                'overdue' => 'متأخرة',
                'cancelled' => 'ملغية',
                default => $this->status
            }
        );
    }

    protected function statusColor(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->status) {
                'draft' => 'secondary',
                'sent' => 'info',
                'paid' => 'success',
                'partially_paid' => 'warning',
                'overdue' => 'danger',
                'cancelled' => 'dark',
                default => 'secondary'
            }
        );
    }

    protected function isOverdue(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->due_date < now() && $this->status !== 'paid' && $this->status !== 'cancelled'
        );
    }

    protected function daysOverdue(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->is_overdue ? now()->diffInDays($this->due_date) : 0
        );
    }

    // Scopes
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopePartiallyPaid($query)
    {
        return $query->where('status', 'partially_paid');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue')
                    ->orWhere(function($q) {
                        $q->where('due_date', '<', now())
                          ->whereNotIn('status', ['paid', 'cancelled']);
                    });
    }

    public function scopeUnpaid($query)
    {
        return $query->whereIn('status', ['sent', 'partially_paid', 'overdue']);
    }

    // Methods
    public function generateInvoiceNumber(): string
    {
        $settings = InvoiceSetting::getSettings();
        $prefix = $settings['invoice_prefix'] ?? 'INV';
        $length = $settings['invoice_number_length'] ?? 6;

        $lastInvoice = static::latest('id')->first();
        $nextNumber = $lastInvoice ? $lastInvoice->id + 1 : 1;

        return $prefix . '-' . str_pad($nextNumber, $length, '0', STR_PAD_LEFT);
    }

    public function calculateTotals(): void
    {
        $this->subtotal = $this->items->sum('total_price');
        $this->tax_amount = $this->subtotal * ($this->tax_rate / 100);
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        $this->remaining_amount = $this->total_amount - $this->paid_amount;

        // Calculate cost of goods and profit
        $this->calculateProfitMetrics();

        $this->updateStatus();
    }

    /**
     * Calculate profit metrics for sales analytics.
     */
    public function calculateProfitMetrics(): void
    {
        $totalCost = 0;

        foreach ($this->items as $item) {
            if ($item->item_type === 'part' && $item->inventoryItem) {
                $totalCost += $item->quantity * $item->inventoryItem->cost_price;
            } elseif ($item->item_type === 'service') {
                // For services, assume 30% cost ratio if not specified
                $totalCost += $item->total_price * 0.30;
            }
        }

        $this->cost_of_goods = $totalCost;
        $this->profit_amount = $this->total_amount - $totalCost;
        $this->profit_margin_percentage = $this->total_amount > 0
            ? ($this->profit_amount / $this->total_amount) * 100
            : 0;
    }

    public function updateStatus(): void
    {
        if ($this->paid_amount >= $this->total_amount) {
            $this->status = 'paid';
        } elseif ($this->paid_amount > 0) {
            $this->status = 'partially_paid';
        } elseif ($this->is_overdue) {
            $this->status = 'overdue';
        }
    }

    public function addPayment(float $amount, string $method, array $details = []): Payment
    {
        $payment = $this->payments()->create([
            'payment_number' => Payment::generatePaymentNumber(),
            'customer_id' => $this->customer_id,
            'amount' => $amount,
            'payment_method' => $method,
            'payment_date' => now(),
            'received_by' => auth()->id(),
            'payment_details' => $details
        ]);

        $this->paid_amount += $amount;

        // Track first payment date and days to payment for analytics
        if (!$this->first_payment_date) {
            $this->first_payment_date = now();
            $this->days_to_payment = $this->invoice_date->diffInDays(now());
        }

        $this->calculateTotals();
        $this->save();

        // Update customer payment summary
        CustomerPaymentSummary::updateForCustomer($this->customer_id);

        return $payment;
    }

    public function canEdit(): bool
    {
        return in_array($this->status, ['draft', 'sent']);
    }

    public function canDelete(): bool
    {
        return $this->status === 'draft' && $this->payments->isEmpty();
    }

    public function canSend(): bool
    {
        return in_array($this->status, ['draft']) && $this->items->isNotEmpty();
    }

    /**
     * Get sales channel display name.
     */
    public function getSalesChannelDisplayAttribute(): string
    {
        $channels = [
            'repair_shop' => app()->getLocale() === 'ar' ? 'ورشة الإصلاح' : 'Repair Shop',
            'pos' => app()->getLocale() === 'ar' ? 'نقطة البيع' : 'Point of Sale',
            'online' => app()->getLocale() === 'ar' ? 'أونلاين' : 'Online',
            'phone' => app()->getLocale() === 'ar' ? 'هاتف' : 'Phone',
        ];

        return $channels[$this->sales_channel] ?? $this->sales_channel;
    }

    /**
     * Get payment terms display name.
     */
    public function getPaymentTermsDisplayAttribute(): string
    {
        $terms = [
            'immediate' => app()->getLocale() === 'ar' ? 'فوري' : 'Immediate',
            'net_15' => app()->getLocale() === 'ar' ? '15 يوم' : 'Net 15',
            'net_30' => app()->getLocale() === 'ar' ? '30 يوم' : 'Net 30',
            'net_60' => app()->getLocale() === 'ar' ? '60 يوم' : 'Net 60',
        ];

        return $terms[$this->payment_terms] ?? $this->payment_terms;
    }

    /**
     * Check if invoice is profitable.
     */
    public function isProfitable(): bool
    {
        return $this->profit_amount > 0;
    }

    /**
     * Get profit margin category.
     */
    public function getProfitMarginCategoryAttribute(): string
    {
        if ($this->profit_margin_percentage >= 50) {
            return 'high';
        } elseif ($this->profit_margin_percentage >= 25) {
            return 'medium';
        } elseif ($this->profit_margin_percentage >= 10) {
            return 'low';
        } else {
            return 'very_low';
        }
    }

    /**
     * Get profit margin color for UI.
     */
    public function getProfitMarginColorAttribute(): string
    {
        return match ($this->profit_margin_category) {
            'high' => 'success',
            'medium' => 'primary',
            'low' => 'warning',
            'very_low' => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Scope for sales channel.
     */
    public function scopeBySalesChannel($query, string $channel)
    {
        return $query->where('sales_channel', $channel);
    }

    /**
     * Scope for profitable invoices.
     */
    public function scopeProfitable($query)
    {
        return $query->where('profit_amount', '>', 0);
    }

    /**
     * Scope for high margin invoices.
     */
    public function scopeHighMargin($query, float $minMargin = 25)
    {
        return $query->where('profit_margin_percentage', '>=', $minMargin);
    }

    /**
     * Generate sales analytics for this invoice.
     */
    public function generateAnalytics(): void
    {
        // Update daily analytics
        SalesAnalytics::generateAnalytics($this->invoice_date, 'daily');

        // Update weekly analytics
        SalesAnalytics::generateAnalytics($this->invoice_date, 'weekly');

        // Update monthly analytics
        SalesAnalytics::generateAnalytics($this->invoice_date, 'monthly');
    }
}
