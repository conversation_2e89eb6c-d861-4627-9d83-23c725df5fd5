<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CashDrawerOperation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'terminal_id',
        'operation_type',
        'amount',
        'balance_before',
        'balance_after',
        'reason',
        'notes',
        'performed_by',
        'operation_date',
        'denomination_breakdown',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
        'operation_date' => 'datetime',
        'denomination_breakdown' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the terminal that owns this operation.
     */
    public function terminal(): BelongsTo
    {
        return $this->belongsTo(PosTerminal::class, 'terminal_id');
    }

    /**
     * Get the user who performed this operation.
     */
    public function performedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'performed_by');
    }

    /**
     * Get operation type display name.
     */
    public function getOperationTypeDisplayAttribute(): string
    {
        $types = [
            'open_shift' => app()->getLocale() === 'ar' ? 'فتح الوردية' : 'Open Shift',
            'close_shift' => app()->getLocale() === 'ar' ? 'إغلاق الوردية' : 'Close Shift',
            'cash_in' => app()->getLocale() === 'ar' ? 'إضافة نقد' : 'Cash In',
            'cash_out' => app()->getLocale() === 'ar' ? 'سحب نقد' : 'Cash Out',
            'count' => app()->getLocale() === 'ar' ? 'عد النقد' : 'Cash Count',
            'adjustment' => app()->getLocale() === 'ar' ? 'تعديل' : 'Adjustment',
        ];

        return $types[$this->operation_type] ?? $this->operation_type;
    }

    /**
     * Get operation type color for UI.
     */
    public function getOperationTypeColorAttribute(): string
    {
        return match ($this->operation_type) {
            'open_shift' => 'success',
            'close_shift' => 'info',
            'cash_in' => 'primary',
            'cash_out' => 'warning',
            'count' => 'secondary',
            'adjustment' => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Get operation type icon.
     */
    public function getOperationTypeIconAttribute(): string
    {
        return match ($this->operation_type) {
            'open_shift' => 'bi bi-unlock',
            'close_shift' => 'bi bi-lock',
            'cash_in' => 'bi bi-plus-circle',
            'cash_out' => 'bi bi-dash-circle',
            'count' => 'bi bi-calculator',
            'adjustment' => 'bi bi-gear',
            default => 'bi bi-cash',
        };
    }

    /**
     * Create cash in operation.
     */
    public static function createCashIn(
        PosTerminal $terminal,
        User $user,
        float $amount,
        string $reason = null,
        array $denominationBreakdown = null
    ): self {
        $balanceBefore = $terminal->cash_drawer_balance;
        $balanceAfter = $balanceBefore + $amount;

        return static::create([
            'terminal_id' => $terminal->id,
            'operation_type' => 'cash_in',
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'reason' => $reason ?? 'Cash added to drawer',
            'performed_by' => $user->id,
            'operation_date' => now(),
            'denomination_breakdown' => $denominationBreakdown,
        ]);
    }

    /**
     * Create cash out operation.
     */
    public static function createCashOut(
        PosTerminal $terminal,
        User $user,
        float $amount,
        string $reason = null,
        array $denominationBreakdown = null
    ): self {
        $balanceBefore = $terminal->cash_drawer_balance;
        $balanceAfter = $balanceBefore - $amount;

        return static::create([
            'terminal_id' => $terminal->id,
            'operation_type' => 'cash_out',
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'reason' => $reason ?? 'Cash removed from drawer',
            'performed_by' => $user->id,
            'operation_date' => now(),
            'denomination_breakdown' => $denominationBreakdown,
        ]);
    }

    /**
     * Create cash count operation.
     */
    public static function createCashCount(
        PosTerminal $terminal,
        User $user,
        float $countedAmount,
        array $denominationBreakdown = null,
        string $notes = null
    ): self {
        $expectedBalance = $terminal->cash_drawer_balance;
        $difference = $countedAmount - $expectedBalance;

        return static::create([
            'terminal_id' => $terminal->id,
            'operation_type' => 'count',
            'amount' => $countedAmount,
            'balance_before' => $expectedBalance,
            'balance_after' => $countedAmount,
            'reason' => 'Cash count performed',
            'notes' => $notes . ($difference != 0 ? " (Difference: {$difference})" : ""),
            'performed_by' => $user->id,
            'operation_date' => now(),
            'denomination_breakdown' => $denominationBreakdown,
        ]);
    }

    /**
     * Create adjustment operation.
     */
    public static function createAdjustment(
        PosTerminal $terminal,
        User $user,
        float $adjustmentAmount,
        string $reason,
        string $notes = null
    ): self {
        $balanceBefore = $terminal->cash_drawer_balance;
        $balanceAfter = $balanceBefore + $adjustmentAmount;

        return static::create([
            'terminal_id' => $terminal->id,
            'operation_type' => 'adjustment',
            'amount' => $adjustmentAmount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'reason' => $reason,
            'notes' => $notes,
            'performed_by' => $user->id,
            'operation_date' => now(),
        ]);
    }

    /**
     * Get balance change amount.
     */
    public function getBalanceChangeAttribute(): float
    {
        return $this->balance_after - $this->balance_before;
    }

    /**
     * Check if operation increased balance.
     */
    public function isIncrease(): bool
    {
        return $this->balance_change > 0;
    }

    /**
     * Check if operation decreased balance.
     */
    public function isDecrease(): bool
    {
        return $this->balance_change < 0;
    }

    /**
     * Get formatted denomination breakdown.
     */
    public function getFormattedDenominationBreakdownAttribute(): array
    {
        if (!$this->denomination_breakdown) {
            return [];
        }

        $formatted = [];
        foreach ($this->denomination_breakdown as $denomination => $count) {
            $formatted[] = [
                'denomination' => $denomination,
                'count' => $count,
                'total' => $denomination * $count,
                'display' => app()->getLocale() === 'ar' ? 
                    "{$count} × {$denomination} = " . ($denomination * $count) :
                    "{$count} × {$denomination} = " . ($denomination * $count)
            ];
        }

        return $formatted;
    }

    /**
     * Validate denomination breakdown.
     */
    public function validateDenominationBreakdown(): bool
    {
        if (!$this->denomination_breakdown) {
            return true; // No breakdown to validate
        }

        $calculatedTotal = 0;
        foreach ($this->denomination_breakdown as $denomination => $count) {
            $calculatedTotal += $denomination * $count;
        }

        return abs($calculatedTotal - abs($this->amount)) < 0.01; // Allow for rounding
    }

    /**
     * Get operation summary.
     */
    public function getSummary(): array
    {
        return [
            'operation_info' => [
                'type' => $this->operation_type_display,
                'date' => $this->operation_date,
                'performed_by' => $this->performedBy->name,
                'terminal' => $this->terminal->terminal_name,
            ],
            'financial_impact' => [
                'amount' => $this->amount,
                'balance_before' => $this->balance_before,
                'balance_after' => $this->balance_after,
                'balance_change' => $this->balance_change,
            ],
            'details' => [
                'reason' => $this->reason,
                'notes' => $this->notes,
                'denomination_breakdown' => $this->formatted_denomination_breakdown,
            ]
        ];
    }

    /**
     * Scope for operations by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('operation_type', $type);
    }

    /**
     * Scope for operations by terminal.
     */
    public function scopeByTerminal($query, $terminalId)
    {
        return $query->where('terminal_id', $terminalId);
    }

    /**
     * Scope for operations by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('performed_by', $userId);
    }

    /**
     * Scope for today's operations.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('operation_date', today());
    }

    /**
     * Scope for operations in date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('operation_date', [$startDate, $endDate]);
    }

    /**
     * Scope for cash in operations.
     */
    public function scopeCashIn($query)
    {
        return $query->where('operation_type', 'cash_in');
    }

    /**
     * Scope for cash out operations.
     */
    public function scopeCashOut($query)
    {
        return $query->where('operation_type', 'cash_out');
    }

    /**
     * Get operations summary for a collection.
     */
    public static function getOperationsSummary($operations): array
    {
        $summary = [
            'total_operations' => $operations->count(),
            'by_type' => [],
            'total_cash_in' => 0,
            'total_cash_out' => 0,
            'net_change' => 0,
        ];

        foreach ($operations as $operation) {
            $type = $operation->operation_type;
            
            if (!isset($summary['by_type'][$type])) {
                $summary['by_type'][$type] = [
                    'count' => 0,
                    'total_amount' => 0,
                    'display_name' => $operation->operation_type_display
                ];
            }
            
            $summary['by_type'][$type]['count']++;
            $summary['by_type'][$type]['total_amount'] += abs($operation->amount);

            if ($operation->isIncrease()) {
                $summary['total_cash_in'] += $operation->balance_change;
            } else {
                $summary['total_cash_out'] += abs($operation->balance_change);
            }
        }

        $summary['net_change'] = $summary['total_cash_in'] - $summary['total_cash_out'];

        return $summary;
    }
}
