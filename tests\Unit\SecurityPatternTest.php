<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\RepairTicket;
use App\Models\Customer;
use App\Models\Brand;
use App\Models\RepairStatus;
use App\Models\DeviceCondition;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SecurityPatternTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_check_if_has_security_pattern()
    {
        // Create necessary related models
        $customer = Customer::create(['name' => 'Test Customer', 'phone_number' => '123456789']);
        $brand = Brand::create(['name' => 'Test Brand']);
        $status = RepairStatus::create(['name' => 'Test Status', 'color' => '#000000']);
        $condition = DeviceCondition::create(['name' => 'Test Condition']);
        $user = User::factory()->create();

        // Create ticket with security pattern
        $ticketWithPattern = RepairTicket::create([
            'ticket_number' => 'NJ20250730001',
            'customer_id' => $customer->id,
            'brand_id' => $brand->id,
            'device_model' => 'iPhone 13',
            'reported_problem' => 'Test problem',
            'device_pattern' => true,
            'security_pattern' => '123456',
            'device_condition_id' => $condition->id,
            'repair_status_id' => $status->id,
            'received_date' => now(),
            'created_by' => $user->id,
        ]);

        // Create ticket without security pattern
        $ticketWithoutPattern = RepairTicket::create([
            'ticket_number' => 'NJ20250730002',
            'customer_id' => $customer->id,
            'brand_id' => $brand->id,
            'device_model' => 'iPhone 12',
            'reported_problem' => 'Test problem 2',
            'device_pattern' => false,
            'security_pattern' => null,
            'device_condition_id' => $condition->id,
            'repair_status_id' => $status->id,
            'received_date' => now(),
            'created_by' => $user->id,
        ]);

        $this->assertTrue($ticketWithPattern->hasSecurityPattern());
        $this->assertFalse($ticketWithoutPattern->hasSecurityPattern());
    }

    /** @test */
    public function it_can_get_masked_security_pattern()
    {
        // Create necessary related models
        $customer = Customer::create(['name' => 'Test Customer', 'phone_number' => '123456789']);
        $brand = Brand::create(['name' => 'Test Brand 2']);
        $status = RepairStatus::create(['name' => 'Test Status 2', 'color' => '#000000']);
        $condition = DeviceCondition::create(['name' => 'Test Condition 2']);
        $user = User::factory()->create();

        $ticket = RepairTicket::create([
            'ticket_number' => 'NJ20250730003',
            'customer_id' => $customer->id,
            'brand_id' => $brand->id,
            'device_model' => 'iPhone 13',
            'reported_problem' => 'Test problem',
            'device_pattern' => true,
            'security_pattern' => '123456',
            'device_condition_id' => $condition->id,
            'repair_status_id' => $status->id,
            'received_date' => now(),
            'created_by' => $user->id,
        ]);

        $masked = $ticket->getMaskedSecurityPattern();
        $this->assertEquals('1****6', $masked);
    }

    /** @test */
    public function it_returns_no_pattern_message_when_empty()
    {
        // Create necessary related models
        $customer = Customer::create(['name' => 'Test Customer', 'phone_number' => '123456789']);
        $brand = Brand::create(['name' => 'Test Brand 3']);
        $status = RepairStatus::create(['name' => 'Test Status 3', 'color' => '#000000']);
        $condition = DeviceCondition::create(['name' => 'Test Condition 3']);
        $user = User::factory()->create();

        $ticket = RepairTicket::create([
            'ticket_number' => 'NJ20250730004',
            'customer_id' => $customer->id,
            'brand_id' => $brand->id,
            'device_model' => 'iPhone 13',
            'reported_problem' => 'Test problem',
            'device_pattern' => false,
            'security_pattern' => null,
            'device_condition_id' => $condition->id,
            'repair_status_id' => $status->id,
            'received_date' => now(),
            'created_by' => $user->id,
        ]);

        $this->assertEquals('لا يوجد', $ticket->getMaskedSecurityPattern());
    }

    /** @test */
    public function security_pattern_is_encrypted_in_database()
    {
        // Create necessary related models
        $customer = Customer::create(['name' => 'Test Customer', 'phone_number' => '123456789']);
        $brand = Brand::create(['name' => 'Test Brand 4']);
        $status = RepairStatus::create(['name' => 'Test Status 4', 'color' => '#000000']);
        $condition = DeviceCondition::create(['name' => 'Test Condition 4']);
        $user = User::factory()->create();

        $originalPattern = '123456';
        $ticket = RepairTicket::create([
            'ticket_number' => 'NJ20250730005',
            'customer_id' => $customer->id,
            'brand_id' => $brand->id,
            'device_model' => 'iPhone 13',
            'reported_problem' => 'Test problem',
            'device_pattern' => true,
            'security_pattern' => $originalPattern,
            'device_condition_id' => $condition->id,
            'repair_status_id' => $status->id,
            'received_date' => now(),
            'created_by' => $user->id,
        ]);

        // The pattern should be encrypted in the database
        $rawValue = $ticket->getAttributes()['security_pattern'];
        $this->assertNotEquals($originalPattern, $rawValue);

        // But should be decrypted when accessed through the model
        $this->assertEquals($originalPattern, $ticket->security_pattern);
    }
}
