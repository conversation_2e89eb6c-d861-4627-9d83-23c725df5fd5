<?php

namespace App\Http\Controllers;

use App\Models\PosTerminal;
use App\Models\PosTransaction;
use App\Models\PosTransactionItem;
use App\Models\PosTransactionPayment;
use App\Models\PosSession;
use App\Models\BarcodeInventory;
use App\Models\InventoryItem;
use App\Models\Customer;
use App\Models\PaymentMethodConfig;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class PosController extends Controller
{
    /**
     * Display POS dashboard.
     */
    public function dashboard(Request $request): View
    {
        $user = Auth::user();
        
        // Get available terminals
        $terminals = PosTerminal::active()
            ->with(['assignedUser', 'currentSession'])
            ->orderBy('terminal_name')
            ->get();

        // Get user's assigned terminal or allow selection
        $selectedTerminal = null;
        if ($request->filled('terminal_id')) {
            $selectedTerminal = PosTerminal::find($request->terminal_id);
        } else {
            $selectedTerminal = $terminals->where('assigned_user_id', $user->id)->first();
        }

        // Get today's statistics
        $todayStats = $this->getTodayStatistics($selectedTerminal);

        // Get recent transactions
        $recentTransactions = [];
        if ($selectedTerminal) {
            $recentTransactions = PosTransaction::byTerminal($selectedTerminal->id)
                ->today()
                ->with(['customer', 'cashier', 'items'])
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();
        }

        return view('pos.dashboard', compact(
            'terminals',
            'selectedTerminal',
            'todayStats',
            'recentTransactions'
        ));
    }

    /**
     * Display POS interface.
     */
    public function interface(Request $request, PosTerminal $terminal): View
    {
        $user = Auth::user();

        // Check if terminal is available
        if (!$terminal->is_active) {
            return redirect()->route('pos.dashboard')
                ->with('error', 'Terminal is not active');
        }

        // Check if user has access to this terminal
        if ($terminal->assigned_user_id && $terminal->assigned_user_id !== $user->id) {
            return redirect()->route('pos.dashboard')
                ->with('error', 'You do not have access to this terminal');
        }

        // Check if terminal has an active session
        $session = $terminal->currentSession;
        if (!$session) {
            return redirect()->route('pos.open-shift', $terminal)
                ->with('info', 'Please open a shift to start using the POS');
        }

        // Get current transaction or create new one
        $transaction = null;
        if ($request->filled('transaction_id')) {
            $transaction = PosTransaction::find($request->transaction_id);
            if ($transaction && $transaction->terminal_id !== $terminal->id) {
                $transaction = null;
            }
        }

        // Get payment methods
        $paymentMethods = PaymentMethodConfig::getActivePaymentMethods();

        // Get quick sale templates
        $quickSaleTemplates = \App\Models\PosQuickSaleTemplate::active()
            ->orderBy('sort_order')
            ->orderBy('template_name')
            ->get();

        // Get recent items for quick access
        $recentItems = $this->getRecentItems($terminal);

        return view('pos.interface', compact(
            'terminal',
            'session',
            'transaction',
            'paymentMethods',
            'quickSaleTemplates',
            'recentItems'
        ));
    }

    /**
     * Open shift for terminal.
     */
    public function openShift(Request $request, PosTerminal $terminal): View|RedirectResponse
    {
        if ($request->isMethod('POST')) {
            $validated = $request->validate([
                'opening_cash' => 'required|numeric|min:0',
                'notes' => 'nullable|string|max:1000'
            ]);

            try {
                $session = $terminal->openShift(Auth::user(), $validated['opening_cash']);
                
                if ($validated['notes']) {
                    $session->update(['notes' => $validated['notes']]);
                }

                return redirect()->route('pos.interface', $terminal)
                    ->with('success', 'Shift opened successfully');
            } catch (\Exception $e) {
                return back()->with('error', $e->getMessage());
            }
        }

        return view('pos.open-shift', compact('terminal'));
    }

    /**
     * Close shift for terminal.
     */
    public function closeShift(Request $request, PosTerminal $terminal): View|RedirectResponse
    {
        $session = $terminal->currentSession;
        
        if (!$session) {
            return redirect()->route('pos.dashboard')
                ->with('error', 'No active shift to close');
        }

        if ($request->isMethod('POST')) {
            $validated = $request->validate([
                'closing_cash' => 'required|numeric|min:0',
                'notes' => 'nullable|string|max:1000'
            ]);

            try {
                $session->close($validated['closing_cash']);
                
                if ($validated['notes']) {
                    $session->update(['notes' => $session->notes . "\n" . $validated['notes']]);
                }

                return redirect()->route('pos.dashboard')
                    ->with('success', 'Shift closed successfully');
            } catch (\Exception $e) {
                return back()->with('error', $e->getMessage());
            }
        }

        // Get shift summary
        $shiftSummary = $session->getSummary();

        return view('pos.close-shift', compact('terminal', 'session', 'shiftSummary'));
    }

    /**
     * Create new transaction.
     */
    public function createTransaction(Request $request, PosTerminal $terminal): JsonResponse
    {
        try {
            $validated = $request->validate([
                'customer_id' => 'nullable|exists:customers,id',
                'customer_name' => 'nullable|string|max:255',
                'customer_phone' => 'nullable|string|max:20'
            ]);

            $transaction = PosTransaction::create([
                'terminal_id' => $terminal->id,
                'customer_id' => $validated['customer_id'] ?? null,
                'customer_name' => $validated['customer_name'] ?? null,
                'customer_phone' => $validated['customer_phone'] ?? null,
                'cashier_id' => Auth::id(),
                'subtotal' => 0,
                'total_amount' => 0,
                'status' => 'pending'
            ]);

            $terminal->updateActivity();

            return response()->json([
                'success' => true,
                'transaction' => $transaction->load(['customer', 'items', 'payments'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Add item to transaction.
     */
    public function addItem(Request $request, PosTransaction $transaction): JsonResponse
    {
        try {
            $validated = $request->validate([
                'barcode' => 'nullable|string',
                'inventory_item_id' => 'nullable|exists:inventory_items,id',
                'item_name' => 'required|string|max:255',
                'unit_price' => 'required|numeric|min:0',
                'quantity' => 'required|numeric|min:0.01',
                'discount_percentage' => 'nullable|numeric|min:0|max:100',
                'discount_amount' => 'nullable|numeric|min:0'
            ]);

            // Create item data
            $itemData = [
                'item_name' => $validated['item_name'],
                'unit_price' => $validated['unit_price'],
                'quantity' => $validated['quantity'],
                'discount_percentage' => $validated['discount_percentage'] ?? 0,
                'discount_amount' => $validated['discount_amount'] ?? 0,
            ];

            // Handle barcode or inventory item
            if ($validated['barcode']) {
                $barcodeData = PosTransactionItem::createFromBarcode($validated['barcode'], $validated['quantity']);
                if ($barcodeData) {
                    $itemData = array_merge($itemData, $barcodeData);
                }
            } elseif ($validated['inventory_item_id']) {
                $inventoryItem = InventoryItem::find($validated['inventory_item_id']);
                if ($inventoryItem) {
                    $inventoryData = PosTransactionItem::createFromInventoryItem($inventoryItem, $validated['quantity']);
                    $itemData = array_merge($itemData, $inventoryData);
                }
            }

            $item = $transaction->addItem($itemData);

            return response()->json([
                'success' => true,
                'item' => $item->load('inventoryItem'),
                'transaction' => $transaction->fresh(['items', 'payments'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Update item quantity.
     */
    public function updateItemQuantity(Request $request, PosTransactionItem $item): JsonResponse
    {
        try {
            $validated = $request->validate([
                'quantity' => 'required|numeric|min:0.01'
            ]);

            $item->updateQuantity($validated['quantity']);

            return response()->json([
                'success' => true,
                'item' => $item->fresh('inventoryItem'),
                'transaction' => $item->transaction->fresh(['items', 'payments'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Remove item from transaction.
     */
    public function removeItem(PosTransactionItem $item): JsonResponse
    {
        try {
            $transaction = $item->transaction;
            $transaction->removeItem($item);

            return response()->json([
                'success' => true,
                'transaction' => $transaction->fresh(['items', 'payments'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Add payment to transaction.
     */
    public function addPayment(Request $request, PosTransaction $transaction): JsonResponse
    {
        try {
            $validated = $request->validate([
                'payment_method' => 'required|exists:payment_method_configs,method_code',
                'amount' => 'required|numeric|min:0.01',
                'cash_received' => 'nullable|numeric|min:0',
                'payment_reference' => 'nullable|string|max:255',
                'card_last_four' => 'nullable|string|size:4',
                'bank_name' => 'nullable|string|max:255',
                'payment_details' => 'nullable|array'
            ]);

            // Create payment data based on method
            $paymentData = match ($validated['payment_method']) {
                'cash' => PosTransactionPayment::createCashPayment(
                    $validated['amount'],
                    $validated['cash_received'] ?? $validated['amount']
                ),
                'card' => PosTransactionPayment::createCardPayment(
                    $validated['amount'],
                    array_filter([
                        'last_four' => $validated['card_last_four'] ?? null,
                        'reference' => $validated['payment_reference'] ?? null,
                    ])
                ),
                'bank_transfer' => PosTransactionPayment::createBankTransferPayment(
                    $validated['amount'],
                    array_filter([
                        'bank_name' => $validated['bank_name'] ?? null,
                        'reference' => $validated['payment_reference'] ?? null,
                    ])
                ),
                default => [
                    'payment_method' => $validated['payment_method'],
                    'amount' => $validated['amount'],
                    'payment_reference' => $validated['payment_reference'] ?? null,
                    'status' => 'completed',
                    'payment_details' => $validated['payment_details'] ?? []
                ]
            };

            $payment = $transaction->addPayment($paymentData);

            return response()->json([
                'success' => true,
                'payment' => $payment,
                'transaction' => $transaction->fresh(['items', 'payments']),
                'change_amount' => $payment->change_amount ?? 0
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Complete transaction.
     */
    public function completeTransaction(PosTransaction $transaction): JsonResponse
    {
        try {
            $transaction->complete();

            return response()->json([
                'success' => true,
                'transaction' => $transaction->fresh(['items', 'payments']),
                'message' => 'Transaction completed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Cancel transaction.
     */
    public function cancelTransaction(Request $request, PosTransaction $transaction): JsonResponse
    {
        try {
            $validated = $request->validate([
                'reason' => 'nullable|string|max:500'
            ]);

            $transaction->cancel($validated['reason'] ?? null);

            return response()->json([
                'success' => true,
                'message' => 'Transaction cancelled successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Search items by barcode or name.
     */
    public function searchItems(Request $request): JsonResponse
    {
        $search = $request->get('q', '');
        
        if (empty($search)) {
            return response()->json(['items' => []]);
        }

        // Search inventory items
        $inventoryItems = InventoryItem::where('is_active', true)
            ->where(function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('sku', 'like', "%{$search}%")
                      ->orWhere('barcode', 'like', "%{$search}%");
            })
            ->with(['category', 'brand'])
            ->limit(20)
            ->get();

        // Search barcode inventory
        $barcodeItems = BarcodeInventory::active()
            ->where(function ($query) use ($search) {
                $query->where('barcode', 'like', "%{$search}%")
                      ->orWhere('item_name', 'like', "%{$search}%");
            })
            ->with('inventoryItem')
            ->limit(10)
            ->get();

        $items = [];

        // Add inventory items
        foreach ($inventoryItems as $item) {
            $items[] = [
                'type' => 'inventory',
                'id' => $item->id,
                'name' => $item->name,
                'sku' => $item->sku,
                'barcode' => $item->barcode,
                'price' => $item->selling_price,
                'cost_price' => $item->cost_price,
                'stock' => $item->current_stock,
                'category' => $item->category?->name,
                'brand' => $item->brand?->name,
            ];
        }

        // Add barcode items
        foreach ($barcodeItems as $item) {
            $items[] = [
                'type' => 'barcode',
                'id' => $item->id,
                'name' => $item->item_name,
                'barcode' => $item->barcode,
                'price' => $item->price,
                'inventory_item_id' => $item->inventory_item_id,
                'stock' => $item->inventoryItem?->current_stock,
            ];
        }

        return response()->json(['items' => $items]);
    }

    /**
     * Get item by barcode.
     */
    public function getItemByBarcode(Request $request): JsonResponse
    {
        $barcode = $request->get('barcode');
        
        if (empty($barcode)) {
            return response()->json([
                'success' => false,
                'message' => 'Barcode is required'
            ], 400);
        }

        // Try barcode inventory first
        $barcodeItem = BarcodeInventory::findByBarcode($barcode);
        if ($barcodeItem) {
            $barcodeItem->recordScan();
            
            return response()->json([
                'success' => true,
                'item' => [
                    'type' => 'barcode',
                    'id' => $barcodeItem->id,
                    'name' => $barcodeItem->item_name,
                    'barcode' => $barcodeItem->barcode,
                    'price' => $barcodeItem->price,
                    'inventory_item_id' => $barcodeItem->inventory_item_id,
                    'stock' => $barcodeItem->inventoryItem?->current_stock,
                ]
            ]);
        }

        // Try inventory items
        $inventoryItem = InventoryItem::where('barcode', $barcode)
            ->where('is_active', true)
            ->first();

        if ($inventoryItem) {
            return response()->json([
                'success' => true,
                'item' => [
                    'type' => 'inventory',
                    'id' => $inventoryItem->id,
                    'name' => $inventoryItem->name,
                    'sku' => $inventoryItem->sku,
                    'barcode' => $inventoryItem->barcode,
                    'price' => $inventoryItem->selling_price,
                    'cost_price' => $inventoryItem->cost_price,
                    'stock' => $inventoryItem->current_stock,
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Item not found'
        ], 404);
    }

    /**
     * Print receipt.
     */
    public function printReceipt(PosTransaction $transaction): JsonResponse
    {
        try {
            $transaction->printReceipt();

            return response()->json([
                'success' => true,
                'receipt_number' => $transaction->receipt_number,
                'message' => 'Receipt printed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get today's statistics for terminal.
     */
    private function getTodayStatistics(?PosTerminal $terminal): array
    {
        if (!$terminal) {
            return [
                'transactions_count' => 0,
                'total_sales' => 0,
                'average_transaction' => 0,
                'payment_breakdown' => []
            ];
        }

        return $terminal->getTodaysSalesSummary();
    }

    /**
     * Get recent items for quick access.
     */
    private function getRecentItems(PosTerminal $terminal): array
    {
        $recentItemIds = PosTransactionItem::join('pos_transactions', 'pos_transaction_items.transaction_id', '=', 'pos_transactions.id')
            ->where('pos_transactions.terminal_id', $terminal->id)
            ->where('pos_transactions.status', 'completed')
            ->whereDate('pos_transactions.transaction_date', '>=', now()->subDays(7))
            ->whereNotNull('pos_transaction_items.inventory_item_id')
            ->select('pos_transaction_items.inventory_item_id')
            ->groupBy('pos_transaction_items.inventory_item_id')
            ->orderByRaw('COUNT(*) DESC')
            ->limit(12)
            ->pluck('inventory_item_id');

        return InventoryItem::whereIn('id', $recentItemIds)
            ->where('is_active', true)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'sku' => $item->sku,
                    'price' => $item->selling_price,
                    'stock' => $item->current_stock,
                ];
            })
            ->toArray();
    }
}
