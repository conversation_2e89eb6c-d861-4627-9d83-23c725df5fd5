@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="bi bi-eye text-primary"></i> عرض قالب واتساب</h2>
                    <p class="text-muted">{{ $template->display_name_ar }}</p>
                </div>
                <div class="btn-group">
                    @if($template->status !== 'APPROVED')
                        <a href="{{ route('whatsapp.templates.edit', $template) }}" class="btn btn-primary">
                            <i class="bi bi-pencil"></i> تعديل
                        </a>
                    @endif
                    <a href="{{ route('whatsapp.templates.duplicate', $template) }}" class="btn btn-outline-secondary">
                        <i class="bi bi-files"></i> نسخ
                    </a>
                    <a href="{{ route('whatsapp.templates.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Template Details -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-info-circle"></i> معلومات القالب</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>الاسم التقني:</strong></td>
                                    <td><code>{{ $template->name }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>اسم واتساب:</strong></td>
                                    <td><code>{{ $template->whatsapp_name }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>الاسم المعروض:</strong></td>
                                    <td>{{ $template->display_name_ar }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الفئة:</strong></td>
                                    <td>
                                        <span class="badge" style="background-color: {{ $template->category->color }}">
                                            <i class="{{ $template->category->icon }}"></i>
                                            {{ $template->category->name_ar }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>اللغة:</strong></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ $template->language === 'ar' ? 'عربي' : 'English' }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ $template->status_color }}">
                                            {{ $template->status }}
                                        </span>
                                        @if($template->quality_rating !== 'UNKNOWN')
                                            <span class="badge bg-{{ $template->quality_color }} ms-1">
                                                {{ $template->quality_rating }}
                                            </span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>نشط:</strong></td>
                                    <td>
                                        @if($template->is_active)
                                            <span class="badge bg-success">نعم</span>
                                        @else
                                            <span class="badge bg-secondary">لا</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td>{{ $template->created_at->format('Y-m-d H:i') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>المنشئ:</strong></td>
                                    <td>{{ $template->creator?->name ?? 'غير محدد' }}</td>
                                </tr>
                                @if($template->approved_at)
                                <tr>
                                    <td><strong>تاريخ الاعتماد:</strong></td>
                                    <td>{{ $template->approved_at->format('Y-m-d H:i') }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                    </div>

                    @if($template->description_ar)
                        <div class="mt-3">
                            <strong>الوصف:</strong>
                            <p class="text-muted">{{ $template->description_ar }}</p>
                        </div>
                    @endif

                    @if($template->rejection_reason)
                        <div class="alert alert-danger mt-3">
                            <strong>سبب الرفض:</strong> {{ $template->rejection_reason }}
                        </div>
                    @endif

                    @if($template->approval_notes)
                        <div class="alert alert-success mt-3">
                            <strong>ملاحظات الموافقة:</strong> {{ $template->approval_notes }}
                        </div>
                    @endif
                </div>
            </div>

            <!-- Template Content -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="bi bi-chat-square-text"></i> محتوى القالب</h5>
                </div>
                <div class="card-body">
                    @if($template->header_text)
                        <div class="mb-3">
                            <strong>نص الرأس:</strong>
                            <div class="border rounded p-2 bg-light">{{ $template->header_text }}</div>
                        </div>
                    @endif

                    <div class="mb-3">
                        <strong>نص الرسالة الرئيسي:</strong>
                        <div class="border rounded p-3 bg-light" style="white-space: pre-wrap;">{{ $template->body_text }}</div>
                    </div>

                    @if($template->footer_text)
                        <div class="mb-3">
                            <strong>نص التذييل:</strong>
                            <div class="border rounded p-2 bg-light">{{ $template->footer_text }}</div>
                        </div>
                    @endif

                    @if($template->buttons)
                        <div class="mb-3">
                            <strong>الأزرار:</strong>
                            <div class="mt-2">
                                @foreach($template->buttons as $button)
                                    <button class="btn btn-outline-primary btn-sm me-2 mb-1" disabled>
                                        {{ $button['text'] }}
                                        <small class="text-muted">({{ $button['type'] }})</small>
                                    </button>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    @if($template->variables)
                        <div class="mb-3">
                            <strong>المتغيرات:</strong>
                            <div class="mt-2">
                                @foreach($template->variables as $variable => $description)
                                    <span class="badge bg-info me-1 mb-1">
                                        @{{ $variable }} - {{ $description }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Usage Statistics -->
            @if($template->usage_count > 0)
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="bi bi-graph-up"></i> إحصائيات الاستخدام</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary">{{ number_format($template->usage_count) }}</h4>
                                <small class="text-muted">إجمالي الاستخدام</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">{{ number_format($template->success_count) }}</h4>
                                <small class="text-muted">نجح</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-danger">{{ number_format($template->failure_count) }}</h4>
                                <small class="text-muted">فشل</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info">{{ $template->success_rate }}%</h4>
                                <small class="text-muted">معدل النجاح</small>
                            </div>
                        </div>
                    </div>

                    @if($template->last_used_at)
                        <div class="mt-3 text-center">
                            <small class="text-muted">آخر استخدام: {{ $template->last_used_at->diffForHumans() }}</small>
                        </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Recent Usage -->
            @if($recentUsage->count() > 0)
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="bi bi-clock-history"></i> الاستخدام الأخير</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                    <th>المصدر</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentUsage as $usage)
                                <tr>
                                    <td>
                                        {{ $usage->customer?->name ?? 'غير محدد' }}
                                        <br>
                                        <small class="text-muted">{{ $usage->customer_phone }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $usage->status === 'delivered' ? 'success' : ($usage->status === 'failed' ? 'danger' : 'warning') }}">
                                            {{ $usage->status_display }}
                                        </span>
                                    </td>
                                    <td>{{ $usage->created_at->format('Y-m-d H:i') }}</td>
                                    <td>{{ $usage->trigger_display }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Actions Sidebar -->
        <div class="col-lg-4">
            <div class="card sticky-top">
                <div class="card-header">
                    <h5><i class="bi bi-gear"></i> الإجراءات</h5>
                </div>
                <div class="card-body">
                    @if($template->status === 'DRAFT')
                        <form method="POST" action="{{ route('whatsapp.templates.submit-approval', $template) }}" class="mb-3">
                            @csrf
                            <button type="submit" class="btn btn-success w-100">
                                <i class="bi bi-check-circle"></i> تقديم للموافقة
                            </button>
                        </form>
                    @endif

                    @if($template->status === 'PENDING' && auth()->check() && auth()->user()->can('approve-templates'))
                        <div class="mb-3">
                            <button type="button" class="btn btn-success w-100 mb-2" onclick="approveTemplate()">
                                <i class="bi bi-check-lg"></i> اعتماد القالب
                            </button>
                            <button type="button" class="btn btn-danger w-100" onclick="rejectTemplate()">
                                <i class="bi bi-x-lg"></i> رفض القالب
                            </button>
                        </div>
                    @endif

                    @if($template->isApproved() && !$template->whatsapp_template_id)
                        <form method="POST" action="{{ route('whatsapp.templates.submit-whatsapp', $template) }}" class="mb-3">
                            @csrf
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-whatsapp"></i> إرسال لواتساب
                            </button>
                        </form>
                    @endif

                    @if($template->canBeUsed())
                        <button type="button" class="btn btn-outline-primary w-100 mb-3" onclick="testTemplate()">
                            <i class="bi bi-send"></i> اختبار الإرسال
                        </button>
                    @endif

                    <a href="{{ route('whatsapp.templates.duplicate', $template) }}" class="btn btn-outline-secondary w-100 mb-3">
                        <i class="bi bi-files"></i> نسخ القالب
                    </a>

                    @if($template->status !== 'APPROVED' || $template->usage_count === 0)
                        <form method="POST" action="{{ route('whatsapp.templates.destroy', $template) }}"
                              onsubmit="return confirm('هل أنت متأكد من حذف هذا القالب؟')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="bi bi-trash"></i> حذف القالب
                            </button>
                        </form>
                    @endif
                </div>
            </div>

            <!-- Template Preview -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="bi bi-eye"></i> معاينة القالب</h5>
                </div>
                <div class="card-body">
                    <div class="whatsapp-preview border rounded p-3 bg-light">
                        @if($template->header_text)
                            <div class="fw-bold text-primary mb-2">{{ $template->header_text }}</div>
                        @endif

                        <div class="mb-2" style="white-space: pre-wrap;">{{ $template->body_text }}</div>

                        @if($template->footer_text)
                            <div class="text-muted small">{{ $template->footer_text }}</div>
                        @endif

                        @if($template->buttons)
                            <div class="mt-3">
                                @foreach($template->buttons as $button)
                                    <button class="btn btn-outline-primary btn-sm me-2 mb-1" disabled>
                                        {{ $button['text'] }}
                                    </button>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function approveTemplate() {
    const notes = prompt('ملاحظات الموافقة (اختياري):');
    if (notes !== null) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("whatsapp.templates.approve", $template) }}';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        const notesInput = document.createElement('input');
        notesInput.type = 'hidden';
        notesInput.name = 'approval_notes';
        notesInput.value = notes;

        form.appendChild(csrfToken);
        form.appendChild(notesInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function rejectTemplate() {
    const reason = prompt('سبب الرفض (مطلوب):');
    if (reason && reason.trim()) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("whatsapp.templates.reject", $template) }}';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        const reasonInput = document.createElement('input');
        reasonInput.type = 'hidden';
        reasonInput.name = 'rejection_reason';
        reasonInput.value = reason;

        form.appendChild(csrfToken);
        form.appendChild(reasonInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function testTemplate() {
    const phone = prompt('رقم الهاتف للاختبار (مثال: 966501234567):');
    if (phone && phone.trim()) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("whatsapp.templates.test-send", $template) }}';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        const phoneInput = document.createElement('input');
        phoneInput.type = 'hidden';
        phoneInput.name = 'test_phone';
        phoneInput.value = phone;

        form.appendChild(csrfToken);
        form.appendChild(phoneInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
