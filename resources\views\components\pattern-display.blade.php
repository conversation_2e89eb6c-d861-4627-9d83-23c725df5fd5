{{-- Pattern Display Component for Technicians --}}
@props(['ticket', 'showControls' => true, 'size' => 'medium'])

@php
    $canvasSize = match($size) {
        'small' => 200,
        'large' => 350,
        default => 280
    };
@endphp

<div class="pattern-display-container">
    @if($ticket->hasAnySecurityPattern())
        <div class="pattern-display-header">
            <i class="bi bi-shield-lock-fill text-primary"></i>
            <span>معلومات الحماية</span>
        </div>

        {{-- Pattern Type Display --}}
        <div class="mb-3">
            <div class="alert alert-info d-flex align-items-center">
                <i class="bi bi-info-circle me-2"></i>
                <div>
                    <strong>نوع الحماية:</strong>
                    @switch($ticket->pattern_type)
                        @case('text')
                            <span class="badge bg-primary">كلمة مرور نصية</span>
                            @break
                        @case('visual')
                            <span class="badge bg-success">نمط بصري</span>
                            @break
                        @case('both')
                            <span class="badge bg-warning">كلمة مرور + نمط بصري</span>
                            @break
                        @default
                            <span class="badge bg-secondary">غير محدد</span>
                    @endswitch
                </div>
            </div>
        </div>

        {{-- Text Password Section --}}
        @if($ticket->hasSecurityPattern())
            <div class="card border-primary mb-3">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="bi bi-key-fill"></i> كلمة المرور النصية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <span class="text-muted">كلمة المرور:</span>
                            <span id="text-pattern-display" class="fw-bold ms-2">
                                {{ $ticket->getMaskedSecurityPattern() }}
                            </span>
                        </div>
                        @if($showControls)
                            <button type="button"
                                    class="btn btn-sm btn-outline-primary"
                                    id="reveal-text-pattern-btn"
                                    data-pattern="{{ $ticket->security_pattern }}"
                                    onclick="revealTextPattern(this)">
                                <i class="bi bi-eye"></i> كشف
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        @endif

        {{-- Visual Pattern Section --}}
        @if($ticket->hasVisualPattern())
            <div class="card border-success mb-3">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="bi bi-grid-3x3-gap-fill"></i> النمط البصري
                    </h6>
                </div>
                <div class="card-body">
                    <div class="pattern-display-canvas-wrapper text-center">
                        <div id="pattern-display-canvas-{{ $ticket->id }}" class="d-inline-block"></div>
                    </div>

                    <div class="mt-3">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <span class="text-muted">النمط:</span>
                                <span class="fw-bold ms-2">{{ $ticket->getMaskedVisualPattern() }}</span>
                            </div>
                            <div class="text-muted small">
                                {{ count($ticket->getVisualPatternArray()) }} نقاط
                            </div>
                        </div>
                    </div>

                    @if($showControls)
                        <div class="pattern-display-controls mt-3 d-flex flex-wrap gap-2">
                            <button type="button"
                                    class="btn btn-sm btn-primary flex-fill"
                                    id="replay-pattern-btn-{{ $ticket->id }}"
                                    onclick="replayPattern('{{ $ticket->id }}')">
                                <i class="bi bi-play-fill"></i>
                                <span class="d-none d-sm-inline">إعادة تشغيل النمط</span>
                                <span class="d-sm-none">تشغيل</span>
                            </button>

                            <button type="button"
                                    class="btn btn-sm btn-outline-success flex-fill"
                                    id="reveal-pattern-btn-{{ $ticket->id }}"
                                    onclick="revealPattern('{{ $ticket->id }}', '{{ $ticket->visual_pattern }}')"
                                    style="display: none;">
                                <i class="bi bi-eye"></i>
                                <span class="d-none d-sm-inline">كشف النمط</span>
                                <span class="d-sm-none">كشف</span>
                            </button>

                            <button type="button"
                                    class="btn btn-sm btn-outline-secondary flex-fill"
                                    id="hide-pattern-btn-{{ $ticket->id }}"
                                    onclick="hidePattern('{{ $ticket->id }}')"
                                    style="display: none;">
                                <i class="bi bi-eye-slash"></i>
                                <span class="d-none d-sm-inline">إخفاء النمط</span>
                                <span class="d-sm-none">إخفاء</span>
                            </button>
                        </div>
                    @endif

                    {{-- Authentication Modal for Pattern Reveal --}}
                    <div class="modal fade" id="authModal-{{ $ticket->id }}" tabindex="-1">
                        <div class="modal-dialog modal-sm">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h6 class="modal-title">
                                        <i class="bi bi-shield-exclamation"></i>
                                        تأكيد الهوية
                                    </h6>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <p class="mb-3">أدخل كلمة المرور لكشف النمط:</p>
                                    <input type="password"
                                           class="form-control"
                                           id="auth-password-{{ $ticket->id }}"
                                           placeholder="كلمة المرور">
                                    <div id="auth-error-{{ $ticket->id }}" class="text-danger mt-2" style="display: none;">
                                        كلمة مرور خاطئة
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                        إلغاء
                                    </button>
                                    <button type="button"
                                            class="btn btn-primary"
                                            onclick="authenticateAndReveal('{{ $ticket->id }}', '{{ $ticket->visual_pattern }}')">
                                        تأكيد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        {{-- Pattern Security Notice --}}
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i>
            <strong>تنبيه أمني:</strong>
            هذه المعلومات سرية وخاصة بالعميل. يرجى التعامل معها بحذر وعدم مشاركتها مع أطراف غير مخولة.
        </div>

    @else
        <div class="alert alert-secondary text-center">
            <i class="bi bi-shield-x fs-1 text-muted"></i>
            <h6 class="mt-2">لا توجد حماية</h6>
            <p class="mb-0 text-muted">هذا الجهاز غير محمي بكلمة مرور أو نمط</p>
        </div>
    @endif
</div>

<script>
// Pattern display functionality
let patternDisplays = {};

document.addEventListener('DOMContentLoaded', function() {
    @if($ticket->hasVisualPattern())
        initializePatternDisplay('{{ $ticket->id }}', {{ $canvasSize }});
    @endif
});

function initializePatternDisplay(ticketId, canvasSize = 280) {
    const containerId = 'pattern-display-canvas-' + ticketId;

    if (!document.getElementById(containerId)) {
        return;
    }

    patternDisplays[ticketId] = new PatternLock(containerId, {
        canvasSize: canvasSize,
        dotRadius: 16,
        lineWidth: 5,
        dotColor: '#6c757d',
        activeDotColor: '#198754',
        lineColor: '#198754'
    });

    // Show reveal button
    const revealBtn = document.getElementById('reveal-pattern-btn-' + ticketId);
    if (revealBtn) {
        revealBtn.style.display = 'inline-block';
    }
}

function replayPattern(ticketId) {
    const patternDisplay = patternDisplays[ticketId];
    if (!patternDisplay) return;

    const replayBtn = document.getElementById('replay-pattern-btn-' + ticketId);
    if (replayBtn) {
        replayBtn.disabled = true;
        replayBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري التشغيل...';
    }

    // Clear and replay pattern with animation
    patternDisplay.clear();

    setTimeout(() => {
        // This would need the actual pattern - for now just show completion
        if (replayBtn) {
            replayBtn.disabled = false;
            replayBtn.innerHTML = '<i class="bi bi-play-fill"></i> إعادة تشغيل النمط';
        }
    }, 2000);
}

function revealPattern(ticketId, pattern) {
    // Show authentication modal
    const modal = new bootstrap.Modal(document.getElementById('authModal-' + ticketId));
    modal.show();
}

function authenticateAndReveal(ticketId, pattern) {
    const passwordInput = document.getElementById('auth-password-' + ticketId);
    const errorDiv = document.getElementById('auth-error-' + ticketId);
    const password = passwordInput.value;

    // Enhanced authentication check with multiple valid passwords
    const validPasswords = [
        'admin',           // كلمة مرور المدير
        '123456',          // كلمة مرور الفنيين
        'reveal2024',      // كلمة مرور إضافية
        'workshop',        // كلمة مرور الورشة
        'pattern123'       // كلمة مرور النمط
    ];

    if (validPasswords.includes(password)) {
        // Hide modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('authModal-' + ticketId));
        modal.hide();

        // Show pattern
        const patternDisplay = patternDisplays[ticketId];
        if (patternDisplay && pattern) {
            patternDisplay.setPattern(pattern);
        }

        // Update buttons
        document.getElementById('reveal-pattern-btn-' + ticketId).style.display = 'none';
        document.getElementById('hide-pattern-btn-' + ticketId).style.display = 'inline-block';

        // Clear password
        passwordInput.value = '';
        errorDiv.style.display = 'none';

        // Log access (in real app, this would be sent to server)
        console.log(`Pattern revealed for ticket ${ticketId} at ${new Date().toISOString()}`);

        // Show success message
        showNotification('تم كشف النمط بنجاح', 'success');
    } else {
        errorDiv.style.display = 'block';
        errorDiv.textContent = 'كلمة مرور خاطئة. حاول مرة أخرى.';

        // Clear password field
        passwordInput.value = '';
        passwordInput.focus();
    }
}

function hidePattern(ticketId) {
    const patternDisplay = patternDisplays[ticketId];
    if (patternDisplay) {
        patternDisplay.clear();
    }

    // Update buttons
    document.getElementById('reveal-pattern-btn-' + ticketId).style.display = 'inline-block';
    document.getElementById('hide-pattern-btn-' + ticketId).style.display = 'none';
}

function revealTextPattern(button) {
    const pattern = button.getAttribute('data-pattern');
    const displaySpan = document.getElementById('text-pattern-display');

    if (button.classList.contains('revealed')) {
        // Hide pattern
        displaySpan.textContent = '{{ $ticket->getMaskedSecurityPattern() }}';
        button.innerHTML = '<i class="bi bi-eye"></i> كشف';
        button.classList.remove('revealed');
    } else {
        // Show authentication prompt
        const password = prompt('أدخل كلمة المرور لكشف النمط:\n\nكلمات المرور المقبولة:\n• admin\n• 123456\n• reveal2024\n• workshop\n• pattern123');

        const validPasswords = ['admin', '123456', 'reveal2024', 'workshop', 'pattern123'];

        if (password && validPasswords.includes(password)) {
            displaySpan.textContent = pattern;
            button.innerHTML = '<i class="bi bi-eye-slash"></i> إخفاء';
            button.classList.add('revealed');

            // Log access
            console.log(`Text pattern revealed at ${new Date().toISOString()}`);
            showNotification('تم كشف كلمة المرور بنجاح', 'success');
        } else if (password) {
            alert('كلمة مرور خاطئة!');
        }
    }
}

// Utility function to show notifications
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}
</script>
