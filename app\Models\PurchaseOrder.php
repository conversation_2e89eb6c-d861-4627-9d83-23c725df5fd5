<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PurchaseOrder extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'po_number',
        'supplier_id',
        'status',
        'order_date',
        'expected_delivery_date',
        'actual_delivery_date',
        'subtotal',
        'tax_rate',
        'tax_amount',
        'shipping_cost',
        'discount_amount',
        'total_amount',
        'payment_status',
        'paid_amount',
        'payment_due_date',
        'notes',
        'terms_conditions',
        'delivery_address',
        'attachments',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'order_date' => 'date',
        'expected_delivery_date' => 'date',
        'actual_delivery_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'shipping_cost' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'payment_due_date' => 'date',
        'attachments' => 'array',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($purchaseOrder) {
            if (empty($purchaseOrder->po_number)) {
                $purchaseOrder->po_number = self::generatePONumber();
            }
        });
    }

    /**
     * Get the supplier that owns the purchase order.
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the user who created this purchase order.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who approved this purchase order.
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get purchase order items.
     */
    public function items(): HasMany
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    /**
     * Scope for orders by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for pending orders.
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', ['draft', 'sent', 'confirmed']);
    }

    /**
     * Scope for completed orders.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for overdue orders.
     */
    public function scopeOverdue($query)
    {
        return $query->where('expected_delivery_date', '<', now())
                    ->whereIn('status', ['sent', 'confirmed', 'partial']);
    }

    /**
     * Scope for orders by payment status.
     */
    public function scopeByPaymentStatus($query, $paymentStatus)
    {
        return $query->where('payment_status', $paymentStatus);
    }

    /**
     * Generate unique PO number.
     */
    public static function generatePONumber(): string
    {
        $prefix = 'PO';
        $date = now()->format('Ymd');
        $lastOrder = self::whereDate('created_at', today())
                        ->orderBy('id', 'desc')
                        ->first();
        
        $sequence = $lastOrder ? (int)substr($lastOrder->po_number, -3) + 1 : 1;
        
        return $prefix . $date . str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Get status display name.
     */
    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            'draft' => __('app.inventory.po_status.draft'),
            'sent' => __('app.inventory.po_status.sent'),
            'confirmed' => __('app.inventory.po_status.confirmed'),
            'partial' => __('app.inventory.po_status.partial'),
            'completed' => __('app.inventory.po_status.completed'),
            'cancelled' => __('app.inventory.po_status.cancelled'),
            default => $this->status
        };
    }

    /**
     * Get payment status display name.
     */
    public function getPaymentStatusDisplayAttribute(): string
    {
        return match($this->payment_status) {
            'pending' => __('app.inventory.payment_status.pending'),
            'partial' => __('app.inventory.payment_status.partial'),
            'paid' => __('app.inventory.payment_status.paid'),
            'overdue' => __('app.inventory.payment_status.overdue'),
            default => $this->payment_status
        };
    }

    /**
     * Get status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'draft' => 'bg-secondary',
            'sent' => 'bg-info',
            'confirmed' => 'bg-primary',
            'partial' => 'bg-warning',
            'completed' => 'bg-success',
            'cancelled' => 'bg-danger',
            default => 'bg-secondary'
        };
    }

    /**
     * Get payment status badge class.
     */
    public function getPaymentStatusBadgeClassAttribute(): string
    {
        return match($this->payment_status) {
            'pending' => 'bg-warning',
            'partial' => 'bg-info',
            'paid' => 'bg-success',
            'overdue' => 'bg-danger',
            default => 'bg-secondary'
        };
    }

    /**
     * Check if order is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->expected_delivery_date && 
               $this->expected_delivery_date->isPast() && 
               in_array($this->status, ['sent', 'confirmed', 'partial']);
    }

    /**
     * Check if order is fully received.
     */
    public function isFullyReceived(): bool
    {
        return $this->items->every(function ($item) {
            return $item->quantity_received >= $item->quantity_ordered;
        });
    }

    /**
     * Check if order is partially received.
     */
    public function isPartiallyReceived(): bool
    {
        return $this->items->some(function ($item) {
            return $item->quantity_received > 0 && $item->quantity_received < $item->quantity_ordered;
        });
    }

    /**
     * Get remaining balance.
     */
    public function getRemainingBalanceAttribute(): float
    {
        return max(0, $this->total_amount - $this->paid_amount);
    }

    /**
     * Get payment progress percentage.
     */
    public function getPaymentProgressAttribute(): float
    {
        if ($this->total_amount <= 0) {
            return 0;
        }
        
        return min(100, ($this->paid_amount / $this->total_amount) * 100);
    }

    /**
     * Get delivery progress percentage.
     */
    public function getDeliveryProgressAttribute(): float
    {
        $totalOrdered = $this->items->sum('quantity_ordered');
        $totalReceived = $this->items->sum('quantity_received');
        
        if ($totalOrdered <= 0) {
            return 0;
        }
        
        return min(100, ($totalReceived / $totalOrdered) * 100);
    }

    /**
     * Get days until delivery.
     */
    public function getDaysUntilDeliveryAttribute(): ?int
    {
        if (!$this->expected_delivery_date) {
            return null;
        }
        
        return now()->diffInDays($this->expected_delivery_date, false);
    }

    /**
     * Calculate totals.
     */
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items->sum('total_cost');
        $this->tax_amount = $this->subtotal * ($this->tax_rate / 100);
        $this->total_amount = $this->subtotal + $this->tax_amount + $this->shipping_cost - $this->discount_amount;
        $this->save();
    }

    /**
     * Approve the purchase order.
     */
    public function approve(User $user): void
    {
        $this->update([
            'approved_by' => $user->id,
            'approved_at' => now(),
            'status' => 'sent'
        ]);
    }

    /**
     * Mark as sent to supplier.
     */
    public function markAsSent(): void
    {
        $this->update(['status' => 'sent']);
    }

    /**
     * Confirm order with supplier.
     */
    public function confirm(): void
    {
        $this->update(['status' => 'confirmed']);
    }

    /**
     * Cancel the purchase order.
     */
    public function cancel(): void
    {
        $this->update(['status' => 'cancelled']);
    }

    /**
     * Update status based on delivery progress.
     */
    public function updateStatusBasedOnDelivery(): void
    {
        if ($this->isFullyReceived()) {
            $this->update(['status' => 'completed']);
        } elseif ($this->isPartiallyReceived()) {
            $this->update(['status' => 'partial']);
        }
    }

    /**
     * Record payment.
     */
    public function recordPayment(float $amount): void
    {
        $this->paid_amount += $amount;
        
        if ($this->paid_amount >= $this->total_amount) {
            $this->payment_status = 'paid';
        } elseif ($this->paid_amount > 0) {
            $this->payment_status = 'partial';
        }
        
        $this->save();
        
        // Update supplier balance
        $this->supplier->updateBalance($amount, 'subtract');
    }

    /**
     * Get order summary.
     */
    public function getSummaryAttribute(): array
    {
        return [
            'total_items' => $this->items->count(),
            'total_quantity_ordered' => $this->items->sum('quantity_ordered'),
            'total_quantity_received' => $this->items->sum('quantity_received'),
            'delivery_progress' => $this->delivery_progress,
            'payment_progress' => $this->payment_progress,
            'is_overdue' => $this->isOverdue(),
            'days_until_delivery' => $this->days_until_delivery,
        ];
    }
}
