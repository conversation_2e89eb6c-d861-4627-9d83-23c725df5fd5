@extends('layouts.app')

@section('title', 'إضافة عميل جديد')

@push('styles')
<style>
.customer-form-header {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    padding: 2rem;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.form-control {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.required-field::after {
    content: " *";
    color: #e74a3b;
    font-weight: bold;
}

.btn-submit {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 0.35rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.btn-submit:hover {
    background: linear-gradient(45deg, #20c997, #17a2b8);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    color: white;
}

@media (max-width: 768px) {
    .customer-form-header {
        padding: 1rem;
    }

    .form-section {
        padding: 1.5rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="customer-form-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">إضافة عميل جديد</h1>
                <p class="mb-0 opacity-75">إضافة عميل جديد إلى قاعدة بيانات الورشة</p>
            </div>
            <div>
                <a href="{{ route('customers.index') }}" class="btn btn-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للعملاء
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="form-section">
                <form method="POST" action="{{ route('customers.store') }}">
                    @csrf

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label required-field">الاسم الكامل</label>
                            <input type="text"
                                   class="form-control @error('name') is-invalid @enderror"
                                   id="name"
                                   name="name"
                                   value="{{ old('name') }}"
                                   required
                                   autofocus
                                   placeholder="أدخل الاسم الكامل للعميل">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="phone_number" class="form-label required-field">رقم الهاتف</label>
                            <input type="tel"
                                   class="form-control @error('phone_number') is-invalid @enderror"
                                   id="phone_number"
                                   name="phone_number"
                                   value="{{ old('phone_number') }}"
                                   required
                                   placeholder="مثال: 0501234567">
                            @error('phone_number')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email"
                                   class="form-control @error('email') is-invalid @enderror"
                                   id="email"
                                   name="email"
                                   value="{{ old('email') }}"
                                   placeholder="مثال: <EMAIL>">
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control @error('address') is-invalid @enderror"
                                  id="address"
                                  name="address"
                                  rows="3"
                                  placeholder="أدخل عنوان العميل (اختياري)">{{ old('address') }}</textarea>
                        @error('address')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-4">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control @error('notes') is-invalid @enderror"
                                  id="notes"
                                  name="notes"
                                  rows="3"
                                  placeholder="أي ملاحظات إضافية عن العميل (اختياري)">{{ old('notes') }}</textarea>
                        @error('notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn-submit">
                            <i class="fas fa-save me-2"></i>حفظ العميل
                        </button>
                        <a href="{{ route('customers.index') }}" class="btn btn-secondary ms-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تنسيق رقم الهاتف أثناء الكتابة
    document.getElementById('phone_number').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        // تنسيق أرقام الهاتف السعودية
        if (value.length > 9) {
            value = value.substring(0, 10);
        }
        if (value.length === 10 && value.startsWith('05')) {
            value = value.replace(/(\d{2})(\d{4})(\d{4})/, '$1$2$3');
        }
        e.target.value = value;
    });

    // التحقق من صحة النموذج قبل الإرسال
    document.querySelector('form').addEventListener('submit', function(e) {
        const phoneNumber = document.getElementById('phone_number').value;
        const name = document.getElementById('name').value;

        if (!name.trim()) {
            e.preventDefault();
            alert('يرجى إدخال اسم العميل');
            return false;
        }

        if (!phoneNumber.trim() || phoneNumber.length < 10) {
            e.preventDefault();
            alert('يرجى إدخال رقم هاتف صحيح');
            return false;
        }

        return true;
    });
});
</script>
@endpush
