#!/bin/bash

# WhatsApp Business API Integration Setup Script
# Run this after completing Facebook App setup

echo "📱 Setting up WhatsApp Business API Integration for NJ Repair Shop"
echo "================================================================"

# Check if <PERSON><PERSON> is installed
if [ ! -f "artisan" ]; then
    echo "❌ Laravel not found. Please run this script from your Laravel root directory."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create it first."
    exit 1
fi

echo "🔧 Running database migrations..."
php artisan migrate --force

echo "📊 Creating WhatsApp log channel..."
# Add WhatsApp log channel to config/logging.php if not exists
if ! grep -q "whatsapp" config/logging.php; then
    echo "Adding WhatsApp log channel to config/logging.php"
    # This would need manual addition or a more complex sed command
fi

echo "🧹 Clearing caches..."
php artisan config:clear
php artisan route:clear
php artisan view:clear

echo "🔍 Running integration tests..."
php tests/whatsapp-integration-test.php

echo "📋 Checking configuration..."
php artisan tinker --execute="
echo 'WhatsApp Configuration Check:' . PHP_EOL;
echo 'Access Token: ' . (config('whatsapp.access_token') ? 'Set' : 'Missing') . PHP_EOL;
echo 'Phone Number ID: ' . (config('whatsapp.phone_number_id') ? 'Set' : 'Missing') . PHP_EOL;
echo 'Webhook Token: ' . (config('whatsapp.webhook_verify_token') ? 'Set' : 'Missing') . PHP_EOL;
echo 'Business Name: ' . config('whatsapp.business_name') . PHP_EOL;
"

echo "🎯 Testing webhook endpoint..."
curl -s -o /dev/null -w "%{http_code}" "$(php artisan route:list | grep 'whatsapp.webhook.verify' | awk '{print $4}' | head -1)?hub_mode=subscribe&hub_verify_token=$(php artisan tinker --execute='echo config("whatsapp.webhook_verify_token");')&hub_challenge=test" || echo "Webhook test failed"

echo ""
echo "✅ WhatsApp integration setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Complete Facebook App configuration"
echo "2. Set up webhook URL in Facebook Developer Console"
echo "3. Verify phone number in WhatsApp Business API"
echo "4. Test with a real WhatsApp message"
echo "5. Access dashboard at: /whatsapp/dashboard"
echo ""
echo "📖 For detailed setup instructions, see: WHATSAPP_SETUP_GUIDE.md"
echo ""
echo "🧪 Test the integration by sending 'مرحبا' to your WhatsApp Business number"
