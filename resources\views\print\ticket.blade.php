<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('app.repair_tickets.title') }} - {{ $repairTicket->ticket_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }

        @if(app()->getLocale() == 'ar')
        body {
            font-family: "Cairo", Arial, sans-serif;
            direction: rtl;
            text-align: right;
        }
        @else
        body {
            font-family: Arial, sans-serif;
            direction: ltr;
            text-align: left;
        }
        @endif

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .company-info {
            font-size: 11px;
            color: #666;
        }

        .ticket-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .ticket-number {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            text-align: center;
        }

        .ticket-dates {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 11px;
        }

        .section {
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #007bff;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .info-item {
            margin-bottom: 8px;
        }

        .info-label {
            font-weight: bold;
            color: #495057;
            display: inline-block;
            width: 120px;
        }

        .info-value {
            color: #212529;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-in-progress {
            background-color: #cce5ff;
            color: #004085;
        }

        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }

        .status-cancelled {
            background-color: #f8d7da;
            color: #721c24;
        }

        .priority-high {
            color: #dc3545;
            font-weight: bold;
        }

        .priority-medium {
            color: #fd7e14;
            font-weight: bold;
        }

        .priority-normal {
            color: #28a745;
        }

        .cost-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }

        .cost-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .cost-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #dee2e6;
            padding-top: 5px;
            margin-top: 10px;
        }

        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }

        .signature-section {
            margin-top: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .signature-box {
            text-align: center;
            border-top: 1px solid #333;
            padding-top: 5px;
            margin-top: 40px;
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ __('app.app_name') }}</div>
        <div class="company-info">
            {{ __('app.company.services') }}<br>
            {{ __('app.company.phone') }}: +**************** | {{ __('app.company.email') }}: <EMAIL>
        </div>
    </div>

    <div class="ticket-header">
        <div class="ticket-number">Ticket #{{ $repairTicket->ticket_number }}</div>
        <div class="ticket-dates">
            <span><strong>Received:</strong> {{ $repairTicket->received_date->format('M d, Y H:i') }}</span>
            @if($repairTicket->estimated_completion_date)
                <span><strong>Est. Completion:</strong> {{ $repairTicket->estimated_completion_date->format('M d, Y') }}</span>
            @endif
            @if($repairTicket->completed_date)
                <span><strong>Completed:</strong> {{ $repairTicket->completed_date->format('M d, Y H:i') }}</span>
            @endif
        </div>
    </div>

    <div class="section">
        <div class="section-title">Customer Information</div>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Name:</span>
                <span class="info-value">{{ $repairTicket->customer->name }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Phone:</span>
                <span class="info-value">{{ $repairTicket->customer->phone_number }}</span>
            </div>
            @if($repairTicket->customer->email)
            <div class="info-item">
                <span class="info-label">Email:</span>
                <span class="info-value">{{ $repairTicket->customer->email }}</span>
            </div>
            @endif
            @if($repairTicket->customer->address)
            <div class="info-item full-width">
                <span class="info-label">Address:</span>
                <span class="info-value">{{ $repairTicket->customer->address }}</span>
            </div>
            @endif
        </div>
    </div>

    <div class="section">
        <div class="section-title">Device Information</div>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Brand:</span>
                <span class="info-value">{{ $repairTicket->brand->name }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Model:</span>
                <span class="info-value">{{ $repairTicket->device_model }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Condition:</span>
                <span class="info-value">{{ $repairTicket->deviceCondition->name ?? 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Has Pattern:</span>
                <span class="info-value">{{ $repairTicket->device_pattern ? 'Yes' : 'No' }}</span>
            </div>
            <div class="info-item full-width">
                <span class="info-label">Problem:</span>
                <span class="info-value">{{ $repairTicket->reported_problem }}</span>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Repair Information</div>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Status:</span>
                <span class="status-badge status-{{ strtolower(str_replace(' ', '-', $repairTicket->repairStatus->name)) }}">
                    {{ $repairTicket->repairStatus->name }}
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">Priority:</span>
                <span class="priority-{{ $repairTicket->priority }}">
                    {{ ucfirst($repairTicket->priority) }}
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">Technician:</span>
                <span class="info-value">{{ $repairTicket->assignedTo->name ?? 'Unassigned' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Days Since:</span>
                <span class="info-value">{{ $repairTicket->days_since_received }} days</span>
            </div>
            @if($repairTicket->technician_comments)
            <div class="info-item full-width">
                <span class="info-label">Comments:</span>
                <span class="info-value">{{ $repairTicket->technician_comments }}</span>
            </div>
            @endif
        </div>
    </div>

    @if($repairTicket->initial_cost || $repairTicket->final_cost)
    <div class="section">
        <div class="section-title">Cost Information</div>
        <div class="cost-section">
            @if($repairTicket->initial_cost)
            <div class="cost-item">
                <span>Initial Estimate:</span>
                <span>${{ number_format($repairTicket->initial_cost, 2) }}</span>
            </div>
            @endif
            @if($repairTicket->final_cost)
            <div class="cost-item cost-total">
                <span>Final Cost:</span>
                <span>${{ number_format($repairTicket->final_cost, 2) }}</span>
            </div>
            @endif
        </div>
    </div>
    @endif

    {{-- Security Pattern Information --}}
    @if($repairTicket->hasAnySecurityPattern())
    <div class="section">
        <div class="section-title">Security Pattern Information</div>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Pattern Type:</span>
                <span class="info-value">
                    @switch($repairTicket->pattern_type)
                        @case('text')
                            Text Password
                            @break
                        @case('visual')
                            Visual Pattern
                            @break
                        @case('both')
                            Text Password + Visual Pattern
                            @break
                        @default
                            Unknown
                    @endswitch
                </span>
            </div>

            @if($repairTicket->hasSecurityPattern())
            <div class="info-item">
                <span class="info-label">Text Pattern:</span>
                <span class="info-value">{{ $repairTicket->getMaskedSecurityPattern() }}</span>
            </div>
            @endif

            @if($repairTicket->hasVisualPattern())
            <div class="info-item">
                <span class="info-label">Visual Pattern:</span>
                <span class="info-value">{{ $repairTicket->getMaskedVisualPattern() }} ({{ count($repairTicket->getVisualPatternArray()) }} dots)</span>
            </div>
            @endif
        </div>

        @if($repairTicket->hasVisualPattern())
        <div style="margin-top: 15px; text-align: center;">
            <div style="display: inline-block; border: 1px solid #ddd; padding: 10px; border-radius: 5px;">
                <div style="font-weight: bold; margin-bottom: 10px; font-size: 11px;">Visual Pattern Grid</div>
                <div style="display: grid; grid-template-columns: repeat(3, 20px); grid-gap: 5px; justify-content: center;">
                    @php
                        $patternGrid = \App\Services\PatternValidationService::generatePatternGrid($repairTicket->visual_pattern);
                    @endphp
                    @for($row = 0; $row < 3; $row++)
                        @for($col = 0; $col < 3; $col++)
                            <div style="width: 20px; height: 20px; border-radius: 50%; border: 1px solid #333;
                                        background-color: {{ $patternGrid[$row][$col]['active'] ? '#007bff' : '#fff' }};
                                        display: flex; align-items: center; justify-content: center; font-size: 8px; color: white; font-weight: bold;">
                                @if($patternGrid[$row][$col]['active'])
                                    {{ $patternGrid[$row][$col]['order'] }}
                                @endif
                            </div>
                        @endfor
                    @endfor
                </div>
                <div style="font-size: 9px; color: #666; margin-top: 5px;">
                    Pattern: {{ $repairTicket->visual_pattern }}
                </div>
            </div>
        </div>
        @endif

        <div style="margin-top: 10px; padding: 8px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">
            <div style="font-size: 10px; color: #856404;">
                <strong>⚠️ Security Notice:</strong> This information is confidential and should be handled with care.
            </div>
        </div>
    </div>
    @endif

    <div class="signature-section">
        <div>
            <div class="signature-box">Customer Signature</div>
        </div>
        <div>
            <div class="signature-box">Technician Signature</div>
        </div>
    </div>

    <div class="footer">
        <p>Thank you for choosing NJ Repair Shop!</p>
        <p>This document was generated on {{ now()->format('M d, Y H:i') }}</p>
        <p>For questions or concerns, please contact <NAME_EMAIL></p>
    </div>
</body>
</html>
