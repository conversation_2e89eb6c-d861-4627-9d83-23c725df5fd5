<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('security_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('action', 100)->index();
            $table->string('resource_type', 50)->nullable()->index();
            $table->unsignedBigInteger('resource_id')->nullable()->index();
            $table->ipAddress('ip_address')->index();
            $table->text('user_agent')->nullable();
            $table->json('details')->nullable();
            $table->enum('severity', ['low', 'medium', 'high', 'critical'])->default('low')->index();
            $table->enum('status', ['success', 'failed', 'blocked'])->default('success')->index();
            $table->string('session_id')->nullable()->index();
            $table->timestamps();

            // Composite indexes for common queries
            $table->index(['user_id', 'created_at']);
            $table->index(['action', 'status', 'created_at']);
            $table->index(['severity', 'created_at']);
            $table->index(['ip_address', 'action', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('security_logs');
    }
};
