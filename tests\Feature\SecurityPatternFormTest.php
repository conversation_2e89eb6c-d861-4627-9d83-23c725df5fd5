<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\RepairTicket;
use App\Models\Customer;
use App\Models\Brand;
use App\Models\RepairStatus;
use App\Models\DeviceCondition;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SecurityPatternFormTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $customer;
    protected $brand;
    protected $status;
    protected $condition;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->customer = Customer::create(['name' => 'Test Customer', 'phone_number' => '123456789']);
        $this->brand = Brand::create(['name' => 'Test Brand']);
        $this->status = RepairStatus::create(['name' => 'Test Status', 'color' => '#000000']);
        $this->condition = DeviceCondition::create(['name' => 'Test Condition']);
        
        $this->actingAs($this->user);
    }

    /** @test */
    public function user_can_create_ticket_with_security_pattern()
    {
        $ticketData = [
            'customer_id' => $this->customer->id,
            'brand_id' => $this->brand->id,
            'device_model' => 'iPhone 13 Pro',
            'reported_problem' => 'Screen is cracked',
            'device_pattern' => true,
            'security_pattern' => '123456',
            'device_condition_id' => $this->condition->id,
            'repair_status_id' => $this->status->id,
            'received_date' => now()->format('Y-m-d'),
        ];

        $response = $this->post(route('repair-tickets.store'), $ticketData);

        $this->assertDatabaseHas('repair_tickets', [
            'customer_id' => $this->customer->id,
            'device_model' => 'iPhone 13 Pro',
            'device_pattern' => true,
        ]);

        $ticket = RepairTicket::where('customer_id', $this->customer->id)->first();
        $this->assertTrue($ticket->hasSecurityPattern());
        $this->assertEquals('123456', $ticket->security_pattern);
        $this->assertEquals('1****6', $ticket->getMaskedSecurityPattern());
    }

    /** @test */
    public function user_can_create_ticket_without_security_pattern()
    {
        $ticketData = [
            'customer_id' => $this->customer->id,
            'brand_id' => $this->brand->id,
            'device_model' => 'iPhone 12',
            'reported_problem' => 'Battery issues',
            'device_pattern' => false,
            'security_pattern' => '',
            'device_condition_id' => $this->condition->id,
            'repair_status_id' => $this->status->id,
            'received_date' => now()->format('Y-m-d'),
        ];

        $response = $this->post(route('repair-tickets.store'), $ticketData);

        $this->assertDatabaseHas('repair_tickets', [
            'customer_id' => $this->customer->id,
            'device_model' => 'iPhone 12',
            'device_pattern' => false,
        ]);

        $ticket = RepairTicket::where('device_model', 'iPhone 12')->first();
        $this->assertFalse($ticket->hasSecurityPattern());
        $this->assertEquals('لا يوجد', $ticket->getMaskedSecurityPattern());
    }

    /** @test */
    public function user_can_update_ticket_security_pattern()
    {
        $ticket = RepairTicket::create([
            'ticket_number' => 'NJ20250730001',
            'customer_id' => $this->customer->id,
            'brand_id' => $this->brand->id,
            'device_model' => 'iPhone 13',
            'reported_problem' => 'Test problem',
            'device_pattern' => false,
            'security_pattern' => null,
            'device_condition_id' => $this->condition->id,
            'repair_status_id' => $this->status->id,
            'received_date' => now(),
            'created_by' => $this->user->id,
        ]);

        $updateData = [
            'customer_id' => $this->customer->id,
            'brand_id' => $this->brand->id,
            'device_model' => 'iPhone 13',
            'reported_problem' => 'Test problem updated',
            'device_pattern' => true,
            'security_pattern' => '987654',
            'device_condition_id' => $this->condition->id,
            'repair_status_id' => $this->status->id,
            'received_date' => now()->format('Y-m-d'),
        ];

        $response = $this->put(route('repair-tickets.update', $ticket), $updateData);

        $ticket->refresh();
        $this->assertTrue($ticket->hasSecurityPattern());
        $this->assertEquals('987654', $ticket->security_pattern);
        $this->assertEquals('9****4', $ticket->getMaskedSecurityPattern());
    }

    /** @test */
    public function security_pattern_is_properly_encrypted_in_database()
    {
        $ticketData = [
            'customer_id' => $this->customer->id,
            'brand_id' => $this->brand->id,
            'device_model' => 'iPhone 14',
            'reported_problem' => 'Screen replacement',
            'device_pattern' => true,
            'security_pattern' => 'secret123',
            'device_condition_id' => $this->condition->id,
            'repair_status_id' => $this->status->id,
            'received_date' => now()->format('Y-m-d'),
        ];

        $response = $this->post(route('repair-tickets.store'), $ticketData);

        $ticket = RepairTicket::where('device_model', 'iPhone 14')->first();
        
        // The raw database value should be encrypted (different from original)
        $rawValue = $ticket->getAttributes()['security_pattern'];
        $this->assertNotEquals('secret123', $rawValue);
        
        // But the model should decrypt it automatically
        $this->assertEquals('secret123', $ticket->security_pattern);
    }
}
