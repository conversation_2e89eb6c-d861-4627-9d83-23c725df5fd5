<?php

namespace App\Console\Commands\WhatsApp;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use App\Services\WhatsAppService;

class TestConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'whatsapp:test-connection 
                            {--phone= : Phone number to send test message to}
                            {--template= : Template name to test}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test WhatsApp Business API connection and functionality';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧪 Testing WhatsApp Business API Connection...');
        $this->newLine();

        try {
            // Test 1: Basic API connectivity
            $this->testBasicConnectivity();

            // Test 2: Phone number information
            $this->testPhoneNumberInfo();

            // Test 3: Business profile
            $this->testBusinessProfile();

            // Test 4: Template availability
            $this->testTemplateAvailability();

            // Test 5: Send test message (if phone provided)
            if ($this->option('phone')) {
                $this->sendTestMessage();
            }

            // Test 6: Webhook endpoint
            $this->testWebhookEndpoint();

            $this->newLine();
            $this->info('✅ All tests completed successfully!');

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ Connection test failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Test basic API connectivity.
     */
    private function testBasicConnectivity(): void
    {
        $this->info('1️⃣ Testing basic API connectivity...');

        $accessToken = config('whatsapp.auth.access_token');
        $apiVersion = config('whatsapp.api.version');

        if (empty($accessToken)) {
            throw new \Exception('Access token not configured');
        }

        $response = Http::withToken($accessToken)
            ->timeout(30)
            ->get("https://graph.facebook.com/{$apiVersion}/me");

        if ($response->successful()) {
            $data = $response->json();
            $this->info("   ✓ API connection successful");
            $this->info("   App ID: {$data['id']}");
            $this->info("   App Name: {$data['name']}");
        } else {
            throw new \Exception("API connection failed: " . $response->body());
        }
    }

    /**
     * Test phone number information.
     */
    private function testPhoneNumberInfo(): void
    {
        $this->info('2️⃣ Testing phone number information...');

        $accessToken = config('whatsapp.auth.access_token');
        $phoneNumberId = config('whatsapp.auth.phone_number_id');
        $apiVersion = config('whatsapp.api.version');

        $response = Http::withToken($accessToken)
            ->get("https://graph.facebook.com/{$apiVersion}/{$phoneNumberId}");

        if ($response->successful()) {
            $data = $response->json();
            $this->info("   ✓ Phone number information retrieved");
            $this->info("   Display Phone: {$data['display_phone_number']}");
            $this->info("   Verified Name: {$data['verified_name']}");
            $this->info("   Code Verification: {$data['code_verification_status']}");
            $this->info("   Quality Rating: {$data['quality_rating']}");
        } else {
            throw new \Exception("Phone number info failed: " . $response->body());
        }
    }

    /**
     * Test business profile.
     */
    private function testBusinessProfile(): void
    {
        $this->info('3️⃣ Testing business profile...');

        $accessToken = config('whatsapp.auth.access_token');
        $phoneNumberId = config('whatsapp.auth.phone_number_id');
        $apiVersion = config('whatsapp.api.version');

        $response = Http::withToken($accessToken)
            ->get("https://graph.facebook.com/{$apiVersion}/{$phoneNumberId}/whatsapp_business_profile");

        if ($response->successful()) {
            $data = $response->json();
            if (isset($data['data'][0])) {
                $profile = $data['data'][0];
                $this->info("   ✓ Business profile retrieved");
                $this->info("   About: " . ($profile['about'] ?? 'Not set'));
                $this->info("   Address: " . ($profile['address'] ?? 'Not set'));
                $this->info("   Description: " . ($profile['description'] ?? 'Not set'));
            } else {
                $this->warn("   ⚠️  Business profile not configured");
            }
        } else {
            $this->warn("   ⚠️  Business profile retrieval failed: " . $response->body());
        }
    }

    /**
     * Test template availability.
     */
    private function testTemplateAvailability(): void
    {
        $this->info('4️⃣ Testing message templates...');

        $accessToken = config('whatsapp.auth.access_token');
        $businessAccountId = config('whatsapp.auth.business_account_id');
        $apiVersion = config('whatsapp.api.version');

        $response = Http::withToken($accessToken)
            ->get("https://graph.facebook.com/{$apiVersion}/{$businessAccountId}/message_templates");

        if ($response->successful()) {
            $data = $response->json();
            $templates = $data['data'] ?? [];
            
            $this->info("   ✓ Templates retrieved: " . count($templates) . " found");
            
            $approvedCount = 0;
            foreach ($templates as $template) {
                if ($template['status'] === 'APPROVED') {
                    $approvedCount++;
                }
            }
            
            $this->info("   Approved templates: {$approvedCount}");
            
            if ($approvedCount === 0) {
                $this->warn("   ⚠️  No approved templates found. Submit templates for approval.");
            }

            // Show first few templates
            $this->info("   Template list:");
            foreach (array_slice($templates, 0, 5) as $template) {
                $status = $template['status'];
                $statusIcon = $status === 'APPROVED' ? '✅' : ($status === 'PENDING' ? '⏳' : '❌');
                $this->line("     {$statusIcon} {$template['name']} ({$template['language']}) - {$status}");
            }

        } else {
            $this->warn("   ⚠️  Template retrieval failed: " . $response->body());
        }
    }

    /**
     * Send test message.
     */
    private function sendTestMessage(): void
    {
        $this->info('5️⃣ Sending test message...');

        $phone = $this->option('phone');
        $template = $this->option('template') ?? 'welcome_message_ar';

        try {
            $whatsappService = app(WhatsAppService::class);
            
            if ($template) {
                // Send template message
                $result = $whatsappService->sendTemplateMessage($phone, $template, []);
            } else {
                // Send simple text message
                $result = $whatsappService->sendMessage($phone, 'مرحباً! هذه رسالة تجريبية من ورشة إصلاح الأجهزة 🔧');
            }

            if ($result['success']) {
                $this->info("   ✅ Test message sent successfully");
                $this->info("   Message ID: {$result['message_id']}");
                $this->info("   Phone: {$phone}");
            } else {
                $this->error("   ❌ Test message failed: {$result['error']}");
            }

        } catch (\Exception $e) {
            $this->error("   ❌ Test message error: " . $e->getMessage());
        }
    }

    /**
     * Test webhook endpoint.
     */
    private function testWebhookEndpoint(): void
    {
        $this->info('6️⃣ Testing webhook endpoint...');

        $webhookUrl = url('/api/whatsapp/webhook');
        $verifyToken = config('whatsapp.webhook.verify_token');

        try {
            $response = Http::timeout(10)->get($webhookUrl, [
                'hub.mode' => 'subscribe',
                'hub.challenge' => 'test_challenge_' . time(),
                'hub.verify_token' => $verifyToken
            ]);

            if ($response->successful()) {
                $this->info("   ✅ Webhook endpoint is accessible");
                $this->info("   URL: {$webhookUrl}");
                $this->info("   Response: " . $response->body());
            } else {
                $this->warn("   ⚠️  Webhook endpoint returned: " . $response->status());
                $this->warn("   Make sure the webhook route is properly configured");
            }

        } catch (\Exception $e) {
            $this->warn("   ⚠️  Webhook test failed: " . $e->getMessage());
            $this->warn("   URL: {$webhookUrl}");
            $this->warn("   Make sure your application is accessible from the internet");
        }
    }
}
