# 📱 NJ Repair Shop - WhatsApp Business API Setup Guide

This comprehensive guide will walk you through setting up WhatsApp Business API integration for the NJ Repair Shop management system with full Arabic language support.

## 📋 Table of Contents

1. [Prerequisites and Requirements](#prerequisites-and-requirements)
2. [Meta Developer Account Setup](#meta-developer-account-setup)
3. [WhatsApp Business API Configuration](#whatsapp-business-api-configuration)
4. [Laravel Integration Setup](#laravel-integration-setup)
5. [Database Configuration](#database-configuration)
6. [Template Message Setup](#template-message-setup)
7. [Testing and Validation](#testing-and-validation)
8. [Production Deployment](#production-deployment)
9. [Troubleshooting Guide](#troubleshooting-guide)
10. [Monitoring and Maintenance](#monitoring-and-maintenance)

---

## 🔧 Prerequisites and Requirements

### System Requirements
- **PHP**: 8.1 or higher
- **Laravel**: 10.x
- **SSL Certificate**: Required for webhook endpoints
- **Public Domain**: Accessible webhook URL
- **Queue System**: Redis or Database queues configured

### WhatsApp Business Requirements
- **Business Phone Number**: Dedicated phone number for WhatsApp Business
- **Business Verification**: Verified business account
- **Meta Business Account**: Active Meta Business account
- **WhatsApp Business Account**: Approved WhatsApp Business account

### Required Permissions
- `whatsapp_business_messaging`: Send and receive messages
- `whatsapp_business_management`: Manage WhatsApp Business account
- `business_management`: Manage business assets

---

## 🏢 Meta Developer Account Setup

### Step 1: Create Meta Developer Account

1. **Visit Meta for Developers**
   ```
   https://developers.facebook.com/
   ```

2. **Create Developer Account**
   - Click "Get Started"
   - Log in with your Facebook/Meta account
   - Complete developer registration
   - Verify your account via email/SMS

3. **Create New App**
   - Click "Create App"
   - Select "Business" as app type
   - Enter app details:
     - **App Name**: "NJ Repair Shop WhatsApp"
     - **App Contact Email**: <EMAIL>
     - **Business Account**: Select your business account

### Step 2: Add WhatsApp Product

1. **Navigate to App Dashboard**
2. **Add WhatsApp Product**
   - Click "Add Product"
   - Select "WhatsApp"
   - Click "Set Up"

3. **Configure Basic Settings**
   - **Display Name**: "NJ Repair Shop"
   - **Category**: "Business Services"
   - **Description**: "Repair shop customer service"

---

## 📱 WhatsApp Business API Configuration

### Step 1: Phone Number Setup

1. **Add Phone Number**
   ```
   Navigate to: WhatsApp > Getting Started > Add Phone Number
   ```

2. **Verify Phone Number**
   - Enter your business phone number
   - Select country code (+966 for Saudi Arabia)
   - Choose verification method (SMS/Call)
   - Enter verification code

3. **Configure Phone Number**
   - **Display Name**: "NJ Repair Shop"
   - **About**: "خدمة عملاء ورشة إصلاح الأجهزة"
   - **Profile Picture**: Upload your business logo

### Step 2: Generate Access Tokens

1. **Temporary Access Token**
   ```
   Navigate to: WhatsApp > Getting Started > Temporary Access Token
   ```
   - Copy the temporary token (valid for 24 hours)
   - Use for initial testing only

2. **Permanent Access Token**
   ```
   Navigate to: WhatsApp > Getting Started > Permanent Access Token
   ```
   - Click "Generate Token"
   - Select required permissions:
     - whatsapp_business_messaging
     - whatsapp_business_management
   - Copy and securely store the token

### Step 3: Webhook Configuration

1. **Configure Webhook URL**
   ```
   Navigate to: WhatsApp > Configuration > Webhook
   ```

2. **Webhook Settings**
   - **Callback URL**: `https://yourdomain.com/api/whatsapp/webhook`
   - **Verify Token**: Generate a secure random string
   - **Webhook Fields**: Select all relevant fields:
     - messages
     - message_deliveries
     - message_reads
     - message_reactions

3. **Webhook Verification**
   ```bash
   # Test webhook endpoint
   curl -X GET "https://yourdomain.com/api/whatsapp/webhook?hub.mode=subscribe&hub.challenge=CHALLENGE_SENT_BY_FACEBOOK&hub.verify_token=YOUR_VERIFY_TOKEN"
   ```

---

## 🔧 Laravel Integration Setup

### Step 1: Environment Configuration

Add the following variables to your `.env` file:

```env
# WhatsApp Business API Configuration
WHATSAPP_API_VERSION=v18.0
WHATSAPP_ACCESS_TOKEN=your_permanent_access_token_here
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id_here
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_secure_verify_token_here
WHATSAPP_APP_ID=your_app_id_here
WHATSAPP_APP_SECRET=your_app_secret_here

# WhatsApp Features
WHATSAPP_ENABLED=true
WHATSAPP_FALLBACK_TO_SMS=true
WHATSAPP_RATE_LIMIT_PER_MINUTE=80
WHATSAPP_TEMPLATE_APPROVAL_REQUIRED=true

# Message Templates
WHATSAPP_DEFAULT_LANGUAGE=ar
WHATSAPP_TEMPLATE_NAMESPACE=your_template_namespace

# Webhook Security
WHATSAPP_WEBHOOK_SECRET=your_webhook_secret_key
WHATSAPP_WEBHOOK_TIMEOUT=30

# Queue Configuration (for message processing)
QUEUE_CONNECTION=redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

### Step 2: Install Required Packages

```bash
# Install HTTP client for API requests
composer require guzzlehttp/guzzle

# Install Redis for queue processing
composer require predis/predis

# Install queue monitoring (optional)
composer require laravel/horizon
```

### Step 3: Configuration Files

Create WhatsApp configuration file:

```bash
php artisan make:config whatsapp
```

---

## 🗄️ Database Configuration

### Step 1: Create Migration Files

```bash
# Create WhatsApp messages table
php artisan make:migration create_whatsapp_messages_table

# Create WhatsApp templates table
php artisan make:migration create_whatsapp_templates_table

# Create WhatsApp webhooks table
php artisan make:migration create_whatsapp_webhooks_table

# Create WhatsApp contacts table
php artisan make:migration create_whatsapp_contacts_table
```

### Step 2: Run Migrations

```bash
# Run all WhatsApp-related migrations
php artisan migrate

# Verify tables were created
php artisan migrate:status
```

### Step 3: Seed Template Data

```bash
# Create seeder for Arabic templates
php artisan make:seeder WhatsAppTemplateSeeder

# Run the seeder
php artisan db:seed --class=WhatsAppTemplateSeeder
```

---

## 📝 Template Message Setup

### Step 1: Create Arabic Templates

Templates must be approved by Meta before use. Here are the required templates:

#### Repair Status Templates

1. **Repair Received** (`repair_received_ar`)
   ```
   مرحباً {{1}},
   
   تم استلام جهازك {{2}} بنجاح.
   رقم التذكرة: {{3}}
   
   سنقوم بفحص الجهاز وإرسال تقرير مفصل قريباً.
   
   شكراً لثقتكم بنا
   ورشة إصلاح الأجهزة
   ```

2. **Repair In Progress** (`repair_in_progress_ar`)
   ```
   عزيزي {{1}},
   
   جهازك {{2}} قيد الإصلاح حالياً.
   رقم التذكرة: {{3}}
   التقدير المبدئي: {{4}}
   
   سنبلغكم فور انتهاء الإصلاح.
   
   ورشة إصلاح الأجهزة
   ```

3. **Repair Completed** (`repair_completed_ar`)
   ```
   تهانينا {{1}}!
   
   تم إصلاح جهازك {{2}} بنجاح ✅
   رقم التذكرة: {{3}}
   التكلفة النهائية: {{4}} ريال
   
   يمكنكم استلام الجهاز في أي وقت.
   
   ورشة إصلاح الأجهزة
   ```

### Step 2: Submit Templates for Approval

1. **Navigate to Template Manager**
   ```
   WhatsApp Manager > Message Templates > Create Template
   ```

2. **Template Submission Process**
   - Select template category: "UTILITY"
   - Enter template name and content
   - Add Arabic language support
   - Submit for review (usually takes 24-48 hours)

### Step 3: Template Status Monitoring

```bash
# Check template approval status
php artisan whatsapp:check-templates

# List all approved templates
php artisan whatsapp:list-templates
```

---

## 🧪 Testing and Validation

### Step 1: Webhook Testing

```bash
# Test webhook endpoint locally using ngrok
ngrok http 8000

# Update webhook URL in Meta Developer Console
# Test webhook with Meta's testing tool
```

### Step 2: Message Sending Test

```bash
# Send test message via Artisan command
php artisan whatsapp:send-test-message +************

# Test template message
php artisan whatsapp:send-template +************ repair_received_ar "أحمد" "iPhone 13" "TKT-001"
```

### Step 3: Integration Testing

```php
// Test in Tinker
php artisan tinker

// Send a test message
use App\Services\WhatsAppService;
$whatsapp = app(WhatsAppService::class);
$result = $whatsapp->sendMessage('+************', 'مرحباً! هذه رسالة تجريبية من ورشة إصلاح الأجهزة');
```

---

## 🚀 Production Deployment

### Step 1: SSL Certificate Setup

```bash
# Install SSL certificate (Let's Encrypt example)
sudo certbot --nginx -d yourdomain.com

# Verify SSL is working
curl -I https://yourdomain.com/api/whatsapp/webhook
```

### Step 2: Webhook URL Configuration

1. **Update Webhook URL in Meta Console**
   ```
   Production URL: https://yourdomain.com/api/whatsapp/webhook
   ```

2. **Test Production Webhook**
   ```bash
   # Verify webhook is accessible
   curl -X GET "https://yourdomain.com/api/whatsapp/webhook?hub.mode=subscribe&hub.challenge=test&hub.verify_token=YOUR_TOKEN"
   ```

### Step 3: Queue Worker Setup

```bash
# Start queue workers for message processing
php artisan queue:work --queue=whatsapp,default --tries=3

# For production, use Supervisor
sudo nano /etc/supervisor/conf.d/laravel-worker.conf
```

Supervisor configuration:
```ini
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/your/app/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/path/to/your/app/storage/logs/worker.log
stopwaitsecs=3600
```

### Step 4: Monitoring Setup

```bash
# Install Laravel Horizon for queue monitoring
composer require laravel/horizon
php artisan horizon:install
php artisan horizon:publish

# Start Horizon
php artisan horizon
```

---

## 🔍 Troubleshooting Guide

### Common Issues and Solutions

#### 1. Webhook Verification Failed
```
Error: Webhook verification failed
```
**Solution:**
- Verify webhook URL is accessible via HTTPS
- Check verify token matches exactly
- Ensure proper response format

#### 2. Message Sending Failed
```
Error: (#100) Param phone_number is not a valid phone number
```
**Solution:**
- Ensure phone number includes country code
- Remove any spaces or special characters
- Format: +************

#### 3. Template Not Found
```
Error: Template not found or not approved
```
**Solution:**
- Check template approval status in Meta Business Manager
- Verify template name and language code
- Wait for approval (24-48 hours)

#### 4. Rate Limit Exceeded
```
Error: (#4) Application request limit reached
```
**Solution:**
- Implement rate limiting in application
- Use queue system for bulk messages
- Monitor API usage in Meta Developer Console

### Debug Commands

```bash
# Check WhatsApp service status
php artisan whatsapp:status

# View recent webhook logs
php artisan whatsapp:webhook-logs

# Test API connectivity
php artisan whatsapp:test-connection

# Validate templates
php artisan whatsapp:validate-templates
```

---

## 📊 Monitoring and Maintenance

### Performance Monitoring

1. **Message Delivery Tracking**
   - Monitor delivery status webhooks
   - Track failed message attempts
   - Set up alerts for high failure rates

2. **API Usage Monitoring**
   - Track API call volume
   - Monitor rate limit usage
   - Set up usage alerts

3. **Queue Monitoring**
   - Monitor queue length
   - Track processing times
   - Set up failed job alerts

### Regular Maintenance Tasks

```bash
# Daily: Clean old webhook logs
php artisan whatsapp:cleanup-logs --days=30

# Weekly: Check template status
php artisan whatsapp:check-templates

# Monthly: Generate usage report
php artisan whatsapp:usage-report --month=current
```

### Security Best Practices

1. **Token Security**
   - Rotate access tokens regularly
   - Store tokens in encrypted environment variables
   - Use different tokens for staging/production

2. **Webhook Security**
   - Validate webhook signatures
   - Use HTTPS only
   - Implement rate limiting

3. **Data Protection**
   - Encrypt sensitive message data
   - Implement data retention policies
   - Regular security audits

---

## 📞 Support and Resources

### Official Documentation
- [WhatsApp Business API Documentation](https://developers.facebook.com/docs/whatsapp)
- [Meta Business API Reference](https://developers.facebook.com/docs/graph-api)

### Community Resources
- [WhatsApp Business API Community](https://developers.facebook.com/community/)
- [Laravel WhatsApp Packages](https://packagist.org/search/?q=whatsapp)

### Emergency Contacts
- **Meta Business Support**: Available through Business Manager
- **WhatsApp Business API Support**: Via Developer Console

---

## ✅ Setup Checklist

- [ ] Meta Developer account created
- [ ] WhatsApp Business API app configured
- [ ] Phone number verified and added
- [ ] Access tokens generated and secured
- [ ] Webhook URL configured and verified
- [ ] Laravel environment variables set
- [ ] Database migrations completed
- [ ] Message templates created and approved
- [ ] Testing completed successfully
- [ ] Production deployment configured
- [ ] SSL certificate installed
- [ ] Queue workers running
- [ ] Monitoring systems active
- [ ] Documentation reviewed and understood

---

**🎉 Congratulations!** Your NJ Repair Shop WhatsApp Business API integration is now ready for production use with full Arabic language support.

For additional support or questions, please refer to the troubleshooting section or contact the development team.
