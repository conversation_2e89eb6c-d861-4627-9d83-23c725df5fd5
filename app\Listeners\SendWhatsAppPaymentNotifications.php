<?php

namespace App\Listeners;

use App\Events\PaymentCreated;
use App\Events\InvoiceCreated;
use App\Events\PaymentReminder;
use App\Services\WhatsAppPaymentNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendWhatsAppPaymentNotifications implements ShouldQueue
{
    use InteractsWithQueue;

    protected WhatsAppPaymentNotificationService $paymentNotificationService;

    /**
     * Create the event listener.
     */
    public function __construct(WhatsAppPaymentNotificationService $paymentNotificationService)
    {
        $this->paymentNotificationService = $paymentNotificationService;
    }

    /**
     * Handle the event.
     */
    public function handle($event): void
    {
        try {
            match (get_class($event)) {
                PaymentCreated::class => $this->handlePaymentCreated($event),
                InvoiceCreated::class => $this->handleInvoiceCreated($event),
                PaymentReminder::class => $this->handlePaymentReminder($event),
                default => null,
            };
        } catch (\Exception $e) {
            Log::error('Failed to send WhatsApp payment notification', [
                'event' => get_class($event),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle payment created event.
     */
    private function handlePaymentCreated(PaymentCreated $event): void
    {
        $payment = $event->payment;
        
        // Check if WhatsApp notifications are enabled for this customer
        if (!$this->shouldSendNotification($payment->invoice->customer)) {
            return;
        }

        // Send payment confirmation
        $result = $this->paymentNotificationService->sendPaymentConfirmation($payment);
        
        if ($result['success']) {
            Log::info('WhatsApp payment confirmation sent', [
                'payment_id' => $payment->id,
                'customer_phone' => $payment->invoice->customer->phone_number
            ]);
        } else {
            Log::warning('Failed to send WhatsApp payment confirmation', [
                'payment_id' => $payment->id,
                'error' => $result['error']
            ]);
        }

        // Send payment receipt if payment is complete
        if ($payment->invoice->payment_status === 'paid') {
            $receiptResult = $this->paymentNotificationService->sendPaymentReceipt($payment);
            
            if ($receiptResult['success']) {
                Log::info('WhatsApp payment receipt sent', [
                    'payment_id' => $payment->id,
                    'customer_phone' => $payment->invoice->customer->phone_number
                ]);
            }
        }
    }

    /**
     * Handle invoice created event.
     */
    private function handleInvoiceCreated(InvoiceCreated $event): void
    {
        $invoice = $event->invoice;
        
        // Check if WhatsApp notifications are enabled for this customer
        if (!$this->shouldSendNotification($invoice->customer)) {
            return;
        }

        // Only send invoice notification if it's not immediately paid
        if ($invoice->payment_status !== 'paid') {
            $result = $this->paymentNotificationService->sendInvoiceNotification($invoice);
            
            if ($result['success']) {
                Log::info('WhatsApp invoice notification sent', [
                    'invoice_id' => $invoice->id,
                    'customer_phone' => $invoice->customer->phone_number
                ]);
            } else {
                Log::warning('Failed to send WhatsApp invoice notification', [
                    'invoice_id' => $invoice->id,
                    'error' => $result['error']
                ]);
            }
        }
    }

    /**
     * Handle payment reminder event.
     */
    private function handlePaymentReminder(PaymentReminder $event): void
    {
        $invoice = $event->invoice;
        $reminderType = $event->reminderType ?? 'gentle';
        
        // Check if WhatsApp notifications are enabled for this customer
        if (!$this->shouldSendNotification($invoice->customer)) {
            return;
        }

        $result = $this->paymentNotificationService->sendPaymentReminder($invoice, $reminderType);
        
        if ($result['success']) {
            Log::info('WhatsApp payment reminder sent', [
                'invoice_id' => $invoice->id,
                'reminder_type' => $reminderType,
                'customer_phone' => $invoice->customer->phone_number
            ]);
        } else {
            Log::warning('Failed to send WhatsApp payment reminder', [
                'invoice_id' => $invoice->id,
                'reminder_type' => $reminderType,
                'error' => $result['error']
            ]);
        }
    }

    /**
     * Check if WhatsApp notifications should be sent to this customer.
     */
    private function shouldSendNotification($customer): bool
    {
        if (!$customer || !$customer->phone_number) {
            return false;
        }

        // Check if WhatsApp is enabled globally
        if (!config('whatsapp.enabled', true)) {
            return false;
        }

        // Check customer preferences
        $preferences = \App\Models\WhatsAppCustomerPreference::getOrCreateForCustomer($customer->phone_number);
        
        return $preferences->canReceivePaymentNotifications();
    }

    /**
     * Handle a job failure.
     */
    public function failed($event, $exception): void
    {
        Log::error('WhatsApp payment notification job failed', [
            'event' => get_class($event),
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
