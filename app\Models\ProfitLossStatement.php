<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ProfitLossStatement extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'statement_date',
        'period_type',
        'total_revenue',
        'repair_services_revenue',
        'parts_sales_revenue',
        'pos_sales_revenue',
        'other_revenue',
        'cost_of_goods_sold',
        'gross_profit',
        'operating_expenses',
        'staff_salaries',
        'rent_utilities',
        'marketing_expenses',
        'equipment_depreciation',
        'other_expenses',
        'operating_income',
        'interest_income',
        'interest_expense',
        'net_income',
        'gross_profit_margin',
        'operating_profit_margin',
        'net_profit_margin',
        'detailed_breakdown',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'statement_date' => 'date',
        'total_revenue' => 'decimal:2',
        'repair_services_revenue' => 'decimal:2',
        'parts_sales_revenue' => 'decimal:2',
        'pos_sales_revenue' => 'decimal:2',
        'other_revenue' => 'decimal:2',
        'cost_of_goods_sold' => 'decimal:2',
        'gross_profit' => 'decimal:2',
        'operating_expenses' => 'decimal:2',
        'staff_salaries' => 'decimal:2',
        'rent_utilities' => 'decimal:2',
        'marketing_expenses' => 'decimal:2',
        'equipment_depreciation' => 'decimal:2',
        'other_expenses' => 'decimal:2',
        'operating_income' => 'decimal:2',
        'interest_income' => 'decimal:2',
        'interest_expense' => 'decimal:2',
        'net_income' => 'decimal:2',
        'gross_profit_margin' => 'decimal:2',
        'operating_profit_margin' => 'decimal:2',
        'net_profit_margin' => 'decimal:2',
        'detailed_breakdown' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Generate profit & loss statement for a period.
     */
    public static function generateStatement(Carbon $date, string $periodType): self
    {
        $startDate = self::getStartDateForPeriod($date, $periodType);
        $endDate = self::getEndDateForPeriod($date, $periodType);

        // Calculate revenue streams
        $revenueData = self::calculateRevenue($startDate, $endDate);
        
        // Calculate cost of goods sold
        $costOfGoodsSold = self::calculateCostOfGoodsSold($startDate, $endDate);
        
        // Calculate operating expenses
        $operatingExpenses = self::calculateOperatingExpenses($startDate, $endDate);
        
        // Calculate other income/expenses
        $otherIncomeExpenses = self::calculateOtherIncomeExpenses($startDate, $endDate);

        // Calculate totals and margins
        $totalRevenue = array_sum($revenueData);
        $grossProfit = $totalRevenue - $costOfGoodsSold;
        $totalOperatingExpenses = array_sum($operatingExpenses);
        $operatingIncome = $grossProfit - $totalOperatingExpenses;
        $netIncome = $operatingIncome + $otherIncomeExpenses['interest_income'] - $otherIncomeExpenses['interest_expense'];

        // Calculate margins
        $grossProfitMargin = $totalRevenue > 0 ? ($grossProfit / $totalRevenue) * 100 : 0;
        $operatingProfitMargin = $totalRevenue > 0 ? ($operatingIncome / $totalRevenue) * 100 : 0;
        $netProfitMargin = $totalRevenue > 0 ? ($netIncome / $totalRevenue) * 100 : 0;

        // Prepare detailed breakdown
        $detailedBreakdown = [
            'period' => [
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
                'period_type' => $periodType,
            ],
            'revenue_breakdown' => $revenueData,
            'expense_breakdown' => $operatingExpenses,
            'other_income_expenses' => $otherIncomeExpenses,
            'calculations' => [
                'gross_profit_calculation' => [
                    'total_revenue' => $totalRevenue,
                    'minus_cogs' => $costOfGoodsSold,
                    'equals_gross_profit' => $grossProfit,
                ],
                'operating_income_calculation' => [
                    'gross_profit' => $grossProfit,
                    'minus_operating_expenses' => $totalOperatingExpenses,
                    'equals_operating_income' => $operatingIncome,
                ],
                'net_income_calculation' => [
                    'operating_income' => $operatingIncome,
                    'plus_interest_income' => $otherIncomeExpenses['interest_income'],
                    'minus_interest_expense' => $otherIncomeExpenses['interest_expense'],
                    'equals_net_income' => $netIncome,
                ],
            ]
        ];

        return self::updateOrCreate(
            [
                'statement_date' => $date->toDateString(),
                'period_type' => $periodType
            ],
            [
                'total_revenue' => $totalRevenue,
                'repair_services_revenue' => $revenueData['repair_services'],
                'parts_sales_revenue' => $revenueData['parts_sales'],
                'pos_sales_revenue' => $revenueData['pos_sales'],
                'other_revenue' => $revenueData['other'],
                'cost_of_goods_sold' => $costOfGoodsSold,
                'gross_profit' => $grossProfit,
                'operating_expenses' => $totalOperatingExpenses,
                'staff_salaries' => $operatingExpenses['staff_salaries'],
                'rent_utilities' => $operatingExpenses['rent_utilities'],
                'marketing_expenses' => $operatingExpenses['marketing'],
                'equipment_depreciation' => $operatingExpenses['depreciation'],
                'other_expenses' => $operatingExpenses['other'],
                'operating_income' => $operatingIncome,
                'interest_income' => $otherIncomeExpenses['interest_income'],
                'interest_expense' => $otherIncomeExpenses['interest_expense'],
                'net_income' => $netIncome,
                'gross_profit_margin' => $grossProfitMargin,
                'operating_profit_margin' => $operatingProfitMargin,
                'net_profit_margin' => $netProfitMargin,
                'detailed_breakdown' => $detailedBreakdown,
            ]
        );
    }

    /**
     * Calculate revenue from different sources.
     */
    private static function calculateRevenue(Carbon $startDate, Carbon $endDate): array
    {
        // Repair services revenue from invoices
        $repairServicesRevenue = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->whereHas('repairTicket')
            ->sum('total_amount');

        // Parts sales revenue from invoices (items marked as parts)
        $partsSalesRevenue = DB::table('invoice_items')
            ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->whereBetween('invoices.invoice_date', [$startDate, $endDate])
            ->where('invoices.status', '!=', 'cancelled')
            ->where('invoice_items.item_type', 'part')
            ->sum('invoice_items.total_price');

        // POS sales revenue
        $posSalesRevenue = DB::table('pos_transactions')
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->where('status', 'completed')
            ->sum('total_amount');

        // Other revenue (could include warranty extensions, consulting, etc.)
        $otherRevenue = DB::table('invoice_items')
            ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->whereBetween('invoices.invoice_date', [$startDate, $endDate])
            ->where('invoices.status', '!=', 'cancelled')
            ->where('invoice_items.item_type', 'other')
            ->sum('invoice_items.total_price');

        return [
            'repair_services' => $repairServicesRevenue - $partsSalesRevenue, // Subtract parts to avoid double counting
            'parts_sales' => $partsSalesRevenue,
            'pos_sales' => $posSalesRevenue,
            'other' => $otherRevenue,
        ];
    }

    /**
     * Calculate cost of goods sold.
     */
    private static function calculateCostOfGoodsSold(Carbon $startDate, Carbon $endDate): float
    {
        // COGS from invoice items (parts and products)
        $invoiceCogs = DB::table('invoice_items')
            ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->join('inventory_items', 'invoice_items.inventory_item_id', '=', 'inventory_items.id')
            ->whereBetween('invoices.invoice_date', [$startDate, $endDate])
            ->where('invoices.status', '!=', 'cancelled')
            ->whereIn('invoice_items.item_type', ['part', 'product'])
            ->sum(DB::raw('invoice_items.quantity * inventory_items.cost_price'));

        // COGS from POS transactions
        $posCogs = DB::table('pos_transaction_items')
            ->join('pos_transactions', 'pos_transaction_items.transaction_id', '=', 'pos_transactions.id')
            ->whereBetween('pos_transactions.transaction_date', [$startDate, $endDate])
            ->where('pos_transactions.status', 'completed')
            ->whereNotNull('pos_transaction_items.cost_price')
            ->sum(DB::raw('pos_transaction_items.quantity * pos_transaction_items.cost_price'));

        return $invoiceCogs + $posCogs;
    }

    /**
     * Calculate operating expenses.
     */
    private static function calculateOperatingExpenses(Carbon $startDate, Carbon $endDate): array
    {
        $expenses = DB::table('expenses')
            ->whereBetween('expense_date', [$startDate, $endDate])
            ->where('approval_status', 'approved')
            ->get();

        $breakdown = [
            'staff_salaries' => 0,
            'rent_utilities' => 0,
            'marketing' => 0,
            'depreciation' => 0,
            'other' => 0,
        ];

        foreach ($expenses as $expense) {
            switch ($expense->expense_type) {
                case 'salary':
                case 'payroll':
                    $breakdown['staff_salaries'] += $expense->amount;
                    break;
                case 'rent':
                case 'utilities':
                    $breakdown['rent_utilities'] += $expense->amount;
                    break;
                case 'marketing':
                case 'advertising':
                    $breakdown['marketing'] += $expense->amount;
                    break;
                case 'depreciation':
                    $breakdown['depreciation'] += $expense->amount;
                    break;
                default:
                    $breakdown['other'] += $expense->amount;
                    break;
            }
        }

        return $breakdown;
    }

    /**
     * Calculate other income and expenses.
     */
    private static function calculateOtherIncomeExpenses(Carbon $startDate, Carbon $endDate): array
    {
        // This would typically come from a separate income/expense tracking system
        // For now, we'll return default values
        return [
            'interest_income' => 0,
            'interest_expense' => 0,
        ];
    }

    /**
     * Get start date for period type.
     */
    private static function getStartDateForPeriod(Carbon $date, string $periodType): Carbon
    {
        return match ($periodType) {
            'monthly' => $date->copy()->startOfMonth(),
            'quarterly' => $date->copy()->startOfQuarter(),
            'yearly' => $date->copy()->startOfYear(),
            default => $date->copy()->startOfMonth(),
        };
    }

    /**
     * Get end date for period type.
     */
    private static function getEndDateForPeriod(Carbon $date, string $periodType): Carbon
    {
        return match ($periodType) {
            'monthly' => $date->copy()->endOfMonth(),
            'quarterly' => $date->copy()->endOfQuarter(),
            'yearly' => $date->copy()->endOfYear(),
            default => $date->copy()->endOfMonth(),
        };
    }

    /**
     * Get comparison with previous period.
     */
    public function getComparisonWithPreviousPeriod(): array
    {
        $previousDate = match ($this->period_type) {
            'monthly' => $this->statement_date->copy()->subMonth(),
            'quarterly' => $this->statement_date->copy()->subQuarter(),
            'yearly' => $this->statement_date->copy()->subYear(),
            default => $this->statement_date->copy()->subMonth(),
        };

        $previous = self::where('statement_date', $previousDate)
            ->where('period_type', $this->period_type)
            ->first();

        if (!$previous) {
            return [];
        }

        return [
            'revenue_change' => $this->calculatePercentageChange($previous->total_revenue, $this->total_revenue),
            'gross_profit_change' => $this->calculatePercentageChange($previous->gross_profit, $this->gross_profit),
            'operating_income_change' => $this->calculatePercentageChange($previous->operating_income, $this->operating_income),
            'net_income_change' => $this->calculatePercentageChange($previous->net_income, $this->net_income),
            'gross_margin_change' => $previous->gross_profit_margin - $this->gross_profit_margin,
            'operating_margin_change' => $previous->operating_profit_margin - $this->operating_profit_margin,
            'net_margin_change' => $previous->net_profit_margin - $this->net_profit_margin,
        ];
    }

    /**
     * Calculate percentage change between two values.
     */
    private function calculatePercentageChange($oldValue, $newValue): float
    {
        if ($oldValue == 0) {
            return $newValue > 0 ? 100 : 0;
        }

        return (($newValue - $oldValue) / $oldValue) * 100;
    }

    /**
     * Get financial health indicators.
     */
    public function getFinancialHealthIndicators(): array
    {
        return [
            'profitability' => [
                'gross_profit_margin' => $this->gross_profit_margin,
                'operating_profit_margin' => $this->operating_profit_margin,
                'net_profit_margin' => $this->net_profit_margin,
                'is_profitable' => $this->net_income > 0,
            ],
            'efficiency' => [
                'revenue_per_expense' => $this->operating_expenses > 0 ? $this->total_revenue / $this->operating_expenses : 0,
                'cost_control' => $this->total_revenue > 0 ? ($this->operating_expenses / $this->total_revenue) * 100 : 0,
            ],
            'growth_indicators' => $this->getComparisonWithPreviousPeriod(),
        ];
    }

    /**
     * Export statement data for reporting.
     */
    public function exportData(): array
    {
        return [
            'statement_info' => [
                'date' => $this->statement_date,
                'period_type' => $this->period_type,
                'generated_at' => $this->updated_at,
            ],
            'revenue' => [
                'repair_services' => $this->repair_services_revenue,
                'parts_sales' => $this->parts_sales_revenue,
                'pos_sales' => $this->pos_sales_revenue,
                'other' => $this->other_revenue,
                'total' => $this->total_revenue,
            ],
            'costs_expenses' => [
                'cost_of_goods_sold' => $this->cost_of_goods_sold,
                'staff_salaries' => $this->staff_salaries,
                'rent_utilities' => $this->rent_utilities,
                'marketing_expenses' => $this->marketing_expenses,
                'equipment_depreciation' => $this->equipment_depreciation,
                'other_expenses' => $this->other_expenses,
                'total_operating_expenses' => $this->operating_expenses,
            ],
            'profitability' => [
                'gross_profit' => $this->gross_profit,
                'operating_income' => $this->operating_income,
                'net_income' => $this->net_income,
                'gross_profit_margin' => $this->gross_profit_margin,
                'operating_profit_margin' => $this->operating_profit_margin,
                'net_profit_margin' => $this->net_profit_margin,
            ],
            'other' => [
                'interest_income' => $this->interest_income,
                'interest_expense' => $this->interest_expense,
            ],
            'detailed_breakdown' => $this->detailed_breakdown,
        ];
    }
}
