<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class NotificationTemplate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'slug',
        'type',
        'category',
        'subject',
        'message_ar',
        'message_en',
        'variables',
        'description',
        'is_active',
        'is_system',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'variables' => 'array',
        'is_active' => 'boolean',
        'is_system' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get notifications that use this template.
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class, 'template_id');
    }

    /**
     * Scope for active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for system templates.
     */
    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }

    /**
     * Scope for custom templates.
     */
    public function scopeCustom($query)
    {
        return $query->where('is_system', false);
    }

    /**
     * Scope for templates by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for templates by category.
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Render template with provided data.
     */
    public function render(array $data = [], string $language = 'ar'): string
    {
        $message = $language === 'en' && $this->message_en 
            ? $this->message_en 
            : $this->message_ar;

        // Replace template variables with actual data
        foreach ($data as $key => $value) {
            $message = str_replace("{{$key}}", $value, $message);
        }

        return $message;
    }

    /**
     * Render subject with provided data.
     */
    public function renderSubject(array $data = []): ?string
    {
        if (!$this->subject) {
            return null;
        }

        $subject = $this->subject;

        // Replace template variables with actual data
        foreach ($data as $key => $value) {
            $subject = str_replace("{{$key}}", $value, $subject);
        }

        return $subject;
    }

    /**
     * Get available variables for this template.
     */
    public function getAvailableVariables(): array
    {
        return $this->variables ?? [];
    }

    /**
     * Validate template variables against provided data.
     */
    public function validateData(array $data): array
    {
        $availableVars = $this->getAvailableVariables();
        $missingVars = [];

        foreach ($availableVars as $var) {
            if (!array_key_exists($var, $data)) {
                $missingVars[] = $var;
            }
        }

        return $missingVars;
    }

    /**
     * Get category display name.
     */
    public function getCategoryDisplayAttribute(): string
    {
        return match($this->category) {
            'status_update' => __('app.notifications.status_update'),
            'appointment_reminder' => __('app.notifications.appointment_reminder'),
            'pickup_ready' => __('app.notifications.pickup_ready'),
            'payment_reminder' => __('app.notifications.payment_reminder'),
            'satisfaction_survey' => __('app.notifications.satisfaction_survey'),
            'general' => __('app.notifications.general'),
            default => $this->category
        };
    }

    /**
     * Get type display name.
     */
    public function getTypeDisplayAttribute(): string
    {
        return match($this->type) {
            'sms' => __('app.notifications.sms'),
            'whatsapp' => __('app.notifications.whatsapp'),
            'email' => __('app.notifications.email'),
            default => $this->type
        };
    }

    /**
     * Check if template can be deleted.
     */
    public function canBeDeleted(): bool
    {
        return !$this->is_system;
    }

    /**
     * Get template preview with sample data.
     */
    public function getPreview(): string
    {
        $sampleData = [
            'customer_name' => 'أحمد محمد',
            'ticket_number' => 'NJ20250730001',
            'device_model' => 'iPhone 14 Pro',
            'status' => 'مكتمل',
            'estimated_date' => '2025-08-01',
            'cost' => '500 ريال',
            'shop_name' => 'ورشة NJ للإصلاح',
            'shop_phone' => '0501234567',
        ];

        return $this->render($sampleData);
    }
}
