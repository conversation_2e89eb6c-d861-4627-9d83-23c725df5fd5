<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_customer_preferences', function (Blueprint $table) {
            $table->id();

            // Customer identification
            $table->string('customer_phone', 20)->unique();
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');

            // Communication preferences
            $table->boolean('whatsapp_enabled')->default(true);
            $table->boolean('sms_fallback_enabled')->default(true);
            $table->string('preferred_language', 5)->default('ar');
            $table->string('timezone', 50)->default('Asia/Riyadh');

            // Notification preferences
            $table->boolean('status_updates')->default(true);
            $table->boolean('appointment_reminders')->default(true);
            $table->boolean('payment_reminders')->default(true);
            $table->boolean('marketing_messages')->default(false);
            $table->boolean('promotional_offers')->default(false);

            // Timing preferences
            $table->time('quiet_hours_start')->default('22:00');
            $table->time('quiet_hours_end')->default('08:00');
            $table->json('preferred_days')->nullable(); // Days of week for non-urgent messages

            // Message frequency limits
            $table->integer('max_messages_per_day')->default(5);
            $table->integer('max_marketing_per_week')->default(2);
            $table->timestamp('last_marketing_sent')->nullable();

            // Opt-in/Opt-out tracking
            $table->timestamp('opted_in_at')->nullable();
            $table->timestamp('opted_out_at')->nullable();
            $table->string('opt_out_reason')->nullable();
            $table->boolean('is_opted_out')->default(false);

            // Template preferences
            $table->json('blocked_templates')->nullable(); // Template IDs customer doesn't want
            $table->json('preferred_templates')->nullable(); // Template IDs customer prefers

            // Interaction history
            $table->integer('total_messages_received')->default(0);
            $table->integer('total_messages_sent')->default(0);
            $table->decimal('response_rate', 5, 2)->default(0);
            $table->timestamp('last_interaction_at')->nullable();
            $table->timestamp('last_response_at')->nullable();

            // Customer satisfaction
            $table->decimal('satisfaction_score', 3, 2)->nullable(); // 1.00 to 5.00
            $table->integer('satisfaction_votes')->default(0);
            $table->timestamp('last_feedback_at')->nullable();

            // Compliance and privacy
            $table->boolean('data_processing_consent')->default(false);
            $table->timestamp('consent_given_at')->nullable();
            $table->string('consent_source')->nullable(); // 'whatsapp', 'website', 'in_person'
            $table->boolean('marketing_consent')->default(false);

            $table->timestamps();

            // Indexes
            $table->index(['customer_phone', 'whatsapp_enabled'], 'wa_cust_pref_phone_enabled_idx');
            $table->index(['preferred_language', 'whatsapp_enabled'], 'wa_cust_pref_lang_enabled_idx');
            $table->index(['is_opted_out', 'whatsapp_enabled'], 'wa_cust_pref_opted_enabled_idx');
            $table->index('last_interaction_at', 'wa_cust_pref_last_interaction_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_customer_preferences');
    }
};
