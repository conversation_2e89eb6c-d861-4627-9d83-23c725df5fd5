<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions first
        $this->command->info('Creating permissions...');
        Permission::createDefaults();

        // Create roles with permissions
        $this->command->info('Creating roles...');
        Role::createDefaults();

        // Create default admin user if not exists
        $this->command->info('Creating default admin user...');
        $adminUser = User::where('email', '<EMAIL>')->first();
        
        if (!$adminUser) {
            $adminUser = User::create([
                'name' => 'System Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('Admin@123456'),
                'role' => 'admin',
                'is_active' => true,
                'email_verified_at' => now(),
                'phone_number' => '+966501234567',
                'address' => 'System Administrator',
            ]);

            $this->command->info('Default admin user created: <EMAIL> / Admin@123456');
        } else {
            $this->command->info('Admin user already exists.');
        }

        // Create sample users for different roles
        $this->createSampleUsers();

        $this->command->info('Role and permission seeding completed!');
    }

    /**
     * Create sample users for different roles.
     */
    private function createSampleUsers(): void
    {
        $sampleUsers = [
            [
                'name' => 'محمد أحمد',
                'email' => '<EMAIL>',
                'role' => 'manager',
                'phone_number' => '+966501234568',
            ],
            [
                'name' => 'فاطمة علي',
                'email' => '<EMAIL>',
                'role' => 'technician',
                'phone_number' => '+966501234569',
            ],
            [
                'name' => 'عبدالله محمد',
                'email' => '<EMAIL>',
                'role' => 'receptionist',
                'phone_number' => '+966501234570',
            ],
            [
                'name' => 'نورا سعد',
                'email' => '<EMAIL>',
                'role' => 'cashier',
                'phone_number' => '+966501234571',
            ],
        ];

        foreach ($sampleUsers as $userData) {
            $existingUser = User::where('email', $userData['email'])->first();
            
            if (!$existingUser) {
                User::create([
                    'name' => $userData['name'],
                    'email' => $userData['email'],
                    'password' => Hash::make('Password@123'),
                    'role' => $userData['role'],
                    'is_active' => true,
                    'email_verified_at' => now(),
                    'phone_number' => $userData['phone_number'],
                    'address' => 'Sample Address',
                ]);

                $this->command->info("Created sample user: {$userData['email']} ({$userData['role']})");
            }
        }
    }
}
