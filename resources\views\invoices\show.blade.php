@extends('layouts.app')

@section('title', 'فاتورة رقم ' . $invoice->invoice_number)

@push('styles')
<style>
.invoice-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.invoice-card {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    margin-bottom: 1.5rem;
}

.invoice-card-header {
    background: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
}

.invoice-card-body {
    padding: 1.5rem;
}

.company-info {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e3e6f0;
}

.company-name {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.invoice-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.detail-section h6 {
    color: #667eea;
    font-weight: bold;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e3e6f0;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fc;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: #5a5c69;
}

.detail-value {
    color: #858796;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
}

.status-draft {
    background: #e2e3e5;
    color: #6c757d;
}

.status-sent {
    background: #cce5ff;
    color: #0066cc;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.payment-status-pending {
    background: #fff3cd;
    color: #856404;
}

.payment-status-partial {
    background: #cce5ff;
    color: #0066cc;
}

.payment-status-paid {
    background: #d4edda;
    color: #155724;
}

.payment-status-overdue {
    background: #f8d7da;
    color: #721c24;
}

.items-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1.5rem;
}

.items-table th {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    padding: 1rem 0.75rem;
    font-weight: 600;
    color: #5a5c69;
    text-align: right;
}

.items-table td {
    border: 1px solid #e3e6f0;
    padding: 0.75rem;
    vertical-align: middle;
}

.item-type-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
}

.type-service {
    background: #d4edda;
    color: #155724;
}

.type-part {
    background: #cce5ff;
    color: #0066cc;
}

.type-labor {
    background: #fff3cd;
    color: #856404;
}

.type-other {
    background: #e2e3e5;
    color: #6c757d;
}

.totals-section {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e3e6f0;
}

.total-row:last-child {
    border-bottom: none;
    font-weight: bold;
    font-size: 1.2rem;
    color: #667eea;
    background: #fff;
    margin: 0.5rem -1.5rem -1.5rem;
    padding: 1rem 1.5rem;
    border-radius: 0 0 0.5rem 0.5rem;
}

.total-label {
    font-weight: 600;
}

.total-value {
    font-weight: 600;
    font-size: 1.1rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-action {
    padding: 0.75rem 1.5rem;
    border-radius: 0.35rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    text-decoration: none;
}

.btn-edit {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    color: #212529;
}

.btn-send {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.btn-pdf {
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
}

.btn-print {
    background: linear-gradient(45deg, #6c757d, #5a6268);
    color: white;
}

.btn-payment {
    background: linear-gradient(45deg, #17a2b8, #138496);
    color: white;
}

.payments-section {
    margin-top: 2rem;
}

.payment-item {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.payment-info {
    display: flex;
    flex-direction: column;
}

.payment-amount {
    font-weight: bold;
    font-size: 1.1rem;
    color: #28a745;
}

.payment-method {
    font-size: 0.875rem;
    color: #6c757d;
}

.payment-date {
    font-size: 0.875rem;
    color: #6c757d;
}

.overdue-warning {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 1rem;
    border-radius: 0.35rem;
    margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
    .invoice-header {
        padding: 1rem;
    }
    
    .invoice-details {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .invoice-card-body {
        padding: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn-action {
        width: 100%;
        justify-content: center;
    }
    
    .items-table {
        font-size: 0.875rem;
    }
}

@media print {
    .invoice-header,
    .action-buttons,
    .btn {
        display: none !important;
    }
    
    .invoice-card {
        box-shadow: none;
        border: none;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="invoice-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">فاتورة رقم {{ $invoice->invoice_number }}</h1>
                <p class="mb-0 opacity-75">تفاصيل الفاتورة الكاملة</p>
            </div>
            <div class="action-buttons">
                @if($invoice->status === 'draft')
                    <a href="{{ route('invoices.edit', $invoice) }}" class="btn-action btn-edit">
                        <i class="fas fa-edit"></i>تعديل
                    </a>
                @endif
                
                @if($invoice->status === 'draft')
                    <form method="POST" action="{{ route('invoices.send', $invoice) }}" style="display: inline;">
                        @csrf
                        <button type="submit" class="btn-action btn-send" onclick="return confirm('هل تريد إرسال هذه الفاتورة؟')">
                            <i class="fas fa-paper-plane"></i>إرسال
                        </button>
                    </form>
                @endif
                
                <a href="{{ route('invoices.pdf', $invoice) }}" class="btn-action btn-pdf" target="_blank">
                    <i class="fas fa-file-pdf"></i>PDF
                </a>
                
                <button onclick="window.print()" class="btn-action btn-print">
                    <i class="fas fa-print"></i>طباعة
                </button>
                
                @if($invoice->payment_status !== 'paid')
                    <a href="{{ route('payments.create', ['invoice_id' => $invoice->id]) }}" class="btn-action btn-payment">
                        <i class="fas fa-credit-card"></i>تسجيل دفعة
                    </a>
                @endif
                
                <a href="{{ route('invoices.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة
                </a>
            </div>
        </div>
    </div>

    <!-- Overdue Warning -->
    @if($invoice->due_date < now() && $invoice->payment_status !== 'paid')
        <div class="overdue-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> هذه الفاتورة متأخرة عن موعد الاستحقاق بـ {{ $invoice->due_date->diffInDays(now()) }} يوم.
        </div>
    @endif

    <!-- Invoice Details -->
    <div class="invoice-card">
        <div class="invoice-card-body">
            <!-- Company Info -->
            <div class="company-info">
                <div class="company-name">{{ $invoice->company_name }}</div>
                <div>{{ $invoice->company_address }}</div>
                <div>هاتف: {{ $invoice->company_phone }} | بريد إلكتروني: {{ $invoice->company_email }}</div>
                @if($invoice->company_tax_number)
                    <div>الرقم الضريبي: {{ $invoice->company_tax_number }}</div>
                @endif
            </div>

            <!-- Invoice & Customer Details -->
            <div class="invoice-details">
                <div class="detail-section">
                    <h6>تفاصيل الفاتورة</h6>
                    <div class="detail-item">
                        <span class="detail-label">رقم الفاتورة:</span>
                        <span class="detail-value">{{ $invoice->invoice_number }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تاريخ الفاتورة:</span>
                        <span class="detail-value">{{ $invoice->invoice_date->format('Y-m-d') }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تاريخ الاستحقاق:</span>
                        <span class="detail-value">{{ $invoice->due_date->format('Y-m-d') }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">حالة الفاتورة:</span>
                        <span class="detail-value">
                            <span class="status-badge status-{{ $invoice->status }}">
                                @switch($invoice->status)
                                    @case('draft') مسودة @break
                                    @case('sent') مرسلة @break
                                    @case('cancelled') ملغية @break
                                    @default {{ $invoice->status }}
                                @endswitch
                            </span>
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">حالة الدفع:</span>
                        <span class="detail-value">
                            <span class="status-badge payment-status-{{ $invoice->payment_status }}">
                                @switch($invoice->payment_status)
                                    @case('pending') معلق @break
                                    @case('partial') جزئي @break
                                    @case('paid') مدفوع @break
                                    @case('overdue') متأخر @break
                                    @default {{ $invoice->payment_status }}
                                @endswitch
                            </span>
                        </span>
                    </div>
                    @if($invoice->repairTicket)
                        <div class="detail-item">
                            <span class="detail-label">بطاقة الإصلاح:</span>
                            <span class="detail-value">
                                <a href="{{ route('repair-tickets.show', $invoice->repairTicket) }}" class="text-decoration-none">
                                    #{{ $invoice->repairTicket->ticket_number }}
                                </a>
                            </span>
                        </div>
                    @endif
                </div>

                <div class="detail-section">
                    <h6>بيانات العميل</h6>
                    <div class="detail-item">
                        <span class="detail-label">الاسم:</span>
                        <span class="detail-value">{{ $invoice->customer_name }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الهاتف:</span>
                        <span class="detail-value">
                            <a href="tel:{{ $invoice->customer_phone }}" class="text-decoration-none">
                                {{ $invoice->customer_phone }}
                            </a>
                        </span>
                    </div>
                    @if($invoice->customer_email)
                        <div class="detail-item">
                            <span class="detail-label">البريد الإلكتروني:</span>
                            <span class="detail-value">
                                <a href="mailto:{{ $invoice->customer_email }}" class="text-decoration-none">
                                    {{ $invoice->customer_email }}
                                </a>
                            </span>
                        </div>
                    @endif
                    @if($invoice->customer_address)
                        <div class="detail-item">
                            <span class="detail-label">العنوان:</span>
                            <span class="detail-value">{{ $invoice->customer_address }}</span>
                        </div>
                    @endif
                    <div class="detail-item">
                        <span class="detail-label">أنشأ بواسطة:</span>
                        <span class="detail-value">{{ $invoice->createdBy->name }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تاريخ الإنشاء:</span>
                        <span class="detail-value">{{ $invoice->created_at->format('Y-m-d H:i') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Items -->
    <div class="invoice-card">
        <div class="invoice-card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                عناصر الفاتورة
            </h5>
        </div>
        <div class="invoice-card-body">
            <table class="items-table">
                <thead>
                    <tr>
                        <th style="width: 5%">#</th>
                        <th style="width: 15%">النوع</th>
                        <th style="width: 30%">الوصف</th>
                        <th style="width: 10%">الكمية</th>
                        <th style="width: 15%">السعر</th>
                        <th style="width: 15%">الإجمالي</th>
                        <th style="width: 10%">الضريبة</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($invoice->items as $index => $item)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <span class="item-type-badge type-{{ $item->item_type }}">
                                    @switch($item->item_type)
                                        @case('service') خدمة @break
                                        @case('part') قطعة @break
                                        @case('labor') عمالة @break
                                        @case('other') أخرى @break
                                        @default {{ $item->item_type }}
                                    @endswitch
                                </span>
                            </td>
                            <td>
                                <div class="fw-bold">{{ $item->item_name }}</div>
                                @if($item->item_description)
                                    <small class="text-muted">{{ $item->item_description }}</small>
                                @endif
                                @if($item->item_sku)
                                    <small class="text-muted d-block">SKU: {{ $item->item_sku }}</small>
                                @endif
                            </td>
                            <td>@arabicNumber($item->quantity) {{ $item->unit_of_measure }}</td>
                            <td>@arabicCurrency($item->unit_price)</td>
                            <td>@arabicCurrency($item->total_price)</td>
                            <td>
                                @if($item->is_taxable)
                                    @arabicCurrency($item->tax_amount)
                                @else
                                    <span class="text-muted">معفى</span>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Totals -->
    <div class="totals-section">
        <div class="total-row">
            <span class="total-label">المجموع الفرعي:</span>
            <span class="total-value">@arabicCurrency($invoice->subtotal)</span>
        </div>
        @if($invoice->discount_amount > 0)
            <div class="total-row">
                <span class="total-label">الخصم 
                    @if($invoice->discount_percentage > 0)
                        (@arabicNumber($invoice->discount_percentage)%)
                    @endif:
                </span>
                <span class="total-value">@arabicCurrency($invoice->discount_amount)</span>
            </div>
        @endif
        <div class="total-row">
            <span class="total-label">الضريبة (@arabicNumber($invoice->tax_rate)%):</span>
            <span class="total-value">@arabicCurrency($invoice->tax_amount)</span>
        </div>
        <div class="total-row">
            <span class="total-label">المجموع الإجمالي:</span>
            <span class="total-value">@arabicCurrency($invoice->total_amount)</span>
        </div>
    </div>

    <!-- Payments Section -->
    @if($invoice->payments->count() > 0)
        <div class="invoice-card payments-section">
            <div class="invoice-card-header">
                <h5 class="mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    المدفوعات المسجلة
                </h5>
            </div>
            <div class="invoice-card-body">
                @foreach($invoice->payments as $payment)
                    <div class="payment-item">
                        <div class="payment-info">
                            <div class="payment-amount">@arabicCurrency($payment->amount)</div>
                            <div class="payment-method">
                                <i class="{{ $payment->payment_method_icon }} me-1"></i>
                                {{ $payment->payment_method_display }}
                            </div>
                            <div class="payment-date">{{ $payment->payment_date->format('Y-m-d') }}</div>
                        </div>
                        <div>
                            <span class="status-badge payment-status-{{ $payment->status }}">
                                {{ $payment->status_display }}
                            </span>
                        </div>
                    </div>
                @endforeach
                
                <div class="mt-3 pt-3 border-top">
                    <div class="d-flex justify-content-between">
                        <strong>إجمالي المدفوع:</strong>
                        <strong class="text-success">@arabicCurrency($invoice->paid_amount)</strong>
                    </div>
                    @if($invoice->total_amount > $invoice->paid_amount)
                        <div class="d-flex justify-content-between">
                            <strong>المتبقي:</strong>
                            <strong class="text-danger">@arabicCurrency($invoice->total_amount - $invoice->paid_amount)</strong>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    @endif

    <!-- Notes and Terms -->
    @if($invoice->notes || $invoice->terms_conditions)
        <div class="invoice-card">
            <div class="invoice-card-body">
                @if($invoice->notes)
                    <div class="mb-3">
                        <h6 class="text-muted">ملاحظات:</h6>
                        <p>{{ $invoice->notes }}</p>
                    </div>
                @endif
                
                @if($invoice->terms_conditions)
                    <div>
                        <h6 class="text-muted">الشروط والأحكام:</h6>
                        <p class="small text-muted">{{ $invoice->terms_conditions }}</p>
                    </div>
                @endif
            </div>
        </div>
    @endif
</div>
@endsection
