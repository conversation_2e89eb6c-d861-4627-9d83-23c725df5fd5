<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NotificationTemplate;

class NotificationTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $templates = [
            [
                'name' => 'تحديث حالة الإصلاح',
                'slug' => 'status-update',
                'type' => 'sms',
                'category' => 'status_update',
                'subject' => null,
                'message_ar' => 'عزيزي {customer_name}، تم تحديث حالة جهازك {device_model} (تذكرة #{ticket_number}) إلى: {new_status}. للاستفسار: {shop_phone} - {shop_name}',
                'message_en' => 'Dear {customer_name}, your device {device_model} (ticket #{ticket_number}) status has been updated to: {new_status}. For inquiries: {shop_phone} - {shop_name}',
                'variables' => ['customer_name', 'device_model', 'ticket_number', 'new_status', 'shop_phone', 'shop_name'],
                'description' => 'يتم إرسالها عند تغيير حالة تذكرة الإصلاح',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'جاهز للاستلام',
                'slug' => 'pickup-ready',
                'type' => 'sms',
                'category' => 'pickup_ready',
                'subject' => null,
                'message_ar' => 'مبروك {customer_name}! جهازك {device_model} جاهز للاستلام. التكلفة: {cost}. يرجى الحضور خلال 7 أيام. {shop_name} - {shop_phone}',
                'message_en' => 'Congratulations {customer_name}! Your device {device_model} is ready for pickup. Cost: {cost}. Please collect within 7 days. {shop_name} - {shop_phone}',
                'variables' => ['customer_name', 'device_model', 'cost', 'shop_name', 'shop_phone'],
                'description' => 'يتم إرسالها عند اكتمال الإصلاح',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'تذكير بالموعد',
                'slug' => 'appointment-reminder',
                'type' => 'sms',
                'category' => 'appointment_reminder',
                'subject' => null,
                'message_ar' => 'تذكير: لديك موعد غداً لاستلام جهازك {device_model} (تذكرة #{ticket_number}). الوقت المتوقع: {estimated_date}. {shop_name} - {shop_phone}',
                'message_en' => 'Reminder: You have an appointment tomorrow to collect your device {device_model} (ticket #{ticket_number}). Expected time: {estimated_date}. {shop_name} - {shop_phone}',
                'variables' => ['device_model', 'ticket_number', 'estimated_date', 'shop_name', 'shop_phone'],
                'description' => 'تذكير بموعد الاستلام',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'تذكير بالدفع',
                'slug' => 'payment-reminder',
                'type' => 'sms',
                'category' => 'payment_reminder',
                'subject' => null,
                'message_ar' => 'عزيزي {customer_name}، جهازك {device_model} جاهز للاستلام منذ أسبوع. المبلغ المستحق: {cost}. يرجى الحضور لتجنب رسوم التخزين. {shop_name} - {shop_phone}',
                'message_en' => 'Dear {customer_name}, your device {device_model} has been ready for pickup for a week. Amount due: {cost}. Please collect to avoid storage fees. {shop_name} - {shop_phone}',
                'variables' => ['customer_name', 'device_model', 'cost', 'shop_name', 'shop_phone'],
                'description' => 'تذكير بالدفع واستلام الجهاز',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'استطلاع رضا العملاء',
                'slug' => 'satisfaction-survey',
                'type' => 'sms',
                'category' => 'satisfaction_survey',
                'subject' => null,
                'message_ar' => 'شكراً لثقتك {customer_name}! كيف كانت تجربتك معنا في إصلاح {device_model}؟ تقييمك يهمنا. رد بـ: ممتاز/جيد/مقبول/ضعيف. {shop_name}',
                'message_en' => 'Thank you for your trust {customer_name}! How was your experience with us repairing {device_model}? Your feedback matters. Reply: Excellent/Good/Fair/Poor. {shop_name}',
                'variables' => ['customer_name', 'device_model', 'shop_name'],
                'description' => 'استطلاع رضا العملاء بعد الانتهاء من الخدمة',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'ترحيب بالعميل الجديد',
                'slug' => 'welcome-new-customer',
                'type' => 'sms',
                'category' => 'general',
                'subject' => null,
                'message_ar' => 'أهلاً وسهلاً {customer_name}! شكراً لاختيارك {shop_name}. تم استلام جهازك {device_model} وسنبدأ الفحص فوراً. سنبقيك على اطلاع بكل جديد. {shop_phone}',
                'message_en' => 'Welcome {customer_name}! Thank you for choosing {shop_name}. We have received your device {device_model} and will start inspection immediately. We will keep you updated. {shop_phone}',
                'variables' => ['customer_name', 'shop_name', 'device_model', 'shop_phone'],
                'description' => 'رسالة ترحيب للعملاء الجدد',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'تأكيد الاستلام',
                'slug' => 'delivery-confirmation',
                'type' => 'sms',
                'category' => 'general',
                'subject' => null,
                'message_ar' => 'تم تسليم جهازك {device_model} بنجاح. شكراً لثقتك في {shop_name}. نتطلع لخدمتك مرة أخرى. للدعم الفني: {shop_phone}',
                'message_en' => 'Your device {device_model} has been successfully delivered. Thank you for trusting {shop_name}. We look forward to serving you again. Technical support: {shop_phone}',
                'variables' => ['device_model', 'shop_name', 'shop_phone'],
                'description' => 'تأكيد تسليم الجهاز للعميل',
                'is_active' => true,
                'is_system' => true,
            ],
            // WhatsApp Templates
            [
                'name' => 'تحديث حالة الإصلاح - واتساب',
                'slug' => 'status-update-whatsapp',
                'type' => 'whatsapp',
                'category' => 'status_update',
                'subject' => null,
                'message_ar' => '🔧 *تحديث حالة الإصلاح*\n\nعزيزي *{customer_name}*\n\nتم تحديث حالة جهازك:\n📱 الجهاز: {device_model}\n🎫 رقم التذكرة: #{ticket_number}\n✅ الحالة الجديدة: *{new_status}*\n\nللاستفسار: {shop_phone}\n{shop_name}',
                'message_en' => '🔧 *Repair Status Update*\n\nDear *{customer_name}*\n\nYour device status has been updated:\n📱 Device: {device_model}\n🎫 Ticket: #{ticket_number}\n✅ New Status: *{new_status}*\n\nFor inquiries: {shop_phone}\n{shop_name}',
                'variables' => ['customer_name', 'device_model', 'ticket_number', 'new_status', 'shop_phone', 'shop_name'],
                'description' => 'تحديث حالة الإصلاح عبر واتساب',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'جاهز للاستلام - واتساب',
                'slug' => 'pickup-ready-whatsapp',
                'type' => 'whatsapp',
                'category' => 'pickup_ready',
                'subject' => null,
                'message_ar' => '🎉 *جهازك جاهز للاستلام!*\n\nمبروك {customer_name}!\n\n📱 الجهاز: {device_model}\n💰 التكلفة: {cost}\n⏰ يرجى الحضور خلال 7 أيام\n\n📍 {shop_name}\n📞 {shop_phone}',
                'message_en' => '🎉 *Your Device is Ready!*\n\nCongratulations {customer_name}!\n\n📱 Device: {device_model}\n💰 Cost: {cost}\n⏰ Please collect within 7 days\n\n📍 {shop_name}\n📞 {shop_phone}',
                'variables' => ['customer_name', 'device_model', 'cost', 'shop_name', 'shop_phone'],
                'description' => 'إشعار جاهزية الاستلام عبر واتساب',
                'is_active' => true,
                'is_system' => true,
            ],
        ];

        foreach ($templates as $template) {
            NotificationTemplate::updateOrCreate(
                ['slug' => $template['slug']],
                $template
            );
        }

        $this->command->info('Created ' . count($templates) . ' notification templates.');
    }
}
