<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Customer;
use App\Models\SalesAnalytics;
use App\Models\CustomerPaymentSummary;
use App\Models\PaymentMethodConfig;
use App\Models\InventoryItem;
use App\Models\RepairTicket;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class SalesController extends Controller
{
    /**
     * Display sales dashboard with analytics.
     */
    public function dashboard(Request $request): View
    {
        $period = $request->get('period', 'monthly');
        $startDate = $this->getStartDate($period);
        $endDate = now();

        // Get current period analytics
        $currentAnalytics = SalesAnalytics::where('period_type', $period)
            ->whereBetween('analytics_date', [$startDate, $endDate])
            ->orderBy('analytics_date', 'desc')
            ->first();

        if (!$currentAnalytics) {
            $currentAnalytics = SalesAnalytics::generateAnalytics(now(), $period);
        }

        // Get comparison with previous period
        $comparison = $currentAnalytics->getComparisonWithPreviousPeriod();

        // Get top performing metrics
        $topMetrics = $this->getTopPerformingMetrics($startDate, $endDate);

        // Get recent invoices
        $recentInvoices = Invoice::with(['customer', 'repairTicket'])
            ->whereBetween('invoice_date', [$startDate, $endDate])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get sales trends for chart
        $salesTrends = $this->getSalesTrends($period, $startDate, $endDate);

        // Get payment method performance
        $paymentMethodStats = $this->getPaymentMethodStats($startDate, $endDate);

        return view('sales.dashboard', compact(
            'currentAnalytics',
            'comparison',
            'topMetrics',
            'recentInvoices',
            'salesTrends',
            'paymentMethodStats',
            'period'
        ));
    }

    /**
     * Display sales analytics page.
     */
    public function analytics(Request $request): View
    {
        $period = $request->get('period', 'monthly');
        $startDate = $request->get('start_date') ? Carbon::parse($request->start_date) : $this->getStartDate($period);
        $endDate = $request->get('end_date') ? Carbon::parse($request->end_date) : now();

        // Get analytics data for the period
        $analytics = SalesAnalytics::getAnalyticsForRange($startDate, $endDate, $period);

        // Get detailed breakdowns
        $channelBreakdown = $this->getSalesChannelBreakdown($startDate, $endDate);
        $customerAnalysis = $this->getCustomerAnalysis($startDate, $endDate);
        $productPerformance = $this->getProductPerformance($startDate, $endDate);
        $profitabilityAnalysis = $this->getProfitabilityAnalysis($startDate, $endDate);

        return view('sales.analytics', compact(
            'analytics',
            'channelBreakdown',
            'customerAnalysis',
            'productPerformance',
            'profitabilityAnalysis',
            'period',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Display customer payment analysis.
     */
    public function customerPaymentAnalysis(Request $request): View
    {
        $query = CustomerPaymentSummary::with('customer');

        // Filter by payment behavior
        if ($request->filled('behavior')) {
            $query->where('payment_behavior', $request->behavior);
        }

        // Filter by credit status
        if ($request->filled('credit_status')) {
            if ($request->credit_status === 'on_hold') {
                $query->where('credit_hold', true);
            } elseif ($request->credit_status === 'over_limit') {
                $query->whereRaw('outstanding_balance > credit_limit');
            }
        }

        // Search by customer name
        if ($request->filled('search')) {
            $query->whereHas('customer', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('phone_number', 'like', '%' . $request->search . '%');
            });
        }

        $customerSummaries = $query->orderBy('outstanding_balance', 'desc')
            ->paginate(20)
            ->withQueryString();

        // Get summary statistics
        $stats = [
            'total_customers' => CustomerPaymentSummary::count(),
            'excellent_customers' => CustomerPaymentSummary::where('payment_behavior', 'excellent')->count(),
            'poor_customers' => CustomerPaymentSummary::where('payment_behavior', 'poor')->count(),
            'customers_on_hold' => CustomerPaymentSummary::where('credit_hold', true)->count(),
            'total_outstanding' => CustomerPaymentSummary::sum('outstanding_balance'),
            'average_payment_days' => CustomerPaymentSummary::avg('average_payment_days'),
        ];

        return view('sales.customer-payment-analysis', compact('customerSummaries', 'stats'));
    }

    /**
     * Generate sales report.
     */
    public function generateReport(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'report_type' => 'required|in:sales_summary,profit_analysis,customer_analysis,product_performance',
            'period' => 'required|in:daily,weekly,monthly,yearly',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'required|in:json,pdf,excel'
        ]);

        $startDate = Carbon::parse($validated['start_date']);
        $endDate = Carbon::parse($validated['end_date']);

        $reportData = match ($validated['report_type']) {
            'sales_summary' => $this->generateSalesSummaryReport($startDate, $endDate, $validated['period']),
            'profit_analysis' => $this->generateProfitAnalysisReport($startDate, $endDate),
            'customer_analysis' => $this->generateCustomerAnalysisReport($startDate, $endDate),
            'product_performance' => $this->generateProductPerformanceReport($startDate, $endDate),
        };

        return response()->json([
            'success' => true,
            'data' => $reportData,
            'meta' => [
                'report_type' => $validated['report_type'],
                'period' => $validated['period'],
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
                'generated_at' => now()->toISOString()
            ]
        ]);
    }

    /**
     * Update customer credit limit.
     */
    public function updateCustomerCredit(Request $request, Customer $customer): RedirectResponse
    {
        $validated = $request->validate([
            'credit_limit' => 'required|numeric|min:0',
            'credit_hold' => 'boolean',
            'notes' => 'nullable|string|max:1000'
        ]);

        $summary = CustomerPaymentSummary::where('customer_id', $customer->id)->first();
        
        if (!$summary) {
            $summary = CustomerPaymentSummary::updateForCustomer($customer->id);
        }

        $summary->update([
            'credit_limit' => $validated['credit_limit'],
            'credit_hold' => $validated['credit_hold'] ?? false
        ]);

        return back()->with('success', 'تم تحديث حد الائتمان بنجاح');
    }

    /**
     * Get start date based on period.
     */
    private function getStartDate(string $period): Carbon
    {
        return match ($period) {
            'daily' => now()->startOfDay(),
            'weekly' => now()->startOfWeek(),
            'monthly' => now()->startOfMonth(),
            'yearly' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };
    }

    /**
     * Get top performing metrics.
     */
    private function getTopPerformingMetrics(Carbon $startDate, Carbon $endDate): array
    {
        $invoices = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->with(['customer', 'items.inventoryItem'])
            ->get();

        // Top customers by revenue
        $topCustomers = $invoices->groupBy('customer_id')
            ->map(function ($customerInvoices) {
                return [
                    'customer' => $customerInvoices->first()->customer,
                    'total_revenue' => $customerInvoices->sum('total_amount'),
                    'invoice_count' => $customerInvoices->count()
                ];
            })
            ->sortByDesc('total_revenue')
            ->take(5)
            ->values();

        // Top selling items
        $topItems = [];
        foreach ($invoices as $invoice) {
            foreach ($invoice->items as $item) {
                $key = $item->item_name;
                if (!isset($topItems[$key])) {
                    $topItems[$key] = [
                        'name' => $item->item_name,
                        'quantity' => 0,
                        'revenue' => 0
                    ];
                }
                $topItems[$key]['quantity'] += $item->quantity;
                $topItems[$key]['revenue'] += $item->total_price;
            }
        }

        $topItems = collect($topItems)
            ->sortByDesc('revenue')
            ->take(5)
            ->values();

        return [
            'top_customers' => $topCustomers,
            'top_items' => $topItems
        ];
    }

    /**
     * Get sales trends for chart.
     */
    private function getSalesTrends(string $period, Carbon $startDate, Carbon $endDate): array
    {
        $analytics = SalesAnalytics::where('period_type', $period)
            ->whereBetween('analytics_date', [$startDate, $endDate])
            ->orderBy('analytics_date')
            ->get();

        return [
            'labels' => $analytics->pluck('analytics_date')->map(function ($date) use ($period) {
                return match ($period) {
                    'daily' => $date->format('M d'),
                    'weekly' => 'Week ' . $date->weekOfYear,
                    'monthly' => $date->format('M Y'),
                    'yearly' => $date->format('Y'),
                    default => $date->format('M d'),
                };
            })->toArray(),
            'sales' => $analytics->pluck('total_sales')->toArray(),
            'profit' => $analytics->pluck('total_profit')->toArray(),
            'invoices' => $analytics->pluck('invoices_count')->toArray()
        ];
    }

    /**
     * Get payment method statistics.
     */
    private function getPaymentMethodStats(Carbon $startDate, Carbon $endDate): array
    {
        $payments = DB::table('payments')
            ->join('invoices', 'payments.invoice_id', '=', 'invoices.id')
            ->whereBetween('payments.payment_date', [$startDate, $endDate])
            ->where('payments.status', 'completed')
            ->select('payments.payment_method', DB::raw('COUNT(*) as count'), DB::raw('SUM(payments.amount) as total'))
            ->groupBy('payments.payment_method')
            ->get();

        $total = $payments->sum('total');

        return $payments->map(function ($payment) use ($total) {
            return [
                'method' => $payment->payment_method,
                'count' => $payment->count,
                'total' => $payment->total,
                'percentage' => $total > 0 ? ($payment->total / $total) * 100 : 0
            ];
        })->toArray();
    }

    /**
     * Get sales channel breakdown.
     */
    private function getSalesChannelBreakdown(Carbon $startDate, Carbon $endDate): array
    {
        return Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->select('sales_channel', DB::raw('COUNT(*) as count'), DB::raw('SUM(total_amount) as total'))
            ->groupBy('sales_channel')
            ->get()
            ->toArray();
    }

    /**
     * Get customer analysis.
     */
    private function getCustomerAnalysis(Carbon $startDate, Carbon $endDate): array
    {
        $customerStats = DB::table('invoices')
            ->join('customers', 'invoices.customer_id', '=', 'customers.id')
            ->whereBetween('invoices.invoice_date', [$startDate, $endDate])
            ->where('invoices.status', '!=', 'cancelled')
            ->select(
                'customers.id',
                'customers.name',
                DB::raw('COUNT(invoices.id) as invoice_count'),
                DB::raw('SUM(invoices.total_amount) as total_revenue'),
                DB::raw('AVG(invoices.total_amount) as avg_invoice_value'),
                DB::raw('SUM(invoices.profit_amount) as total_profit')
            )
            ->groupBy('customers.id', 'customers.name')
            ->orderBy('total_revenue', 'desc')
            ->limit(20)
            ->get();

        return $customerStats->toArray();
    }

    /**
     * Get product performance.
     */
    private function getProductPerformance(Carbon $startDate, Carbon $endDate): array
    {
        $itemStats = DB::table('invoice_items')
            ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->whereBetween('invoices.invoice_date', [$startDate, $endDate])
            ->where('invoices.status', '!=', 'cancelled')
            ->select(
                'invoice_items.item_name',
                'invoice_items.item_type',
                DB::raw('SUM(invoice_items.quantity) as total_quantity'),
                DB::raw('SUM(invoice_items.total_price) as total_revenue'),
                DB::raw('COUNT(DISTINCT invoices.id) as invoice_count')
            )
            ->groupBy('invoice_items.item_name', 'invoice_items.item_type')
            ->orderBy('total_revenue', 'desc')
            ->limit(20)
            ->get();

        return $itemStats->toArray();
    }

    /**
     * Get profitability analysis.
     */
    private function getProfitabilityAnalysis(Carbon $startDate, Carbon $endDate): array
    {
        $profitStats = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->selectRaw('
                COUNT(*) as total_invoices,
                SUM(total_amount) as total_revenue,
                SUM(cost_of_goods) as total_cost,
                SUM(profit_amount) as total_profit,
                AVG(profit_margin_percentage) as avg_profit_margin,
                COUNT(CASE WHEN profit_margin_percentage >= 50 THEN 1 END) as high_margin_count,
                COUNT(CASE WHEN profit_margin_percentage >= 25 AND profit_margin_percentage < 50 THEN 1 END) as medium_margin_count,
                COUNT(CASE WHEN profit_margin_percentage >= 10 AND profit_margin_percentage < 25 THEN 1 END) as low_margin_count,
                COUNT(CASE WHEN profit_margin_percentage < 10 THEN 1 END) as very_low_margin_count
            ')
            ->first();

        return $profitStats ? $profitStats->toArray() : [];
    }

    /**
     * Generate sales summary report.
     */
    private function generateSalesSummaryReport(Carbon $startDate, Carbon $endDate, string $period): array
    {
        $analytics = SalesAnalytics::getAnalyticsForRange($startDate, $endDate, $period);
        
        return [
            'summary' => [
                'total_sales' => $analytics->sum('total_sales'),
                'total_profit' => $analytics->sum('total_profit'),
                'total_invoices' => $analytics->sum('invoices_count'),
                'avg_invoice_value' => $analytics->avg('average_invoice_value'),
                'avg_profit_margin' => $analytics->avg('profit_margin')
            ],
            'trends' => $analytics->toArray()
        ];
    }

    /**
     * Generate profit analysis report.
     */
    private function generateProfitAnalysisReport(Carbon $startDate, Carbon $endDate): array
    {
        return $this->getProfitabilityAnalysis($startDate, $endDate);
    }

    /**
     * Generate customer analysis report.
     */
    private function generateCustomerAnalysisReport(Carbon $startDate, Carbon $endDate): array
    {
        return $this->getCustomerAnalysis($startDate, $endDate);
    }

    /**
     * Generate product performance report.
     */
    private function generateProductPerformanceReport(Carbon $startDate, Carbon $endDate): array
    {
        return $this->getProductPerformance($startDate, $endDate);
    }
}
