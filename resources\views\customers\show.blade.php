@extends('layouts.app')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Customer Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="bi bi-person-circle"></i> {{ $customer->name }}
                    </h1>
                    <p class="text-muted mb-0">{{ __('app.customers.details') }}</p>
                </div>
                <div class="btn-group" role="group">
                    <a href="{{ route('customers.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> {{ __('app.back') }} {{ __('app.nav.customers') }}
                    </a>
                    <a href="{{ route('customers.edit', $customer) }}" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> {{ __('app.customers.edit') }}
                    </a>
                    <a href="{{ route('repair-tickets.create', ['customer_id' => $customer->id]) }}" class="btn btn-success">
                        <i class="bi bi-plus-circle"></i> {{ __('app.repair_tickets.create') }}
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Customer Information -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-info-circle"></i> {{ __('app.customers.information') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.customers.name') }}</label>
                                <p class="mb-0">{{ $customer->name }}</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">{{ __('app.customers.phone_number') }}</label>
                                <p class="mb-0">
                                    <a href="tel:{{ $customer->phone_number }}" class="text-decoration-none">
                                        <i class="bi bi-telephone"></i> {{ $customer->phone_number }}
                                    </a>
                                </p>
                            </div>

                            @if($customer->email)
                                <div class="mb-3">
                                    <label class="form-label text-muted">Email</label>
                                    <p class="mb-0">
                                        <a href="mailto:{{ $customer->email }}" class="text-decoration-none">
                                            <i class="bi bi-envelope"></i> {{ $customer->email }}
                                        </a>
                                    </p>
                                </div>
                            @endif

                            @if($customer->address)
                                <div class="mb-3">
                                    <label class="form-label text-muted">Address</label>
                                    <p class="mb-0">{{ $customer->address }}</p>
                                </div>
                            @endif

                            @if($customer->notes)
                                <div class="mb-3">
                                    <label class="form-label text-muted">Notes</label>
                                    <p class="mb-0">{{ $customer->notes }}</p>
                                </div>
                            @endif

                            <div class="mb-0">
                                <label class="form-label text-muted">Customer Since</label>
                                <p class="mb-0">{{ $customer->created_at->format('F j, Y') }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Repair Tickets -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-ticket"></i> Repair Tickets
                                    <span class="badge bg-secondary">{{ $customer->repairTickets->count() }}</span>
                                </h5>
                                <a href="{{ route('repair-tickets.create', ['customer_id' => $customer->id]) }}"
                                   class="btn btn-success btn-sm">
                                    <i class="bi bi-plus-circle"></i> New Ticket
                                </a>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            @if($customer->repairTickets->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Ticket #</th>
                                                <th>Device</th>
                                                <th>Problem</th>
                                                <th>Status</th>
                                                <th>Received</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($customer->repairTickets->sortByDesc('created_at') as $ticket)
                                                <tr>
                                                    <td>
                                                        <strong>{{ $ticket->ticket_number }}</strong>
                                                    </td>
                                                    <td>
                                                        {{ $ticket->brand->name }} {{ $ticket->device_model }}
                                                    </td>
                                                    <td>
                                                        <span title="{{ $ticket->reported_problem }}">
                                                            {{ Str::limit($ticket->reported_problem, 50) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge"
                                                              style="background-color: {{ $ticket->repairStatus->color }}">
                                                            {{ $ticket->repairStatus->name }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <small>{{ $ticket->received_date->format('M d, Y') }}</small>
                                                    </td>
                                                    <td>
                                                        <a href="{{ route('repair-tickets.show', $ticket) }}"
                                                           class="btn btn-outline-primary btn-sm">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <i class="bi bi-ticket display-1 text-muted"></i>
                                    <h4 class="mt-3">No repair tickets yet</h4>
                                    <p class="text-muted">This customer hasn't brought any devices for repair.</p>
                                    <a href="{{ route('repair-tickets.create', ['customer_id' => $customer->id]) }}"
                                       class="btn btn-success">
                                        <i class="bi bi-plus-circle"></i> Create First Ticket
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
