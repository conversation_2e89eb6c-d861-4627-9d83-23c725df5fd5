<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class WhatsAppMessageTemplate extends Model
{
    use HasFactory;

    protected $table = 'whatsapp_message_templates';

    protected $fillable = [
        'name',
        'whatsapp_name',
        'display_name',
        'display_name_ar',
        'description',
        'description_ar',
        'category_id',
        'language',
        'header_text',
        'body_text',
        'footer_text',
        'buttons',
        'status',
        'whatsapp_template_id',
        'quality_rating',
        'rejection_reason',
        'variables',
        'sample_values',
        'has_media',
        'media_type',
        'is_active',
        'usage_count',
        'success_count',
        'failure_count',
        'success_rate',
        'last_used_at',
        'created_by',
        'approved_by',
        'approved_at',
        'approval_notes',
        'auto_send_enabled',
        'auto_send_triggers',
        'send_schedule',
        'requires_opt_in',
        'includes_opt_out',
        'opt_out_instructions',
    ];

    protected $casts = [
        'buttons' => 'array',
        'variables' => 'array',
        'sample_values' => 'array',
        'auto_send_triggers' => 'array',
        'send_schedule' => 'array',
        'has_media' => 'boolean',
        'is_active' => 'boolean',
        'auto_send_enabled' => 'boolean',
        'requires_opt_in' => 'boolean',
        'includes_opt_out' => 'boolean',
        'last_used_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($template) {
            if (empty($template->whatsapp_name)) {
                $template->whatsapp_name = Str::slug($template->name, '_');
            }
        });

        static::updated(function ($template) {
            if ($template->isDirty('success_count') || $template->isDirty('failure_count')) {
                $template->calculateSuccessRate();
            }
        });
    }

    /**
     * Get the category that owns the template.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(WhatsAppTemplateCategory::class, 'category_id');
    }

    /**
     * Get the user who created the template.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who approved the template.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the usage records for this template.
     */
    public function usageRecords(): HasMany
    {
        return $this->hasMany(WhatsAppTemplateUsage::class, 'template_id');
    }

    /**
     * Scope for active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for approved templates.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'APPROVED');
    }

    /**
     * Scope for templates by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for templates by language.
     */
    public function scopeByLanguage($query, $language)
    {
        return $query->where('language', $language);
    }

    /**
     * Scope for templates by category.
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Get display name based on locale.
     */
    public function getDisplayNameAttribute(): string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' ? $this->attributes['display_name_ar'] : $this->attributes['display_name'];
    }

    /**
     * Get display description based on locale.
     */
    public function getDisplayDescriptionAttribute(): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' ? $this->description_ar : $this->description;
    }

    /**
     * Check if template is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === 'APPROVED';
    }

    /**
     * Check if template is pending approval.
     */
    public function isPending(): bool
    {
        return $this->status === 'PENDING';
    }

    /**
     * Check if template is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === 'REJECTED';
    }

    /**
     * Check if template can be used.
     */
    public function canBeUsed(): bool
    {
        return $this->is_active && $this->isApproved();
    }

    /**
     * Get template variables.
     */
    public function getVariables(): array
    {
        return $this->variables ?? [];
    }

    /**
     * Get sample values for variables.
     */
    public function getSampleValues(): array
    {
        return $this->sample_values ?? [];
    }

    /**
     * Render template with variables.
     */
    public function render(array $variables = []): array
    {
        $variables = array_merge($this->getSampleValues(), $variables);

        return [
            'header' => $this->renderText($this->header_text, $variables),
            'body' => $this->renderText($this->body_text, $variables),
            'footer' => $this->renderText($this->footer_text, $variables),
            'buttons' => $this->buttons,
        ];
    }

    /**
     * Render text with variables.
     */
    protected function renderText(?string $text, array $variables): ?string
    {
        if (!$text) {
            return null;
        }

        foreach ($variables as $key => $value) {
            $text = str_replace("{{$key}}", $value, $text);
            $text = str_replace("{{{$key}}}", $value, $text);
        }

        return $text;
    }

    /**
     * Extract variables from template text.
     */
    public function extractVariables(): array
    {
        $text = $this->header_text . ' ' . $this->body_text . ' ' . $this->footer_text;
        preg_match_all('/\{\{(\w+)\}\}/', $text, $matches);

        return array_unique($matches[1]);
    }

    /**
     * Validate template variables.
     */
    public function validateVariables(array $variables): array
    {
        $required = $this->extractVariables();
        $missing = [];

        foreach ($required as $variable) {
            if (!isset($variables[$variable]) || $variables[$variable] === '') {
                $missing[] = $variable;
            }
        }

        return $missing;
    }

    /**
     * Submit template for approval.
     */
    public function submitForApproval(): void
    {
        $this->update([
            'status' => 'PENDING',
        ]);
    }

    /**
     * Approve template.
     */
    public function approve(User $approver, string $notes = null): void
    {
        $this->update([
            'status' => 'APPROVED',
            'approved_by' => $approver->id,
            'approved_at' => now(),
            'approval_notes' => $notes,
        ]);
    }

    /**
     * Reject template.
     */
    public function reject(string $reason, User $reviewer = null): void
    {
        $this->update([
            'status' => 'REJECTED',
            'rejection_reason' => $reason,
            'approved_by' => $reviewer?->id,
            'approved_at' => now(),
        ]);
    }

    /**
     * Increment usage count.
     */
    public function incrementUsage(bool $success = true): void
    {
        $this->increment('usage_count');

        if ($success) {
            $this->increment('success_count');
        } else {
            $this->increment('failure_count');
        }

        $this->update(['last_used_at' => now()]);
        $this->calculateSuccessRate();
    }

    /**
     * Calculate success rate.
     */
    protected function calculateSuccessRate(): void
    {
        $total = $this->success_count + $this->failure_count;
        $rate = $total > 0 ? ($this->success_count / $total) * 100 : 0;

        $this->update(['success_rate' => round($rate, 2)]);
    }

    /**
     * Get template statistics.
     */
    public function getStatsAttribute(): array
    {
        return [
            'usage_count' => $this->usage_count,
            'success_count' => $this->success_count,
            'failure_count' => $this->failure_count,
            'success_rate' => $this->success_rate,
            'last_used' => $this->last_used_at?->diffForHumans(),
            'quality_rating' => $this->quality_rating,
        ];
    }

    /**
     * Get status badge color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'APPROVED' => 'success',
            'PENDING' => 'warning',
            'REJECTED' => 'danger',
            'DISABLED' => 'secondary',
            'PAUSED' => 'info',
            default => 'light',
        };
    }

    /**
     * Get quality rating color.
     */
    public function getQualityColorAttribute(): string
    {
        return match($this->quality_rating) {
            'GREEN' => 'success',
            'YELLOW' => 'warning',
            'RED' => 'danger',
            default => 'secondary',
        };
    }
}
