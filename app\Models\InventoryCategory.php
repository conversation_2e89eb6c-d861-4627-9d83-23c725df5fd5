<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class InventoryCategory extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'color',
        'parent_id',
        'sort_order',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            if (empty($category->slug)) {
                $category->slug = Str::slug($category->name);
            }
        });

        static::updating(function ($category) {
            if ($category->isDirty('name') && empty($category->slug)) {
                $category->slug = Str::slug($category->name);
            }
        });
    }

    /**
     * Get inventory items in this category.
     */
    public function inventoryItems(): HasMany
    {
        return $this->hasMany(InventoryItem::class, 'category_id');
    }

    /**
     * Get parent category.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(InventoryCategory::class, 'parent_id');
    }

    /**
     * Get child categories.
     */
    public function children(): HasMany
    {
        return $this->hasMany(InventoryCategory::class, 'parent_id');
    }

    /**
     * Get all descendants (children, grandchildren, etc.).
     */
    public function descendants(): HasMany
    {
        return $this->children()->with('descendants');
    }

    /**
     * Scope for active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for root categories (no parent).
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope for child categories (has parent).
     */
    public function scopeChild($query)
    {
        return $query->whereNotNull('parent_id');
    }

    /**
     * Scope for categories ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Search categories by name or description.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
    }

    /**
     * Get category hierarchy path.
     */
    public function getHierarchyPathAttribute(): string
    {
        $path = [$this->name];
        $parent = $this->parent;
        
        while ($parent) {
            array_unshift($path, $parent->name);
            $parent = $parent->parent;
        }
        
        return implode(' > ', $path);
    }

    /**
     * Get category depth level.
     */
    public function getDepthAttribute(): int
    {
        $depth = 0;
        $parent = $this->parent;
        
        while ($parent) {
            $depth++;
            $parent = $parent->parent;
        }
        
        return $depth;
    }

    /**
     * Check if category has children.
     */
    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }

    /**
     * Check if category has items.
     */
    public function hasItems(): bool
    {
        return $this->inventoryItems()->exists();
    }

    /**
     * Get total items count including subcategories.
     */
    public function getTotalItemsCountAttribute(): int
    {
        $count = $this->inventoryItems()->count();
        
        foreach ($this->children as $child) {
            $count += $child->total_items_count;
        }
        
        return $count;
    }

    /**
     * Get total stock value for this category.
     */
    public function getTotalStockValue(): float
    {
        $value = $this->inventoryItems()
            ->selectRaw('SUM(current_stock * cost_price) as total_value')
            ->value('total_value') ?? 0;
            
        foreach ($this->children as $child) {
            $value += $child->getTotalStockValue();
        }
        
        return $value;
    }

    /**
     * Get low stock items count.
     */
    public function getLowStockItemsCount(): int
    {
        $count = $this->inventoryItems()
            ->whereRaw('current_stock <= minimum_stock')
            ->count();
            
        foreach ($this->children as $child) {
            $count += $child->getLowStockItemsCount();
        }
        
        return $count;
    }

    /**
     * Get category statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total_items' => $this->total_items_count,
            'active_items' => $this->inventoryItems()->where('is_active', true)->count(),
            'low_stock_items' => $this->getLowStockItemsCount(),
            'out_of_stock_items' => $this->inventoryItems()->where('current_stock', 0)->count(),
            'total_stock_value' => $this->getTotalStockValue(),
            'subcategories_count' => $this->children()->count(),
        ];
    }

    /**
     * Get all category IDs in this branch (including self and descendants).
     */
    public function getBranchIds(): array
    {
        $ids = [$this->id];
        
        foreach ($this->children as $child) {
            $ids = array_merge($ids, $child->getBranchIds());
        }
        
        return $ids;
    }

    /**
     * Check if this category can be deleted.
     */
    public function canBeDeleted(): bool
    {
        return !$this->hasChildren() && !$this->hasItems();
    }

    /**
     * Get icon HTML.
     */
    public function getIconHtmlAttribute(): string
    {
        if ($this->icon) {
            if (Str::startsWith($this->icon, 'fa')) {
                return "<i class=\"{$this->icon}\"></i>";
            } else {
                return "<img src=\"{$this->icon}\" alt=\"{$this->name}\" class=\"category-icon\">";
            }
        }
        
        return '<i class="fas fa-folder"></i>';
    }

    /**
     * Get color style attribute.
     */
    public function getColorStyleAttribute(): string
    {
        return "background-color: {$this->color}; color: " . $this->getContrastColor();
    }

    /**
     * Get contrasting text color for the background.
     */
    private function getContrastColor(): string
    {
        $hex = ltrim($this->color, '#');
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        $brightness = (($r * 299) + ($g * 587) + ($b * 114)) / 1000;
        
        return $brightness > 155 ? '#000000' : '#ffffff';
    }
}
