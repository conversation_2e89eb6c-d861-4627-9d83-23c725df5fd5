<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;

class PosTransaction extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'transaction_number',
        'terminal_id',
        'customer_id',
        'customer_name',
        'customer_phone',
        'subtotal',
        'tax_rate',
        'tax_amount',
        'discount_percentage',
        'discount_amount',
        'total_amount',
        'status',
        'transaction_date',
        'cashier_id',
        'payment_breakdown',
        'notes',
        'receipt_printed',
        'receipt_number',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'transaction_date' => 'datetime',
        'payment_breakdown' => 'array',
        'receipt_printed' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (empty($transaction->transaction_number)) {
                $transaction->transaction_number = static::generateTransactionNumber();
            }
            if (empty($transaction->transaction_date)) {
                $transaction->transaction_date = now();
            }
        });
    }

    /**
     * Get the terminal that owns this transaction.
     */
    public function terminal(): BelongsTo
    {
        return $this->belongsTo(PosTerminal::class, 'terminal_id');
    }

    /**
     * Get the customer.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the cashier.
     */
    public function cashier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'cashier_id');
    }

    /**
     * Get transaction items.
     */
    public function items(): HasMany
    {
        return $this->hasMany(PosTransactionItem::class, 'transaction_id');
    }

    /**
     * Get transaction payments.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(PosTransactionPayment::class, 'transaction_id');
    }

    /**
     * Generate unique transaction number.
     */
    public static function generateTransactionNumber(): string
    {
        $prefix = 'POS';
        $date = now()->format('Ymd');

        // Get the last transaction number for today
        $lastTransaction = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = 1;
        if ($lastTransaction) {
            // Extract sequence number from last transaction
            $lastNumber = substr($lastTransaction->transaction_number, -4);
            $sequence = intval($lastNumber) + 1;
        }

        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate totals based on items.
     */
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items->sum('total_price');
        $this->tax_amount = $this->subtotal * ($this->tax_rate / 100);
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
    }

    /**
     * Add item to transaction.
     */
    public function addItem(array $itemData): PosTransactionItem
    {
        $item = $this->items()->create($itemData);
        $this->calculateTotals();
        $this->save();

        // Update inventory if linked
        if ($item->inventory_item_id && $item->inventoryItem) {
            $item->inventoryItem->updateStock(-$item->quantity, 'pos_sale', [
                'transaction_id' => $this->id,
                'transaction_number' => $this->transaction_number,
                'notes' => "POS Sale - Transaction {$this->transaction_number}"
            ]);
        }

        return $item;
    }

    /**
     * Remove item from transaction.
     */
    public function removeItem(PosTransactionItem $item): void
    {
        // Restore inventory if linked
        if ($item->inventory_item_id && $item->inventoryItem) {
            $item->inventoryItem->updateStock($item->quantity, 'pos_return', [
                'transaction_id' => $this->id,
                'transaction_number' => $this->transaction_number,
                'notes' => "POS Return - Transaction {$this->transaction_number}"
            ]);
        }

        $item->delete();
        $this->calculateTotals();
        $this->save();
    }

    /**
     * Add payment to transaction.
     */
    public function addPayment(array $paymentData): PosTransactionPayment
    {
        $payment = $this->payments()->create(array_merge($paymentData, [
            'payment_date' => now()
        ]));

        $this->updatePaymentBreakdown();
        $this->checkIfFullyPaid();

        return $payment;
    }

    /**
     * Update payment breakdown summary.
     */
    private function updatePaymentBreakdown(): void
    {
        $breakdown = [];
        foreach ($this->payments as $payment) {
            $method = $payment->payment_method;
            if (!isset($breakdown[$method])) {
                $breakdown[$method] = 0;
            }
            $breakdown[$method] += $payment->amount;
        }

        $this->update(['payment_breakdown' => $breakdown]);
    }

    /**
     * Check if transaction is fully paid.
     */
    private function checkIfFullyPaid(): void
    {
        $totalPaid = $this->payments->where('status', 'completed')->sum('amount');
        
        if ($totalPaid >= $this->total_amount) {
            $this->update(['status' => 'completed']);
            
            // Update terminal cash drawer for cash payments
            $cashAmount = $this->payments
                ->where('payment_method', 'cash')
                ->where('status', 'completed')
                ->sum('amount');
                
            if ($cashAmount > 0) {
                $this->terminal->addCash(
                    $this->cashier,
                    $cashAmount,
                    "POS Sale - {$this->transaction_number}"
                );
            }

            // Update POS session totals
            $session = $this->terminal->currentSession;
            if ($session) {
                $session->updateTotals();
            }
        }
    }

    /**
     * Complete the transaction.
     */
    public function complete(): void
    {
        if ($this->status !== 'pending') {
            throw new \Exception('Transaction is not in pending status');
        }

        if ($this->items->isEmpty()) {
            throw new \Exception('Transaction has no items');
        }

        $totalPaid = $this->payments->where('status', 'completed')->sum('amount');
        
        if ($totalPaid < $this->total_amount) {
            throw new \Exception('Transaction is not fully paid');
        }

        $this->update(['status' => 'completed']);
        $this->terminal->updateActivity();
    }

    /**
     * Cancel the transaction.
     */
    public function cancel(string $reason = null): void
    {
        if ($this->status === 'completed') {
            throw new \Exception('Cannot cancel completed transaction');
        }

        DB::transaction(function () use ($reason) {
            // Restore inventory for all items
            foreach ($this->items as $item) {
                if ($item->inventory_item_id && $item->inventoryItem) {
                    $item->inventoryItem->updateStock($item->quantity, 'pos_cancel', [
                        'transaction_id' => $this->id,
                        'transaction_number' => $this->transaction_number,
                        'notes' => "POS Cancel - Transaction {$this->transaction_number}" . ($reason ? " - {$reason}" : "")
                    ]);
                }
            }

            // Cancel all payments
            $this->payments()->update(['status' => 'cancelled']);

            $this->update([
                'status' => 'cancelled',
                'notes' => $this->notes . ($reason ? "\nCancelled: {$reason}" : "\nCancelled")
            ]);
        });
    }

    /**
     * Refund the transaction.
     */
    public function refund(string $reason = null): void
    {
        if ($this->status !== 'completed') {
            throw new \Exception('Can only refund completed transactions');
        }

        DB::transaction(function () use ($reason) {
            // Restore inventory for all items
            foreach ($this->items as $item) {
                if ($item->inventory_item_id && $item->inventoryItem) {
                    $item->inventoryItem->updateStock($item->quantity, 'pos_refund', [
                        'transaction_id' => $this->id,
                        'transaction_number' => $this->transaction_number,
                        'notes' => "POS Refund - Transaction {$this->transaction_number}" . ($reason ? " - {$reason}" : "")
                    ]);
                }
            }

            // Mark payments as refunded
            $this->payments()->update(['status' => 'refunded']);

            // Remove cash from drawer for cash payments
            $cashAmount = $this->payments
                ->where('payment_method', 'cash')
                ->sum('amount');
                
            if ($cashAmount > 0) {
                $this->terminal->removeCash(
                    auth()->user(),
                    $cashAmount,
                    "POS Refund - {$this->transaction_number}"
                );
            }

            $this->update([
                'status' => 'refunded',
                'notes' => $this->notes . ($reason ? "\nRefunded: {$reason}" : "\nRefunded")
            ]);
        });
    }

    /**
     * Print receipt.
     */
    public function printReceipt(): void
    {
        if (!$this->receipt_number) {
            $this->update([
                'receipt_number' => 'RCP-' . $this->transaction_number,
                'receipt_printed' => true
            ]);
        }
    }

    /**
     * Get status display.
     */
    public function getStatusDisplayAttribute(): string
    {
        $statuses = [
            'pending' => app()->getLocale() === 'ar' ? 'في الانتظار' : 'Pending',
            'completed' => app()->getLocale() === 'ar' ? 'مكتملة' : 'Completed',
            'cancelled' => app()->getLocale() === 'ar' ? 'ملغية' : 'Cancelled',
            'refunded' => app()->getLocale() === 'ar' ? 'مسترد' : 'Refunded',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'warning',
            'completed' => 'success',
            'cancelled' => 'secondary',
            'refunded' => 'info',
            default => 'secondary',
        };
    }

    /**
     * Scope for today's transactions.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('transaction_date', today());
    }

    /**
     * Scope for completed transactions.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for transactions by terminal.
     */
    public function scopeByTerminal($query, $terminalId)
    {
        return $query->where('terminal_id', $terminalId);
    }

    /**
     * Get total paid amount.
     */
    public function getTotalPaidAttribute(): float
    {
        return $this->payments->where('status', 'completed')->sum('amount');
    }

    /**
     * Get remaining amount to be paid.
     */
    public function getRemainingAmountAttribute(): float
    {
        return max(0, $this->total_amount - $this->total_paid);
    }

    /**
     * Check if transaction is fully paid.
     */
    public function isFullyPaid(): bool
    {
        return $this->remaining_amount <= 0;
    }

    /**
     * Get change amount for cash payments.
     */
    public function getChangeAmountAttribute(): float
    {
        return $this->payments->sum('change_amount');
    }
}
