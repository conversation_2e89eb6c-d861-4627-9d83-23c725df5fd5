<?php

namespace App\Services;

use App\Models\WhatsAppMessage;
use App\Models\WhatsAppSession;
use App\Models\Customer;
use App\Models\RepairTicket;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class WhatsAppBotService
{
    protected WhatsAppService $whatsappService;
    protected array $arabicKeywords;
    protected array $responses;

    public function __construct(WhatsAppService $whatsappService)
    {
        $this->whatsappService = $whatsappService;
        $this->initializeKeywords();
        $this->initializeResponses();
    }

    /**
     * Process incoming message
     */
    public function processIncomingMessage(array $messageData): void
    {
        try {
            $customerPhone = $messageData['from'];
            $messageType = $messageData['type'];
            $messageContent = $this->extractMessageContent($messageData);

            // Get or create session
            $session = WhatsAppSession::getOrCreateForCustomer($customerPhone);

            // Check rate limiting
            if ($session->isRateLimited()) {
                $this->sendRateLimitMessage($customerPhone);
                return;
            }

            // Log incoming message
            $message = WhatsAppMessage::create([
                'whatsapp_message_id' => $messageData['id'],
                'conversation_id' => $this->generateConversationId($customerPhone),
                'customer_phone' => $customerPhone,
                'customer_id' => $session->customer_id,
                'direction' => 'inbound',
                'message_type' => $messageType,
                'message_content' => $messageContent,
                'message_data' => $messageData,
                'status' => 'received',
                'session_id' => $session->session_id,
                'language' => $this->detectLanguage($messageContent),
                'whatsapp_timestamp' => now(),
            ]);

            // Mark as read
            $this->whatsappService->markAsRead($messageData['id']);

            // Update session activity
            $session->updateActivity();
            $session->incrementMessageCount();

            // Process message based on type and content
            $this->handleMessage($message, $session);

        } catch (\Exception $e) {
            Log::error('Error processing WhatsApp message', [
                'error' => $e->getMessage(),
                'message_data' => $messageData
            ]);
        }
    }

    /**
     * Handle message based on content and context
     */
    protected function handleMessage(WhatsAppMessage $message, WhatsAppSession $session): void
    {
        $content = strtolower(trim($message->message_content));
        $intent = $this->detectIntent($content, $session);

        // Update message with detected intent
        $message->update(['intent' => $intent]);

        // Handle based on current flow or intent
        if ($session->current_flow) {
            $this->handleFlowMessage($message, $session);
        } else {
            $this->handleInitialMessage($message, $session, $intent);
        }
    }

    /**
     * Handle initial message (no active flow)
     */
    protected function handleInitialMessage(WhatsAppMessage $message, WhatsAppSession $session, string $intent): void
    {
        switch ($intent) {
            case 'greeting':
                $this->sendWelcomeMessage($message->customer_phone, $session);
                break;

            case 'ticket_status':
                $this->startTicketStatusFlow($message, $session);
                break;

            case 'business_hours':
                $this->sendBusinessHours($message->customer_phone);
                break;

            case 'location':
                $this->sendLocationInfo($message->customer_phone);
                break;

            case 'services':
                $this->sendServicesInfo($message->customer_phone);
                break;

            case 'appointment':
                $this->startAppointmentFlow($message, $session);
                break;

            case 'callback_request':
                $this->startCallbackFlow($message, $session);
                break;

            case 'phone_number':
                $this->handlePhoneNumberQuery($message, $session);
                break;

            default:
                $this->sendDefaultResponse($message->customer_phone, $session);
        }
    }

    /**
     * Handle message within an active flow
     */
    protected function handleFlowMessage(WhatsAppMessage $message, WhatsAppSession $session): void
    {
        switch ($session->current_flow) {
            case 'ticket_status':
                $this->handleTicketStatusFlow($message, $session);
                break;

            case 'appointment_booking':
                $this->handleAppointmentFlow($message, $session);
                break;

            case 'callback_request':
                $this->handleCallbackFlow($message, $session);
                break;

            default:
                $this->sendDefaultResponse($message->customer_phone, $session);
                $session->setFlow(null);
        }
    }

    /**
     * Send welcome message with options
     */
    protected function sendWelcomeMessage(string $customerPhone, WhatsAppSession $session): void
    {
        $customer = $session->customer;
        $customerName = $customer ? $customer->name : 'عزيزي العميل';

        $welcomeText = "أهلاً وسهلاً {$customerName} 👋\n\n";
        $welcomeText .= "مرحباً بك في ورشة إصلاح NJ\n";
        $welcomeText .= "كيف يمكنني مساعدتك اليوم؟";

        $buttons = [
            ['id' => 'check_status', 'title' => '🔍 حالة الجهاز'],
            ['id' => 'business_hours', 'title' => '🕐 ساعات العمل'],
            ['id' => 'location', 'title' => '📍 الموقع']
        ];

        $this->whatsappService->sendInteractiveMessage(
            $customerPhone,
            $welcomeText,
            $buttons
        );

        $message = WhatsAppMessage::where('customer_phone', $customerPhone)
                                 ->outbound()
                                 ->latest()
                                 ->first();
        if ($message) {
            $message->update(['auto_responded' => true]);
        }
    }

    /**
     * Start ticket status inquiry flow
     */
    protected function startTicketStatusFlow(WhatsAppMessage $message, WhatsAppSession $session): void
    {
        $customer = $session->customer;

        if (!$customer) {
            $this->requestPhoneNumber($message->customer_phone, $session);
            return;
        }

        $tickets = RepairTicket::where('customer_id', $customer->id)
                              ->with(['repairStatus', 'brand'])
                              ->orderBy('created_at', 'desc')
                              ->limit(5)
                              ->get();

        if ($tickets->isEmpty()) {
            $this->whatsappService->sendTextMessage(
                $message->customer_phone,
                "عذراً، لم نجد أي أجهزة مسجلة باسمك في نظامنا.\n\nإذا كنت قد أحضرت جهازك مؤخراً، يرجى الانتظار قليلاً أو الاتصال بنا على " . config('whatsapp.business_phone')
            );
            return;
        }

        if ($tickets->count() === 1) {
            $this->sendTicketDetails($message->customer_phone, $tickets->first());
        } else {
            $this->sendTicketsList($message->customer_phone, $tickets, $session);
        }
    }

    /**
     * Send ticket details
     */
    protected function sendTicketDetails(string $customerPhone, RepairTicket $ticket): void
    {
        $statusIcon = $this->getStatusIcon($ticket->repairStatus->name);
        $deviceInfo = $ticket->brand->name . ' ' . $ticket->device_model;

        $message = "📱 *تفاصيل جهازك*\n\n";
        $message .= "🎫 رقم التذكرة: *{$ticket->ticket_number}*\n";
        $message .= "📱 الجهاز: *{$deviceInfo}*\n";
        $message .= "📅 تاريخ الاستلام: *{$ticket->received_date->format('Y-m-d')}*\n";
        $message .= "{$statusIcon} الحالة: *{$ticket->repairStatus->name}*\n\n";

        if ($ticket->reported_problem) {
            $message .= "🔧 المشكلة المبلغ عنها:\n{$ticket->reported_problem}\n\n";
        }

        if ($ticket->technician_comments) {
            $message .= "💬 ملاحظات الفني:\n{$ticket->technician_comments}\n\n";
        }

        if ($ticket->estimated_completion_date) {
            $message .= "⏰ التاريخ المتوقع للإنجاز: *{$ticket->estimated_completion_date->format('Y-m-d')}*\n\n";
        }

        if ($ticket->repairStatus->is_final) {
            $message .= "🎉 *جهازك جاهز للاستلام!*\n";
            $message .= "يرجى زيارة الورشة لاستلام جهازك\n\n";
        }

        $message .= "📞 للاستفسار: " . config('whatsapp.business_phone');

        $this->whatsappService->sendTextMessage($customerPhone, $message);
    }

    /**
     * Send tickets list for selection
     */
    protected function sendTicketsList(string $customerPhone, $tickets, WhatsAppSession $session): void
    {
        $session->setFlow('ticket_status', 'select_ticket');

        $sections = [[
            'title' => 'أجهزتك',
            'rows' => $tickets->map(function ($ticket) {
                $statusIcon = $this->getStatusIcon($ticket->repairStatus->name);
                return [
                    'id' => "ticket_{$ticket->id}",
                    'title' => $ticket->ticket_number,
                    'description' => "{$statusIcon} {$ticket->brand->name} - {$ticket->repairStatus->name}"
                ];
            })->toArray()
        ]];

        $this->whatsappService->sendListMessage(
            $customerPhone,
            "لديك عدة أجهزة في الورشة. اختر الجهاز الذي تريد الاستفسار عنه:",
            $sections,
            'اختر الجهاز'
        );
    }

    /**
     * Handle ticket status flow messages
     */
    protected function handleTicketStatusFlow(WhatsAppMessage $message, WhatsAppSession $session): void
    {
        if ($session->current_step === 'select_ticket') {
            $content = $message->message_content;

            if (str_contains($content, 'ticket_')) {
                $ticketId = str_replace('ticket_', '', $content);
                $ticket = RepairTicket::find($ticketId);

                if ($ticket && $ticket->customer_id === $session->customer_id) {
                    $this->sendTicketDetails($message->customer_phone, $ticket);
                    $session->setFlow(null);
                } else {
                    $this->whatsappService->sendTextMessage(
                        $message->customer_phone,
                        "عذراً، لم أتمكن من العثور على هذه التذكرة. يرجى المحاولة مرة أخرى."
                    );
                }
            }
        }
    }

    /**
     * Send business hours
     */
    protected function sendBusinessHours(string $customerPhone): void
    {
        $message = "🕐 *ساعات العمل*\n\n";
        $message .= "📅 السبت - الخميس: 9:00 ص - 10:00 م\n";
        $message .= "📅 الجمعة: 2:00 م - 10:00 م\n\n";
        $message .= "📞 للطوارئ: " . config('whatsapp.business_phone') . "\n";
        $message .= "🌐 الموقع الإلكتروني: " . config('whatsapp.business_website');

        $this->whatsappService->sendTextMessage($customerPhone, $message);
    }

    /**
     * Send location information
     */
    protected function sendLocationInfo(string $customerPhone): void
    {
        // Send location
        $this->whatsappService->sendLocationMessage(
            $customerPhone,
            24.7136, // Riyadh latitude
            46.6753, // Riyadh longitude
            'ورشة إصلاح NJ',
            config('whatsapp.business_address')
        );

        // Send additional info
        $message = "📍 *موقع ورشة إصلاح NJ*\n\n";
        $message .= config('whatsapp.business_address') . "\n\n";
        $message .= "🚗 يمكنك الوصول إلينا بسهولة عبر:\n";
        $message .= "• طريق الملك فهد\n";
        $message .= "• بالقرب من مجمع العثيم\n\n";
        $message .= "📞 للاستفسار عن الاتجاهات: " . config('whatsapp.business_phone');

        $this->whatsappService->sendTextMessage($customerPhone, $message);
    }

    /**
     * Send services information
     */
    protected function sendServicesInfo(string $customerPhone): void
    {
        $message = "🔧 *خدماتنا*\n\n";
        $message .= "📱 إصلاح الهواتف الذكية:\n";
        $message .= "• تغيير الشاشات\n";
        $message .= "• إصلاح البطاريات\n";
        $message .= "• حل مشاكل البرمجيات\n";
        $message .= "• إصلاح أزرار التحكم\n\n";
        $message .= "💻 إصلاح الحاسوب:\n";
        $message .= "• صيانة الأجهزة\n";
        $message .= "• تنظيف الفيروسات\n";
        $message .= "• ترقية المكونات\n\n";
        $message .= "⚡ خدمات سريعة:\n";
        $message .= "• تركيب واقيات الشاشة\n";
        $message .= "• تنظيف الأجهزة\n\n";
        $message .= "📞 للاستفسار: " . config('whatsapp.business_phone');

        $this->whatsappService->sendTextMessage($customerPhone, $message);
    }

    /**
     * Request phone number for customer identification
     */
    protected function requestPhoneNumber(string $customerPhone, WhatsAppSession $session): void
    {
        $session->setFlow('phone_verification', 'request_phone');

        $message = "لمساعدتك في الاستفسار عن حالة جهازك، يرجى إرسال رقم الهاتف المسجل لديك في الورشة.\n\n";
        $message .= "مثال: 0501234567";

        $this->whatsappService->sendTextMessage($customerPhone, $message);
    }

    /**
     * Handle phone number query
     */
    protected function handlePhoneNumberQuery(WhatsAppMessage $message, WhatsAppSession $session): void
    {
        $phoneNumber = $this->extractPhoneNumber($message->message_content);

        if (!$phoneNumber) {
            $this->whatsappService->sendTextMessage(
                $message->customer_phone,
                "رقم الهاتف غير صحيح. يرجى إرسال رقم هاتف صحيح مثل: 0501234567"
            );
            return;
        }

        $customer = Customer::where('phone_number', 'like', '%' . substr($phoneNumber, -9))->first();

        if ($customer) {
            $session->update(['customer_id' => $customer->id]);
            $this->startTicketStatusFlow($message, $session);
        } else {
            $this->whatsappService->sendTextMessage(
                $message->customer_phone,
                "عذراً، لم نجد رقم الهاتف هذا في نظامنا.\n\nيرجى التأكد من الرقم أو الاتصال بنا على " . config('whatsapp.business_phone')
            );
        }
    }

    /**
     * Send default response with options
     */
    protected function sendDefaultResponse(string $customerPhone, WhatsAppSession $session): void
    {
        $message = "عذراً، لم أفهم طلبك. يمكنني مساعدتك في:\n\n";
        $message .= "🔍 الاستفسار عن حالة جهازك\n";
        $message .= "🕐 معرفة ساعات العمل\n";
        $message .= "📍 معرفة موقع الورشة\n";
        $message .= "🔧 معرفة خدماتنا\n\n";
        $message .= "يرجى اختيار أحد الخيارات أو كتابة استفسارك بوضوح.";

        $buttons = [
            ['id' => 'check_status', 'title' => '🔍 حالة الجهاز'],
            ['id' => 'business_hours', 'title' => '🕐 ساعات العمل'],
            ['id' => 'help', 'title' => '❓ مساعدة']
        ];

        $this->whatsappService->sendInteractiveMessage($customerPhone, $message, $buttons);
    }

    /**
     * Send rate limit message
     */
    protected function sendRateLimitMessage(string $customerPhone): void
    {
        $this->whatsappService->sendTextMessage(
            $customerPhone,
            "عذراً، تم تجاوز الحد المسموح من الرسائل. يرجى الانتظار قليلاً قبل إرسال رسائل أخرى.\n\nللطوارئ، يمكنك الاتصال بنا على " . config('whatsapp.business_phone')
        );
    }

    /**
     * Initialize Arabic keywords for intent detection
     */
    protected function initializeKeywords(): void
    {
        $this->arabicKeywords = [
            'greeting' => ['مرحبا', 'السلام عليكم', 'أهلا', 'صباح الخير', 'مساء الخير', 'hello', 'hi'],
            'ticket_status' => ['حالة الجهاز', 'وين جهازي', 'جهازي', 'التذكرة', 'الإصلاح', 'status', 'ticket'],
            'business_hours' => ['ساعات العمل', 'متى مفتوح', 'أوقات العمل', 'مواعيد', 'hours', 'open'],
            'location' => ['العنوان', 'الموقع', 'أين', 'وين', 'location', 'address'],
            'services' => ['الخدمات', 'ماذا تصلحون', 'إيش تسوون', 'services', 'repair'],
            'appointment' => ['موعد', 'حجز', 'appointment', 'booking'],
            'callback_request' => ['اتصال', 'اتصلوا', 'call', 'callback'],
            'phone_number' => ['05', '966', '+966'],
        ];
    }

    /**
     * Initialize response templates
     */
    protected function initializeResponses(): void
    {
        $this->responses = [
            'welcome' => "أهلاً وسهلاً بك في ورشة إصلاح NJ 👋\nكيف يمكنني مساعدتك اليوم؟",
            'goodbye' => "شكراً لتواصلك معنا. نتطلع لخدمتك قريباً! 🙏",
            'error' => "عذراً، حدث خطأ. يرجى المحاولة مرة أخرى أو الاتصال بنا على " . config('whatsapp.business_phone'),
        ];
    }

    /**
     * Detect intent from message content
     */
    protected function detectIntent(string $content, WhatsAppSession $session): string
    {
        $content = strtolower($content);

        foreach ($this->arabicKeywords as $intent => $keywords) {
            foreach ($keywords as $keyword) {
                if (str_contains($content, strtolower($keyword))) {
                    return $intent;
                }
            }
        }

        // Check for phone number pattern
        if (preg_match('/\b(05\d{8}|966\d{9}|\+966\d{9})\b/', $content)) {
            return 'phone_number';
        }

        return 'unknown';
    }

    /**
     * Detect language from content
     */
    protected function detectLanguage(string $content): string
    {
        // Simple Arabic detection - check for Arabic characters
        if (preg_match('/[\x{0600}-\x{06FF}]/u', $content)) {
            return 'ar';
        }
        return 'en';
    }

    /**
     * Extract message content based on type
     */
    protected function extractMessageContent(array $messageData): string
    {
        switch ($messageData['type']) {
            case 'text':
                return $messageData['text']['body'] ?? '';
            case 'interactive':
                if (isset($messageData['interactive']['button_reply'])) {
                    return $messageData['interactive']['button_reply']['id'];
                }
                if (isset($messageData['interactive']['list_reply'])) {
                    return $messageData['interactive']['list_reply']['id'];
                }
                return 'interactive_message';
            case 'image':
            case 'document':
            case 'audio':
            case 'video':
                return $messageData[$messageData['type']]['caption'] ?? 'media_file';
            case 'location':
                return 'location_shared';
            default:
                return 'unknown_message_type';
        }
    }

    /**
     * Extract phone number from text
     */
    protected function extractPhoneNumber(string $text): ?string
    {
        // Remove all non-numeric characters
        $numbers = preg_replace('/[^0-9]/', '', $text);

        // Check for valid Saudi phone number patterns
        if (preg_match('/^(05\d{8})$/', $numbers)) {
            return $numbers;
        }

        if (preg_match('/^(966\d{9})$/', $numbers)) {
            return $numbers;
        }

        if (preg_match('/^(\d{9})$/', $numbers)) {
            return '966' . $numbers;
        }

        return null;
    }

    /**
     * Get status icon for repair status
     */
    protected function getStatusIcon(string $status): string
    {
        $icons = [
            'مستلم' => '📥',
            'قيد التشخيص' => '🔍',
            'في انتظار قطع الغيار' => '⏳',
            'قيد الإصلاح' => '🔧',
            'مكتمل' => '✅',
            'جاهز للاستلام' => '🎉',
            'مُسلم' => '📤',
            'ملغي' => '❌',
        ];

        return $icons[$status] ?? '📋';
    }

    /**
     * Generate conversation ID
     */
    protected function generateConversationId(string $customerPhone): string
    {
        return 'conv_' . md5($customerPhone . date('Y-m-d'));
    }

    /**
     * Start appointment booking flow
     */
    protected function startAppointmentFlow(WhatsAppMessage $message, WhatsAppSession $session): void
    {
        $session->setFlow('appointment_booking', 'request_service');

        $message = "📅 *حجز موعد*\n\n";
        $message .= "يسعدنا خدمتك! ما نوع الخدمة التي تحتاجها؟";

        $buttons = [
            ['id' => 'phone_repair', 'title' => '📱 إصلاح هاتف'],
            ['id' => 'computer_repair', 'title' => '💻 إصلاح حاسوب'],
            ['id' => 'consultation', 'title' => '💬 استشارة']
        ];

        $this->whatsappService->sendInteractiveMessage(
            $message->customer_phone,
            $message,
            $buttons
        );
    }

    /**
     * Handle appointment booking flow
     */
    protected function handleAppointmentFlow(WhatsAppMessage $message, WhatsAppSession $session): void
    {
        $step = $session->current_step;
        $content = $message->message_content;

        switch ($step) {
            case 'request_service':
                $session->setFlowData('service_type', $content);
                $session->setFlow('appointment_booking', 'request_time');

                $this->whatsappService->sendTextMessage(
                    $message->customer_phone,
                    "ممتاز! متى يناسبك الموعد؟\n\nيرجى كتابة التاريخ والوقت المفضل\nمثال: غداً الساعة 2 مساءً"
                );
                break;

            case 'request_time':
                $session->setFlowData('preferred_time', $content);
                $session->setFlow('appointment_booking', 'request_contact');

                $this->whatsappService->sendTextMessage(
                    $message->customer_phone,
                    "شكراً! يرجى تأكيد رقم هاتفك للتواصل معك:\n\nمثال: 0501234567"
                );
                break;

            case 'request_contact':
                $phone = $this->extractPhoneNumber($content);
                if ($phone) {
                    $session->setFlowData('contact_phone', $phone);
                    $this->confirmAppointment($message, $session);
                } else {
                    $this->whatsappService->sendTextMessage(
                        $message->customer_phone,
                        "رقم الهاتف غير صحيح. يرجى إرسال رقم صحيح مثل: 0501234567"
                    );
                }
                break;
        }
    }

    /**
     * Confirm appointment booking
     */
    protected function confirmAppointment(WhatsAppMessage $message, WhatsAppSession $session): void
    {
        $serviceType = $session->getFlowData('service_type');
        $preferredTime = $session->getFlowData('preferred_time');
        $contactPhone = $session->getFlowData('contact_phone');

        $confirmMessage = "✅ *تم استلام طلب الموعد*\n\n";
        $confirmMessage .= "📋 نوع الخدمة: {$serviceType}\n";
        $confirmMessage .= "⏰ الوقت المفضل: {$preferredTime}\n";
        $confirmMessage .= "📞 رقم التواصل: {$contactPhone}\n\n";
        $confirmMessage .= "سنتواصل معك خلال ساعة لتأكيد الموعد.\n\n";
        $confirmMessage .= "شكراً لثقتك بنا! 🙏";

        $this->whatsappService->sendTextMessage($message->customer_phone, $confirmMessage);

        // Create notification for staff
        \App\Models\Notification::create([
            'title' => 'طلب موعد جديد عبر واتساب',
            'message' => "طلب موعد جديد من {$contactPhone} لخدمة {$serviceType} في {$preferredTime}",
            'type' => 'appointment_request',
            'data' => $session->flow_data,
        ]);

        $session->complete();
    }

    /**
     * Start callback request flow
     */
    protected function startCallbackFlow(WhatsAppMessage $message, WhatsAppSession $session): void
    {
        $session->setFlow('callback_request', 'request_reason');

        $this->whatsappService->sendTextMessage(
            $message->customer_phone,
            "📞 *طلب اتصال*\n\nيرجى كتابة سبب الاتصال أو استفسارك بإيجاز:"
        );
    }

    /**
     * Handle callback request flow
     */
    protected function handleCallbackFlow(WhatsAppMessage $message, WhatsAppSession $session): void
    {
        $step = $session->current_step;
        $content = $message->message_content;

        if ($step === 'request_reason') {
            $session->setFlowData('reason', $content);

            $confirmMessage = "✅ *تم استلام طلب الاتصال*\n\n";
            $confirmMessage .= "📝 السبب: {$content}\n\n";
            $confirmMessage .= "سنتواصل معك خلال 30 دقيقة.\n\n";
            $confirmMessage .= "شكراً لصبرك! 🙏";

            $this->whatsappService->sendTextMessage($message->customer_phone, $confirmMessage);

            // Create notification for staff
            \App\Models\Notification::create([
                'title' => 'طلب اتصال عبر واتساب',
                'message' => "طلب اتصال من {$message->customer_phone}: {$content}",
                'type' => 'callback_request',
                'data' => [
                    'customer_phone' => $message->customer_phone,
                    'reason' => $content,
                    'customer_id' => $session->customer_id,
                ],
            ]);

            $session->complete();
        }
    }
}
