@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-whatsapp text-success"></i> {{ __('WhatsApp Business Dashboard') }}</h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#sendMessageModal">
                        <i class="bi bi-send"></i> إرسال رسالة
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="refreshAnalytics()">
                        <i class="bi bi-arrow-clockwise"></i> تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="total-messages">0</h4>
                            <p class="card-text">إجمالي الرسائل</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-chat-dots fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="inbound-messages">0</h4>
                            <p class="card-text">رسائل واردة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-arrow-down-circle fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="auto-responses">0</h4>
                            <p class="card-text">ردود تلقائية</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-robot fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="unique-customers">0</h4>
                            <p class="card-text">عملاء فريدون</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Tables Row -->
    <div class="row">
        <!-- Daily Messages Chart -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-graph-up"></i> الرسائل اليومية</h5>
                </div>
                <div class="card-body">
                    <canvas id="dailyMessagesChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Popular Intents -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-pie-chart"></i> الاستفسارات الشائعة</h5>
                </div>
                <div class="card-body">
                    <canvas id="intentsChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Conversations -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-chat-left-text"></i> المحادثات الأخيرة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="conversationsTable">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>آخر رسالة</th>
                                    <th>النوع</th>
                                    <th>الوقت</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Send Message Modal -->
<div class="modal fade" id="sendMessageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال رسالة واتساب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="sendMessageForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="recipient" class="form-label">رقم الهاتف</label>
                        <input type="text" class="form-control" id="recipient" name="to" 
                               placeholder="966501234567" required>
                        <div class="form-text">أدخل رقم الهاتف مع رمز الدولة</div>
                    </div>
                    <div class="mb-3">
                        <label for="messageType" class="form-label">نوع الرسالة</label>
                        <select class="form-select" id="messageType" name="type">
                            <option value="text">رسالة نصية</option>
                            <option value="template">قالب رسالة</option>
                        </select>
                    </div>
                    <div class="mb-3" id="textMessageDiv">
                        <label for="messageContent" class="form-label">محتوى الرسالة</label>
                        <textarea class="form-control" id="messageContent" name="message" 
                                  rows="4" maxlength="4096" required></textarea>
                        <div class="form-text">الحد الأقصى 4096 حرف</div>
                    </div>
                    <div class="mb-3 d-none" id="templateMessageDiv">
                        <label for="templateName" class="form-label">اسم القالب</label>
                        <select class="form-select" id="templateName" name="template_name">
                            <option value="welcome_message_ar">رسالة ترحيب</option>
                            <option value="ticket_status_update_ar">تحديث حالة التذكرة</option>
                            <option value="appointment_confirmation_ar">تأكيد موعد</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-send"></i> إرسال
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Conversation Modal -->
<div class="modal fade" id="conversationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">محادثة العميل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="conversationHistory" style="max-height: 400px; overflow-y: auto;">
                    <!-- Conversation messages will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let dailyChart, intentsChart;

// Load analytics on page load
document.addEventListener('DOMContentLoaded', function() {
    loadAnalytics();
    loadRecentConversations();
    
    // Setup message type toggle
    document.getElementById('messageType').addEventListener('change', function() {
        const textDiv = document.getElementById('textMessageDiv');
        const templateDiv = document.getElementById('templateMessageDiv');
        
        if (this.value === 'template') {
            textDiv.classList.add('d-none');
            templateDiv.classList.remove('d-none');
            document.getElementById('messageContent').required = false;
        } else {
            textDiv.classList.remove('d-none');
            templateDiv.classList.add('d-none');
            document.getElementById('messageContent').required = true;
        }
    });
    
    // Setup send message form
    document.getElementById('sendMessageForm').addEventListener('submit', function(e) {
        e.preventDefault();
        sendMessage();
    });
});

function loadAnalytics() {
    fetch('/api/whatsapp/analytics')
        .then(response => response.json())
        .then(data => {
            // Update cards
            document.getElementById('total-messages').textContent = data.total_messages;
            document.getElementById('inbound-messages').textContent = data.inbound_messages;
            document.getElementById('auto-responses').textContent = data.auto_responses;
            document.getElementById('unique-customers').textContent = data.unique_customers;
            
            // Update charts
            updateDailyChart(data.daily_stats);
            updateIntentsChart(data.popular_intents);
        })
        .catch(error => {
            console.error('Error loading analytics:', error);
            showAlert('خطأ في تحميل الإحصائيات', 'danger');
        });
}

function updateDailyChart(dailyStats) {
    const ctx = document.getElementById('dailyMessagesChart').getContext('2d');
    
    if (dailyChart) {
        dailyChart.destroy();
    }
    
    dailyChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dailyStats.map(stat => stat.date),
            datasets: [{
                label: 'الرسائل',
                data: dailyStats.map(stat => stat.count),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateIntentsChart(intents) {
    const ctx = document.getElementById('intentsChart').getContext('2d');
    
    if (intentsChart) {
        intentsChart.destroy();
    }
    
    const intentLabels = {
        'ticket_status': 'حالة الجهاز',
        'business_hours': 'ساعات العمل',
        'location': 'الموقع',
        'services': 'الخدمات',
        'greeting': 'تحية',
        'appointment': 'موعد'
    };
    
    intentsChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: intents.map(intent => intentLabels[intent.intent] || intent.intent),
            datasets: [{
                data: intents.map(intent => intent.count),
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function loadRecentConversations() {
    // This would load recent conversations from the API
    // For now, we'll show a placeholder
    const tbody = document.querySelector('#conversationsTable tbody');
    tbody.innerHTML = '<tr><td colspan="6" class="text-center">جاري تحميل المحادثات...</td></tr>';
}

function sendMessage() {
    const form = document.getElementById('sendMessageForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري الإرسال...';
    submitBtn.disabled = true;
    
    fetch('/api/whatsapp/send-message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('تم إرسال الرسالة بنجاح', 'success');
            form.reset();
            bootstrap.Modal.getInstance(document.getElementById('sendMessageModal')).hide();
        } else {
            showAlert('فشل في إرسال الرسالة: ' + result.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error sending message:', error);
        showAlert('حدث خطأ في إرسال الرسالة', 'danger');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function viewConversation(customerPhone) {
    fetch(`/api/whatsapp/conversation/${customerPhone}`)
        .then(response => response.json())
        .then(data => {
            const historyDiv = document.getElementById('conversationHistory');
            historyDiv.innerHTML = '';
            
            data.messages.forEach(message => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `mb-2 ${message.direction === 'inbound' ? 'text-end' : 'text-start'}`;
                messageDiv.innerHTML = `
                    <div class="d-inline-block p-2 rounded ${message.direction === 'inbound' ? 'bg-primary text-white' : 'bg-light'}">
                        <div>${message.formatted_content}</div>
                        <small class="opacity-75">${new Date(message.created_at).toLocaleString('ar-SA')}</small>
                    </div>
                `;
                historyDiv.appendChild(messageDiv);
            });
            
            // Show modal
            new bootstrap.Modal(document.getElementById('conversationModal')).show();
        })
        .catch(error => {
            console.error('Error loading conversation:', error);
            showAlert('خطأ في تحميل المحادثة', 'danger');
        });
}

function refreshAnalytics() {
    loadAnalytics();
    loadRecentConversations();
    showAlert('تم تحديث البيانات', 'info');
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
@endpush
