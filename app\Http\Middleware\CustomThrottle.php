<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Routing\Middleware\ThrottleRequests;
use Symfony\Component\HttpFoundation\Response;

class CustomThrottle extends ThrottleRequests
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  int|string  $maxAttempts
     * @param  float|int  $decayMinutes
     * @param  string  $prefix
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle($request, Closure $next, $maxAttempts = 60, $decayMinutes = 1, $prefix = '')
    {
        // More restrictive limits for authentication routes
        if ($request->is('login') || $request->is('api/login')) {
            $maxAttempts = 5;
            $decayMinutes = 15;
        }

        // API routes get different limits
        if ($request->is('api/*')) {
            $maxAttempts = 100;
            $decayMinutes = 1;
        }

        return parent::handle($request, $next, $maxAttempts, $decayMinutes, $prefix);
    }

    /**
     * Resolve the number of attempts if the user is authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int|string  $maxAttempts
     * @return int
     */
    protected function resolveMaxAttempts($request, $maxAttempts)
    {
        if ($request->user()) {
            // Authenticated users get higher limits
            return (int) $maxAttempts * 2;
        }

        return (int) $maxAttempts;
    }
}
