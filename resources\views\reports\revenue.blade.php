@extends('layouts.app')

@section('title', 'تقرير الإيرادات')

@push('styles')
<style>
.revenue-header {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.875rem;
    text-transform: uppercase;
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.3;
    float: left;
    margin-top: -0.5rem;
}

.filter-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.chart-container {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.payment-methods-table {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    overflow: hidden;
}

.table th {
    background: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.payment-method-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
}

.method-cash {
    background: #d4edda;
    color: #155724;
}

.method-card {
    background: #cce5ff;
    color: #0066cc;
}

.method-bank_transfer {
    background: #fff3cd;
    color: #856404;
}

.method-check {
    background: #e2e3e5;
    color: #6c757d;
}

.method-mobile_payment {
    background: #f8d7da;
    color: #721c24;
}

.method-other {
    background: #e2e3e5;
    color: #6c757d;
}

@media (max-width: 768px) {
    .revenue-header {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .filter-card {
        padding: 1rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="revenue-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">تقرير الإيرادات</h1>
                <p class="mb-0 opacity-75">تحليل مفصل للإيرادات والمبيعات</p>
            </div>
            <div>
                <a href="{{ route('reports.financial') }}" class="btn btn-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="GET" action="{{ route('reports.revenue') }}" class="row g-3">
            <div class="col-md-3">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" 
                       class="form-control" 
                       id="date_from" 
                       name="date_from" 
                       value="{{ $dateFrom }}">
            </div>
            
            <div class="col-md-3">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" 
                       class="form-control" 
                       id="date_to" 
                       name="date_to" 
                       value="{{ $dateTo }}">
            </div>
            
            <div class="col-md-3">
                <label for="group_by" class="form-label">تجميع البيانات</label>
                <select class="form-select" id="group_by" name="group_by">
                    <option value="day" {{ $groupBy === 'day' ? 'selected' : '' }}>يومي</option>
                    <option value="week" {{ $groupBy === 'week' ? 'selected' : '' }}>أسبوعي</option>
                    <option value="month" {{ $groupBy === 'month' ? 'selected' : '' }}>شهري</option>
                    <option value="year" {{ $groupBy === 'year' ? 'selected' : '' }}>سنوي</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>تطبيق
                    </button>
                    <a href="{{ route('reports.revenue') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon text-success">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-value text-success">@arabicCurrency($stats['total_revenue'])</div>
            <div class="stat-label">إجمالي الإيرادات</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-primary">
                <i class="fas fa-file-invoice"></i>
            </div>
            <div class="stat-value text-primary">@arabicNumber($stats['total_invoices'])</div>
            <div class="stat-label">إجمالي الفواتير</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-info">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-value text-info">@arabicNumber($stats['paid_invoices'])</div>
            <div class="stat-label">الفواتير المدفوعة</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-warning">
                <i class="fas fa-calculator"></i>
            </div>
            <div class="stat-value text-warning">@arabicCurrency($stats['average_invoice'])</div>
            <div class="stat-label">متوسط الفاتورة</div>
        </div>
    </div>

    <!-- Revenue Chart -->
    <div class="chart-container">
        <h5 class="mb-3">
            <i class="fas fa-chart-line me-2"></i>
            تطور الإيرادات - {{ ucfirst($groupBy) }}
        </h5>
        <canvas id="revenueChart" height="100"></canvas>
    </div>

    <!-- Payment Methods -->
    <div class="payment-methods-table">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>طريقة الدفع</th>
                        <th>عدد المدفوعات</th>
                        <th>إجمالي المبلغ</th>
                        <th>النسبة المئوية</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($paymentMethods as $method)
                    <tr>
                        <td>
                            <span class="payment-method-badge method-{{ $method->payment_method }}">
                                <i class="fas fa-{{ $method->payment_method === 'cash' ? 'money-bill' : ($method->payment_method === 'card' ? 'credit-card' : 'university') }} me-1"></i>
                                @switch($method->payment_method)
                                    @case('cash') نقدي @break
                                    @case('card') بطاقة @break
                                    @case('bank_transfer') تحويل بنكي @break
                                    @case('check') شيك @break
                                    @case('mobile_payment') دفع عبر الجوال @break
                                    @default أخرى
                                @endswitch
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-primary">{{ $method->count }}</span>
                        </td>
                        <td>
                            <span class="fw-bold text-success">@arabicCurrency($method->total)</span>
                        </td>
                        <td>
                            @php
                                $percentage = $stats['total_revenue'] > 0 ? ($method->total / $stats['total_revenue']) * 100 : 0;
                            @endphp
                            <div class="d-flex align-items-center">
                                <div class="progress me-2" style="width: 60px; height: 8px;">
                                    <div class="progress-bar bg-success" style="width: {{ $percentage }}%"></div>
                                </div>
                                <small class="text-muted">{{ number_format($percentage, 1) }}%</small>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Period Info -->
    <div class="mt-3">
        <small class="text-muted">
            <i class="fas fa-calendar me-1"></i>
            فترة التقرير: من {{ \Carbon\Carbon::parse($dateFrom)->format('Y-m-d') }} إلى {{ \Carbon\Carbon::parse($dateTo)->format('Y-m-d') }}
        </small>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const ctx = document.getElementById('revenueChart').getContext('2d');
    const revenueData = @json($revenueData);
    
    const labels = Object.keys(revenueData);
    const data = Object.values(revenueData);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'الإيرادات (ريال)',
                data: data,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return new Intl.NumberFormat('ar-SA', {
                                style: 'currency',
                                currency: 'SAR'
                            }).format(value);
                        }
                    }
                }
            }
        }
    });
});
</script>
@endpush
@endsection
