<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Role extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'level',
        'is_active',
        'color',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'level' => 'integer',
    ];

    /**
     * Get the users that have this role.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'role', 'slug');
    }

    /**
     * Get the permissions for this role.
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'role_permissions');
    }

    /**
     * Check if role has a specific permission.
     */
    public function hasPermission(string $permission): bool
    {
        return $this->permissions()
            ->where('slug', $permission)
            ->where('is_active', true)
            ->exists();
    }

    /**
     * Check if role has any of the given permissions.
     */
    public function hasAnyPermission(array $permissions): bool
    {
        return $this->permissions()
            ->whereIn('slug', $permissions)
            ->where('is_active', true)
            ->exists();
    }

    /**
     * Check if role has all of the given permissions.
     */
    public function hasAllPermissions(array $permissions): bool
    {
        $rolePermissions = $this->permissions()
            ->whereIn('slug', $permissions)
            ->where('is_active', true)
            ->pluck('slug')
            ->toArray();

        return count($permissions) === count($rolePermissions);
    }

    /**
     * Assign permission to role.
     */
    public function givePermission(string|Permission $permission): void
    {
        if (is_string($permission)) {
            $permission = Permission::where('slug', $permission)->first();
        }

        if ($permission && !$this->hasPermission($permission->slug)) {
            $this->permissions()->attach($permission->id);
        }
    }

    /**
     * Remove permission from role.
     */
    public function revokePermission(string|Permission $permission): void
    {
        if (is_string($permission)) {
            $permission = Permission::where('slug', $permission)->first();
        }

        if ($permission) {
            $this->permissions()->detach($permission->id);
        }
    }

    /**
     * Sync permissions for role.
     */
    public function syncPermissions(array $permissions): void
    {
        $permissionIds = Permission::whereIn('slug', $permissions)->pluck('id')->toArray();
        $this->permissions()->sync($permissionIds);
    }

    /**
     * Get role by slug.
     */
    public static function findBySlug(string $slug): ?self
    {
        return static::where('slug', $slug)->where('is_active', true)->first();
    }

    /**
     * Create default roles with permissions.
     */
    public static function createDefaults(): void
    {
        $roles = [
            [
                'name' => 'Super Admin',
                'slug' => 'admin',
                'description' => 'Full system access with all permissions',
                'level' => 100,
                'color' => '#dc3545',
                'permissions' => 'all' // Special case for all permissions
            ],
            [
                'name' => 'Manager',
                'slug' => 'manager',
                'description' => 'Management access with most permissions',
                'level' => 80,
                'color' => '#fd7e14',
                'permissions' => [
                    'dashboard.view', 'reports.view', 'reports.export', 'reports.advanced',
                    'customers.view', 'customers.create', 'customers.edit', 'customers.history',
                    'tickets.view', 'tickets.create', 'tickets.edit', 'tickets.assign', 'tickets.status', 'tickets.view_all',
                    'inventory.view', 'inventory.create', 'inventory.edit', 'inventory.adjust', 'inventory.reports',
                    'invoices.view', 'invoices.create', 'invoices.edit', 'payments.process', 'payments.view', 'financial.reports',
                    'pos.access', 'pos.sales', 'pos.discounts', 'pos.reports',
                    'users.view', 'users.create', 'users.edit', 'users.roles',
                    'whatsapp.send', 'whatsapp.templates', 'sms.send', 'email.send',
                    'suppliers.view', 'suppliers.create', 'suppliers.edit',
                    'settings.view'
                ]
            ],
            [
                'name' => 'Technician',
                'slug' => 'technician',
                'description' => 'Technical staff with repair-focused permissions',
                'level' => 60,
                'color' => '#20c997',
                'permissions' => [
                    'dashboard.view',
                    'customers.view', 'customers.history',
                    'tickets.view', 'tickets.create', 'tickets.edit', 'tickets.status',
                    'inventory.view', 'inventory.adjust',
                    'invoices.view', 'invoices.create',
                    'whatsapp.send', 'sms.send', 'email.send',
                    'suppliers.view'
                ]
            ],
            [
                'name' => 'Receptionist',
                'slug' => 'receptionist',
                'description' => 'Front desk staff with customer service permissions',
                'level' => 40,
                'color' => '#6f42c1',
                'permissions' => [
                    'dashboard.view',
                    'customers.view', 'customers.create', 'customers.edit', 'customers.history',
                    'tickets.view', 'tickets.create', 'tickets.status',
                    'inventory.view',
                    'invoices.view', 'invoices.create', 'payments.process', 'payments.view',
                    'pos.access', 'pos.sales',
                    'whatsapp.send', 'sms.send', 'email.send'
                ]
            ],
            [
                'name' => 'Cashier',
                'slug' => 'cashier',
                'description' => 'POS and payment processing permissions',
                'level' => 30,
                'color' => '#17a2b8',
                'permissions' => [
                    'dashboard.view',
                    'customers.view', 'customers.create',
                    'inventory.view',
                    'invoices.view', 'payments.process', 'payments.view',
                    'pos.access', 'pos.sales', 'pos.discounts'
                ]
            ],
            [
                'name' => 'Viewer',
                'slug' => 'viewer',
                'description' => 'Read-only access for viewing information',
                'level' => 10,
                'color' => '#6c757d',
                'permissions' => [
                    'dashboard.view',
                    'customers.view', 'customers.history',
                    'tickets.view',
                    'inventory.view',
                    'invoices.view', 'payments.view',
                    'suppliers.view'
                ]
            ]
        ];

        foreach ($roles as $roleData) {
            $permissions = $roleData['permissions'];
            unset($roleData['permissions']);

            $role = static::firstOrCreate(
                ['slug' => $roleData['slug']],
                $roleData + ['is_active' => true]
            );

            // Assign permissions
            if ($permissions === 'all') {
                // Give all permissions to admin
                $allPermissions = Permission::where('is_active', true)->pluck('slug')->toArray();
                $role->syncPermissions($allPermissions);
            } else {
                $role->syncPermissions($permissions);
            }
        }
    }

    /**
     * Get role hierarchy level.
     */
    public function getHierarchyLevel(): int
    {
        return $this->level;
    }

    /**
     * Check if this role is higher than another role.
     */
    public function isHigherThan(Role $role): bool
    {
        return $this->level > $role->level;
    }

    /**
     * Check if this role is lower than another role.
     */
    public function isLowerThan(Role $role): bool
    {
        return $this->level < $role->level;
    }

    /**
     * Get roles that this role can manage.
     */
    public function getManageableRoles(): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('level', '<', $this->level)
            ->where('is_active', true)
            ->orderBy('level', 'desc')
            ->get();
    }
}
