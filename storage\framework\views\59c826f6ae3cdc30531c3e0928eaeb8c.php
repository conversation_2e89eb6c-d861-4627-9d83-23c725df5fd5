<?php $__env->startSection('title', 'بطاقات الإصلاح'); ?>

<?php $__env->startPush('styles'); ?>
<style>
.repair-tickets-header {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.stat-card.stat-primary::before {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.stat-card.stat-warning::before {
    background: linear-gradient(45deg, #ffc107, #e0a800);
}

.stat-card.stat-danger::before {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

.stat-card.stat-success::before {
    background: linear-gradient(45deg, #28a745, #20c997);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.875rem;
    text-transform: uppercase;
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.3;
    float: left;
    margin-top: -0.5rem;
}

.filter-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.tickets-table {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    overflow: hidden;
}

.table th {
    background: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

@media (max-width: 768px) {
    .repair-tickets-header {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .filter-card {
        padding: 1rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="repair-tickets-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">بطاقات الإصلاح</h1>
                <p class="mb-0 opacity-75">إدارة وتتبع جميع بطاقات الإصلاح</p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(route('repair-tickets.create')); ?>" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>بطاقة إصلاح جديدة
                </a>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-light dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-2"></i>تصدير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?php echo e(route('print-export.tickets.export-excel', request()->query())); ?>">
                            <i class="fas fa-file-excel me-2"></i>تصدير إكسل
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="printSelectedTickets()">
                            <i class="fas fa-print me-2"></i>طباعة المحدد
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card stat-primary">
            <div class="stat-icon text-primary">
                <i class="fas fa-ticket-alt"></i>
            </div>
            <div class="stat-value text-primary"><?php echo App\Helpers\ArabicFormatter::formatNumber($stats['total']); ?></div>
            <div class="stat-label">إجمالي البطاقات</div>
        </div>

        <div class="stat-card stat-warning">
            <div class="stat-icon text-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-value text-warning"><?php echo App\Helpers\ArabicFormatter::formatNumber($stats['pending']); ?></div>
            <div class="stat-label">قيد الانتظار</div>
        </div>

        <div class="stat-card stat-danger">
            <div class="stat-icon text-danger">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-value text-danger"><?php echo App\Helpers\ArabicFormatter::formatNumber($stats['overdue']); ?></div>
            <div class="stat-label">متأخرة</div>
        </div>

        <div class="stat-card stat-success">
            <div class="stat-icon text-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-value text-success"><?php echo App\Helpers\ArabicFormatter::formatNumber($stats['completed_today']); ?></div>
            <div class="stat-label">مكتملة اليوم</div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="filter-card">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>فلاتر البحث المتقدم
            </h5>
            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#advancedFilters">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div class="collapse show" id="advancedFilters">
            <form method="GET" action="<?php echo e(route('repair-tickets.index')); ?>" class="row g-3">
                <!-- Search -->
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <div class="input-group">
                        <input type="text"
                               class="form-control"
                               id="search"
                               name="search"
                               value="<?php echo e(request('search')); ?>"
                               placeholder="رقم البطاقة، العميل، الجهاز، المشكلة...">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- Status Filter -->
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($status->id); ?>"
                                    <?php echo e(request('status') == $status->id ? 'selected' : ''); ?>>
                                <?php echo e($status->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Brand Filter -->
                <div class="col-md-2">
                    <label for="brand" class="form-label">الماركة</label>
                    <select name="brand" id="brand" class="form-select">
                        <option value="">جميع الماركات</option>
                        <?php $__currentLoopData = $brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($brand->id); ?>"
                                    <?php echo e(request('brand') == $brand->id ? 'selected' : ''); ?>>
                                <?php echo e($brand->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Technician Filter -->
                <div class="col-md-2">
                    <label for="technician" class="form-label">الفني</label>
                    <select name="technician" id="technician" class="form-select">
                        <option value="">جميع الفنيين</option>
                        <option value="unassigned" <?php echo e(request('technician') == 'unassigned' ? 'selected' : ''); ?>>
                            غير مخصص
                        </option>
                        <?php $__currentLoopData = $technicians; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $technician): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($technician->id); ?>"
                                    <?php echo e(request('technician') == $technician->id ? 'selected' : ''); ?>>
                                <?php echo e($technician->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Priority Filter -->
                <div class="col-md-2">
                    <label for="priority" class="form-label">الأولوية</label>
                    <select name="priority" id="priority" class="form-select">
                        <option value="">جميع الأولويات</option>
                        <option value="overdue" <?php echo e(request('priority') == 'overdue' ? 'selected' : ''); ?>>
                            متأخر
                        </option>
                        <option value="urgent" <?php echo e(request('priority') == 'urgent' ? 'selected' : ''); ?>>
                            عاجل (قريب الانتهاء)
                        </option>
                        <option value="pending" <?php echo e(request('priority') == 'pending' ? 'selected' : ''); ?>>
                            معلق
                        </option>
                        <option value="completed" <?php echo e(request('priority') == 'completed' ? 'selected' : ''); ?>>
                            مكتمل
                        </option>
                    </select>
                </div>

                <!-- Date Range -->
                <div class="col-md-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date"
                           class="form-control"
                           id="date_from"
                           name="date_from"
                           value="<?php echo e(request('date_from')); ?>">
                </div>

                <div class="col-md-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date"
                           class="form-control"
                           id="date_to"
                           name="date_to"
                           value="<?php echo e(request('date_to')); ?>">
                </div>

                <!-- Sort Options -->
                <div class="col-md-3">
                    <label for="sort" class="form-label">ترتيب حسب</label>
                    <select name="sort" id="sort" class="form-select">
                        <option value="created_at" <?php echo e(request('sort') == 'created_at' ? 'selected' : ''); ?>>
                            تاريخ الإنشاء
                        </option>
                        <option value="received_date" <?php echo e(request('sort') == 'received_date' ? 'selected' : ''); ?>>
                            تاريخ الاستلام
                        </option>
                        <option value="ticket_number" <?php echo e(request('sort') == 'ticket_number' ? 'selected' : ''); ?>>
                            رقم البطاقة
                        </option>
                        <option value="estimated_completion_date" <?php echo e(request('sort') == 'estimated_completion_date' ? 'selected' : ''); ?>>
                            تاريخ الاستحقاق
                        </option>
                        <option value="priority" <?php echo e(request('sort') == 'priority' ? 'selected' : ''); ?>>
                            الأولوية
                        </option>
                    </select>
                </div>

                <!-- Action Buttons -->
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary flex-fill">
                            <i class="fas fa-filter me-2"></i>تطبيق الفلتر
                        </button>
                        <?php if(request()->hasAny(['search', 'status', 'brand', 'technician', 'priority', 'date_from', 'date_to', 'sort'])): ?>
                            <a href="<?php echo e(route('repair-tickets.index')); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="filter-card" id="bulkActionsCard" style="display: none;">
        <h5 class="mb-3">
            <i class="fas fa-tasks me-2"></i>الإجراءات المجمعة
        </h5>
        <form id="bulkActionForm" method="POST" action="<?php echo e(route('repair-tickets.bulk-action')); ?>">
            <?php echo csrf_field(); ?>
            <div class="row align-items-end g-3">
                <div class="col-md-3">
                    <label for="bulkAction" class="form-label">الإجراء</label>
                    <select name="action" id="bulkAction" class="form-select" required>
                        <option value="">اختر الإجراء</option>
                        <option value="update_status">تحديث الحالة</option>
                        <option value="assign_technician">تخصيص فني</option>
                        <option value="delete">حذف البطاقات</option>
                    </select>
                </div>

                <div class="col-md-3" id="statusField" style="display: none;">
                    <label for="status_id" class="form-label">الحالة الجديدة</label>
                    <select name="status_id" id="status_id" class="form-select">
                        <option value="">اختر الحالة</option>
                        <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($status->id); ?>"><?php echo e($status->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="col-md-3" id="technicianField" style="display: none;">
                    <label for="technician_id" class="form-label">الفني</label>
                    <select name="technician_id" id="technician_id" class="form-select">
                        <option value="">اختر الفني</option>
                        <?php $__currentLoopData = $technicians; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $technician): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($technician->id); ?>"><?php echo e($technician->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-check-circle me-2"></i>تطبيق على <span id="selectedCount">0</span> بطاقة
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearSelection()">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Tickets Table -->
    <div class="tickets-table">
        <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
            <h5 class="mb-0">
                بطاقات الإصلاح
                <span class="badge bg-secondary"><?php echo e($tickets->total()); ?> إجمالي</span>
            </h5>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-primary" onclick="selectAll()">
                    <i class="fas fa-check-double me-1"></i>تحديد الكل
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="clearSelection()">
                    <i class="fas fa-times me-1"></i>إلغاء التحديد
                </button>
            </div>
        </div>

        <?php if($tickets->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th style="width: 40px">
                                <input type="checkbox" id="selectAllCheckbox" class="form-check-input">
                            </th>
                            <th>رقم البطاقة</th>
                            <th>العميل</th>
                            <th>الجهاز</th>
                            <th>المشكلة</th>
                            <th>الحالة</th>
                            <th>تاريخ الاستلام</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                                <tbody>
                                    <?php $__currentLoopData = $tickets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ticket): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="<?php echo e($ticket->isOverdue() ? 'table-warning' : ''); ?>">
                                            <td>
                                                <input type="checkbox"
                                                       name="tickets[]"
                                                       value="<?php echo e($ticket->id); ?>"
                                                       class="form-check-input ticket-checkbox">
                                            </td>
                                            <td>
                                                <a href="<?php echo e(route('repair-tickets.show', $ticket)); ?>"
                                                   class="text-decoration-none fw-bold text-primary">
                                                    <?php echo e($ticket->ticket_number); ?>

                                                </a>
                                                <?php if($ticket->isOverdue()): ?>
                                                    <i class="fas fa-exclamation-triangle text-warning ms-1"
                                                       title="متأخر"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <a href="<?php echo e(route('customers.show', $ticket->customer)); ?>"
                                                       class="text-decoration-none fw-bold">
                                                        <?php echo e($ticket->customer->name); ?>

                                                    </a>
                                                    <small class="text-muted">
                                                        <i class="fas fa-phone me-1"></i>
                                                        <?php echo e($ticket->customer->phone_number); ?>

                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <strong><?php echo e($ticket->brand->name); ?></strong>
                                                    <small class="text-muted"><?php echo e($ticket->device_model); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span title="<?php echo e($ticket->reported_problem); ?>" class="text-truncate d-inline-block" style="max-width: 200px;">
                                                    <?php echo e(Str::limit($ticket->reported_problem, 40)); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge"
                                                      style="background-color: <?php echo e($ticket->repairStatus->color); ?>; color: white;">
                                                    <?php echo e($ticket->repairStatus->name); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <small><?php echo e($ticket->received_date->format('Y-m-d')); ?></small>
                                                    <small class="text-muted">
                                                        منذ <?php echo e($ticket->daysSinceReceived()); ?> يوم
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if($ticket->estimated_completion_date): ?>
                                                    <small class="<?php echo e($ticket->isOverdue() ? 'text-warning fw-bold' : 'text-muted'); ?>">
                                                        <?php echo e($ticket->estimated_completion_date->format('Y-m-d')); ?>

                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-muted">غير محدد</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="d-flex gap-1 flex-nowrap align-items-center">
                                                    <a href="<?php echo e(route('repair-tickets.show', $ticket)); ?>"
                                                       class="btn btn-sm btn-outline-primary"
                                                       data-bs-toggle="tooltip"
                                                       data-bs-placement="top"
                                                       title="عرض تفاصيل بطاقة الإصلاح"
                                                       style="padding: 0.25rem 0.5rem; font-size: 0.75rem;">
                                                        <i class="fas fa-eye"></i>
                                                        <span class="d-none d-xxl-inline ms-1">عرض</span>
                                                    </a>
                                                    <a href="<?php echo e(route('repair-tickets.edit', $ticket)); ?>"
                                                       class="btn btn-sm btn-outline-secondary"
                                                       data-bs-toggle="tooltip"
                                                       data-bs-placement="top"
                                                       title="تعديل بطاقة الإصلاح"
                                                       style="padding: 0.25rem 0.5rem; font-size: 0.75rem;">
                                                        <i class="fas fa-edit"></i>
                                                        <span class="d-none d-xxl-inline ms-1">تعديل</span>
                                                    </a>
                                                    <?php if($ticket->repairStatus->is_final && !$ticket->invoice): ?>
                                                        <a href="<?php echo e(route('invoices.create', ['repair_ticket_id' => $ticket->id])); ?>"
                                                           class="btn btn-sm btn-outline-success"
                                                           data-bs-toggle="tooltip"
                                                           data-bs-placement="top"
                                                           title="إنشاء فاتورة للعميل"
                                                           style="padding: 0.25rem 0.5rem; font-size: 0.75rem;">
                                                            <i class="fas fa-file-invoice"></i>
                                                            <span class="d-none d-xxl-inline ms-1">فاتورة</span>
                                                        </a>
                                                    <?php elseif($ticket->invoice): ?>
                                                        <a href="<?php echo e(route('invoices.show', $ticket->invoice)); ?>"
                                                           class="btn btn-sm btn-outline-info"
                                                           data-bs-toggle="tooltip"
                                                           data-bs-placement="top"
                                                           title="عرض الفاتورة المرتبطة"
                                                           style="padding: 0.25rem 0.5rem; font-size: 0.75rem;">
                                                            <i class="fas fa-file-invoice-dollar"></i>
                                                            <span class="d-none d-xxl-inline ms-1">الفاتورة</span>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-ticket-alt display-1 text-muted"></i>
                            <h4 class="mt-3">لا توجد بطاقات إصلاح</h4>
                            <p class="text-muted">
                                <?php if(request()->hasAny(['search', 'status', 'brand'])): ?>
                                    لا توجد بطاقات تطابق معايير البحث المحددة.
                                <?php else: ?>
                                    ابدأ بإنشاء أول بطاقة إصلاح.
                                <?php endif; ?>
                            </p>
                            <?php if(!request()->hasAny(['search', 'status', 'brand'])): ?>
                                <a href="<?php echo e(route('repair-tickets.create')); ?>" class="btn btn-success">
                                    <i class="fas fa-plus-circle me-2"></i>إنشاء أول بطاقة
                                </a>
                            <?php else: ?>
                                <a href="<?php echo e(route('repair-tickets.index')); ?>" class="btn btn-primary">
                                    <i class="fas fa-refresh me-2"></i>عرض جميع البطاقات
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>

        <?php if($tickets->hasPages()): ?>
            <div class="d-flex justify-content-center p-3 border-top">
                <?php echo e($tickets->withQueryString()->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const ticketCheckboxes = document.querySelectorAll('.ticket-checkbox');
    const bulkActionsCard = document.getElementById('bulkActionsCard');
    const bulkActionSelect = document.getElementById('bulkAction');
    const statusField = document.getElementById('statusField');
    const technicianField = document.getElementById('technicianField');
    const selectedCountSpan = document.getElementById('selectedCount');
    const bulkActionForm = document.getElementById('bulkActionForm');

    // Handle select all checkbox
    selectAllCheckbox.addEventListener('change', function() {
        ticketCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    // Handle individual checkboxes
    ticketCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateBulkActions();
        });
    });

    // Handle bulk action selection
    bulkActionSelect.addEventListener('change', function() {
        const action = this.value;

        // Hide all fields first
        statusField.style.display = 'none';
        technicianField.style.display = 'none';

        // Show relevant field based on action
        if (action === 'update_status') {
            statusField.style.display = 'block';
        } else if (action === 'assign_technician') {
            technicianField.style.display = 'block';
        }
    });

    // Handle bulk action form submission
    bulkActionForm.addEventListener('submit', function(e) {
        const selectedTickets = document.querySelectorAll('.ticket-checkbox:checked');
        const action = bulkActionSelect.value;

        if (selectedTickets.length === 0) {
            e.preventDefault();
            alert('يرجى تحديد بطاقة واحدة على الأقل.');
            return;
        }

        if (action === 'delete') {
            if (!confirm(`هل أنت متأكد من حذف ${selectedTickets.length} بطاقة؟ لا يمكن التراجع عن هذا الإجراء.`)) {
                e.preventDefault();
                return;
            }
        }

        // Add selected ticket IDs to form
        selectedTickets.forEach(checkbox => {
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'tickets[]';
            hiddenInput.value = checkbox.value;
            this.appendChild(hiddenInput);
        });
    });

    function updateSelectAllState() {
        const checkedCount = document.querySelectorAll('.ticket-checkbox:checked').length;
        const totalCount = ticketCheckboxes.length;

        selectAllCheckbox.checked = checkedCount === totalCount;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
    }

    function updateBulkActions() {
        const checkedCount = document.querySelectorAll('.ticket-checkbox:checked').length;

        if (checkedCount > 0) {
            bulkActionsCard.style.display = 'block';
            selectedCountSpan.textContent = checkedCount;
        } else {
            bulkActionsCard.style.display = 'none';
            bulkActionSelect.value = '';
            statusField.style.display = 'none';
            technicianField.style.display = 'none';
        }
    }
});

// Global functions for buttons
function selectAll() {
    document.getElementById('selectAllCheckbox').checked = true;
    document.getElementById('selectAllCheckbox').dispatchEvent(new Event('change'));
}

function clearSelection() {
    document.getElementById('selectAllCheckbox').checked = false;
    document.getElementById('selectAllCheckbox').dispatchEvent(new Event('change'));
}

// Print selected tickets function
function printSelectedTickets() {
    const selectedTickets = [];
    document.querySelectorAll('.ticket-checkbox:checked').forEach(checkbox => {
        selectedTickets.push(checkbox.value);
    });

    if (selectedTickets.length === 0) {
        alert('يرجى تحديد بطاقة واحدة على الأقل للطباعة.');
        return;
    }

    // Create a form to submit the selected ticket IDs
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?php echo e(route("print-export.tickets.print-multiple")); ?>';
    form.target = '_blank';

    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '<?php echo e(csrf_token()); ?>';
    form.appendChild(csrfToken);

    // Add selected ticket IDs
    selectedTickets.forEach(ticketId => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'ticket_ids[]';
        input.value = ticketId;
        form.appendChild(input);
    });

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// تفعيل tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\nj\resources\views/repair-tickets/index.blade.php ENDPATH**/ ?>