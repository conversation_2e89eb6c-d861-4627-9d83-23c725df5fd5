<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_template_categories', function (Blueprint $table) {
            $table->id();
            
            // Category identification
            $table->string('name')->unique();
            $table->string('name_ar');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->text('description_ar')->nullable();
            
            // WhatsApp Business API category mapping
            $table->enum('whatsapp_category', [
                'TRANSACTIONAL',
                'MARKETING', 
                'UTILITY',
                'AUTHENTICATION'
            ])->default('TRANSACTIONAL');
            
            // Visual and organizational
            $table->string('icon', 50)->default('bi-chat-dots');
            $table->string('color', 7)->default('#007bff');
            $table->integer('sort_order')->default(0);
            
            // Status and permissions
            $table->boolean('is_active')->default(true);
            $table->boolean('requires_approval')->default(true);
            $table->json('allowed_variables')->nullable(); // Common variables for this category
            
            // Usage tracking
            $table->integer('template_count')->default(0);
            $table->integer('usage_count')->default(0);
            $table->timestamp('last_used_at')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['is_active', 'sort_order']);
            $table->index('whatsapp_category');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_template_categories');
    }
};
