@extends('layouts.app')

@section('title', 'تقرير الفواتير المعلقة')

@push('styles')
<style>
.outstanding-header {
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.875rem;
    text-transform: uppercase;
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.3;
    float: left;
    margin-top: -0.5rem;
}

.outstanding-table {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.table th {
    background: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.invoice-number {
    font-weight: 600;
    color: #dc3545;
}

.customer-info {
    display: flex;
    flex-direction: column;
}

.customer-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.customer-phone {
    color: #6c757d;
    font-size: 0.875rem;
}

.amount-display {
    font-weight: 600;
    font-size: 1.1rem;
}

.amount-outstanding {
    color: #dc3545;
}

.amount-total {
    color: #28a745;
}

.overdue-badge {
    background: #dc3545;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
}

.pending-badge {
    background: #ffc107;
    color: #212529;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
}

.days-overdue {
    color: #dc3545;
    font-weight: 600;
    font-size: 0.875rem;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.btn-action {
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-decoration: none;
}

.btn-view {
    background: #17a2b8;
    color: white;
}

.btn-payment {
    background: #28a745;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.3;
}

@media (max-width: 768px) {
    .outstanding-header {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="outstanding-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">تقرير الفواتير المعلقة</h1>
                <p class="mb-0 opacity-75">الفواتير غير المدفوعة والمتأخرة</p>
            </div>
            <div>
                <a href="{{ route('reports.financial') }}" class="btn btn-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon text-warning">
                <i class="fas fa-file-invoice-dollar"></i>
            </div>
            <div class="stat-value text-warning">@arabicCurrency($stats['total_outstanding'])</div>
            <div class="stat-label">إجمالي المبالغ المعلقة</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-danger">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-value text-danger">@arabicCurrency($stats['overdue_amount'])</div>
            <div class="stat-label">المبالغ المتأخرة</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-info">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="stat-value text-info">@arabicNumber($stats['outstanding_count'])</div>
            <div class="stat-label">عدد الفواتير المعلقة</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-primary">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-value text-primary">@arabicNumber($stats['overdue_count'])</div>
            <div class="stat-label">عدد الفواتير المتأخرة</div>
        </div>
    </div>

    <!-- Outstanding Invoices Table -->
    <div class="outstanding-table">
        @if($outstandingInvoices->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>تاريخ الفاتورة</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>إجمالي الفاتورة</th>
                            <th>المبلغ المستحق</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($outstandingInvoices as $invoice)
                        <tr class="{{ $invoice->due_date < now() ? 'table-danger' : '' }}">
                            <td>
                                <span class="invoice-number">{{ $invoice->invoice_number }}</span>
                            </td>
                            <td>
                                <div class="customer-info">
                                    <div class="customer-name">
                                        <a href="{{ route('customers.show', $invoice->customer) }}" class="text-decoration-none">
                                            {{ $invoice->customer->name }}
                                        </a>
                                    </div>
                                    <div class="customer-phone">{{ $invoice->customer->phone_number }}</div>
                                </div>
                            </td>
                            <td>{{ $invoice->invoice_date->format('Y-m-d') }}</td>
                            <td>
                                {{ $invoice->due_date->format('Y-m-d') }}
                                @if($invoice->due_date < now())
                                    <br>
                                    <small class="days-overdue">
                                        متأخر {{ $invoice->due_date->diffInDays(now()) }} يوم
                                    </small>
                                @endif
                            </td>
                            <td>
                                <span class="amount-display amount-total">
                                    @arabicCurrency($invoice->total_amount)
                                </span>
                            </td>
                            <td>
                                <span class="amount-display amount-outstanding">
                                    @arabicCurrency($invoice->total_amount - $invoice->paid_amount)
                                </span>
                            </td>
                            <td>
                                @if($invoice->due_date < now())
                                    <span class="overdue-badge">متأخر</span>
                                @else
                                    <span class="pending-badge">معلق</span>
                                @endif
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="{{ route('invoices.show', $invoice) }}" 
                                       class="btn-action btn-view" 
                                       title="عرض الفاتورة">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    <a href="{{ route('payments.create', ['invoice_id' => $invoice->id]) }}" 
                                       class="btn-action btn-payment" 
                                       title="تسجيل دفعة">
                                        <i class="fas fa-credit-card"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="empty-state">
                <i class="fas fa-check-circle"></i>
                <h5>لا توجد فواتير معلقة</h5>
                <p>جميع الفواتير مدفوعة بالكامل!</p>
                <a href="{{ route('invoices.index') }}" class="btn btn-primary">
                    <i class="fas fa-file-invoice me-2"></i>عرض جميع الفواتير
                </a>
            </div>
        @endif
    </div>

    <!-- Summary -->
    @if($outstandingInvoices->count() > 0)
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            الفواتير المتأخرة ({{ $overdueInvoices->count() }})
                        </h6>
                    </div>
                    <div class="card-body">
                        @if($overdueInvoices->count() > 0)
                            <p class="text-danger mb-2">
                                <strong>إجمالي المبلغ المتأخر: @arabicCurrency($stats['overdue_amount'])</strong>
                            </p>
                            <small class="text-muted">
                                هذه الفواتير تجاوزت تاريخ الاستحقاق وتحتاج لمتابعة عاجلة
                            </small>
                        @else
                            <p class="text-success mb-0">لا توجد فواتير متأخرة</p>
                        @endif
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-clock me-2"></i>
                            الفواتير المعلقة ({{ $stats['outstanding_count'] - $stats['overdue_count'] }})
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-info mb-2">
                            <strong>إجمالي المبلغ المعلق: @arabicCurrency($stats['total_outstanding'] - $stats['overdue_amount'])</strong>
                        </p>
                        <small class="text-muted">
                            هذه الفواتير لم تتجاوز تاريخ الاستحقاق بعد
                        </small>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
@endsection
