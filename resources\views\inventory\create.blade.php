@extends('layouts.app')

@section('title', 'إضافة عنصر جديد - إدارة المخزون')

@push('styles')
<style>
.form-section {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    margin-bottom: 1.5rem;
}

.form-section-header {
    background: linear-gradient(45deg, #4e73df, #224abe);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
    margin: -1px -1px 0 -1px;
}

.form-section-body {
    padding: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.btn-primary {
    background: linear-gradient(45deg, #4e73df, #224abe);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 0.35rem;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #224abe, #1e3a8a);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-secondary {
    background: #858796;
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 0.35rem;
}

.required-field::after {
    content: " *";
    color: #e74a3b;
    font-weight: bold;
}

.help-text {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.input-group-text {
    background-color: #f8f9fc;
    border: 1px solid #d1d3e2;
    color: #5a5c69;
}

.card-header-icon {
    width: 24px;
    height: 24px;
    margin-left: 0.5rem;
}

.preview-box {
    background: #f8f9fc;
    border: 2px dashed #d1d3e2;
    border-radius: 0.35rem;
    padding: 2rem;
    text-align: center;
    color: #6c757d;
}

.form-check-input:checked {
    background-color: #4e73df;
    border-color: #4e73df;
}

.alert {
    border-radius: 0.35rem;
    border: none;
    padding: 1rem 1.5rem;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

@media (max-width: 768px) {
    .form-section-body {
        padding: 1rem;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إضافة عنصر جديد</h1>
            <p class="text-muted">إضافة عنصر جديد إلى المخزون</p>
        </div>
        <div>
            <a href="{{ route('inventory.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للمخزون
            </a>
        </div>
    </div>

    <!-- Alert Info -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle me-2"></i>
        <strong>تنبيه:</strong> الحقول المميزة بعلامة (*) مطلوبة ويجب ملؤها.
    </div>

    <!-- Form -->
    <form action="{{ route('inventory.store') }}" method="POST" enctype="multipart/form-data" id="inventoryForm">
        @csrf

        <!-- Basic Information Section -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle card-header-icon"></i>
                    المعلومات الأساسية
                </h5>
            </div>
            <div class="form-section-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label required-field">اسم العنصر</label>
                        <input type="text"
                               class="form-control @error('name') is-invalid @enderror"
                               id="name"
                               name="name"
                               value="{{ old('name') }}"
                               placeholder="أدخل اسم العنصر"
                               required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">اسم واضح ومميز للعنصر</div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="sku" class="form-label required-field">رمز المنتج (SKU)</label>
                        <input type="text"
                               class="form-control @error('sku') is-invalid @enderror"
                               id="sku"
                               name="sku"
                               value="{{ old('sku') }}"
                               placeholder="مثال: IP14P-SCR-001"
                               required>
                        @error('sku')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">رمز فريد لتمييز المنتج</div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="barcode" class="form-label">الباركود</label>
                        <input type="text"
                               class="form-control @error('barcode') is-invalid @enderror"
                               id="barcode"
                               name="barcode"
                               value="{{ old('barcode') }}"
                               placeholder="أدخل رقم الباركود">
                        @error('barcode')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">رقم الباركود إن وجد</div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="category_id" class="form-label required-field">الفئة</label>
                        <select class="form-select @error('category_id') is-invalid @enderror"
                                id="category_id"
                                name="category_id"
                                required>
                            <option value="">اختر الفئة</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}"
                                        {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('category_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="brand_id" class="form-label">الماركة</label>
                        <select class="form-select @error('brand_id') is-invalid @enderror"
                                id="brand_id"
                                name="brand_id">
                            <option value="">اختر الماركة</option>
                            @foreach($brands as $brand)
                                <option value="{{ $brand->id }}"
                                        {{ old('brand_id') == $brand->id ? 'selected' : '' }}>
                                    {{ $brand->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('brand_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="model_compatibility" class="form-label">توافق الموديل</label>
                        <input type="text"
                               class="form-control @error('model_compatibility') is-invalid @enderror"
                               id="model_compatibility"
                               name="model_compatibility"
                               value="{{ old('model_compatibility') }}"
                               placeholder="مثال: iPhone 14 Pro">
                        @error('model_compatibility')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">الأجهزة المتوافقة مع هذا العنصر</div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">الوصف</label>
                    <textarea class="form-control @error('description') is-invalid @enderror"
                              id="description"
                              name="description"
                              rows="3"
                              placeholder="وصف تفصيلي للعنصر">{{ old('description') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Technical Information Section -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs card-header-icon"></i>
                    المعلومات التقنية
                </h5>
            </div>
            <div class="form-section-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="part_number" class="form-label">رقم القطعة</label>
                        <input type="text"
                               class="form-control @error('part_number') is-invalid @enderror"
                               id="part_number"
                               name="part_number"
                               value="{{ old('part_number') }}"
                               placeholder="رقم القطعة من الشركة المصنعة">
                        @error('part_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="oem_number" class="form-label">رقم الشركة المصنعة</label>
                        <input type="text"
                               class="form-control @error('oem_number') is-invalid @enderror"
                               id="oem_number"
                               name="oem_number"
                               value="{{ old('oem_number') }}"
                               placeholder="رقم OEM">
                        @error('oem_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="condition" class="form-label required-field">حالة العنصر</label>
                        <select class="form-select @error('condition') is-invalid @enderror"
                                id="condition"
                                name="condition"
                                required>
                            <option value="">اختر الحالة</option>
                            <option value="new" {{ old('condition') == 'new' ? 'selected' : '' }}>جديد</option>
                            <option value="refurbished" {{ old('condition') == 'refurbished' ? 'selected' : '' }}>مجدد</option>
                            <option value="used" {{ old('condition') == 'used' ? 'selected' : '' }}>مستعمل</option>
                        </select>
                        @error('condition')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="warranty_months" class="form-label required-field">الضمان (بالأشهر)</label>
                        <input type="number"
                               class="form-control @error('warranty_months') is-invalid @enderror"
                               id="warranty_months"
                               name="warranty_months"
                               value="{{ old('warranty_months', 0) }}"
                               min="0"
                               max="120"
                               required>
                        @error('warranty_months')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="expiry_date" class="form-label">تاريخ الانتهاء</label>
                        <input type="date"
                               class="form-control @error('expiry_date') is-invalid @enderror"
                               id="expiry_date"
                               name="expiry_date"
                               value="{{ old('expiry_date') }}">
                        @error('expiry_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">للمنتجات القابلة للانتهاء</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stock Information Section -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-boxes card-header-icon"></i>
                    معلومات المخزون
                </h5>
            </div>
            <div class="form-section-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="current_stock" class="form-label required-field">المخزون الحالي</label>
                        <input type="number"
                               class="form-control @error('current_stock') is-invalid @enderror"
                               id="current_stock"
                               name="current_stock"
                               value="{{ old('current_stock', 0) }}"
                               min="0"
                               required>
                        @error('current_stock')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="minimum_stock" class="form-label required-field">الحد الأدنى</label>
                        <input type="number"
                               class="form-control @error('minimum_stock') is-invalid @enderror"
                               id="minimum_stock"
                               name="minimum_stock"
                               value="{{ old('minimum_stock', 5) }}"
                               min="0"
                               required>
                        @error('minimum_stock')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">تنبيه عند الوصول لهذا الحد</div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="maximum_stock" class="form-label required-field">الحد الأقصى</label>
                        <input type="number"
                               class="form-control @error('maximum_stock') is-invalid @enderror"
                               id="maximum_stock"
                               name="maximum_stock"
                               value="{{ old('maximum_stock', 100) }}"
                               min="1"
                               required>
                        @error('maximum_stock')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="reorder_quantity" class="form-label required-field">كمية إعادة الطلب</label>
                        <input type="number"
                               class="form-control @error('reorder_quantity') is-invalid @enderror"
                               id="reorder_quantity"
                               name="reorder_quantity"
                               value="{{ old('reorder_quantity', 20) }}"
                               min="1"
                               required>
                        @error('reorder_quantity')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="unit_of_measure" class="form-label required-field">وحدة القياس</label>
                        <select class="form-select @error('unit_of_measure') is-invalid @enderror"
                                id="unit_of_measure"
                                name="unit_of_measure"
                                required>
                            <option value="">اختر وحدة القياس</option>
                            <option value="قطعة" {{ old('unit_of_measure') == 'قطعة' ? 'selected' : '' }}>قطعة</option>
                            <option value="مجموعة" {{ old('unit_of_measure') == 'مجموعة' ? 'selected' : '' }}>مجموعة</option>
                            <option value="متر" {{ old('unit_of_measure') == 'متر' ? 'selected' : '' }}>متر</option>
                            <option value="كيلوجرام" {{ old('unit_of_measure') == 'كيلوجرام' ? 'selected' : '' }}>كيلوجرام</option>
                            <option value="لتر" {{ old('unit_of_measure') == 'لتر' ? 'selected' : '' }}>لتر</option>
                            <option value="علبة" {{ old('unit_of_measure') == 'علبة' ? 'selected' : '' }}>علبة</option>
                        </select>
                        @error('unit_of_measure')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="location" class="form-label">الموقع في المستودع</label>
                        <input type="text"
                               class="form-control @error('location') is-invalid @enderror"
                               id="location"
                               name="location"
                               value="{{ old('location') }}"
                               placeholder="مثال: رف A1">
                        @error('location')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="mb-3">
                    <label for="warehouse_section" class="form-label">قسم المستودع</label>
                    <input type="text"
                           class="form-control @error('warehouse_section') is-invalid @enderror"
                           id="warehouse_section"
                           name="warehouse_section"
                           value="{{ old('warehouse_section') }}"
                           placeholder="مثال: شاشات آيفون">
                    @error('warehouse_section')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Pricing Section -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-dollar-sign card-header-icon"></i>
                    معلومات الأسعار
                </h5>
            </div>
            <div class="form-section-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="cost_price" class="form-label required-field">سعر التكلفة</label>
                        <div class="input-group">
                            <input type="number"
                                   class="form-control @error('cost_price') is-invalid @enderror"
                                   id="cost_price"
                                   name="cost_price"
                                   value="{{ old('cost_price') }}"
                                   step="0.01"
                                   min="0"
                                   required>
                            <span class="input-group-text">ريال</span>
                        </div>
                        @error('cost_price')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="selling_price" class="form-label required-field">سعر البيع</label>
                        <div class="input-group">
                            <input type="number"
                                   class="form-control @error('selling_price') is-invalid @enderror"
                                   id="selling_price"
                                   name="selling_price"
                                   value="{{ old('selling_price') }}"
                                   step="0.01"
                                   min="0"
                                   required>
                            <span class="input-group-text">ريال</span>
                        </div>
                        @error('selling_price')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="wholesale_price" class="form-label">سعر الجملة</label>
                        <div class="input-group">
                            <input type="number"
                                   class="form-control @error('wholesale_price') is-invalid @enderror"
                                   id="wholesale_price"
                                   name="wholesale_price"
                                   value="{{ old('wholesale_price') }}"
                                   step="0.01"
                                   min="0">
                            <span class="input-group-text">ريال</span>
                        </div>
                        @error('wholesale_price')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">نسبة الربح المحسوبة</label>
                        <div class="input-group">
                            <input type="text"
                                   class="form-control"
                                   id="markup_display"
                                   readonly
                                   placeholder="سيتم الحساب تلقائياً">
                            <span class="input-group-text">%</span>
                        </div>
                        <div class="help-text">يتم حسابها تلقائياً بناءً على سعر التكلفة والبيع</div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label">الربح المتوقع للوحدة</label>
                        <div class="input-group">
                            <input type="text"
                                   class="form-control"
                                   id="profit_display"
                                   readonly
                                   placeholder="سيتم الحساب تلقائياً">
                            <span class="input-group-text">ريال</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Supplier Information Section -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-truck card-header-icon"></i>
                    معلومات المورد
                </h5>
            </div>
            <div class="form-section-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="primary_supplier_id" class="form-label">المورد الأساسي</label>
                        <select class="form-select @error('primary_supplier_id') is-invalid @enderror"
                                id="primary_supplier_id"
                                name="primary_supplier_id">
                            <option value="">اختر المورد</option>
                            @foreach($suppliers as $supplier)
                                <option value="{{ $supplier->id }}"
                                        {{ old('primary_supplier_id') == $supplier->id ? 'selected' : '' }}>
                                    {{ $supplier->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('primary_supplier_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="lead_time_days" class="form-label required-field">مدة التوريد (بالأيام)</label>
                        <input type="number"
                               class="form-control @error('lead_time_days') is-invalid @enderror"
                               id="lead_time_days"
                               name="lead_time_days"
                               value="{{ old('lead_time_days', 7) }}"
                               min="0"
                               required>
                        @error('lead_time_days')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">المدة المتوقعة لتوريد العنصر</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Section -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog card-header-icon"></i>
                    الإعدادات
                </h5>
            </div>
            <div class="form-section-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            <input class="form-check-input"
                                   type="checkbox"
                                   id="is_active"
                                   name="is_active"
                                   value="1"
                                   {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                <strong>العنصر نشط</strong>
                                <div class="help-text">العناصر غير النشطة لا تظهر في البحث</div>
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input"
                                   type="checkbox"
                                   id="is_serialized"
                                   name="is_serialized"
                                   value="1"
                                   {{ old('is_serialized') ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_serialized">
                                <strong>يتطلب رقم تسلسلي</strong>
                                <div class="help-text">للعناصر التي تحتاج تتبع فردي</div>
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input"
                                   type="checkbox"
                                   id="allow_backorder"
                                   name="allow_backorder"
                                   value="1"
                                   {{ old('allow_backorder') ? 'checked' : '' }}>
                            <label class="form-check-label" for="allow_backorder">
                                <strong>السماح بالطلب المسبق</strong>
                                <div class="help-text">البيع حتى لو نفد المخزون</div>
                            </label>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            <input class="form-check-input"
                                   type="checkbox"
                                   id="track_expiry"
                                   name="track_expiry"
                                   value="1"
                                   {{ old('track_expiry') ? 'checked' : '' }}>
                            <label class="form-check-label" for="track_expiry">
                                <strong>تتبع تاريخ الانتهاء</strong>
                                <div class="help-text">للمنتجات القابلة للانتهاء</div>
                            </label>
                        </div>

                        <div class="mb-3">
                            <label for="storage_conditions" class="form-label">شروط التخزين</label>
                            <textarea class="form-control @error('storage_conditions') is-invalid @enderror"
                                      id="storage_conditions"
                                      name="storage_conditions"
                                      rows="3"
                                      placeholder="مثال: يحفظ في مكان جاف وبارد">{{ old('storage_conditions') }}</textarea>
                            @error('storage_conditions')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">ملاحظات إضافية</label>
                    <textarea class="form-control @error('notes') is-invalid @enderror"
                              id="notes"
                              name="notes"
                              rows="3"
                              placeholder="أي ملاحظات أو معلومات إضافية">{{ old('notes') }}</textarea>
                    @error('notes')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="text-center mb-4">
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save me-2"></i>حفظ العنصر
            </button>
            <a href="{{ route('inventory.index') }}" class="btn btn-secondary btn-lg ms-2">
                <i class="fas fa-times me-2"></i>إلغاء
            </a>
        </div>
    </form>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate markup percentage and profit
    function calculateMarkup() {
        const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
        const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;

        if (costPrice > 0 && sellingPrice > 0) {
            const markup = ((sellingPrice - costPrice) / costPrice) * 100;
            const profit = sellingPrice - costPrice;

            document.getElementById('markup_display').value = markup.toFixed(2);
            document.getElementById('profit_display').value = profit.toFixed(2);
        } else {
            document.getElementById('markup_display').value = '';
            document.getElementById('profit_display').value = '';
        }
    }

    // Add event listeners
    document.getElementById('cost_price').addEventListener('input', calculateMarkup);
    document.getElementById('selling_price').addEventListener('input', calculateMarkup);

    // Auto-generate SKU based on category and name
    document.getElementById('category_id').addEventListener('change', generateSKU);
    document.getElementById('name').addEventListener('input', generateSKU);

    function generateSKU() {
        const categorySelect = document.getElementById('category_id');
        const nameInput = document.getElementById('name');
        const skuInput = document.getElementById('sku');

        if (categorySelect.value && nameInput.value && !skuInput.value) {
            const categoryText = categorySelect.options[categorySelect.selectedIndex].text;
            const categoryCode = categoryText.substring(0, 3).toUpperCase();
            const nameCode = nameInput.value.substring(0, 3).toUpperCase();
            const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

            skuInput.value = `${categoryCode}-${nameCode}-${randomNum}`;
        }
    }

    // Form validation
    document.getElementById('inventoryForm').addEventListener('submit', function(e) {
        const requiredFields = this.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });

    // Initial calculation
    calculateMarkup();
});
</script>
@endpush
@endsection
