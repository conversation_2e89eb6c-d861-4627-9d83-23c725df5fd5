<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class PurchaseAnalytics extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'analytics_date',
        'period_type',
        'total_purchase_amount',
        'total_paid_amount',
        'outstanding_amount',
        'orders_placed',
        'orders_completed',
        'orders_cancelled',
        'average_order_value',
        'average_delivery_time',
        'on_time_delivery_rate',
        'unique_suppliers',
        'top_suppliers',
        'top_categories',
        'cost_savings',
        'quality_metrics',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'analytics_date' => 'date',
        'total_purchase_amount' => 'decimal:2',
        'total_paid_amount' => 'decimal:2',
        'outstanding_amount' => 'decimal:2',
        'average_order_value' => 'decimal:2',
        'average_delivery_time' => 'decimal:1',
        'on_time_delivery_rate' => 'decimal:2',
        'top_suppliers' => 'array',
        'top_categories' => 'array',
        'cost_savings' => 'array',
        'quality_metrics' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Generate analytics for a specific date and period.
     */
    public static function generateAnalytics(Carbon $date, string $periodType): self
    {
        $startDate = self::getStartDateForPeriod($date, $periodType);
        $endDate = self::getEndDateForPeriod($date, $periodType);

        // Get purchase orders for the period
        $orders = PurchaseOrder::whereBetween('order_date', [$startDate, $endDate])
            ->with(['supplier', 'items.inventoryItem.category'])
            ->get();

        // Calculate basic metrics
        $totalPurchaseAmount = $orders->sum('total_amount');
        $totalPaidAmount = $orders->sum('paid_amount');
        $outstandingAmount = $totalPurchaseAmount - $totalPaidAmount;
        $ordersPlaced = $orders->count();
        $ordersCompleted = $orders->where('status', 'completed')->count();
        $ordersCancelled = $orders->where('status', 'cancelled')->count();
        $averageOrderValue = $ordersPlaced > 0 ? $totalPurchaseAmount / $ordersPlaced : 0;

        // Calculate delivery metrics
        $completedOrders = $orders->where('status', 'completed');
        $averageDeliveryTime = $completedOrders->whereNotNull('delivery_days_actual')->avg('delivery_days_actual') ?? 0;
        $onTimeDeliveries = $completedOrders->where('delivered_on_time', true)->count();
        $onTimeDeliveryRate = $completedOrders->count() > 0 ? ($onTimeDeliveries / $completedOrders->count()) * 100 : 0;

        // Get unique suppliers
        $uniqueSuppliers = $orders->pluck('supplier_id')->unique()->count();

        // Calculate top suppliers
        $topSuppliers = $orders->groupBy('supplier_id')
            ->map(function ($supplierOrders) {
                $supplier = $supplierOrders->first()->supplier;
                return [
                    'supplier_id' => $supplier->id,
                    'supplier_name' => $supplier->name,
                    'total_amount' => $supplierOrders->sum('total_amount'),
                    'order_count' => $supplierOrders->count(),
                    'average_order_value' => $supplierOrders->avg('total_amount'),
                ];
            })
            ->sortByDesc('total_amount')
            ->take(10)
            ->values()
            ->toArray();

        // Calculate top categories
        $topCategories = [];
        foreach ($orders as $order) {
            foreach ($order->items as $item) {
                if ($item->inventoryItem && $item->inventoryItem->category) {
                    $categoryName = $item->inventoryItem->category->name;
                    if (!isset($topCategories[$categoryName])) {
                        $topCategories[$categoryName] = [
                            'category_name' => $categoryName,
                            'total_amount' => 0,
                            'item_count' => 0,
                        ];
                    }
                    $topCategories[$categoryName]['total_amount'] += $item->total_cost;
                    $topCategories[$categoryName]['item_count'] += $item->quantity_ordered;
                }
            }
        }

        $topCategories = collect($topCategories)
            ->sortByDesc('total_amount')
            ->take(10)
            ->values()
            ->toArray();

        // Calculate cost savings
        $costSavings = self::calculateCostSavings($orders);

        // Calculate quality metrics
        $qualityMetrics = self::calculateQualityMetrics($orders);

        return self::updateOrCreate(
            [
                'analytics_date' => $date->toDateString(),
                'period_type' => $periodType
            ],
            [
                'total_purchase_amount' => $totalPurchaseAmount,
                'total_paid_amount' => $totalPaidAmount,
                'outstanding_amount' => $outstandingAmount,
                'orders_placed' => $ordersPlaced,
                'orders_completed' => $ordersCompleted,
                'orders_cancelled' => $ordersCancelled,
                'average_order_value' => $averageOrderValue,
                'average_delivery_time' => $averageDeliveryTime,
                'on_time_delivery_rate' => $onTimeDeliveryRate,
                'unique_suppliers' => $uniqueSuppliers,
                'top_suppliers' => $topSuppliers,
                'top_categories' => $topCategories,
                'cost_savings' => $costSavings,
                'quality_metrics' => $qualityMetrics,
            ]
        );
    }

    /**
     * Calculate cost savings.
     */
    private static function calculateCostSavings($orders): array
    {
        $totalEstimated = $orders->sum('estimated_cost');
        $totalActual = $orders->sum('actual_cost');
        
        if ($totalEstimated <= 0) {
            return [
                'estimated_total' => 0,
                'actual_total' => $totalActual,
                'savings_amount' => 0,
                'savings_percentage' => 0,
            ];
        }

        $savingsAmount = $totalEstimated - $totalActual;
        $savingsPercentage = ($savingsAmount / $totalEstimated) * 100;

        return [
            'estimated_total' => $totalEstimated,
            'actual_total' => $totalActual,
            'savings_amount' => $savingsAmount,
            'savings_percentage' => $savingsPercentage,
        ];
    }

    /**
     * Calculate quality metrics.
     */
    private static function calculateQualityMetrics($orders): array
    {
        $completedOrders = $orders->where('status', 'completed');
        $ordersWithQualityScore = $completedOrders->whereNotNull('quality_score');
        
        $averageQualityScore = $ordersWithQualityScore->avg('quality_score') ?? 0;
        $qualityIssuesCount = $completedOrders->whereNotNull('quality_issues')->count();
        $qualityIssuesRate = $completedOrders->count() > 0 ? ($qualityIssuesCount / $completedOrders->count()) * 100 : 0;

        // Count rejected items
        $totalItemsReceived = 0;
        $totalItemsRejected = 0;
        
        foreach ($completedOrders as $order) {
            foreach ($order->items as $item) {
                $totalItemsReceived += $item->quantity_received;
                $totalItemsRejected += $item->quantity_rejected ?? 0;
            }
        }

        $rejectionRate = $totalItemsReceived > 0 ? ($totalItemsRejected / $totalItemsReceived) * 100 : 0;

        return [
            'average_quality_score' => $averageQualityScore,
            'quality_issues_count' => $qualityIssuesCount,
            'quality_issues_rate' => $qualityIssuesRate,
            'total_items_received' => $totalItemsReceived,
            'total_items_rejected' => $totalItemsRejected,
            'rejection_rate' => $rejectionRate,
        ];
    }

    /**
     * Get start date for period type.
     */
    private static function getStartDateForPeriod(Carbon $date, string $periodType): Carbon
    {
        return match ($periodType) {
            'daily' => $date->copy()->startOfDay(),
            'weekly' => $date->copy()->startOfWeek(),
            'monthly' => $date->copy()->startOfMonth(),
            'yearly' => $date->copy()->startOfYear(),
            default => $date->copy()->startOfDay(),
        };
    }

    /**
     * Get end date for period type.
     */
    private static function getEndDateForPeriod(Carbon $date, string $periodType): Carbon
    {
        return match ($periodType) {
            'daily' => $date->copy()->endOfDay(),
            'weekly' => $date->copy()->endOfWeek(),
            'monthly' => $date->copy()->endOfMonth(),
            'yearly' => $date->copy()->endOfYear(),
            default => $date->copy()->endOfDay(),
        };
    }

    /**
     * Get analytics for date range.
     */
    public static function getAnalyticsForRange(Carbon $startDate, Carbon $endDate, string $periodType)
    {
        return self::where('period_type', $periodType)
            ->whereBetween('analytics_date', [$startDate, $endDate])
            ->orderBy('analytics_date')
            ->get();
    }

    /**
     * Get comparison with previous period.
     */
    public function getComparisonWithPreviousPeriod(): array
    {
        $previousDate = match ($this->period_type) {
            'daily' => $this->analytics_date->copy()->subDay(),
            'weekly' => $this->analytics_date->copy()->subWeek(),
            'monthly' => $this->analytics_date->copy()->subMonth(),
            'yearly' => $this->analytics_date->copy()->subYear(),
            default => $this->analytics_date->copy()->subDay(),
        };

        $previous = self::where('analytics_date', $previousDate)
            ->where('period_type', $this->period_type)
            ->first();

        if (!$previous) {
            return [
                'purchase_amount_change' => 0,
                'orders_change' => 0,
                'delivery_time_change' => 0,
                'on_time_delivery_change' => 0,
            ];
        }

        return [
            'purchase_amount_change' => $this->calculatePercentageChange($previous->total_purchase_amount, $this->total_purchase_amount),
            'orders_change' => $this->calculatePercentageChange($previous->orders_placed, $this->orders_placed),
            'delivery_time_change' => $this->calculatePercentageChange($previous->average_delivery_time, $this->average_delivery_time),
            'on_time_delivery_change' => $this->calculatePercentageChange($previous->on_time_delivery_rate, $this->on_time_delivery_rate),
        ];
    }

    /**
     * Calculate percentage change between two values.
     */
    private function calculatePercentageChange($oldValue, $newValue): float
    {
        if ($oldValue == 0) {
            return $newValue > 0 ? 100 : 0;
        }

        return (($newValue - $oldValue) / $oldValue) * 100;
    }

    /**
     * Get purchase trends for chart.
     */
    public static function getPurchaseTrends(string $periodType, Carbon $startDate, Carbon $endDate): array
    {
        $analytics = self::where('period_type', $periodType)
            ->whereBetween('analytics_date', [$startDate, $endDate])
            ->orderBy('analytics_date')
            ->get();

        return [
            'labels' => $analytics->pluck('analytics_date')->map(function ($date) use ($periodType) {
                return match ($periodType) {
                    'daily' => $date->format('M d'),
                    'weekly' => 'Week ' . $date->weekOfYear,
                    'monthly' => $date->format('M Y'),
                    'yearly' => $date->format('Y'),
                    default => $date->format('M d'),
                };
            })->toArray(),
            'purchase_amounts' => $analytics->pluck('total_purchase_amount')->toArray(),
            'orders_placed' => $analytics->pluck('orders_placed')->toArray(),
            'delivery_times' => $analytics->pluck('average_delivery_time')->toArray(),
            'on_time_rates' => $analytics->pluck('on_time_delivery_rate')->toArray(),
        ];
    }

    /**
     * Get supplier performance comparison.
     */
    public function getSupplierPerformanceComparison(): array
    {
        if (empty($this->top_suppliers)) {
            return [];
        }

        return collect($this->top_suppliers)->map(function ($supplier) {
            return [
                'name' => $supplier['supplier_name'],
                'total_amount' => $supplier['total_amount'],
                'order_count' => $supplier['order_count'],
                'average_order_value' => $supplier['average_order_value'],
                'percentage_of_total' => $this->total_purchase_amount > 0 
                    ? ($supplier['total_amount'] / $this->total_purchase_amount) * 100 
                    : 0,
            ];
        })->toArray();
    }

    /**
     * Get category spending analysis.
     */
    public function getCategorySpendingAnalysis(): array
    {
        if (empty($this->top_categories)) {
            return [];
        }

        $totalCategorySpending = collect($this->top_categories)->sum('total_amount');

        return collect($this->top_categories)->map(function ($category) use ($totalCategorySpending) {
            return [
                'name' => $category['category_name'],
                'total_amount' => $category['total_amount'],
                'item_count' => $category['item_count'],
                'percentage_of_total' => $totalCategorySpending > 0 
                    ? ($category['total_amount'] / $totalCategorySpending) * 100 
                    : 0,
            ];
        })->toArray();
    }

    /**
     * Get key performance indicators.
     */
    public function getKPIs(): array
    {
        return [
            'financial' => [
                'total_spend' => $this->total_purchase_amount,
                'outstanding_amount' => $this->outstanding_amount,
                'payment_rate' => $this->total_purchase_amount > 0 
                    ? ($this->total_paid_amount / $this->total_purchase_amount) * 100 
                    : 0,
                'average_order_value' => $this->average_order_value,
            ],
            'operational' => [
                'orders_placed' => $this->orders_placed,
                'completion_rate' => $this->orders_placed > 0 
                    ? ($this->orders_completed / $this->orders_placed) * 100 
                    : 0,
                'cancellation_rate' => $this->orders_placed > 0 
                    ? ($this->orders_cancelled / $this->orders_placed) * 100 
                    : 0,
                'unique_suppliers' => $this->unique_suppliers,
            ],
            'delivery' => [
                'average_delivery_time' => $this->average_delivery_time,
                'on_time_delivery_rate' => $this->on_time_delivery_rate,
            ],
            'quality' => $this->quality_metrics,
            'savings' => $this->cost_savings,
        ];
    }
}
