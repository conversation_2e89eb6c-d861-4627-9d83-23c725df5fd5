<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notification_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Template name for identification
            $table->string('slug')->unique(); // URL-friendly identifier
            $table->enum('type', ['sms', 'whatsapp', 'email'])->default('sms');
            $table->enum('category', [
                'status_update',
                'appointment_reminder', 
                'pickup_ready',
                'payment_reminder',
                'satisfaction_survey',
                'general'
            ]);
            $table->string('subject')->nullable(); // For email templates
            $table->text('message_ar'); // Arabic message template
            $table->text('message_en')->nullable(); // English message template (optional)
            $table->json('variables')->nullable(); // Available template variables
            $table->text('description')->nullable(); // Template description
            $table->boolean('is_active')->default(true);
            $table->boolean('is_system')->default(false); // System templates cannot be deleted
            $table->timestamps();

            // Indexes
            $table->index(['type', 'category']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_templates');
    }
};
