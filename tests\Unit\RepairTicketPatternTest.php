<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\RepairTicket;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

class RepairTicketPatternTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test hasSecurityPattern method
     */
    public function test_has_security_pattern_method()
    {
        // Ticket with text pattern
        $ticketWithText = RepairTicket::factory()->create([
            'security_pattern' => 'password123',
            'pattern_type' => 'text'
        ]);
        $this->assertTrue($ticketWithText->hasSecurityPattern());

        // Ticket without text pattern
        $ticketWithoutText = RepairTicket::factory()->create([
            'security_pattern' => null,
            'pattern_type' => 'visual'
        ]);
        $this->assertFalse($ticketWithoutText->hasSecurityPattern());

        // Ticket with empty text pattern
        $ticketWithEmptyText = RepairTicket::factory()->create([
            'security_pattern' => '',
            'pattern_type' => 'none'
        ]);
        $this->assertFalse($ticketWithEmptyText->hasSecurityPattern());
    }

    /**
     * Test hasVisualPattern method
     */
    public function test_has_visual_pattern_method()
    {
        // Ticket with visual pattern
        $ticketWithVisual = RepairTicket::factory()->create([
            'visual_pattern' => '1-2-3-6-9',
            'pattern_type' => 'visual'
        ]);
        $this->assertTrue($ticketWithVisual->hasVisualPattern());

        // Ticket without visual pattern
        $ticketWithoutVisual = RepairTicket::factory()->create([
            'visual_pattern' => null,
            'pattern_type' => 'text'
        ]);
        $this->assertFalse($ticketWithoutVisual->hasVisualPattern());

        // Ticket with empty visual pattern
        $ticketWithEmptyVisual = RepairTicket::factory()->create([
            'visual_pattern' => '',
            'pattern_type' => 'none'
        ]);
        $this->assertFalse($ticketWithEmptyVisual->hasVisualPattern());
    }

    /**
     * Test hasAnySecurityPattern method
     */
    public function test_has_any_security_pattern_method()
    {
        // Ticket with text pattern only
        $ticketWithText = RepairTicket::factory()->create([
            'security_pattern' => 'password123',
            'visual_pattern' => null,
            'pattern_type' => 'text'
        ]);
        $this->assertTrue($ticketWithText->hasAnySecurityPattern());

        // Ticket with visual pattern only
        $ticketWithVisual = RepairTicket::factory()->create([
            'security_pattern' => null,
            'visual_pattern' => '1-2-3-6-9',
            'pattern_type' => 'visual'
        ]);
        $this->assertTrue($ticketWithVisual->hasAnySecurityPattern());

        // Ticket with both patterns
        $ticketWithBoth = RepairTicket::factory()->create([
            'security_pattern' => 'password123',
            'visual_pattern' => '1-2-3-6-9',
            'pattern_type' => 'both'
        ]);
        $this->assertTrue($ticketWithBoth->hasAnySecurityPattern());

        // Ticket with no patterns
        $ticketWithNone = RepairTicket::factory()->create([
            'security_pattern' => null,
            'visual_pattern' => null,
            'pattern_type' => 'none'
        ]);
        $this->assertFalse($ticketWithNone->hasAnySecurityPattern());
    }

    /**
     * Test getVisualPatternArray method
     */
    public function test_get_visual_pattern_array_method()
    {
        // Ticket with visual pattern
        $ticket = RepairTicket::factory()->create([
            'visual_pattern' => '1-2-3-6-9',
            'pattern_type' => 'visual'
        ]);

        $patternArray = $ticket->getVisualPatternArray();
        $this->assertEquals([1, 2, 3, 6, 9], $patternArray);

        // Ticket without visual pattern
        $ticketWithoutPattern = RepairTicket::factory()->create([
            'visual_pattern' => null,
            'pattern_type' => 'none'
        ]);

        $emptyArray = $ticketWithoutPattern->getVisualPatternArray();
        $this->assertEquals([], $emptyArray);
    }

    /**
     * Test getMaskedSecurityPattern method
     */
    public function test_get_masked_security_pattern_method()
    {
        // Short password
        $ticketShort = RepairTicket::factory()->create([
            'security_pattern' => '1234',
            'pattern_type' => 'text'
        ]);
        $maskedShort = $ticketShort->getMaskedSecurityPattern();
        $this->assertEquals('1**4', $maskedShort);

        // Medium password
        $ticketMedium = RepairTicket::factory()->create([
            'security_pattern' => 'password',
            'pattern_type' => 'text'
        ]);
        $maskedMedium = $ticketMedium->getMaskedSecurityPattern();
        $this->assertEquals('p******d', $maskedMedium);

        // Long password
        $ticketLong = RepairTicket::factory()->create([
            'security_pattern' => 'verylongpassword123',
            'pattern_type' => 'text'
        ]);
        $maskedLong = $ticketLong->getMaskedSecurityPattern();
        $this->assertEquals('v*****************3', $maskedLong); // First and last char with stars in between

        // No password
        $ticketNone = RepairTicket::factory()->create([
            'security_pattern' => null,
            'pattern_type' => 'none'
        ]);
        $maskedNone = $ticketNone->getMaskedSecurityPattern();
        $this->assertEquals('لا يوجد', $maskedNone); // Arabic for "Not found"
    }

    /**
     * Test getMaskedVisualPattern method
     */
    public function test_get_masked_visual_pattern_method()
    {
        // Short pattern
        $ticketShort = RepairTicket::factory()->create([
            'visual_pattern' => '1-2-3-4',
            'pattern_type' => 'visual'
        ]);
        $maskedShort = $ticketShort->getMaskedVisualPattern();
        $this->assertEquals('1-**-4', $maskedShort);

        // Medium pattern
        $ticketMedium = RepairTicket::factory()->create([
            'visual_pattern' => '1-2-3-6-9',
            'pattern_type' => 'visual'
        ]);
        $maskedMedium = $ticketMedium->getMaskedVisualPattern();
        $this->assertEquals('1-***-9', $maskedMedium);

        // Long pattern
        $ticketLong = RepairTicket::factory()->create([
            'visual_pattern' => '1-2-3-6-9-8-7-4',
            'pattern_type' => 'visual'
        ]);
        $maskedLong = $ticketLong->getMaskedVisualPattern();
        $this->assertStringStartsWith('1-', $maskedLong);
        $this->assertStringEndsWith('-4', $maskedLong);
        $this->assertStringContainsString('*', $maskedLong);

        // No pattern
        $ticketNone = RepairTicket::factory()->create([
            'visual_pattern' => null,
            'pattern_type' => 'none'
        ]);
        $maskedNone = $ticketNone->getMaskedVisualPattern();
        $this->assertEquals('لا يوجد', $maskedNone); // Arabic for "Not found"
    }

    /**
     * Test getSecurityPatternDisplay method
     */
    public function test_get_security_pattern_display_method()
    {
        // Text pattern only
        $ticketText = RepairTicket::factory()->create([
            'security_pattern' => 'password123',
            'visual_pattern' => null,
            'pattern_type' => 'text'
        ]);
        $displayText = $ticketText->getSecurityPatternDisplay();
        $this->assertEquals('p*********3', $displayText); // Just the masked password

        // Visual pattern only
        $ticketVisual = RepairTicket::factory()->create([
            'security_pattern' => null,
            'visual_pattern' => '1-2-3-6-9',
            'pattern_type' => 'visual'
        ]);
        $displayVisual = $ticketVisual->getSecurityPatternDisplay();
        $this->assertEquals('نمط بصري: 1-***-9', $displayVisual); // Arabic for "Visual Pattern: 1-***-9"

        // Both patterns
        $ticketBoth = RepairTicket::factory()->create([
            'security_pattern' => 'mypass',
            'visual_pattern' => '1-4-7-8-9',
            'pattern_type' => 'both'
        ]);
        $displayBoth = $ticketBoth->getSecurityPatternDisplay();
        $this->assertEquals('m****s + نمط بصري', $displayBoth); // Masked password + Arabic for "Visual Pattern"

        // No patterns
        $ticketNone = RepairTicket::factory()->create([
            'security_pattern' => null,
            'visual_pattern' => null,
            'pattern_type' => 'none'
        ]);
        $displayNone = $ticketNone->getSecurityPatternDisplay();
        $this->assertEquals('لا يوجد', $displayNone); // Arabic for "Not found"
    }

    /**
     * Test pattern encryption and decryption
     */
    public function test_pattern_encryption_and_decryption()
    {
        $originalTextPattern = 'mysecretpassword';
        $originalVisualPattern = '1-2-3-6-9-8-7';

        $ticket = RepairTicket::factory()->create([
            'security_pattern' => $originalTextPattern,
            'visual_pattern' => $originalVisualPattern,
            'pattern_type' => 'both'
        ]);

        // Test that patterns are decrypted correctly when accessed
        $this->assertEquals($originalTextPattern, $ticket->security_pattern);
        $this->assertEquals($originalVisualPattern, $ticket->visual_pattern);

        // Test that raw database values are encrypted
        $rawData = DB::table('repair_tickets')->where('id', $ticket->id)->first();
        $this->assertNotEquals($originalTextPattern, $rawData->security_pattern);
        $this->assertNotEquals($originalVisualPattern, $rawData->visual_pattern);

        // Test that encrypted values can be decrypted
        $decryptedText = decrypt($rawData->security_pattern);
        $decryptedVisual = decrypt($rawData->visual_pattern);
        $this->assertEquals($originalTextPattern, $decryptedText);
        $this->assertEquals($originalVisualPattern, $decryptedVisual);
    }

    /**
     * Test pattern mutators handle null values
     */
    public function test_pattern_mutators_handle_null_values()
    {
        $ticket = RepairTicket::factory()->create([
            'security_pattern' => null,
            'visual_pattern' => null,
            'pattern_type' => 'none'
        ]);

        $this->assertNull($ticket->security_pattern);
        $this->assertNull($ticket->visual_pattern);

        // Check database values are also null
        $rawData = DB::table('repair_tickets')->where('id', $ticket->id)->first();
        $this->assertNull($rawData->security_pattern);
        $this->assertNull($rawData->visual_pattern);
    }

    /**
     * Test pattern mutators handle empty strings
     */
    public function test_pattern_mutators_handle_empty_strings()
    {
        $ticket = RepairTicket::factory()->create([
            'security_pattern' => '',
            'visual_pattern' => '',
            'pattern_type' => 'none'
        ]);

        $this->assertNull($ticket->security_pattern);
        $this->assertNull($ticket->visual_pattern);

        // Check database values are null for empty strings
        $rawData = DB::table('repair_tickets')->where('id', $ticket->id)->first();
        $this->assertNull($rawData->security_pattern);
        $this->assertNull($rawData->visual_pattern);
    }

    /**
     * Test fillable attributes include pattern fields
     */
    public function test_fillable_attributes_include_pattern_fields()
    {
        $ticket = new RepairTicket();
        $fillable = $ticket->getFillable();

        $this->assertContains('security_pattern', $fillable);
        $this->assertContains('visual_pattern', $fillable);
        $this->assertContains('pattern_type', $fillable);
    }
}
