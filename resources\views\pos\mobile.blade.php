@extends('layouts.mobile')

@section('title', __('app.pos.mobile_pos'))

@push('styles')
<style>
    .mobile-pos {
        height: 100vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }
    
    .mobile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px;
        position: sticky;
        top: 0;
        z-index: 1000;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .mobile-header h1 {
        font-size: 1.2rem;
        margin: 0;
        font-weight: 600;
    }
    
    .mobile-search {
        background: white;
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
        position: sticky;
        top: 70px;
        z-index: 999;
    }
    
    .mobile-search input {
        border-radius: 25px;
        padding: 12px 20px;
        font-size: 1rem;
        border: 2px solid #e9ecef;
        width: 100%;
    }
    
    .mobile-search input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .mobile-categories {
        background: #f8f9fa;
        padding: 10px 15px;
        border-bottom: 1px solid #e9ecef;
        overflow-x: auto;
        white-space: nowrap;
        position: sticky;
        top: 140px;
        z-index: 998;
    }
    
    .mobile-category {
        display: inline-block;
        padding: 8px 16px;
        margin-right: 8px;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 20px;
        font-size: 0.9rem;
        color: #495057;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .mobile-category.active,
    .mobile-category:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
        text-decoration: none;
    }
    
    .mobile-products {
        flex: 1;
        overflow-y: auto;
        padding: 15px;
        background: #f8f9fa;
    }
    
    .mobile-product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 12px;
    }
    
    .mobile-product-card {
        background: white;
        border-radius: 12px;
        padding: 12px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    
    .mobile-product-card:active {
        transform: scale(0.98);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    }
    
    .mobile-product-card.out-of-stock {
        opacity: 0.5;
        cursor: not-allowed;
    }
    
    .mobile-product-card.out-of-stock::after {
        content: '{{ __("app.pos.out_of_stock") }}';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(220, 53, 69, 0.9);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.7rem;
        font-weight: 600;
    }
    
    .mobile-product-name {
        font-size: 0.9rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
        line-height: 1.2;
    }
    
    .mobile-product-price {
        font-size: 1.1rem;
        font-weight: 700;
        color: #28a745;
        margin-bottom: 4px;
    }
    
    .mobile-product-stock {
        font-size: 0.75rem;
        color: #6c757d;
    }
    
    .mobile-cart-fab {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 50%;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 4px 20px rgba(40, 167, 69, 0.4);
        z-index: 1000;
        transition: all 0.3s ease;
    }
    
    .mobile-cart-fab:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(40, 167, 69, 0.6);
    }
    
    .mobile-cart-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
    }
    
    .mobile-cart-drawer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-radius: 20px 20px 0 0;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
        transform: translateY(100%);
        transition: transform 0.3s ease;
        z-index: 1001;
        max-height: 80vh;
        display: flex;
        flex-direction: column;
    }
    
    .mobile-cart-drawer.open {
        transform: translateY(0);
    }
    
    .mobile-cart-header {
        padding: 20px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: between;
        align-items: center;
    }
    
    .mobile-cart-handle {
        width: 40px;
        height: 4px;
        background: #dee2e6;
        border-radius: 2px;
        margin: 0 auto 15px;
    }
    
    .mobile-cart-items {
        flex: 1;
        overflow-y: auto;
        padding: 0 20px;
    }
    
    .mobile-cart-item {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .mobile-cart-item:last-child {
        border-bottom: none;
    }
    
    .mobile-cart-item-info {
        flex: 1;
        margin-right: 15px;
    }
    
    .mobile-cart-item-name {
        font-weight: 600;
        color: #495057;
        margin-bottom: 4px;
    }
    
    .mobile-cart-item-price {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .mobile-quantity-controls {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .mobile-quantity-btn {
        width: 32px;
        height: 32px;
        border: 1px solid #dee2e6;
        background: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        color: #495057;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .mobile-quantity-btn:active {
        background: #f8f9fa;
        transform: scale(0.95);
    }
    
    .mobile-quantity-display {
        font-weight: 600;
        min-width: 30px;
        text-align: center;
    }
    
    .mobile-cart-total {
        padding: 20px;
        border-top: 2px solid #e9ecef;
        background: #f8f9fa;
    }
    
    .mobile-total-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 0.9rem;
    }
    
    .mobile-total-row.final {
        font-size: 1.2rem;
        font-weight: 700;
        color: #495057;
        border-top: 1px solid #dee2e6;
        padding-top: 8px;
        margin-top: 8px;
    }
    
    .mobile-checkout-btn {
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        color: white;
        font-size: 1.1rem;
        font-weight: 600;
        margin-top: 15px;
        transition: all 0.3s ease;
    }
    
    .mobile-checkout-btn:active {
        transform: scale(0.98);
    }
    
    .mobile-checkout-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    
    .mobile-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .mobile-overlay.show {
        opacity: 1;
        visibility: visible;
    }
    
    .empty-cart {
        text-align: center;
        padding: 40px 20px;
        color: #6c757d;
    }
    
    .empty-cart i {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.5;
    }
    
    /* Loading states */
    .loading-products {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
    }
    
    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* Success animation */
    .add-to-cart-success {
        animation: addSuccess 0.6s ease-out;
    }
    
    @keyframes addSuccess {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); background-color: #28a745; }
        100% { transform: scale(1); }
    }
    
    /* RTL Support */
    body[dir="rtl"] .mobile-category {
        margin-right: 0;
        margin-left: 8px;
    }
    
    body[dir="rtl"] .mobile-cart-fab {
        right: auto;
        left: 20px;
    }
    
    body[dir="rtl"] .mobile-cart-item-info {
        margin-right: 0;
        margin-left: 15px;
    }
    
    body[dir="rtl"] .mobile-quantity-controls {
        flex-direction: row-reverse;
    }
</style>
@endpush

@section('content')
<div class="mobile-pos">
    <!-- Header -->
    <div class="mobile-header">
        <div class="d-flex justify-content-between align-items-center">
            <h1>
                <i class="bi bi-shop"></i> {{ __('app.pos.mobile_pos') }}
            </h1>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-light" id="mobileSettingsBtn">
                    <i class="bi bi-gear"></i>
                </button>
                <button class="btn btn-sm btn-outline-light" id="mobileSyncBtn">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Search -->
    <div class="mobile-search">
        <input type="text" id="mobileSearchInput" placeholder="{{ __('app.pos.search_or_scan') }}" autocomplete="off">
    </div>

    <!-- Categories -->
    <div class="mobile-categories">
        <a href="#" class="mobile-category active" data-category="">{{ __('app.pos.all') }}</a>
        @foreach($categories as $category)
            <a href="#" class="mobile-category" data-category="{{ $category->id }}">{{ $category->name }}</a>
        @endforeach
    </div>

    <!-- Products -->
    <div class="mobile-products">
        <div class="mobile-product-grid" id="mobileProductGrid">
            @foreach($products as $product)
                <div class="mobile-product-card {{ $product->current_stock <= 0 ? 'out-of-stock' : '' }}" 
                     data-product-id="{{ $product->id }}"
                     data-category="{{ $product->category_id }}"
                     data-name="{{ strtolower($product->name) }}"
                     data-barcode="{{ $product->barcode }}">
                    <div>
                        <div class="mobile-product-name">{{ $product->name }}</div>
                        <div class="mobile-product-price">{{ number_format($product->selling_price, 2) }} {{ __('app.currency') }}</div>
                    </div>
                    <div class="mobile-product-stock">
                        @if($product->current_stock > 0)
                            <i class="bi bi-check-circle text-success"></i> {{ $product->current_stock }}
                        @else
                            <i class="bi bi-x-circle text-danger"></i> 0
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
        
        <div class="loading-products" id="loadingProducts" style="display: none;">
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- Cart FAB -->
    <button class="mobile-cart-fab" id="mobileCartFab">
        <i class="bi bi-cart"></i>
        <span class="mobile-cart-badge" id="mobileCartBadge">0</span>
    </button>

    <!-- Cart Drawer -->
    <div class="mobile-cart-drawer" id="mobileCartDrawer">
        <div class="mobile-cart-header">
            <div class="mobile-cart-handle"></div>
            <div class="d-flex justify-content-between align-items-center w-100">
                <h5 class="mb-0">{{ __('app.pos.cart') }}</h5>
                <button class="btn btn-sm btn-outline-danger" id="mobileClearCartBtn">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>

        <div class="mobile-cart-items" id="mobileCartItems">
            <div class="empty-cart">
                <i class="bi bi-cart"></i>
                <p>{{ __('app.pos.empty_cart') }}</p>
            </div>
        </div>

        <div class="mobile-cart-total">
            <div class="mobile-total-row">
                <span>{{ __('app.pos.subtotal') }}:</span>
                <span id="mobileSubtotal">0.00 {{ __('app.currency') }}</span>
            </div>
            <div class="mobile-total-row">
                <span>{{ __('app.pos.tax') }} (15%):</span>
                <span id="mobileTax">0.00 {{ __('app.currency') }}</span>
            </div>
            <div class="mobile-total-row final">
                <span>{{ __('app.pos.total') }}:</span>
                <span id="mobileTotal">0.00 {{ __('app.currency') }}</span>
            </div>
            <button class="mobile-checkout-btn" id="mobileCheckoutBtn" disabled>
                <i class="bi bi-credit-card"></i> {{ __('app.pos.checkout') }}
            </button>
        </div>
    </div>

    <!-- Overlay -->
    <div class="mobile-overlay" id="mobileOverlay"></div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/mobile-pos.js') }}"></script>
@endpush
