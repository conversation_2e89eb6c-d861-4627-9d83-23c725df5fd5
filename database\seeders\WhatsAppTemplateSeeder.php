<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\WhatsAppTemplateCategory;
use App\Models\WhatsAppMessageTemplate;

class WhatsAppTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create categories first
        WhatsAppTemplateCategory::createDefaults();

        // Get categories
        $statusCategory = WhatsAppTemplateCategory::where('slug', 'status-updates')->first();
        $appointmentCategory = WhatsAppTemplateCategory::where('slug', 'appointments')->first();
        $paymentCategory = WhatsAppTemplateCategory::where('slug', 'payment-reminders')->first();
        $businessCategory = WhatsAppTemplateCategory::where('slug', 'business-info')->first();
        $marketingCategory = WhatsAppTemplateCategory::where('slug', 'marketing')->first();

        // Status Update Templates
        $this->createTemplate([
            'name' => 'status_update_ar',
            'whatsapp_name' => 'nj_status_update_ar',
            'display_name' => 'Status Update',
            'display_name_ar' => 'تحديث الحالة',
            'description' => 'Repair status update notification',
            'description_ar' => 'إشعار تحديث حالة الإصلاح',
            'category_id' => $statusCategory->id,
            'language' => 'ar',
            'header_text' => '{{status_icon}} تحديث حالة جهازك',
            'body_text' => 'عزيزي {{customer_name}}،

تم تحديث حالة جهازك في ورشة إصلاح NJ

🎫 رقم التذكرة: {{ticket_number}}
📱 الجهاز: {{device_info}}
📊 الحالة الجديدة: {{status}}

{{additional_info}}

شكراً لثقتك بنا!',
            'footer_text' => '{{business_name}} | {{business_phone}}',
            'variables' => [
                'customer_name' => 'اسم العميل',
                'ticket_number' => 'رقم التذكرة',
                'device_info' => 'معلومات الجهاز',
                'status' => 'الحالة الجديدة',
                'status_icon' => 'أيقونة الحالة',
                'additional_info' => 'معلومات إضافية',
                'business_name' => 'اسم الأعمال',
                'business_phone' => 'هاتف الأعمال'
            ],
            'sample_values' => [
                'customer_name' => 'أحمد محمد',
                'ticket_number' => 'NJ-2024-001',
                'device_info' => 'iPhone 14 Pro',
                'status' => 'قيد الإصلاح',
                'status_icon' => '🔧',
                'additional_info' => 'جاري العمل على إصلاح الشاشة',
                'business_name' => 'ورشة إصلاح NJ',
                'business_phone' => '+966501234567'
            ],
            'buttons' => [
                ['type' => 'QUICK_REPLY', 'text' => 'تفاصيل أكثر'],
                ['type' => 'QUICK_REPLY', 'text' => 'تواصل معنا']
            ]
        ]);

        // Pickup Ready Template
        $this->createTemplate([
            'name' => 'pickup_ready_ar',
            'whatsapp_name' => 'nj_pickup_ready_ar',
            'display_name' => 'Device Ready for Pickup',
            'display_name_ar' => 'الجهاز جاهز للاستلام',
            'description' => 'Device ready for pickup notification',
            'description_ar' => 'إشعار جاهزية الجهاز للاستلام',
            'category_id' => $statusCategory->id,
            'language' => 'ar',
            'header_text' => '🎉 جهازك جاهز للاستلام!',
            'body_text' => 'عزيزي {{customer_name}}،

يسعدنا إبلاغك بأن جهازك قد تم إصلاحه بنجاح وهو جاهز للاستلام

🎫 رقم التذكرة: {{ticket_number}}
📱 الجهاز: {{device_info}}
✅ الحالة: مكتمل - جاهز للاستلام

📍 العنوان: {{business_address}}
🕐 ساعات العمل: {{business_hours}}

يرجى إحضار رقم التذكرة عند الاستلام',
            'footer_text' => 'شكراً لثقتك بنا | {{business_phone}}',
            'variables' => [
                'customer_name' => 'اسم العميل',
                'ticket_number' => 'رقم التذكرة',
                'device_info' => 'معلومات الجهاز',
                'business_address' => 'عنوان الأعمال',
                'business_hours' => 'ساعات العمل',
                'business_phone' => 'هاتف الأعمال'
            ],
            'sample_values' => [
                'customer_name' => 'فاطمة أحمد',
                'ticket_number' => 'NJ-2024-002',
                'device_info' => 'Samsung Galaxy S23',
                'business_address' => 'شارع الملك فهد، الرياض',
                'business_hours' => 'السبت-الخميس: 9ص-10م',
                'business_phone' => '+966501234567'
            ],
            'buttons' => [
                ['type' => 'QUICK_REPLY', 'text' => 'الاتجاهات'],
                ['type' => 'QUICK_REPLY', 'text' => 'تأكيد الاستلام']
            ]
        ]);

        // Appointment Confirmation Template
        $this->createTemplate([
            'name' => 'appointment_confirmation_ar',
            'whatsapp_name' => 'nj_appointment_ar',
            'display_name' => 'Appointment Confirmation',
            'display_name_ar' => 'تأكيد الموعد',
            'description' => 'Appointment confirmation message',
            'description_ar' => 'رسالة تأكيد الموعد',
            'category_id' => $appointmentCategory->id,
            'language' => 'ar',
            'header_text' => '📅 تأكيد موعدك',
            'body_text' => 'عزيزي {{customer_name}}،

تم تأكيد موعدك في ورشة إصلاح NJ

📅 التاريخ: {{appointment_date}}
🕐 الوقت: {{appointment_time}}
🔧 نوع الخدمة: {{service_type}}

📍 العنوان: {{business_address}}

يرجى الحضور في الموعد المحدد. في حالة عدم التمكن من الحضور، يرجى إبلاغنا مسبقاً',
            'footer_text' => 'نتطلع لخدمتك | {{business_phone}}',
            'variables' => [
                'customer_name' => 'اسم العميل',
                'appointment_date' => 'تاريخ الموعد',
                'appointment_time' => 'وقت الموعد',
                'service_type' => 'نوع الخدمة',
                'business_address' => 'عنوان الأعمال',
                'business_phone' => 'هاتف الأعمال'
            ],
            'sample_values' => [
                'customer_name' => 'محمد علي',
                'appointment_date' => '2024-08-01',
                'appointment_time' => '14:00',
                'service_type' => 'إصلاح شاشة هاتف',
                'business_address' => 'شارع الملك فهد، الرياض',
                'business_phone' => '+966501234567'
            ],
            'buttons' => [
                ['type' => 'QUICK_REPLY', 'text' => 'تأكيد الحضور'],
                ['type' => 'QUICK_REPLY', 'text' => 'إعادة جدولة']
            ]
        ]);

        // Payment Reminder Template
        $this->createTemplate([
            'name' => 'payment_reminder_ar',
            'whatsapp_name' => 'nj_payment_reminder_ar',
            'display_name' => 'Payment Reminder',
            'display_name_ar' => 'تذكير الدفع',
            'description' => 'Payment due reminder',
            'description_ar' => 'تذكير استحقاق الدفع',
            'category_id' => $paymentCategory->id,
            'language' => 'ar',
            'header_text' => '💳 تذكير بالدفع',
            'body_text' => 'عزيزي {{customer_name}}،

نذكرك بوجود مبلغ مستحق للدفع

🎫 رقم التذكرة: {{ticket_number}}
📱 الجهاز: {{device_info}}
💰 المبلغ المستحق: {{amount}}

يرجى تسديد المبلغ في أقرب وقت ممكن لتجنب أي تأخير في الخدمة',
            'footer_text' => 'شكراً لتفهمك | {{business_phone}}',
            'variables' => [
                'customer_name' => 'اسم العميل',
                'ticket_number' => 'رقم التذكرة',
                'device_info' => 'معلومات الجهاز',
                'amount' => 'المبلغ المستحق',
                'business_phone' => 'هاتف الأعمال'
            ],
            'sample_values' => [
                'customer_name' => 'سارة محمد',
                'ticket_number' => 'NJ-2024-003',
                'device_info' => 'iPad Air',
                'amount' => '450.00 ر.س',
                'business_phone' => '+966501234567'
            ],
            'buttons' => [
                ['type' => 'QUICK_REPLY', 'text' => 'تأكيد الدفع'],
                ['type' => 'QUICK_REPLY', 'text' => 'استفسار']
            ]
        ]);

        // Welcome Message Template
        $this->createTemplate([
            'name' => 'welcome_message_ar',
            'whatsapp_name' => 'nj_welcome_ar',
            'display_name' => 'Welcome Message',
            'display_name_ar' => 'رسالة ترحيب',
            'description' => 'Welcome message for new customers',
            'description_ar' => 'رسالة ترحيب للعملاء الجدد',
            'category_id' => $businessCategory->id,
            'language' => 'ar',
            'header_text' => '👋 أهلاً وسهلاً بك',
            'body_text' => 'مرحباً {{customer_name}}،

أهلاً وسهلاً بك في ورشة إصلاح NJ!

نحن متخصصون في إصلاح جميع أنواع الأجهزة الإلكترونية:
📱 الهواتف الذكية
💻 أجهزة الحاسوب
🎮 أجهزة الألعاب

🕐 ساعات العمل: {{business_hours}}
📍 الموقع: {{business_address}}

كيف يمكنني مساعدتك اليوم؟',
            'footer_text' => 'خدمة عملاء 24/7 | {{business_phone}}',
            'variables' => [
                'customer_name' => 'اسم العميل',
                'business_hours' => 'ساعات العمل',
                'business_address' => 'عنوان الأعمال',
                'business_phone' => 'هاتف الأعمال'
            ],
            'sample_values' => [
                'customer_name' => 'عميلنا الكريم',
                'business_hours' => 'السبت-الخميس: 9ص-10م',
                'business_address' => 'شارع الملك فهد، الرياض',
                'business_phone' => '+966501234567'
            ],
            'buttons' => [
                ['type' => 'QUICK_REPLY', 'text' => 'حالة الجهاز'],
                ['type' => 'QUICK_REPLY', 'text' => 'حجز موعد'],
                ['type' => 'QUICK_REPLY', 'text' => 'خدماتنا']
            ]
        ]);

        // Business Hours Template
        $this->createTemplate([
            'name' => 'business_hours_ar',
            'whatsapp_name' => 'nj_business_hours_ar',
            'display_name' => 'Business Hours',
            'display_name_ar' => 'ساعات العمل',
            'description' => 'Business hours and contact information',
            'description_ar' => 'ساعات العمل ومعلومات الاتصال',
            'category_id' => $businessCategory->id,
            'language' => 'ar',
            'header_text' => '🕐 ساعات العمل',
            'body_text' => 'ساعات عمل ورشة إصلاح NJ:

📅 السبت - الخميس: 9:00 ص - 10:00 م
📅 الجمعة: 2:00 م - 10:00 م

📍 العنوان: {{business_address}}
📞 الهاتف: {{business_phone}}
🌐 الموقع الإلكتروني: {{business_website}}

للطوارئ خارج ساعات العمل، يمكنك ترك رسالة وسنرد عليك في أقرب وقت',
            'footer_text' => 'نحن هنا لخدمتك دائماً',
            'variables' => [
                'business_address' => 'عنوان الأعمال',
                'business_phone' => 'هاتف الأعمال',
                'business_website' => 'موقع الأعمال'
            ],
            'sample_values' => [
                'business_address' => 'شارع الملك فهد، الرياض، المملكة العربية السعودية',
                'business_phone' => '+966501234567',
                'business_website' => 'https://njrepair.com'
            ]
        ]);

        // Marketing Offer Template
        $this->createTemplate([
            'name' => 'special_offer_ar',
            'whatsapp_name' => 'nj_special_offer_ar',
            'display_name' => 'Special Offer',
            'display_name_ar' => 'عرض خاص',
            'description' => 'Special promotional offer',
            'description_ar' => 'عرض ترويجي خاص',
            'category_id' => $marketingCategory->id,
            'language' => 'ar',
            'header_text' => '🎉 عرض خاص لك!',
            'body_text' => 'عزيزي {{customer_name}}،

لدينا عرض خاص لعملائنا الكرام!

{{offer_details}}

💰 خصم: {{discount_amount}}
⏰ العرض ساري حتى: {{expiry_date}}

لا تفوت هذه الفرصة الذهبية!',
            'footer_text' => 'ورشة إصلاح NJ | {{business_phone}}',
            'variables' => [
                'customer_name' => 'اسم العميل',
                'offer_details' => 'تفاصيل العرض',
                'discount_amount' => 'مبلغ الخصم',
                'expiry_date' => 'تاريخ انتهاء العرض',
                'business_phone' => 'هاتف الأعمال'
            ],
            'sample_values' => [
                'customer_name' => 'عميلنا الكريم',
                'offer_details' => 'خصم على جميع خدمات إصلاح الشاشات',
                'discount_amount' => '25%',
                'expiry_date' => '2024-08-31',
                'business_phone' => '+966501234567'
            ],
            'buttons' => [
                ['type' => 'QUICK_REPLY', 'text' => 'احجز الآن'],
                ['type' => 'QUICK_REPLY', 'text' => 'تفاصيل أكثر']
            ],
            'requires_opt_in' => true
        ]);

        echo "WhatsApp templates seeded successfully!\n";
    }

    /**
     * Create a template with default values
     */
    private function createTemplate(array $data): void
    {
        $defaults = [
            'status' => 'DRAFT',
            'is_active' => true,
            'includes_opt_out' => true,
            'opt_out_instructions' => 'للإلغاء، أرسل "إيقاف"',
        ];

        WhatsAppMessageTemplate::firstOrCreate(
            ['name' => $data['name']],
            array_merge($defaults, $data)
        );
    }
}
