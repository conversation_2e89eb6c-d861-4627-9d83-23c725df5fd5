@extends('layouts.app')

@section('title', 'تحليلات العملاء')

@push('styles')
<style>
.analytics-header {
    background: linear-gradient(45deg, #6f42c1, #e83e8c);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.date-filter-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.metric-card.metric-primary::before {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.metric-card.metric-success::before {
    background: linear-gradient(45deg, #28a745, #20c997);
}

.metric-card.metric-info::before {
    background: linear-gradient(45deg, #17a2b8, #138496);
}

.metric-card.metric-warning::before {
    background: linear-gradient(45deg, #ffc107, #e0a800);
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    color: #6c757d;
    font-size: 0.875rem;
    text-transform: uppercase;
    font-weight: 600;
}

.metric-icon {
    font-size: 3rem;
    opacity: 0.2;
    float: left;
    margin-top: -0.5rem;
}

.chart-card {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    margin-bottom: 1.5rem;
}

.chart-header {
    background: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
}

.chart-body {
    padding: 1.5rem;
}

.top-customers-table {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    overflow: hidden;
}

.table th {
    background: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.rank-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.customer-name {
    font-weight: 600;
    color: #2c3e50;
}

.phone-link {
    color: #495057;
    text-decoration: none;
    transition: color 0.2s ease;
}

.phone-link:hover {
    color: #007bff;
}

.amount-display {
    font-weight: 600;
    font-size: 1.1rem;
}

@media (max-width: 768px) {
    .analytics-header {
        padding: 1rem;
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .date-filter-card .d-flex {
        flex-direction: column;
        gap: 1rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="analytics-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">تحليلات العملاء</h1>
                <p class="mb-0 opacity-75">تحليل شامل لسلوك العملاء وأدائهم</p>
            </div>

            <!-- Date Range Filter -->
            <div class="date-filter-card">
                <div class="p-3">
                    <form method="GET" action="{{ route('reports.customer-analytics') }}" class="d-flex align-items-center gap-3">
                        <div>
                            <label class="form-label mb-1 small text-dark">من تاريخ</label>
                            <input type="date"
                                   name="start_date"
                                   value="{{ $dateRange['start_formatted'] }}"
                                   class="form-control form-control-sm">
                        </div>
                        <div>
                            <label class="form-label mb-1 small text-dark">إلى تاريخ</label>
                            <input type="date"
                                   name="end_date"
                                   value="{{ $dateRange['end_formatted'] }}"
                                   class="form-control form-control-sm">
                        </div>
                        <div class="align-self-end">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-filter me-1"></i>تطبيق
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Metrics -->
    <div class="metrics-grid">
        <div class="metric-card metric-primary">
            <div class="metric-icon text-primary">
                <i class="fas fa-users"></i>
            </div>
            <div class="metric-value text-primary">@arabicNumber($analytics['repeatCustomers']['total_customers'])</div>
            <div class="metric-label">إجمالي العملاء</div>
        </div>

        <div class="metric-card metric-success">
            <div class="metric-icon text-success">
                <i class="fas fa-redo-alt"></i>
            </div>
            <div class="metric-value text-success">@arabicNumber($analytics['repeatCustomers']['repeat_customers'])</div>
            <div class="metric-label">العملاء المتكررون</div>
        </div>

        <div class="metric-card metric-info">
            <div class="metric-icon text-info">
                <i class="fas fa-percentage"></i>
            </div>
            <div class="metric-value text-info">{{ $analytics['repeatCustomers']['repeat_rate'] }}%</div>
            <div class="metric-label">معدل التكرار</div>
        </div>

        <div class="metric-card metric-warning">
            <div class="metric-icon text-warning">
                <i class="fas fa-star"></i>
            </div>
            <div class="metric-value text-warning">{{ count($analytics['topCustomers']) }}</div>
            <div class="metric-label">أفضل العملاء</div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Customer Growth -->
        <div class="col-md-8">
            <div class="chart-card">
                <div class="chart-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>نمو العملاء عبر الزمن
                    </h5>
                </div>
                <div class="chart-body">
                    <canvas id="customerGrowthChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Visit Distribution -->
        <div class="col-md-4">
            <div class="chart-card">
                <div class="chart-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>توزيع الزيارات
                    </h5>
                </div>
                <div class="chart-body">
                    <canvas id="visitDistributionChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Customers Table -->
    <div class="row">
        <div class="col-12">
            <div class="top-customers-table">
                <div class="chart-header">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>أفضل العملاء حسب عدد البطاقات
                    </h5>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>الترتيب</th>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>إجمالي البطاقات</th>
                                <th>إجمالي المبلغ المنفق</th>
                                <th>متوسط البطاقة</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($analytics['topCustomers'] as $index => $customer)
                            <tr>
                                <td>
                                    <div class="rank-badge">
                                        @if($index == 0)
                                            <i class="fas fa-trophy text-warning"></i>
                                            <span>{{ $index + 1 }}</span>
                                        @elseif($index == 1)
                                            <i class="fas fa-medal text-secondary"></i>
                                            <span>{{ $index + 1 }}</span>
                                        @elseif($index == 2)
                                            <i class="fas fa-medal text-warning"></i>
                                            <span>{{ $index + 1 }}</span>
                                        @else
                                            <span>{{ $index + 1 }}</span>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div class="customer-name">{{ $customer['name'] }}</div>
                                </td>
                                <td>
                                    <a href="tel:{{ $customer['phone_number'] }}" class="phone-link">
                                        <i class="fas fa-phone me-1"></i>{{ $customer['phone_number'] }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ $customer['ticket_count'] }}</span>
                                </td>
                                <td>
                                    <div class="amount-display text-success">{{ number_format($customer['total_spent'], 2) }} ريال</div>
                                </td>
                                <td>
                                    <div class="amount-display">{{ number_format($customer['ticket_count'] > 0 ? $customer['total_spent'] / $customer['ticket_count'] : 0, 2) }} ريال</div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6" class="text-center text-muted py-5">
                                    <i class="fas fa-inbox display-4 d-block mb-3 opacity-50"></i>
                                    <h5>لا توجد بيانات عملاء</h5>
                                    <p>لا توجد بيانات عملاء متاحة للفترة المحددة.</p>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <a href="{{ route('reports.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
                </a>
                <a href="{{ route('reports.business-intelligence') }}" class="btn btn-primary">
                    ذكاء الأعمال<i class="fas fa-arrow-left ms-2"></i>
                </a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Chart.js default configuration
    Chart.defaults.responsive = true;
    Chart.defaults.maintainAspectRatio = false;

    // Customer Growth Chart
    const customerGrowthCtx = document.getElementById('customerGrowthChart').getContext('2d');
    new Chart(customerGrowthCtx, {
        type: 'line',
        data: {
            labels: @json($analytics['customerGrowth']['labels']),
            datasets: [{
                label: 'عملاء جدد',
                data: @json($analytics['customerGrowth']['data']),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        usePointStyle: true,
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                }
            }
        }
    });

    // Visit Distribution Chart
    const visitDistributionCtx = document.getElementById('visitDistributionChart').getContext('2d');
    const visitData = @json($analytics['repeatCustomers']['distribution']);

    new Chart(visitDistributionCtx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(visitData).map(visits => visits + ' زيارة'),
            datasets: [{
                data: Object.values(visitData),
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                }
            }
        }
    });
});
</script>
@endpush
