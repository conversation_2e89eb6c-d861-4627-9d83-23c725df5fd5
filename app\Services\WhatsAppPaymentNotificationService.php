<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Customer;
use App\Models\WhatsAppMessageTemplate;
use App\Models\WhatsAppTemplateUsage;
use App\Models\WhatsAppCustomerPreference;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class WhatsAppPaymentNotificationService
{
    protected WhatsAppTemplateService $templateService;
    protected WhatsAppService $whatsappService;

    public function __construct(
        WhatsAppTemplateService $templateService,
        WhatsAppService $whatsappService
    ) {
        $this->templateService = $templateService;
        $this->whatsappService = $whatsappService;
    }

    /**
     * Send payment confirmation notification.
     */
    public function sendPaymentConfirmation(Payment $payment): array
    {
        $invoice = $payment->invoice;
        $customer = $invoice->customer;

        if (!$customer || !$customer->phone_number) {
            return [
                'success' => false,
                'error' => 'Customer phone number not available'
            ];
        }

        $template = WhatsAppMessageTemplate::where('name', 'payment_confirmation_ar')
            ->approved()
            ->active()
            ->first();

        if (!$template) {
            return [
                'success' => false,
                'error' => 'Payment confirmation template not found'
            ];
        }

        $variables = [
            'customer_name' => $customer->name,
            'payment_amount' => number_format($payment->amount, 2),
            'payment_method' => $this->getPaymentMethodDisplay($payment->payment_method),
            'payment_date' => $payment->payment_date->format('Y-m-d'),
            'payment_time' => $payment->payment_date->format('H:i'),
            'invoice_number' => $invoice->invoice_number,
            'remaining_amount' => number_format($invoice->remaining_amount, 2),
            'business_name' => config('whatsapp.business_name'),
            'business_phone' => config('whatsapp.business_phone'),
            'payment_reference' => $payment->payment_reference ?? 'غير متوفر',
        ];

        $context = [
            'trigger_type' => 'automatic',
            'trigger_source' => 'payment_system',
            'business_event' => 'payment_confirmation',
            'invoice_id' => $invoice->id,
            'payment_id' => $payment->id,
        ];

        return $this->templateService->sendTemplate(
            $template,
            $customer->phone_number,
            $variables,
            $context
        );
    }

    /**
     * Send invoice notification.
     */
    public function sendInvoiceNotification(Invoice $invoice): array
    {
        $customer = $invoice->customer;

        if (!$customer || !$customer->phone_number) {
            return [
                'success' => false,
                'error' => 'Customer phone number not available'
            ];
        }

        $template = WhatsAppMessageTemplate::where('name', 'invoice_notification_ar')
            ->approved()
            ->active()
            ->first();

        if (!$template) {
            return [
                'success' => false,
                'error' => 'Invoice notification template not found'
            ];
        }

        $variables = [
            'customer_name' => $customer->name,
            'invoice_number' => $invoice->invoice_number,
            'invoice_date' => $invoice->invoice_date->format('Y-m-d'),
            'due_date' => $invoice->due_date ? $invoice->due_date->format('Y-m-d') : 'غير محدد',
            'total_amount' => number_format($invoice->total_amount, 2),
            'paid_amount' => number_format($invoice->paid_amount, 2),
            'remaining_amount' => number_format($invoice->remaining_amount, 2),
            'payment_status' => $this->getPaymentStatusDisplay($invoice->payment_status),
            'business_name' => config('whatsapp.business_name'),
            'business_phone' => config('whatsapp.business_phone'),
            'business_address' => config('whatsapp.business_address'),
        ];

        // Add repair ticket info if available
        if ($invoice->repairTicket) {
            $variables['ticket_number'] = $invoice->repairTicket->ticket_number;
            $variables['device_info'] = $invoice->repairTicket->brand->name . ' ' . $invoice->repairTicket->device_model;
        }

        $context = [
            'trigger_type' => 'automatic',
            'trigger_source' => 'invoice_system',
            'business_event' => 'invoice_notification',
            'invoice_id' => $invoice->id,
        ];

        return $this->templateService->sendTemplate(
            $template,
            $customer->phone_number,
            $variables,
            $context
        );
    }

    /**
     * Send payment reminder.
     */
    public function sendPaymentReminder(Invoice $invoice, string $reminderType = 'gentle'): array
    {
        $customer = $invoice->customer;

        if (!$customer || !$customer->phone_number) {
            return [
                'success' => false,
                'error' => 'Customer phone number not available'
            ];
        }

        // Check if customer can receive reminders
        $preferences = WhatsAppCustomerPreference::getOrCreateForCustomer($customer->phone_number);
        if (!$preferences->canReceiveReminders()) {
            return [
                'success' => false,
                'error' => 'Customer has opted out of payment reminders'
            ];
        }

        $templateName = match ($reminderType) {
            'gentle' => 'payment_reminder_gentle_ar',
            'urgent' => 'payment_reminder_urgent_ar',
            'final' => 'payment_reminder_final_ar',
            default => 'payment_reminder_gentle_ar',
        };

        $template = WhatsAppMessageTemplate::where('name', $templateName)
            ->approved()
            ->active()
            ->first();

        if (!$template) {
            return [
                'success' => false,
                'error' => "Payment reminder template ({$templateName}) not found"
            ];
        }

        $daysOverdue = $invoice->due_date ? $invoice->due_date->diffInDays(now(), false) : 0;
        $isOverdue = $daysOverdue > 0;

        $variables = [
            'customer_name' => $customer->name,
            'invoice_number' => $invoice->invoice_number,
            'invoice_date' => $invoice->invoice_date->format('Y-m-d'),
            'due_date' => $invoice->due_date ? $invoice->due_date->format('Y-m-d') : 'غير محدد',
            'remaining_amount' => number_format($invoice->remaining_amount, 2),
            'days_overdue' => $daysOverdue,
            'overdue_status' => $isOverdue ? 'متأخر' : 'مستحق قريباً',
            'business_name' => config('whatsapp.business_name'),
            'business_phone' => config('whatsapp.business_phone'),
            'business_address' => config('whatsapp.business_address'),
            'payment_methods' => $this->getAvailablePaymentMethods(),
        ];

        $context = [
            'trigger_type' => 'automatic',
            'trigger_source' => 'reminder_system',
            'business_event' => 'payment_reminder',
            'invoice_id' => $invoice->id,
            'reminder_type' => $reminderType,
        ];

        return $this->templateService->sendTemplate(
            $template,
            $customer->phone_number,
            $variables,
            $context
        );
    }

    /**
     * Send payment receipt.
     */
    public function sendPaymentReceipt(Payment $payment): array
    {
        $invoice = $payment->invoice;
        $customer = $invoice->customer;

        if (!$customer || !$customer->phone_number) {
            return [
                'success' => false,
                'error' => 'Customer phone number not available'
            ];
        }

        $template = WhatsAppMessageTemplate::where('name', 'payment_receipt_ar')
            ->approved()
            ->active()
            ->first();

        if (!$template) {
            return [
                'success' => false,
                'error' => 'Payment receipt template not found'
            ];
        }

        $variables = [
            'customer_name' => $customer->name,
            'receipt_number' => $payment->receipt_number ?? 'RCP-' . $payment->id,
            'payment_amount' => number_format($payment->amount, 2),
            'payment_method' => $this->getPaymentMethodDisplay($payment->payment_method),
            'payment_date' => $payment->payment_date->format('Y-m-d H:i'),
            'invoice_number' => $invoice->invoice_number,
            'payment_reference' => $payment->payment_reference ?? 'غير متوفر',
            'business_name' => config('whatsapp.business_name'),
            'business_phone' => config('whatsapp.business_phone'),
            'business_address' => config('whatsapp.business_address'),
        ];

        $context = [
            'trigger_type' => 'automatic',
            'trigger_source' => 'payment_system',
            'business_event' => 'payment_receipt',
            'invoice_id' => $invoice->id,
            'payment_id' => $payment->id,
        ];

        return $this->templateService->sendTemplate(
            $template,
            $customer->phone_number,
            $variables,
            $context
        );
    }

    /**
     * Send payment plan notification.
     */
    public function sendPaymentPlanNotification(Invoice $invoice, array $paymentPlan): array
    {
        $customer = $invoice->customer;

        if (!$customer || !$customer->phone_number) {
            return [
                'success' => false,
                'error' => 'Customer phone number not available'
            ];
        }

        $template = WhatsAppMessageTemplate::where('name', 'payment_plan_ar')
            ->approved()
            ->active()
            ->first();

        if (!$template) {
            return [
                'success' => false,
                'error' => 'Payment plan template not found'
            ];
        }

        $installmentDetails = '';
        foreach ($paymentPlan['installments'] as $index => $installment) {
            $installmentDetails .= sprintf(
                "القسط %d: %s ريال - تاريخ الاستحقاق: %s\n",
                $index + 1,
                number_format($installment['amount'], 2),
                Carbon::parse($installment['due_date'])->format('Y-m-d')
            );
        }

        $variables = [
            'customer_name' => $customer->name,
            'invoice_number' => $invoice->invoice_number,
            'total_amount' => number_format($invoice->remaining_amount, 2),
            'installments_count' => count($paymentPlan['installments']),
            'installment_amount' => number_format($paymentPlan['installments'][0]['amount'], 2),
            'first_due_date' => Carbon::parse($paymentPlan['installments'][0]['due_date'])->format('Y-m-d'),
            'installment_details' => trim($installmentDetails),
            'business_name' => config('whatsapp.business_name'),
            'business_phone' => config('whatsapp.business_phone'),
        ];

        $context = [
            'trigger_type' => 'manual',
            'trigger_source' => 'payment_plan_system',
            'business_event' => 'payment_plan',
            'invoice_id' => $invoice->id,
        ];

        return $this->templateService->sendTemplate(
            $template,
            $customer->phone_number,
            $variables,
            $context
        );
    }

    /**
     * Send bulk payment reminders.
     */
    public function sendBulkPaymentReminders(array $invoiceIds, string $reminderType = 'gentle'): array
    {
        $results = [
            'total' => count($invoiceIds),
            'sent' => 0,
            'failed' => 0,
            'errors' => []
        ];

        $invoices = Invoice::whereIn('id', $invoiceIds)
            ->with('customer')
            ->get();

        foreach ($invoices as $invoice) {
            try {
                $result = $this->sendPaymentReminder($invoice, $reminderType);
                
                if ($result['success']) {
                    $results['sent']++;
                } else {
                    $results['failed']++;
                    $results['errors'][] = [
                        'invoice_id' => $invoice->id,
                        'invoice_number' => $invoice->invoice_number,
                        'error' => $result['error']
                    ];
                }

                // Add delay between messages to avoid rate limiting
                usleep(500000); // 0.5 second delay
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'invoice_id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Get payment method display name in Arabic.
     */
    private function getPaymentMethodDisplay(string $paymentMethod): string
    {
        $methods = [
            'cash' => 'نقداً',
            'card' => 'بطاقة ائتمان',
            'bank_transfer' => 'تحويل بنكي',
            'check' => 'شيك',
            'mobile_payment' => 'دفع عبر الجوال',
            'installment' => 'تقسيط',
            'other' => 'أخرى',
        ];

        return $methods[$paymentMethod] ?? $paymentMethod;
    }

    /**
     * Get payment status display name in Arabic.
     */
    private function getPaymentStatusDisplay(string $paymentStatus): string
    {
        $statuses = [
            'paid' => 'مدفوع بالكامل',
            'partial' => 'مدفوع جزئياً',
            'unpaid' => 'غير مدفوع',
            'overdue' => 'متأخر',
            'cancelled' => 'ملغي',
        ];

        return $statuses[$paymentStatus] ?? $paymentStatus;
    }

    /**
     * Get available payment methods.
     */
    private function getAvailablePaymentMethods(): string
    {
        return "• نقداً في المحل\n• تحويل بنكي\n• بطاقة ائتمان\n• دفع عبر الجوال";
    }

    /**
     * Schedule automatic payment reminders.
     */
    public function scheduleAutomaticReminders(): array
    {
        $results = [
            'gentle_reminders' => 0,
            'urgent_reminders' => 0,
            'final_reminders' => 0,
            'errors' => []
        ];

        try {
            // Gentle reminders (3 days before due date)
            $gentleReminderInvoices = Invoice::where('payment_status', '!=', 'paid')
                ->where('due_date', '=', now()->addDays(3)->toDateString())
                ->with('customer')
                ->get();

            foreach ($gentleReminderInvoices as $invoice) {
                $result = $this->sendPaymentReminder($invoice, 'gentle');
                if ($result['success']) {
                    $results['gentle_reminders']++;
                }
            }

            // Urgent reminders (1 day overdue)
            $urgentReminderInvoices = Invoice::where('payment_status', '!=', 'paid')
                ->where('due_date', '=', now()->subDay()->toDateString())
                ->with('customer')
                ->get();

            foreach ($urgentReminderInvoices as $invoice) {
                $result = $this->sendPaymentReminder($invoice, 'urgent');
                if ($result['success']) {
                    $results['urgent_reminders']++;
                }
            }

            // Final reminders (7 days overdue)
            $finalReminderInvoices = Invoice::where('payment_status', '!=', 'paid')
                ->where('due_date', '=', now()->subDays(7)->toDateString())
                ->with('customer')
                ->get();

            foreach ($finalReminderInvoices as $invoice) {
                $result = $this->sendPaymentReminder($invoice, 'final');
                if ($result['success']) {
                    $results['final_reminders']++;
                }
            }

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
            Log::error('Error in automatic payment reminders', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $results;
    }
}
