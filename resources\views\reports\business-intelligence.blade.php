@extends('layouts.app')

@section('title', __('app.reports.business_intelligence'))

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-graph-up-arrow"></i> {{ __('app.reports.business_intelligence') }}
                </h1>

                <!-- Date Range Filter -->
                <div class="card">
                    <div class="card-body p-3">
                        <form method="GET" action="{{ route('reports.business-intelligence') }}" class="d-flex align-items-center gap-3">
                            <div>
                                <label class="form-label mb-1 small">{{ __('app.reports.from_date') }}</label>
                                <input type="date"
                                       name="start_date"
                                       value="{{ $dateRange['start_formatted'] }}"
                                       class="form-control form-control-sm">
                            </div>
                            <div>
                                <label class="form-label mb-1 small">{{ __('app.reports.to_date') }}</label>
                                <input type="date"
                                       name="end_date"
                                       value="{{ $dateRange['end_formatted'] }}"
                                       class="form-control form-control-sm">
                            </div>
                            <div class="align-self-end">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="bi bi-funnel"></i> {{ __('app.filter') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Revenue Metrics -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">@arabicCurrency($intelligence['revenueAnalysis']['total_revenue'])</h4>
                                    <small>{{ __('app.reports.total_revenue') }}</small>
                                </div>
                                <i class="bi bi-currency-dollar display-6"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">@arabicCurrency($intelligence['revenueAnalysis']['avg_ticket_value'])</h4>
                                    <small>{{ __('app.reports.avg_ticket_value') }}</small>
                                </div>
                                <i class="bi bi-receipt display-6"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">@arabicNumber(count($intelligence['profitabilityByBrand']))</h4>
                                    <small>{{ __('app.reports.active_brands') }}</small>
                                </div>
                                <i class="bi bi-tags display-6"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Revenue Chart -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-graph-up"></i> {{ __('app.reports.monthly_revenue_trend') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="revenueChart" height="80"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Brand Profitability -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-bar-chart"></i> Brand Profitability Analysis
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Brand</th>
                                            <th>Total Tickets</th>
                                            <th>Total Revenue</th>
                                            <th>Average Revenue</th>
                                            <th>Performance</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($intelligence['profitabilityByBrand'] as $brand)
                                        <tr>
                                            <td>
                                                <strong>{{ $brand['name'] }}</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">{{ $brand['ticket_count'] }}</span>
                                            </td>
                                            <td>
                                                <strong class="text-success">${{ number_format($brand['total_revenue'], 2) }}</strong>
                                            </td>
                                            <td>
                                                ${{ number_format($brand['avg_revenue'], 2) }}
                                            </td>
                                            <td>
                                                @php
                                                    $maxRevenue = collect($intelligence['profitabilityByBrand'])->max('total_revenue');
                                                    $percentage = $maxRevenue > 0 ? ($brand['total_revenue'] / $maxRevenue) * 100 : 0;
                                                @endphp
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar
                                                        @if($percentage >= 80) bg-success
                                                        @elseif($percentage >= 60) bg-info
                                                        @elseif($percentage >= 40) bg-warning
                                                        @else bg-danger
                                                        @endif"
                                                        role="progressbar"
                                                        style="width: {{ $percentage }}%">
                                                        {{ round($percentage, 1) }}%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="5" class="text-center text-muted py-4">
                                                <i class="bi bi-inbox display-4 d-block mb-2"></i>
                                                No brand profitability data available.
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Device Failure Patterns -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-exclamation-triangle"></i> Device Failure Patterns
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Brand</th>
                                            <th>Device Model</th>
                                            <th>Failure Count</th>
                                            <th>Common Problems</th>
                                            <th>Risk Level</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($intelligence['deviceFailurePatterns'] as $pattern)
                                        <tr>
                                            <td>
                                                <strong>{{ $pattern['brand'] }}</strong>
                                            </td>
                                            <td>
                                                {{ $pattern['device_model'] }}
                                            </td>
                                            <td>
                                                <span class="badge
                                                    @if($pattern['failure_count'] >= 10) bg-danger
                                                    @elseif($pattern['failure_count'] >= 5) bg-warning
                                                    @else bg-info
                                                    @endif">
                                                    {{ $pattern['failure_count'] }}
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    {{ Str::limit($pattern['common_problems'], 80) }}
                                                </small>
                                            </td>
                                            <td>
                                                @if($pattern['failure_count'] >= 10)
                                                    <span class="badge bg-danger">High Risk</span>
                                                @elseif($pattern['failure_count'] >= 5)
                                                    <span class="badge bg-warning">Medium Risk</span>
                                                @else
                                                    <span class="badge bg-success">Low Risk</span>
                                                @endif
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="5" class="text-center text-muted py-4">
                                                <i class="bi bi-shield-check display-4 d-block mb-2"></i>
                                                No failure patterns detected.
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('reports.customer-analytics') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Customer Analytics
                        </a>
                        <a href="{{ route('reports.dashboard') }}" class="btn btn-primary">
                            Back to Dashboard <i class="bi bi-house"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Chart.js default configuration
    Chart.defaults.responsive = true;
    Chart.defaults.maintainAspectRatio = false;

    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: @json($intelligence['revenueAnalysis']['monthly_revenue']['labels']),
            datasets: [{
                label: 'Monthly Revenue ($)',
                data: @json($intelligence['revenueAnalysis']['monthly_revenue']['data']),
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.1,
                fill: true,
                pointBackgroundColor: 'rgb(34, 197, 94)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 5
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Revenue: $' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });
});
</script>
@endpush
