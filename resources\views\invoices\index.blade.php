@extends('layouts.app')

@section('title', 'إدارة الفواتير')

@push('styles')
<style>
.invoice-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.875rem;
    text-transform: uppercase;
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.3;
    float: left;
    margin-top: -0.5rem;
}

.filter-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.invoice-table {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    overflow: hidden;
}

.table th {
    background: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.invoice-number {
    font-weight: 600;
    color: #667eea;
}

.customer-info {
    display: flex;
    flex-direction: column;
}

.customer-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.customer-phone {
    color: #6c757d;
    font-size: 0.875rem;
}

.amount-display {
    font-weight: 600;
    font-size: 1.1rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
}

.status-draft {
    background: #e2e3e5;
    color: #6c757d;
}

.status-sent {
    background: #cce5ff;
    color: #0066cc;
}

.status-paid {
    background: #d4edda;
    color: #155724;
}

.status-partial {
    background: #fff3cd;
    color: #856404;
}

.status-overdue {
    background: #f8d7da;
    color: #721c24;
}

.payment-status-pending {
    background: #fff3cd;
    color: #856404;
}

.payment-status-partial {
    background: #cce5ff;
    color: #0066cc;
}

.payment-status-paid {
    background: #d4edda;
    color: #155724;
}

.payment-status-overdue {
    background: #f8d7da;
    color: #721c24;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.btn-action {
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-view {
    background: #17a2b8;
    color: white;
}

.btn-edit {
    background: #ffc107;
    color: #212529;
}

.btn-send {
    background: #28a745;
    color: white;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.3;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .invoice-header {
        padding: 1rem;
    }
    
    .filter-card {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="invoice-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">إدارة الفواتير</h1>
                <p class="mb-0 opacity-75">إدارة شاملة لجميع فواتير الورشة</p>
            </div>
            <div>
                <a href="{{ route('invoices.create') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>فاتورة جديدة
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon text-primary">
                <i class="fas fa-file-invoice"></i>
            </div>
            <div class="stat-value text-primary">@arabicNumber($stats['total_invoices'])</div>
            <div class="stat-label">إجمالي الفواتير</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-value text-success">@arabicNumber($stats['paid_invoices'])</div>
            <div class="stat-label">فواتير مدفوعة</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-value text-warning">@arabicNumber($stats['overdue_invoices'])</div>
            <div class="stat-label">فواتير متأخرة</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-info">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-value text-info">@arabicCurrency($stats['pending_amount'])</div>
            <div class="stat-label">مبالغ معلقة</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="GET" action="{{ route('invoices.index') }}" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">البحث</label>
                <input type="text" 
                       class="form-control" 
                       id="search" 
                       name="search" 
                       value="{{ request('search') }}"
                       placeholder="رقم الفاتورة، اسم العميل، رقم الهاتف">
            </div>
            
            <div class="col-md-2">
                <label for="status" class="form-label">حالة الفاتورة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="draft" {{ request('status') === 'draft' ? 'selected' : '' }}>مسودة</option>
                    <option value="sent" {{ request('status') === 'sent' ? 'selected' : '' }}>مرسلة</option>
                    <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>ملغية</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="payment_status" class="form-label">حالة الدفع</label>
                <select class="form-select" id="payment_status" name="payment_status">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {{ request('payment_status') === 'pending' ? 'selected' : '' }}>معلق</option>
                    <option value="partial" {{ request('payment_status') === 'partial' ? 'selected' : '' }}>جزئي</option>
                    <option value="paid" {{ request('payment_status') === 'paid' ? 'selected' : '' }}>مدفوع</option>
                    <option value="overdue" {{ request('payment_status') === 'overdue' ? 'selected' : '' }}>متأخر</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" 
                       class="form-control" 
                       id="date_from" 
                       name="date_from" 
                       value="{{ request('date_from') }}">
            </div>
            
            <div class="col-md-2">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" 
                       class="form-control" 
                       id="date_to" 
                       name="date_to" 
                       value="{{ request('date_to') }}">
            </div>
            
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-1">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="{{ route('invoices.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Invoices Table -->
    <div class="invoice-table">
        @if($invoices->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>تاريخ الفاتورة</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>المبلغ الإجمالي</th>
                            <th>المبلغ المدفوع</th>
                            <th>حالة الفاتورة</th>
                            <th>حالة الدفع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($invoices as $invoice)
                        <tr>
                            <td>
                                <span class="invoice-number">{{ $invoice->invoice_number }}</span>
                            </td>
                            <td>
                                <div class="customer-info">
                                    <div class="customer-name">{{ $invoice->customer_name }}</div>
                                    <div class="customer-phone">{{ $invoice->customer_phone }}</div>
                                </div>
                            </td>
                            <td>{{ $invoice->invoice_date->format('Y-m-d') }}</td>
                            <td>
                                {{ $invoice->due_date->format('Y-m-d') }}
                                @if($invoice->due_date < now() && $invoice->payment_status !== 'paid')
                                    <small class="text-danger d-block">
                                        متأخر {{ $invoice->due_date->diffInDays(now()) }} يوم
                                    </small>
                                @endif
                            </td>
                            <td>
                                <span class="amount-display">@arabicCurrency($invoice->total_amount)</span>
                            </td>
                            <td>
                                <span class="amount-display">@arabicCurrency($invoice->paid_amount)</span>
                            </td>
                            <td>
                                <span class="status-badge status-{{ $invoice->status }}">
                                    @switch($invoice->status)
                                        @case('draft') مسودة @break
                                        @case('sent') مرسلة @break
                                        @case('cancelled') ملغية @break
                                        @default {{ $invoice->status }}
                                    @endswitch
                                </span>
                            </td>
                            <td>
                                <span class="status-badge payment-status-{{ $invoice->payment_status }}">
                                    @switch($invoice->payment_status)
                                        @case('pending') معلق @break
                                        @case('partial') جزئي @break
                                        @case('paid') مدفوع @break
                                        @case('overdue') متأخر @break
                                        @default {{ $invoice->payment_status }}
                                    @endswitch
                                </span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="{{ route('invoices.show', $invoice) }}" 
                                       class="btn-action btn-view" 
                                       title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    @if($invoice->status === 'draft')
                                        <a href="{{ route('invoices.edit', $invoice) }}" 
                                           class="btn-action btn-edit" 
                                           title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    @endif
                                    
                                    @if($invoice->status === 'draft')
                                        <form method="POST" 
                                              action="{{ route('invoices.send', $invoice) }}" 
                                              style="display: inline;">
                                            @csrf
                                            <button type="submit" 
                                                    class="btn-action btn-send" 
                                                    title="إرسال"
                                                    onclick="return confirm('هل تريد إرسال هذه الفاتورة؟')">
                                                <i class="fas fa-paper-plane"></i>
                                            </button>
                                        </form>
                                    @endif
                                    
                                    @if($invoice->status === 'draft')
                                        <form method="POST" 
                                              action="{{ route('invoices.destroy', $invoice) }}" 
                                              style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" 
                                                    class="btn-action btn-delete" 
                                                    title="حذف"
                                                    onclick="return confirm('هل تريد حذف هذه الفاتورة؟')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center p-3">
                {{ $invoices->withQueryString()->links() }}
            </div>
        @else
            <div class="empty-state">
                <i class="fas fa-file-invoice"></i>
                <h5>لا توجد فواتير</h5>
                <p>لم يتم العثور على فواتير بناءً على المعايير المحددة.</p>
                @if(request()->hasAny(['search', 'status', 'payment_status', 'date_from', 'date_to']))
                    <a href="{{ route('invoices.index') }}" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>عرض جميع الفواتير
                    </a>
                @else
                    <a href="{{ route('invoices.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إنشاء أول فاتورة
                    </a>
                @endif
            </div>
        @endif
    </div>
</div>
@endsection
