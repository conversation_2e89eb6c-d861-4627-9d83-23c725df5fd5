<?php $__env->startSection('title', 'العملاء'); ?>

<?php $__env->startPush('styles'); ?>
<style>
.customers-header {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.search-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.customers-table {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    overflow: hidden;
}

.table th {
    background: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.customer-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
}

.contact-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #495057;
    text-decoration: none;
    transition: color 0.2s ease;
}

.contact-info:hover {
    color: #007bff;
}

.tickets-badge {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-weight: 600;
    font-size: 0.875rem;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
}

.btn-action {
    padding: 0.375rem 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid;
    transition: all 0.2s ease;
    font-size: 0.75rem;
    font-weight: 500;
    min-width: auto;
    text-align: center;
    white-space: nowrap;
    flex-shrink: 0;
}

.btn-action:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.15);
}

.btn-action:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-action i {
    font-size: 0.75rem;
}

/* إصلاح أزرار التنقل */
.pagination {
    margin: 0;
    justify-content: center;
}

.pagination .page-link {
    color: #1cc88a;
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
    margin: 0 0.125rem;
    border-radius: 0.375rem;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination .page-link:hover {
    color: #17a673;
    background-color: #f8f9fc;
    border-color: #1cc88a;
}

.pagination .page-item.active .page-link {
    background-color: #1cc88a;
    border-color: #1cc88a;
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

.pagination-wrapper {
    background: #fff;
    padding: 1rem;
    border-radius: 0.5rem;
    border-top: 1px solid #e3e6f0;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .customers-header {
        padding: 1rem;
    }

    .search-card {
        padding: 1rem;
    }

    .action-buttons {
        flex-direction: column;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="customers-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">العملاء</h1>
                <p class="mb-0 opacity-75">إدارة وتتبع جميع عملاء الورشة</p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(route('customers.create')); ?>" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>عميل جديد
                </a>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-light dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-2"></i>تصدير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?php echo e(route('print-export.customers.export-excel', request()->query())); ?>">
                            <i class="fas fa-file-excel me-2"></i>تصدير إكسل
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Form -->
    <div class="search-card">
        <h5 class="mb-3">
            <i class="fas fa-search me-2"></i>البحث في العملاء
        </h5>
        <form method="GET" action="<?php echo e(route('customers.index')); ?>" class="row g-3">
            <div class="col-md-8">
                <div class="input-group">
                    <input type="text"
                           class="form-control"
                           name="search"
                           value="<?php echo e(request('search')); ?>"
                           placeholder="البحث بالاسم، الهاتف، أو البريد الإلكتروني...">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <?php if(request('search')): ?>
                    <a href="<?php echo e(route('customers.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>مسح البحث
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>

    <!-- Customers Table -->
    <div class="customers-table">
        <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
            <h5 class="mb-0">
                قائمة العملاء
                <span class="badge bg-secondary"><?php echo App\Helpers\ArabicFormatter::formatNumber($customers->total()); ?> إجمالي</span>
            </h5>
        </div>

        <?php if($customers->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>رقم الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>بطاقات الإصلاح</th>
                            <th>تاريخ التسجيل</th>
                            <th style="width: 150px">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div class="customer-name"><?php echo e($customer->name); ?></div>
                                </td>
                                <td>
                                    <a href="tel:<?php echo e($customer->phone_number); ?>" class="contact-info">
                                        <i class="fas fa-phone"></i>
                                        <span><?php echo e($customer->phone_number); ?></span>
                                    </a>
                                </td>
                                <td>
                                    <?php if($customer->email): ?>
                                        <a href="mailto:<?php echo e($customer->email); ?>" class="contact-info">
                                            <i class="fas fa-envelope"></i>
                                            <span><?php echo e($customer->email); ?></span>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">لا يوجد بريد إلكتروني</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="tickets-badge">
                                        <?php echo App\Helpers\ArabicFormatter::formatNumber($customer->repairTickets()->count()); ?> بطاقة
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo e($customer->created_at->format('Y-m-d')); ?>

                                    </small>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="<?php echo e(route('customers.show', $customer)); ?>"
                                           class="btn btn-sm btn-outline-primary btn-action"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="عرض تفاصيل العميل">
                                            <i class="fas fa-eye"></i>
                                            <span class="d-none d-xl-inline ms-1">عرض</span>
                                        </a>
                                        <a href="<?php echo e(route('customers.edit', $customer)); ?>"
                                           class="btn btn-sm btn-outline-secondary btn-action"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="تعديل معلومات العميل">
                                            <i class="fas fa-edit"></i>
                                            <span class="d-none d-xl-inline ms-1">تعديل</span>
                                        </a>
                                        <a href="<?php echo e(route('repair-tickets.create', ['customer_id' => $customer->id])); ?>"
                                           class="btn btn-sm btn-outline-success btn-action"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="إنشاء بطاقة إصلاح جديدة للعميل">
                                            <i class="fas fa-plus"></i>
                                            <span class="d-none d-xl-inline ms-1">بطاقة</span>
                                        </a>
                                        <?php if($customer->repairTickets()->count() == 0): ?>
                                            <form method="POST"
                                                  action="<?php echo e(route('customers.destroy', $customer)); ?>"
                                                  class="d-inline"
                                                  onsubmit="return confirm('هل أنت متأكد من حذف هذا العميل؟ لا يمكن التراجع عن هذا الإجراء.')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit"
                                                        class="btn btn-sm btn-outline-danger btn-action"
                                                        data-bs-toggle="tooltip"
                                                        data-bs-placement="top"
                                                        title="حذف العميل نهائياً">
                                                    <i class="fas fa-trash"></i>
                                                    <span class="d-none d-xl-inline ms-1">حذف</span>
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-secondary btn-action"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="لا يمكن حذف العميل لوجود <?php echo e($customer->repairTickets()->count()); ?> بطاقة إصلاح"
                                                    disabled>
                                                <i class="fas fa-lock"></i>
                                                <span class="d-none d-xl-inline ms-1">محمي</span>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                            </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <h4>لا يوجد عملاء</h4>
                <p>
                    <?php if(request('search')): ?>
                        لا يوجد عملاء يطابقون معايير البحث المحددة.
                    <?php else: ?>
                        ابدأ بإضافة أول عميل للورشة.
                    <?php endif; ?>
                </p>
                <?php if(!request('search')): ?>
                    <a href="<?php echo e(route('customers.create')); ?>" class="btn btn-success">
                        <i class="fas fa-plus-circle me-2"></i>إضافة أول عميل
                    </a>
                <?php else: ?>
                    <a href="<?php echo e(route('customers.index')); ?>" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>عرض جميع العملاء
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php if($customers->hasPages()): ?>
            <div class="pagination-wrapper">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="pagination-info">
                        <small class="text-muted">
                            عرض <?php echo e($customers->firstItem()); ?> إلى <?php echo e($customers->lastItem()); ?>

                            من أصل <?php echo e($customers->total()); ?> نتيجة
                        </small>
                    </div>
                    <div class="pagination-links">
                        <?php echo e($customers->withQueryString()->links('custom-pagination')); ?>

                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // تحسين تجربة المستخدم للأزرار
    document.querySelectorAll('.btn-action').forEach(function(button) {
        button.addEventListener('mouseenter', function() {
            if (!this.disabled) {
                this.style.transform = 'translateY(-2px)';
            }
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\nj\resources\views/customers/index.blade.php ENDPATH**/ ?>