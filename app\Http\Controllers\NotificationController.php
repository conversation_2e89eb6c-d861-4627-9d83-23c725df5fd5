<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\NotificationTemplate;
use App\Models\Customer;
use App\Models\RepairTicket;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;

class NotificationController extends Controller
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Display a listing of notifications.
     */
    public function index(Request $request): View
    {
        $query = Notification::with(['customer', 'repairTicket', 'createdBy']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('message', 'like', "%{$search}%")
                  ->orWhere('recipient_phone', 'like', "%{$search}%")
                  ->orWhere('recipient_email', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by type
        if ($request->has('type') && $request->type) {
            $query->byType($request->type);
        }

        // Filter by category
        if ($request->has('category') && $request->category) {
            $query->byCategory($request->category);
        }

        // Filter by status
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $notifications = $query->orderBy('created_at', 'desc')->paginate(20)->withQueryString();

        // Get statistics
        $stats = [
            'total' => Notification::count(),
            'pending' => Notification::pending()->count(),
            'sent' => Notification::sent()->count(),
            'failed' => Notification::failed()->count(),
            'today' => Notification::whereDate('created_at', today())->count(),
        ];

        return view('notifications.index', compact('notifications', 'stats'));
    }

    /**
     * Show the form for creating a new notification.
     */
    public function create(Request $request): View
    {
        $customers = Customer::orderBy('name')->get();
        $templates = NotificationTemplate::active()->orderBy('name')->get();
        $repairTickets = RepairTicket::with(['customer', 'brand'])
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get();

        // Pre-select customer and ticket if provided
        $selectedCustomer = $request->customer_id ? Customer::find($request->customer_id) : null;
        $selectedTicket = $request->ticket_id ? RepairTicket::find($request->ticket_id) : null;

        return view('notifications.create', compact(
            'customers', 
            'templates', 
            'repairTickets',
            'selectedCustomer',
            'selectedTicket'
        ));
    }

    /**
     * Store a newly created notification.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'repair_ticket_id' => 'nullable|exists:repair_tickets,id',
            'template_id' => 'nullable|exists:notification_templates,id',
            'type' => 'required|in:sms,whatsapp,email',
            'category' => 'required|in:status_update,appointment_reminder,pickup_ready,payment_reminder,satisfaction_survey,general',
            'message' => 'required|string|max:1000',
            'subject' => 'nullable|string|max:255',
            'scheduled_at' => 'nullable|date|after:now',
            'send_immediately' => 'boolean',
        ]);

        $customer = Customer::findOrFail($validated['customer_id']);
        $repairTicket = $validated['repair_ticket_id'] ? RepairTicket::find($validated['repair_ticket_id']) : null;

        // Determine recipient based on type
        $recipientPhone = in_array($validated['type'], ['sms', 'whatsapp']) ? $customer->phone_number : null;
        $recipientEmail = $validated['type'] === 'email' ? $customer->email : null;

        $notification = Notification::create([
            'customer_id' => $validated['customer_id'],
            'repair_ticket_id' => $validated['repair_ticket_id'],
            'type' => $validated['type'],
            'category' => $validated['category'],
            'recipient_phone' => $recipientPhone,
            'recipient_email' => $recipientEmail,
            'subject' => $validated['subject'],
            'message' => $validated['message'],
            'scheduled_at' => $validated['scheduled_at'] ?? null,
            'created_by' => auth()->id() ?? 1,
        ]);

        // Send immediately if requested and not scheduled
        if ($request->boolean('send_immediately') && !$validated['scheduled_at']) {
            $this->notificationService->sendNotification($notification);
            $message = __('app.notifications.sent_successfully');
        } else {
            $message = $validated['scheduled_at'] 
                ? __('app.notifications.scheduled_successfully')
                : __('app.notifications.created_successfully');
        }

        return redirect()->route('notifications.index')->with('success', $message);
    }

    /**
     * Display the specified notification.
     */
    public function show(Notification $notification): View
    {
        $notification->load(['customer', 'repairTicket.brand', 'createdBy']);
        
        return view('notifications.show', compact('notification'));
    }

    /**
     * Resend a failed notification.
     */
    public function resend(Notification $notification): RedirectResponse
    {
        if ($notification->status !== 'failed') {
            return redirect()->back()->with('error', __('app.notifications.can_only_resend_failed'));
        }

        // Reset status to pending
        $notification->update([
            'status' => 'pending',
            'error_message' => null,
            'sent_at' => null,
        ]);

        // Attempt to send
        $success = $this->notificationService->sendNotification($notification);

        $message = $success 
            ? __('app.notifications.resent_successfully')
            : __('app.notifications.resend_failed');

        return redirect()->back()->with($success ? 'success' : 'error', $message);
    }

    /**
     * Get template preview.
     */
    public function templatePreview(Request $request): JsonResponse
    {
        $template = NotificationTemplate::findOrFail($request->template_id);
        $customer = $request->customer_id ? Customer::find($request->customer_id) : null;
        $repairTicket = $request->repair_ticket_id ? RepairTicket::find($request->repair_ticket_id) : null;

        // Prepare sample data
        $data = [
            'customer_name' => $customer?->name ?? 'أحمد محمد',
            'shop_name' => config('app.name', 'ورشة NJ للإصلاح'),
            'shop_phone' => config('app.shop_phone', '0501234567'),
        ];

        if ($repairTicket) {
            $data = array_merge($data, [
                'ticket_number' => $repairTicket->ticket_number,
                'device_model' => $repairTicket->device_model,
                'device_brand' => $repairTicket->brand->name,
                'status' => $repairTicket->repairStatus->name,
                'cost' => $repairTicket->final_cost ? number_format($repairTicket->final_cost, 2) . ' ريال' : 'غير محدد',
            ]);
        }

        return response()->json([
            'message' => $template->render($data),
            'subject' => $template->renderSubject($data),
        ]);
    }

    /**
     * Get notification statistics.
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total' => Notification::count(),
            'by_status' => [
                'pending' => Notification::pending()->count(),
                'sent' => Notification::sent()->count(),
                'delivered' => Notification::where('status', 'delivered')->count(),
                'failed' => Notification::failed()->count(),
                'read' => Notification::where('status', 'read')->count(),
            ],
            'by_type' => [
                'sms' => Notification::byType('sms')->count(),
                'whatsapp' => Notification::byType('whatsapp')->count(),
                'email' => Notification::byType('email')->count(),
            ],
            'by_category' => [
                'status_update' => Notification::byCategory('status_update')->count(),
                'pickup_ready' => Notification::byCategory('pickup_ready')->count(),
                'appointment_reminder' => Notification::byCategory('appointment_reminder')->count(),
                'payment_reminder' => Notification::byCategory('payment_reminder')->count(),
                'satisfaction_survey' => Notification::byCategory('satisfaction_survey')->count(),
                'general' => Notification::byCategory('general')->count(),
            ],
            'today' => Notification::whereDate('created_at', today())->count(),
            'this_week' => Notification::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month' => Notification::whereMonth('created_at', now()->month)->count(),
            'total_cost' => Notification::sum('cost'),
        ];

        return response()->json($stats);
    }

    /**
     * Process scheduled notifications manually.
     */
    public function processScheduled(): JsonResponse
    {
        $processed = $this->notificationService->processScheduledNotifications();
        
        return response()->json([
            'success' => true,
            'processed' => $processed,
            'message' => __('app.notifications.processed_scheduled', ['count' => $processed])
        ]);
    }
}
