<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\PatternValidationService;

class PatternValidationServiceTest extends TestCase
{
    /**
     * Test valid visual pattern validation
     */
    public function test_validates_valid_visual_pattern()
    {
        $validPattern = '1-2-3-6-9';
        $result = PatternValidationService::validateVisualPattern($validPattern);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);

        // Test complexity separately
        $complexity = PatternValidationService::getPatternComplexity($validPattern);
        $this->assertEquals(6, $complexity); // 5 dots + 1 direction change
    }

    /**
     * Test invalid visual pattern with too few dots
     */
    public function test_rejects_pattern_with_too_few_dots()
    {
        $invalidPattern = '1-2-3';
        $result = PatternValidationService::validateVisualPattern($invalidPattern);

        $this->assertFalse($result['valid']);
        $this->assertContains('<PERSON><PERSON> must connect at least 4 dots', $result['errors']);
    }

    /**
     * Test invalid visual pattern with invalid dot numbers
     */
    public function test_rejects_pattern_with_invalid_dot_numbers()
    {
        $invalidPattern = '1-2-10-11';
        $result = PatternValidationService::validateVisualPattern($invalidPattern);

        $this->assertFalse($result['valid']);
        $this->assertContains('Invalid dot ID: 10', $result['errors']);
        $this->assertContains('Invalid dot ID: 11', $result['errors']);
    }

    /**
     * Test invalid visual pattern with duplicate dots
     */
    public function test_rejects_pattern_with_duplicate_dots()
    {
        $invalidPattern = '1-2-2-3';
        $result = PatternValidationService::validateVisualPattern($invalidPattern);

        $this->assertFalse($result['valid']);
        $this->assertContains('Pattern cannot use the same dot twice', $result['errors']);
    }

    /**
     * Test invalid visual pattern with non-adjacent connections
     */
    public function test_rejects_pattern_with_invalid_connections()
    {
        $invalidPattern = '1-3-7-9'; // 1 to 3 skips 2, 3 to 7 skips 5
        $result = PatternValidationService::validateVisualPattern($invalidPattern);

        $this->assertFalse($result['valid']);
        $this->assertContains('Invalid connection from dot 1 to dot 3', $result['errors']);
        $this->assertContains('Invalid connection from dot 3 to dot 7', $result['errors']);
    }

    /**
     * Test valid L-shaped pattern
     */
    public function test_validates_l_shaped_pattern()
    {
        $lPattern = '1-2-3-6-9-8-7';
        $result = PatternValidationService::validateVisualPattern($lPattern);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);

        // Test complexity separately
        $complexity = PatternValidationService::getPatternComplexity($lPattern);
        $this->assertGreaterThan(5, $complexity);
    }

    /**
     * Test pattern grid generation
     */
    public function test_generates_correct_pattern_grid()
    {
        $pattern = '1-2-3-6-9';
        $grid = PatternValidationService::generatePatternGrid($pattern);

        // Check grid structure
        $this->assertCount(3, $grid);
        $this->assertCount(3, $grid[0]);

        // Check specific dots are active
        $this->assertTrue($grid[0][0]['active']); // Dot 1
        $this->assertEquals(1, $grid[0][0]['order']);

        $this->assertTrue($grid[0][1]['active']); // Dot 2
        $this->assertEquals(2, $grid[0][1]['order']);

        $this->assertTrue($grid[0][2]['active']); // Dot 3
        $this->assertEquals(3, $grid[0][2]['order']);

        $this->assertTrue($grid[1][2]['active']); // Dot 6
        $this->assertEquals(4, $grid[1][2]['order']);

        $this->assertTrue($grid[2][2]['active']); // Dot 9
        $this->assertEquals(5, $grid[2][2]['order']);

        // Check inactive dots
        $this->assertFalse($grid[1][0]['active']); // Dot 4
        $this->assertFalse($grid[1][1]['active']); // Dot 5
    }

    /**
     * Test pattern complexity scoring
     */
    public function test_calculates_pattern_complexity()
    {
        // Simple 4-dot pattern
        $simplePattern = '1-2-5-8';
        $simpleComplexity = PatternValidationService::getPatternComplexity($simplePattern);

        // Complex 7-dot pattern with direction changes
        $complexPattern = '1-2-3-6-9-8-7';
        $complexComplexity = PatternValidationService::getPatternComplexity($complexPattern);

        $this->assertGreaterThan($simpleComplexity, $complexComplexity);
    }

    /**
     * Test pattern type determination
     */
    public function test_determines_pattern_type_correctly()
    {
        // Text only
        $textOnly = PatternValidationService::determinePatternType('password123', null);
        $this->assertEquals('text', $textOnly);

        // Visual only
        $visualOnly = PatternValidationService::determinePatternType(null, '1-2-3-6-9');
        $this->assertEquals('visual', $visualOnly);

        // Both
        $both = PatternValidationService::determinePatternType('password123', '1-2-3-6-9');
        $this->assertEquals('both', $both);

        // None
        $none = PatternValidationService::determinePatternType(null, null);
        $this->assertEquals('none', $none);

        // Empty strings should be treated as null
        $emptyStrings = PatternValidationService::determinePatternType('', '');
        $this->assertEquals('none', $emptyStrings);
    }

    /**
     * Test pattern connections generation
     */
    public function test_generates_pattern_connections()
    {
        $pattern = '1-2-3-6-9';
        $connections = PatternValidationService::getPatternConnections($pattern);

        $this->assertCount(4, $connections); // 5 dots = 4 connections

        // Check first connection (1 to 2)
        $this->assertEquals(['row' => 0, 'col' => 0], $connections[0]['from']);
        $this->assertEquals(['row' => 0, 'col' => 1], $connections[0]['to']);

        // Check last connection (6 to 9)
        $this->assertEquals(['row' => 1, 'col' => 2], $connections[3]['from']);
        $this->assertEquals(['row' => 2, 'col' => 2], $connections[3]['to']);
    }

    /**
     * Test pattern hashing
     */
    public function test_hashes_patterns_consistently()
    {
        $pattern = '1-2-3-6-9';
        $hash1 = PatternValidationService::hashPattern($pattern);
        $hash2 = PatternValidationService::hashPattern($pattern);

        $this->assertEquals($hash1, $hash2);
        $this->assertNotEmpty($hash1);

        // Different patterns should have different hashes
        $differentPattern = '1-4-7-8-9';
        $differentHash = PatternValidationService::hashPattern($differentPattern);
        $this->assertNotEquals($hash1, $differentHash);
    }

    /**
     * Test edge cases and error handling
     */
    public function test_handles_edge_cases()
    {
        // Empty pattern
        $result = PatternValidationService::validateVisualPattern('');
        $this->assertFalse($result['valid']);

        // Single dot
        $result = PatternValidationService::validateVisualPattern('5');
        $this->assertFalse($result['valid']);

        // Invalid format
        $result = PatternValidationService::validateVisualPattern('1,2,3,4');
        $this->assertFalse($result['valid']);

        // Pattern with spaces
        $result = PatternValidationService::validateVisualPattern('1 - 2 - 3 - 4');
        $this->assertFalse($result['valid']);
    }

    /**
     * Test maximum pattern length
     */
    public function test_validates_maximum_pattern_length()
    {
        // All 9 dots - should be valid
        $maxPattern = '1-2-3-6-9-8-7-4-5';
        $result = PatternValidationService::validateVisualPattern($maxPattern);
        $this->assertTrue($result['valid']);

        // Check complexity score for maximum pattern
        $complexity = PatternValidationService::getPatternComplexity($maxPattern);
        $this->assertEquals(10, $complexity); // 9 dots + direction changes = max 10
    }

    /**
     * Test pattern normalization
     */
    public function test_normalizes_pattern_input()
    {
        // Pattern with extra spaces and different separators should be handled
        // This test assumes the service has normalization logic
        $patterns = [
            '1-2-3-6-9',
            ' 1-2-3-6-9 ',
            '1 - 2 - 3 - 6 - 9'
        ];

        foreach ($patterns as $pattern) {
            $result = PatternValidationService::validateVisualPattern(trim(str_replace(' ', '', $pattern)));
            $this->assertTrue($result['valid'], "Pattern '{$pattern}' should be valid after normalization");
        }
    }
}
