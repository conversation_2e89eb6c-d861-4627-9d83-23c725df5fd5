<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_sessions', function (Blueprint $table) {
            $table->id();
            
            // Session identification
            $table->string('session_id')->unique();
            $table->string('customer_phone', 20)->index();
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
            
            // Session state
            $table->enum('status', ['active', 'expired', 'completed', 'abandoned'])->default('active')->index();
            $table->string('current_flow')->nullable(); // 'ticket_query', 'appointment_booking', 'general_chat'
            $table->string('current_step')->nullable(); // Current step in the flow
            $table->json('flow_data')->nullable(); // Store flow-specific data
            
            // Context preservation
            $table->json('context')->nullable(); // Store conversation context
            $table->string('last_intent')->nullable();
            $table->text('last_message')->nullable();
            $table->timestamp('last_activity_at')->nullable();
            
            // User preferences
            $table->string('preferred_language', 5)->default('ar');
            $table->boolean('notifications_enabled')->default(true);
            $table->json('user_preferences')->nullable();
            
            // Session metadata
            $table->timestamp('started_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->integer('message_count')->default(0);
            $table->integer('auto_responses_count')->default(0);
            
            // Rate limiting
            $table->timestamp('last_message_at')->nullable();
            $table->integer('messages_in_current_minute')->default(0);
            $table->timestamp('rate_limit_reset_at')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['customer_phone', 'status']);
            $table->index(['expires_at', 'status']);
            $table->index(['current_flow', 'current_step']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_sessions');
    }
};
