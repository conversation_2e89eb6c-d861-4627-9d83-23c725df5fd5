<?php

namespace App\Http\Controllers;

use App\Models\Brand;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class BrandController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = Brand::withCount('repairTickets as repair_tickets_count');

        if ($request->filled('search')) {
            $query->search($request->search);
        }

        $brands = $query->orderBy('name')->paginate(15);

        return view('brands.index', compact('brands'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('brands.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:brands',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        $validated['is_active'] = $request->has('is_active');

        Brand::create($validated);

        return redirect()->route('brands.index')
                        ->with('success', 'تم إنشاء الماركة بنجاح.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Brand $brand): View
    {
        return view('brands.show', compact('brand'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Brand $brand): View
    {
        return view('brands.edit', compact('brand'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Brand $brand): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:brands,name,' . $brand->id,
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        $validated['is_active'] = $request->has('is_active');

        $brand->update($validated);

        return redirect()->route('brands.index')
                        ->with('success', 'تم تحديث الماركة بنجاح.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Brand $brand): RedirectResponse
    {
        if ($brand->repairTickets()->count() > 0) {
            return redirect()->route('brands.index')
                            ->with('error', 'لا يمكن حذف الماركة لوجود بطاقات إصلاح مرتبطة بها.');
        }

        $brand->delete();

        return redirect()->route('brands.index')
                        ->with('success', 'تم حذف الماركة بنجاح.');
    }
}
