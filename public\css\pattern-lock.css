/**
 * Pattern Lock Component Styles
 * Responsive design for both desktop and mobile devices
 * Arabic/RTL language support included
 */

.pattern-lock-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #dee2e6;
}

.pattern-lock-header {
    text-align: center;
    margin-bottom: 1rem;
}

.pattern-lock-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.pattern-lock-instructions {
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.4;
}

.pattern-lock-canvas-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.pattern-lock-canvas {
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: border-color 0.3s ease;
}

.pattern-lock-canvas:hover {
    border-color: #007bff;
}

.pattern-lock-canvas.drawing {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.pattern-lock-controls {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    justify-content: center;
}

.pattern-lock-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
    color: #495057;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pattern-lock-btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
}

.pattern-lock-btn.primary {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.pattern-lock-btn.primary:hover {
    background: #0056b3;
    border-color: #0056b3;
}

.pattern-lock-btn.danger {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.pattern-lock-btn.danger:hover {
    background: #c82333;
    border-color: #c82333;
}

.pattern-lock-status {
    padding: 0.75rem;
    border-radius: 6px;
    font-size: 0.9rem;
    text-align: center;
    margin-top: 0.5rem;
    transition: all 0.3s ease;
}

.pattern-lock-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.pattern-lock-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.pattern-lock-status.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.pattern-lock-status.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Pattern Display (Read-only) */
.pattern-display-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #dee2e6;
}

.pattern-display-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.pattern-display-canvas {
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pattern-display-controls {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    justify-content: center;
}

.pattern-replay-btn {
    padding: 0.5rem 1rem;
    background: #28a745;
    color: white;
    border: 1px solid #28a745;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.pattern-replay-btn:hover {
    background: #218838;
    border-color: #218838;
}

.pattern-replay-btn:disabled {
    background: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
}

/* Security Pattern Type Selector */
.security-pattern-selector {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
}

.pattern-type-options {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.pattern-type-option {
    flex: 1;
    min-width: 200px;
}

.pattern-type-radio {
    display: none;
}

.pattern-type-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.pattern-type-label:hover {
    border-color: #007bff;
    background: #f8f9fa;
}

.pattern-type-radio:checked + .pattern-type-label {
    border-color: #007bff;
    background: #e7f3ff;
    color: #007bff;
}

.pattern-type-icon {
    font-size: 1.5rem;
    width: 2rem;
    text-align: center;
}

.pattern-type-info h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.pattern-type-info p {
    margin: 0;
    font-size: 0.85rem;
    color: #6c757d;
}

.pattern-type-radio:checked + .pattern-type-label .pattern-type-info p {
    color: #0056b3;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pattern-lock-container {
        padding: 0.75rem;
    }

    .pattern-lock-canvas {
        max-width: 280px;
        max-height: 280px;
    }

    .pattern-lock-controls {
        gap: 0.5rem;
    }

    .pattern-lock-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }

    .pattern-type-options {
        flex-direction: column;
    }

    .pattern-type-option {
        min-width: auto;
    }

    .pattern-type-label {
        padding: 0.75rem;
    }
}

@media (max-width: 480px) {
    .pattern-lock-canvas {
        max-width: 250px;
        max-height: 250px;
    }

    .pattern-lock-title {
        font-size: 1rem;
    }

    .pattern-lock-instructions {
        font-size: 0.85rem;
    }
}

/* Animation for pattern replay */
@keyframes dot-activate {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.pattern-dot-animate {
    animation: dot-activate 0.3s ease-in-out;
}

/* Loading state */
.pattern-lock-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    color: #6c757d;
}

.pattern-lock-spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid #dee2e6;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Arabic/RTL Language Support */
[dir="rtl"] .pattern-lock-container {
    direction: rtl;
}

[dir="rtl"] .pattern-lock-title {
    flex-direction: row-reverse;
}

[dir="rtl"] .pattern-lock-controls {
    flex-direction: row-reverse;
}

[dir="rtl"] .pattern-lock-controls .btn {
    margin-left: 0.5rem;
    margin-right: 0;
}

[dir="rtl"] .pattern-lock-controls .btn:first-child {
    margin-left: 0;
}

/* Arabic Text Styling */
.pattern-lock-container[lang="ar"],
.pattern-lock-container .arabic-text {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
}

.pattern-lock-container .arabic-text {
    font-size: 0.95rem;
    line-height: 1.6;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 576px) {
    .pattern-lock-container {
        padding: 0.75rem;
        gap: 0.75rem;
        margin: 0.5rem 0;
    }

    .pattern-lock-title {
        font-size: 1rem;
    }

    .pattern-lock-instructions {
        font-size: 0.85rem;
    }

    .pattern-lock-canvas {
        max-width: 100%;
        height: auto;
    }

    .pattern-lock-controls {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
    }

    .pattern-lock-controls .btn {
        width: 100%;
        margin: 0;
    }

    .pattern-display-container .card {
        margin-bottom: 1rem;
    }

    .pattern-display-controls {
        flex-direction: column;
        gap: 0.5rem;
    }

    .pattern-display-controls .btn {
        width: 100%;
    }
}

/* Extra Small Devices */
@media (max-width: 400px) {
    .pattern-lock-container {
        padding: 0.5rem;
        border-radius: 8px;
    }

    .pattern-lock-canvas {
        border-width: 1px;
    }

    .pattern-lock-title {
        font-size: 0.95rem;
    }

    .pattern-lock-instructions {
        font-size: 0.8rem;
    }
}

/* High DPI/Retina Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .pattern-lock-canvas {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .pattern-lock-container {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }

    .pattern-lock-canvas {
        background: #1a202c;
        border-color: #4a5568;
    }

    .pattern-lock-title {
        color: #e2e8f0;
    }

    .pattern-lock-instructions {
        color: #a0aec0;
    }

    .pattern-display-container .card {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }

    .pattern-display-container .card-header {
        background: #4a5568;
        border-color: #4a5568;
    }
}

/* Print Styles */
@media print {
    .pattern-lock-container {
        background: white;
        border: 1px solid #000;
        box-shadow: none;
    }

    .pattern-lock-controls {
        display: none;
    }

    .pattern-display-controls {
        display: none;
    }

    .pattern-lock-canvas {
        background: white;
        border: 1px solid #000;
    }
}
