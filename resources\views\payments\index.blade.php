@extends('layouts.app')

@section('title', 'إدارة المدفوعات')

@push('styles')
<style>
.payments-header {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.875rem;
    text-transform: uppercase;
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.3;
    float: left;
    margin-top: -0.5rem;
}

.filter-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.payments-table {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    overflow: hidden;
}

.table th {
    background: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.payment-number {
    font-weight: 600;
    color: #28a745;
}

.invoice-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.invoice-link:hover {
    text-decoration: underline;
}

.customer-info {
    display: flex;
    flex-direction: column;
}

.customer-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.customer-phone {
    color: #6c757d;
    font-size: 0.875rem;
}

.amount-display {
    font-weight: 600;
    font-size: 1.1rem;
    color: #28a745;
}

.payment-method-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
}

.method-cash {
    background: #d4edda;
    color: #155724;
}

.method-card {
    background: #cce5ff;
    color: #0066cc;
}

.method-bank_transfer {
    background: #fff3cd;
    color: #856404;
}

.method-check {
    background: #e2e3e5;
    color: #6c757d;
}

.method-mobile_payment {
    background: #f8d7da;
    color: #721c24;
}

.method-other {
    background: #e2e3e5;
    color: #6c757d;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.status-cancelled {
    background: #e2e3e5;
    color: #6c757d;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.btn-action {
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-view {
    background: #17a2b8;
    color: white;
}

.btn-edit {
    background: #ffc107;
    color: #212529;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.3;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .payments-header {
        padding: 1rem;
    }
    
    .filter-card {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="payments-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">إدارة المدفوعات</h1>
                <p class="mb-0 opacity-75">تتبع وإدارة جميع المدفوعات</p>
            </div>
            <div>
                <a href="{{ route('payments.create') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>تسجيل دفعة جديدة
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon text-success">
                <i class="fas fa-credit-card"></i>
            </div>
            <div class="stat-value text-success">@arabicNumber($stats['total_payments'])</div>
            <div class="stat-label">إجمالي المدفوعات</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-primary">
                <i class="fas fa-calendar-day"></i>
            </div>
            <div class="stat-value text-primary">@arabicNumber($stats['today_payments'])</div>
            <div class="stat-label">مدفوعات اليوم</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-info">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-value text-info">@arabicCurrency($stats['total_amount'])</div>
            <div class="stat-label">إجمالي المبالغ</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-warning">
                <i class="fas fa-hourglass-half"></i>
            </div>
            <div class="stat-value text-warning">@arabicNumber($stats['pending_payments'])</div>
            <div class="stat-label">مدفوعات معلقة</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="GET" action="{{ route('payments.index') }}" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">البحث</label>
                <input type="text" 
                       class="form-control" 
                       id="search" 
                       name="search" 
                       value="{{ request('search') }}"
                       placeholder="رقم الدفعة، رقم الفاتورة، اسم العميل">
            </div>
            
            <div class="col-md-2">
                <label for="payment_method" class="form-label">طريقة الدفع</label>
                <select class="form-select" id="payment_method" name="payment_method">
                    <option value="">جميع الطرق</option>
                    <option value="cash" {{ request('payment_method') === 'cash' ? 'selected' : '' }}>نقدي</option>
                    <option value="card" {{ request('payment_method') === 'card' ? 'selected' : '' }}>بطاقة</option>
                    <option value="bank_transfer" {{ request('payment_method') === 'bank_transfer' ? 'selected' : '' }}>تحويل بنكي</option>
                    <option value="check" {{ request('payment_method') === 'check' ? 'selected' : '' }}>شيك</option>
                    <option value="mobile_payment" {{ request('payment_method') === 'mobile_payment' ? 'selected' : '' }}>دفع عبر الجوال</option>
                    <option value="other" {{ request('payment_method') === 'other' ? 'selected' : '' }}>أخرى</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>مكتمل</option>
                    <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>معلق</option>
                    <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>فاشل</option>
                    <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>ملغي</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" 
                       class="form-control" 
                       id="date_from" 
                       name="date_from" 
                       value="{{ request('date_from') }}">
            </div>
            
            <div class="col-md-2">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" 
                       class="form-control" 
                       id="date_to" 
                       name="date_to" 
                       value="{{ request('date_to') }}">
            </div>
            
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-1">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="{{ route('payments.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Payments Table -->
    <div class="payments-table">
        @if($payments->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>رقم الدفعة</th>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>المبلغ</th>
                            <th>تاريخ الدفع</th>
                            <th>طريقة الدفع</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($payments as $payment)
                        <tr>
                            <td>
                                <span class="payment-number">{{ $payment->payment_number }}</span>
                            </td>
                            <td>
                                <a href="{{ route('invoices.show', $payment->invoice) }}" class="invoice-link">
                                    {{ $payment->invoice->invoice_number }}
                                </a>
                            </td>
                            <td>
                                <div class="customer-info">
                                    <div class="customer-name">{{ $payment->customer->name }}</div>
                                    <div class="customer-phone">{{ $payment->customer->phone_number }}</div>
                                </div>
                            </td>
                            <td>
                                <span class="amount-display">@arabicCurrency($payment->amount)</span>
                            </td>
                            <td>{{ $payment->payment_date->format('Y-m-d') }}</td>
                            <td>
                                <span class="payment-method-badge method-{{ $payment->payment_method }}">
                                    <i class="{{ $payment->payment_method_icon }} me-1"></i>
                                    {{ $payment->payment_method_display }}
                                </span>
                            </td>
                            <td>
                                <span class="status-badge status-{{ $payment->status }}">
                                    {{ $payment->status_display }}
                                </span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="{{ route('payments.show', $payment) }}" 
                                       class="btn-action btn-view" 
                                       title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    @if($payment->status === 'pending')
                                        <a href="{{ route('payments.edit', $payment) }}" 
                                           class="btn-action btn-edit" 
                                           title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        
                                        <form method="POST" 
                                              action="{{ route('payments.destroy', $payment) }}" 
                                              style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" 
                                                    class="btn-action btn-delete" 
                                                    title="حذف"
                                                    onclick="return confirm('هل تريد حذف هذه الدفعة؟')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center p-3">
                {{ $payments->withQueryString()->links() }}
            </div>
        @else
            <div class="empty-state">
                <i class="fas fa-credit-card"></i>
                <h5>لا توجد مدفوعات</h5>
                <p>لم يتم العثور على مدفوعات بناءً على المعايير المحددة.</p>
                @if(request()->hasAny(['search', 'payment_method', 'status', 'date_from', 'date_to']))
                    <a href="{{ route('payments.index') }}" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>عرض جميع المدفوعات
                    </a>
                @else
                    <a href="{{ route('payments.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>تسجيل أول دفعة
                    </a>
                @endif
            </div>
        @endif
    </div>
</div>
@endsection
