<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('purchase_order_id')->constrained()->onDelete('cascade');
            $table->foreignId('inventory_item_id')->constrained()->onDelete('restrict');
            
            // Quantities
            $table->integer('quantity_ordered'); // Quantity ordered
            $table->integer('quantity_received')->default(0); // Quantity received so far
            $table->integer('quantity_pending')->virtualAs('quantity_ordered - quantity_received'); // Calculated field
            
            // Pricing
            $table->decimal('unit_cost', 10, 2); // Cost per unit
            $table->decimal('total_cost', 10, 2); // Total cost for this line item
            $table->decimal('discount_percentage', 5, 2)->default(0); // Discount percentage
            $table->decimal('discount_amount', 10, 2)->default(0); // Discount amount
            
            // Item Details (snapshot at time of order)
            $table->string('item_name'); // Item name at time of order
            $table->string('item_sku'); // Item SKU at time of order
            $table->text('item_description')->nullable(); // Item description
            
            // Delivery Information
            $table->date('expected_delivery_date')->nullable(); // Expected delivery for this item
            $table->date('actual_delivery_date')->nullable(); // Actual delivery date
            
            // Quality Control
            $table->enum('quality_status', ['pending', 'approved', 'rejected', 'partial'])->default('pending');
            $table->integer('quantity_approved')->default(0); // Quantity approved after inspection
            $table->integer('quantity_rejected')->default(0); // Quantity rejected
            $table->text('quality_notes')->nullable(); // Quality inspection notes
            
            // Additional Information
            $table->text('notes')->nullable(); // Line item notes
            $table->string('batch_number')->nullable(); // Batch/lot number
            $table->date('expiry_date')->nullable(); // Expiry date if applicable
            
            $table->timestamps();

            // Indexes
            $table->index('purchase_order_id');
            $table->index('inventory_item_id');
            $table->index('quality_status');
            $table->index('expected_delivery_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_order_items');
    }
};
