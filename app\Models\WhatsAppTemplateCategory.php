<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class WhatsAppTemplateCategory extends Model
{
    use HasFactory;

    protected $table = 'whatsapp_template_categories';

    protected $fillable = [
        'name',
        'name_ar',
        'slug',
        'description',
        'description_ar',
        'whatsapp_category',
        'icon',
        'color',
        'sort_order',
        'is_active',
        'requires_approval',
        'allowed_variables',
        'template_count',
        'usage_count',
        'last_used_at',
    ];

    protected $casts = [
        'allowed_variables' => 'array',
        'is_active' => 'boolean',
        'requires_approval' => 'boolean',
        'last_used_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            if (empty($category->slug)) {
                $category->slug = Str::slug($category->name);
            }
        });

        static::updating(function ($category) {
            if ($category->isDirty('name') && empty($category->slug)) {
                $category->slug = Str::slug($category->name);
            }
        });
    }

    /**
     * Get the templates for this category.
     */
    public function templates(): HasMany
    {
        return $this->hasMany(WhatsAppMessageTemplate::class, 'category_id');
    }

    /**
     * Get active templates for this category.
     */
    public function activeTemplates(): HasMany
    {
        return $this->templates()->where('is_active', true);
    }

    /**
     * Get approved templates for this category.
     */
    public function approvedTemplates(): HasMany
    {
        return $this->templates()->where('status', 'APPROVED');
    }

    /**
     * Scope for active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered categories.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Scope by WhatsApp category type.
     */
    public function scopeByWhatsAppCategory($query, $category)
    {
        return $query->where('whatsapp_category', $category);
    }

    /**
     * Get display name based on locale.
     */
    public function getDisplayNameAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : $this->name;
    }

    /**
     * Get display description based on locale.
     */
    public function getDisplayDescriptionAttribute(): ?string
    {
        return app()->getLocale() === 'ar' ? $this->description_ar : $this->description;
    }

    /**
     * Update template count.
     */
    public function updateTemplateCount(): void
    {
        $this->update([
            'template_count' => $this->templates()->count()
        ]);
    }

    /**
     * Increment usage count.
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Get category statistics.
     */
    public function getStatsAttribute(): array
    {
        return [
            'total_templates' => $this->template_count,
            'active_templates' => $this->activeTemplates()->count(),
            'approved_templates' => $this->approvedTemplates()->count(),
            'usage_count' => $this->usage_count,
            'last_used' => $this->last_used_at?->diffForHumans(),
        ];
    }

    /**
     * Check if category requires approval for templates.
     */
    public function requiresApproval(): bool
    {
        return $this->requires_approval;
    }

    /**
     * Get allowed variables for this category.
     */
    public function getAllowedVariables(): array
    {
        return $this->allowed_variables ?? [];
    }

    /**
     * Add allowed variable.
     */
    public function addAllowedVariable(string $variable, string $description = null): void
    {
        $variables = $this->getAllowedVariables();
        $variables[$variable] = $description ?? $variable;

        $this->update(['allowed_variables' => $variables]);
    }

    /**
     * Remove allowed variable.
     */
    public function removeAllowedVariable(string $variable): void
    {
        $variables = $this->getAllowedVariables();
        unset($variables[$variable]);

        $this->update(['allowed_variables' => $variables]);
    }

    /**
     * Get WhatsApp category display name.
     */
    public function getWhatsAppCategoryDisplayAttribute(): string
    {
        $categories = [
            'TRANSACTIONAL' => 'معاملات',
            'MARKETING' => 'تسويق',
            'UTILITY' => 'خدمات',
            'AUTHENTICATION' => 'مصادقة',
        ];

        return $categories[$this->whatsapp_category] ?? $this->whatsapp_category;
    }

    /**
     * Get category color with fallback.
     */
    public function getColorAttribute($value): string
    {
        return $value ?: '#007bff';
    }

    /**
     * Get category icon with fallback.
     */
    public function getIconAttribute($value): string
    {
        return $value ?: 'bi-chat-dots';
    }

    /**
     * Create default categories.
     */
    public static function createDefaults(): void
    {
        $categories = [
            [
                'name' => 'Status Updates',
                'name_ar' => 'تحديثات الحالة',
                'slug' => 'status-updates',
                'description' => 'Repair status update notifications',
                'description_ar' => 'إشعارات تحديث حالة الإصلاح',
                'whatsapp_category' => 'TRANSACTIONAL',
                'icon' => 'bi-arrow-repeat',
                'color' => '#28a745',
                'sort_order' => 1,
                'allowed_variables' => [
                    'customer_name' => 'اسم العميل',
                    'ticket_number' => 'رقم التذكرة',
                    'device_info' => 'معلومات الجهاز',
                    'status' => 'الحالة',
                    'estimated_date' => 'التاريخ المتوقع'
                ]
            ],
            [
                'name' => 'Appointments',
                'name_ar' => 'المواعيد',
                'slug' => 'appointments',
                'description' => 'Appointment confirmations and reminders',
                'description_ar' => 'تأكيدات وتذكيرات المواعيد',
                'whatsapp_category' => 'TRANSACTIONAL',
                'icon' => 'bi-calendar-check',
                'color' => '#007bff',
                'sort_order' => 2,
                'allowed_variables' => [
                    'customer_name' => 'اسم العميل',
                    'appointment_date' => 'تاريخ الموعد',
                    'appointment_time' => 'وقت الموعد',
                    'service_type' => 'نوع الخدمة'
                ]
            ],
            [
                'name' => 'Payment Reminders',
                'name_ar' => 'تذكيرات الدفع',
                'slug' => 'payment-reminders',
                'description' => 'Payment due and overdue notifications',
                'description_ar' => 'إشعارات استحقاق ومتأخرات الدفع',
                'whatsapp_category' => 'TRANSACTIONAL',
                'icon' => 'bi-credit-card',
                'color' => '#ffc107',
                'sort_order' => 3,
                'allowed_variables' => [
                    'customer_name' => 'اسم العميل',
                    'amount' => 'المبلغ',
                    'due_date' => 'تاريخ الاستحقاق',
                    'ticket_number' => 'رقم التذكرة'
                ]
            ],
            [
                'name' => 'Business Information',
                'name_ar' => 'معلومات الأعمال',
                'slug' => 'business-info',
                'description' => 'Business hours, location, and contact information',
                'description_ar' => 'ساعات العمل والموقع ومعلومات الاتصال',
                'whatsapp_category' => 'UTILITY',
                'icon' => 'bi-info-circle',
                'color' => '#6c757d',
                'sort_order' => 4,
                'requires_approval' => false,
                'allowed_variables' => [
                    'business_name' => 'اسم الأعمال',
                    'business_hours' => 'ساعات العمل',
                    'address' => 'العنوان',
                    'phone' => 'الهاتف'
                ]
            ],
            [
                'name' => 'Marketing',
                'name_ar' => 'التسويق',
                'slug' => 'marketing',
                'description' => 'Promotional messages and offers',
                'description_ar' => 'الرسائل الترويجية والعروض',
                'whatsapp_category' => 'MARKETING',
                'icon' => 'bi-megaphone',
                'color' => '#dc3545',
                'sort_order' => 5,
                'allowed_variables' => [
                    'customer_name' => 'اسم العميل',
                    'offer_details' => 'تفاصيل العرض',
                    'discount_amount' => 'مبلغ الخصم',
                    'expiry_date' => 'تاريخ الانتهاء'
                ]
            ]
        ];

        foreach ($categories as $categoryData) {
            static::firstOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );
        }
    }
}
