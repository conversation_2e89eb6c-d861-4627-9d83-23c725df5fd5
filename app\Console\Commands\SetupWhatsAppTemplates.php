<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\WhatsAppTemplateSeeder;

class SetupWhatsAppTemplates extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'whatsapp:setup-templates 
                            {--force : Force recreation of templates}
                            {--approve : Auto-approve all templates}';

    /**
     * The console command description.
     */
    protected $description = 'Setup WhatsApp message templates and categories';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Setting up WhatsApp message templates...');

        try {
            // Run the seeder
            $seeder = new WhatsAppTemplateSeeder();
            $seeder->run();

            $this->info('✅ Templates and categories created successfully!');

            // Auto-approve if requested
            if ($this->option('approve')) {
                $this->info('📋 Auto-approving templates...');
                $this->call('whatsapp:approve-templates', ['--all' => true]);
            }

            $this->newLine();
            $this->info('📊 Template Summary:');
            
            $categories = \App\Models\WhatsAppTemplateCategory::withCount('templates')->get();
            foreach ($categories as $category) {
                $this->line("  • {$category->name_ar}: {$category->templates_count} templates");
            }

            $this->newLine();
            $this->info('🎯 Next Steps:');
            $this->line('1. Review templates in admin dashboard: /whatsapp/templates');
            $this->line('2. Customize template content as needed');
            $this->line('3. Submit templates to WhatsApp for approval');
            $this->line('4. Configure business profile: php artisan whatsapp:setup-profile');

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ Failed to setup templates: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
