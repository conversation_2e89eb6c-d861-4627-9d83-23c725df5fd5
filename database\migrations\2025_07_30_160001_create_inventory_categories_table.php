<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Category name (e.g., "شاشات", "بطاريات", "كاميرات")
            $table->string('slug')->unique(); // URL-friendly identifier
            $table->text('description')->nullable(); // Category description
            $table->string('icon')->nullable(); // Icon class or image path
            $table->string('color')->default('#6B7280'); // Category color for UI
            $table->foreignId('parent_id')->nullable()->constrained('inventory_categories')->onDelete('cascade'); // For subcategories
            $table->integer('sort_order')->default(0); // Display order
            $table->boolean('is_active')->default(true); // Active status
            $table->timestamps();

            // Indexes
            $table->index('name');
            $table->index('slug');
            $table->index('parent_id');
            $table->index('is_active');
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_categories');
    }
};
