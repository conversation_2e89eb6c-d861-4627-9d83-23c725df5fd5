<?php

namespace App\Http\Controllers;

use App\Models\DeviceCondition;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Str;

class DeviceConditionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): View
    {
        $deviceConditions = DeviceCondition::withCount('repairTickets')
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        return view('device-conditions.index', compact('deviceConditions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('device-conditions.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:device_conditions,name',
            'description' => 'nullable|string|max:1000',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ], [
            'name.required' => __('validation.required', ['attribute' => __('app.device_conditions.name')]),
            'name.unique' => __('validation.unique', ['attribute' => __('app.device_conditions.name')]),
        ]);

        // Set defaults
        $validated['is_active'] = $request->has('is_active');
        $validated['sort_order'] = $validated['sort_order'] ?? 0;

        // Generate slug
        $validated['slug'] = Str::slug($validated['name']);

        DeviceCondition::create($validated);

        return redirect()->route('device-conditions.index')
                        ->with('success', __('app.device_conditions.created_successfully'));
    }

    /**
     * Display the specified resource.
     */
    public function show(DeviceCondition $deviceCondition): View
    {
        $deviceCondition->load(['repairTickets.customer', 'repairTickets.brand']);
        $deviceCondition->loadCount('repairTickets');

        return view('device-conditions.show', compact('deviceCondition'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DeviceCondition $deviceCondition): View
    {
        $deviceCondition->loadCount('repairTickets');

        return view('device-conditions.edit', compact('deviceCondition'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, DeviceCondition $deviceCondition): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:device_conditions,name,' . $deviceCondition->id,
            'description' => 'nullable|string|max:1000',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ], [
            'name.required' => __('validation.required', ['attribute' => __('app.device_conditions.name')]),
            'name.unique' => __('validation.unique', ['attribute' => __('app.device_conditions.name')]),
        ]);

        // Set defaults
        $validated['is_active'] = $request->has('is_active');
        $validated['sort_order'] = $validated['sort_order'] ?? 0;

        $deviceCondition->update($validated);

        return redirect()->route('device-conditions.show', $deviceCondition)
                        ->with('success', __('app.device_conditions.updated_successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DeviceCondition $deviceCondition): RedirectResponse
    {
        // Check if condition is being used by any tickets
        if ($deviceCondition->repairTickets()->count() > 0) {
            return redirect()->route('device-conditions.index')
                            ->with('error', __('app.device_conditions.cannot_delete_has_tickets'));
        }

        $deviceCondition->delete();

        return redirect()->route('device-conditions.index')
                        ->with('success', __('app.device_conditions.deleted_successfully'));
    }
}
