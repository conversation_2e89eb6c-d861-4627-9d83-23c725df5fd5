<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\Brand;
use App\Models\RepairStatus;
use App\Models\DeviceCondition;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairTicket>
 */
class RepairTicketFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $receivedDate = $this->faker->dateTimeBetween('-30 days', 'now');
        $hasPattern = $this->faker->boolean(70); // 70% chance of having a pattern

        return [
            'ticket_number' => 'NJ' . date('Ymd') . str_pad($this->faker->unique()->numberBetween(1, 9999), 4, '0', STR_PAD_LEFT),
            'customer_id' => Customer::factory(),
            'brand_id' => Brand::factory(),
            'device_model' => $this->faker->randomElement([
                'iPhone 13', 'iPhone 12', 'iPhone 11', 'Samsung Galaxy S21',
                'Samsung Galaxy S20', 'Huawei P30', 'MacBook Pro', 'iPad Air'
            ]),
            'reported_problem' => $this->faker->randomElement([
                'Screen is cracked and not responding',
                'Battery drains very quickly',
                'Device won\'t turn on',
                'Water damage - phone got wet',
                'Charging port not working',
                'Camera not functioning properly',
                'Speaker has no sound',
                'Touch screen not responsive'
            ]),
            'device_pattern' => $hasPattern,
            'security_pattern' => $hasPattern ? $this->faker->randomElement([
                '1234', '0000', '1111', '2580', '1357',
                'pattern123', 'unlock', 'password'
            ]) : null,
            'device_condition_id' => DeviceCondition::factory(),
            'technician_comments' => $this->faker->optional(0.6)->sentence(),
            'repair_status_id' => RepairStatus::factory(),
            'received_date' => $receivedDate,
            'estimated_completion_date' => $this->faker->optional(0.8)->dateTimeBetween($receivedDate, '+14 days'),
            'completed_date' => $this->faker->optional(0.3)->dateTimeBetween($receivedDate, 'now'),
            'estimated_cost' => $this->faker->randomFloat(2, 50, 500),
            'final_cost' => $this->faker->optional(0.4)->randomFloat(2, 50, 600),
            'created_by' => User::factory(),
            'assigned_technician_id' => $this->faker->optional(0.7)->randomElement([
                User::factory(), User::factory(), null
            ]),
        ];
    }

    /**
     * Indicate that the ticket is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'completed_date' => $this->faker->dateTimeBetween($attributes['received_date'], 'now'),
            'final_cost' => $this->faker->randomFloat(2, 50, 600),
            'repair_status_id' => RepairStatus::factory()->state(['name' => 'Completed']),
        ]);
    }

    /**
     * Indicate that the ticket is overdue.
     */
    public function overdue(): static
    {
        return $this->state(fn (array $attributes) => [
            'received_date' => $this->faker->dateTimeBetween('-20 days', '-10 days'),
            'estimated_completion_date' => $this->faker->dateTimeBetween('-5 days', '-1 day'),
            'completed_date' => null,
            'repair_status_id' => RepairStatus::factory()->state(['name' => 'In Progress']),
        ]);
    }

    /**
     * Indicate that the ticket has high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'estimated_completion_date' => $this->faker->dateTimeBetween('now', '+3 days'),
        ]);
    }

    /**
     * Indicate that the ticket has security pattern.
     */
    public function withSecurityPattern(): static
    {
        return $this->state(fn (array $attributes) => [
            'device_pattern' => true,
            'security_pattern' => $this->faker->randomElement([
                '1234', '0000', '9876', '2580', '1357',
                'unlock123', 'mypassword', 'secure123'
            ]),
        ]);
    }

    /**
     * Indicate that the ticket has no security pattern.
     */
    public function withoutSecurityPattern(): static
    {
        return $this->state(fn (array $attributes) => [
            'device_pattern' => false,
            'security_pattern' => null,
        ]);
    }
}
