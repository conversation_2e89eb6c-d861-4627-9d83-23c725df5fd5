<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class SalesAnalytics extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'analytics_date',
        'period_type',
        'total_sales',
        'total_cost',
        'total_profit',
        'profit_margin',
        'invoices_count',
        'customers_count',
        'average_invoice_value',
        'sales_by_channel',
        'sales_by_category',
        'payment_methods_breakdown',
        'top_selling_items',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'analytics_date' => 'date',
        'total_sales' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'total_profit' => 'decimal:2',
        'profit_margin' => 'decimal:2',
        'average_invoice_value' => 'decimal:2',
        'sales_by_channel' => 'array',
        'sales_by_category' => 'array',
        'payment_methods_breakdown' => 'array',
        'top_selling_items' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Generate analytics for a specific date and period.
     */
    public static function generateAnalytics(Carbon $date, string $periodType): self
    {
        $startDate = self::getStartDateForPeriod($date, $periodType);
        $endDate = self::getEndDateForPeriod($date, $periodType);

        // Get invoices for the period
        $invoices = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->with(['items.inventoryItem', 'payments'])
            ->get();

        // Calculate metrics
        $totalSales = $invoices->sum('total_amount');
        $totalCost = $invoices->sum('cost_of_goods');
        $totalProfit = $totalSales - $totalCost;
        $profitMargin = $totalSales > 0 ? ($totalProfit / $totalSales) * 100 : 0;
        $invoicesCount = $invoices->count();
        $customersCount = $invoices->pluck('customer_id')->unique()->count();
        $averageInvoiceValue = $invoicesCount > 0 ? $totalSales / $invoicesCount : 0;

        // Sales by channel
        $salesByChannel = $invoices->groupBy('sales_channel')
            ->map(function ($channelInvoices) {
                return [
                    'count' => $channelInvoices->count(),
                    'total' => $channelInvoices->sum('total_amount'),
                    'percentage' => 0 // Will be calculated after
                ];
            })->toArray();

        // Calculate percentages for channels
        foreach ($salesByChannel as $channel => &$data) {
            $data['percentage'] = $totalSales > 0 ? ($data['total'] / $totalSales) * 100 : 0;
        }

        // Payment methods breakdown
        $paymentMethodsBreakdown = [];
        foreach ($invoices as $invoice) {
            foreach ($invoice->payments as $payment) {
                $method = $payment->payment_method;
                if (!isset($paymentMethodsBreakdown[$method])) {
                    $paymentMethodsBreakdown[$method] = [
                        'count' => 0,
                        'total' => 0,
                        'percentage' => 0
                    ];
                }
                $paymentMethodsBreakdown[$method]['count']++;
                $paymentMethodsBreakdown[$method]['total'] += $payment->amount;
            }
        }

        // Calculate percentages for payment methods
        $totalPayments = collect($paymentMethodsBreakdown)->sum('total');
        foreach ($paymentMethodsBreakdown as $method => &$data) {
            $data['percentage'] = $totalPayments > 0 ? ($data['total'] / $totalPayments) * 100 : 0;
        }

        // Top selling items
        $itemSales = [];
        foreach ($invoices as $invoice) {
            foreach ($invoice->items as $item) {
                $key = $item->item_name;
                if (!isset($itemSales[$key])) {
                    $itemSales[$key] = [
                        'name' => $item->item_name,
                        'quantity' => 0,
                        'total_sales' => 0,
                        'profit' => 0
                    ];
                }
                $itemSales[$key]['quantity'] += $item->quantity;
                $itemSales[$key]['total_sales'] += $item->total_price;
                
                if ($item->inventoryItem) {
                    $cost = $item->quantity * $item->inventoryItem->cost_price;
                    $itemSales[$key]['profit'] += $item->total_price - $cost;
                }
            }
        }

        // Sort by total sales and take top 10
        $topSellingItems = collect($itemSales)
            ->sortByDesc('total_sales')
            ->take(10)
            ->values()
            ->toArray();

        return self::updateOrCreate(
            [
                'analytics_date' => $date->toDateString(),
                'period_type' => $periodType
            ],
            [
                'total_sales' => $totalSales,
                'total_cost' => $totalCost,
                'total_profit' => $totalProfit,
                'profit_margin' => $profitMargin,
                'invoices_count' => $invoicesCount,
                'customers_count' => $customersCount,
                'average_invoice_value' => $averageInvoiceValue,
                'sales_by_channel' => $salesByChannel,
                'sales_by_category' => [],
                'payment_methods_breakdown' => $paymentMethodsBreakdown,
                'top_selling_items' => $topSellingItems,
            ]
        );
    }

    /**
     * Get start date for period type.
     */
    private static function getStartDateForPeriod(Carbon $date, string $periodType): Carbon
    {
        return match ($periodType) {
            'daily' => $date->copy()->startOfDay(),
            'weekly' => $date->copy()->startOfWeek(),
            'monthly' => $date->copy()->startOfMonth(),
            'yearly' => $date->copy()->startOfYear(),
            default => $date->copy()->startOfDay(),
        };
    }

    /**
     * Get end date for period type.
     */
    private static function getEndDateForPeriod(Carbon $date, string $periodType): Carbon
    {
        return match ($periodType) {
            'daily' => $date->copy()->endOfDay(),
            'weekly' => $date->copy()->endOfWeek(),
            'monthly' => $date->copy()->endOfMonth(),
            'yearly' => $date->copy()->endOfYear(),
            default => $date->copy()->endOfDay(),
        };
    }

    /**
     * Get analytics for date range.
     */
    public static function getAnalyticsForRange(Carbon $startDate, Carbon $endDate, string $periodType)
    {
        return self::where('period_type', $periodType)
            ->whereBetween('analytics_date', [$startDate, $endDate])
            ->orderBy('analytics_date')
            ->get();
    }

    /**
     * Get comparison with previous period.
     */
    public function getComparisonWithPreviousPeriod(): array
    {
        $previousDate = match ($this->period_type) {
            'daily' => $this->analytics_date->copy()->subDay(),
            'weekly' => $this->analytics_date->copy()->subWeek(),
            'monthly' => $this->analytics_date->copy()->subMonth(),
            'yearly' => $this->analytics_date->copy()->subYear(),
            default => $this->analytics_date->copy()->subDay(),
        };

        $previous = self::where('analytics_date', $previousDate)
            ->where('period_type', $this->period_type)
            ->first();

        if (!$previous) {
            return [
                'sales_change' => 0,
                'profit_change' => 0,
                'invoices_change' => 0,
                'customers_change' => 0,
            ];
        }

        return [
            'sales_change' => $this->calculatePercentageChange($previous->total_sales, $this->total_sales),
            'profit_change' => $this->calculatePercentageChange($previous->total_profit, $this->total_profit),
            'invoices_change' => $this->calculatePercentageChange($previous->invoices_count, $this->invoices_count),
            'customers_change' => $this->calculatePercentageChange($previous->customers_count, $this->customers_count),
        ];
    }

    /**
     * Calculate percentage change between two values.
     */
    private function calculatePercentageChange($oldValue, $newValue): float
    {
        if ($oldValue == 0) {
            return $newValue > 0 ? 100 : 0;
        }

        return (($newValue - $oldValue) / $oldValue) * 100;
    }
}
