<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\App;
use Carbon\Carbon;

class LocalizationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Set Carbon locale based on application locale
        if (App::getLocale() === 'ar') {
            Carbon::setLocale('ar');
            
            // Set Arabic date format preferences
            Carbon::macro('toArabicDateString', function () {
                return $this->translatedFormat('j F Y');
            });
            
            Carbon::macro('toArabicDateTimeString', function () {
                return $this->translatedFormat('j F Y - H:i');
            });
            
            Carbon::macro('toArabicTimeString', function () {
                return $this->translatedFormat('H:i');
            });
            
            // Set Arabic day and month names
            Carbon::macro('getArabicDayName', function () {
                $days = [
                    'Sunday' => 'الأحد',
                    'Monday' => 'الاثنين',
                    'Tuesday' => 'الثلاثاء',
                    'Wednesday' => 'الأربعاء',
                    'Thursday' => 'الخميس',
                    'Friday' => 'الجمعة',
                    'Saturday' => 'السبت',
                ];
                return $days[$this->format('l')] ?? $this->format('l');
            });
            
            Carbon::macro('getArabicMonthName', function () {
                $months = [
                    'January' => 'يناير',
                    'February' => 'فبراير',
                    'March' => 'مارس',
                    'April' => 'أبريل',
                    'May' => 'مايو',
                    'June' => 'يونيو',
                    'July' => 'يوليو',
                    'August' => 'أغسطس',
                    'September' => 'سبتمبر',
                    'October' => 'أكتوبر',
                    'November' => 'نوفمبر',
                    'December' => 'ديسمبر',
                ];
                return $months[$this->format('F')] ?? $this->format('F');
            });
        }
    }
}
