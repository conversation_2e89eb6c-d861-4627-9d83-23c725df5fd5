<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_messages', function (Blueprint $table) {
            $table->id();
            
            // WhatsApp message identifiers
            $table->string('whatsapp_message_id')->unique()->nullable();
            $table->string('conversation_id')->index();
            
            // Customer information
            $table->string('customer_phone', 20)->index();
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('set null');
            
            // Message details
            $table->enum('direction', ['inbound', 'outbound'])->index();
            $table->enum('message_type', ['text', 'image', 'document', 'audio', 'video', 'location', 'interactive', 'template'])->index();
            $table->text('message_content')->nullable();
            $table->json('message_data')->nullable(); // Store structured data for interactive messages
            
            // Media information
            $table->string('media_id')->nullable();
            $table->string('media_url')->nullable();
            $table->string('media_mime_type')->nullable();
            $table->string('media_filename')->nullable();
            $table->bigInteger('media_file_size')->nullable();
            
            // Message status
            $table->enum('status', ['sent', 'delivered', 'read', 'failed', 'pending'])->default('pending')->index();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->text('error_message')->nullable();
            
            // Context and threading
            $table->string('context_message_id')->nullable(); // For replies
            $table->string('context_type')->nullable(); // 'reply', 'forward', etc.
            
            // Business logic
            $table->string('intent')->nullable()->index(); // 'ticket_query', 'appointment', 'general_info', etc.
            $table->json('extracted_data')->nullable(); // Parsed information from message
            $table->boolean('requires_human_response')->default(false)->index();
            $table->boolean('auto_responded')->default(false)->index();
            
            // Related entities
            $table->unsignedBigInteger('repair_ticket_id')->nullable();
            $table->foreign('repair_ticket_id')->references('id')->on('repair_tickets')->onDelete('set null');
            
            // Session management
            $table->string('session_id')->nullable()->index();
            $table->timestamp('session_expires_at')->nullable();
            
            // Analytics
            $table->string('user_agent')->nullable();
            $table->ipAddress('ip_address')->nullable();
            $table->string('language', 5)->default('ar')->index();
            
            // Timestamps
            $table->timestamp('whatsapp_timestamp')->nullable(); // Original WhatsApp timestamp
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['customer_phone', 'created_at']);
            $table->index(['direction', 'status', 'created_at']);
            $table->index(['intent', 'created_at']);
            $table->index(['requires_human_response', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_messages');
    }
};
