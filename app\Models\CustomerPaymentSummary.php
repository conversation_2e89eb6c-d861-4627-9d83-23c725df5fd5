<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class CustomerPaymentSummary extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'customer_id',
        'total_invoiced',
        'total_paid',
        'outstanding_balance',
        'total_invoices',
        'paid_invoices',
        'overdue_invoices',
        'average_payment_days',
        'payment_behavior',
        'credit_limit',
        'credit_hold',
        'last_payment_date',
        'last_invoice_date',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'total_invoiced' => 'decimal:2',
        'total_paid' => 'decimal:2',
        'outstanding_balance' => 'decimal:2',
        'average_payment_days' => 'decimal:1',
        'credit_limit' => 'decimal:2',
        'credit_hold' => 'boolean',
        'last_payment_date' => 'date',
        'last_invoice_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the customer that owns this summary.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Update or create payment summary for a customer.
     */
    public static function updateForCustomer(int $customerId): self
    {
        $customer = Customer::findOrFail($customerId);
        
        // Get all invoices for this customer
        $invoices = Invoice::where('customer_id', $customerId)
            ->where('status', '!=', 'cancelled')
            ->with('payments')
            ->get();

        // Calculate totals
        $totalInvoiced = $invoices->sum('total_amount');
        $totalPaid = $invoices->sum('paid_amount');
        $outstandingBalance = $totalInvoiced - $totalPaid;
        $totalInvoices = $invoices->count();
        $paidInvoices = $invoices->where('payment_status', 'paid')->count();
        
        // Calculate overdue invoices
        $overdueInvoices = $invoices->filter(function ($invoice) {
            return $invoice->due_date && 
                   $invoice->due_date->isPast() && 
                   $invoice->payment_status !== 'paid';
        })->count();

        // Calculate average payment days
        $paymentDays = [];
        foreach ($invoices as $invoice) {
            if ($invoice->first_payment_date && $invoice->invoice_date) {
                $days = $invoice->invoice_date->diffInDays($invoice->first_payment_date);
                $paymentDays[] = $days;
            }
        }
        $averagePaymentDays = count($paymentDays) > 0 ? array_sum($paymentDays) / count($paymentDays) : 0;

        // Determine payment behavior
        $paymentBehavior = self::calculatePaymentBehavior(
            $averagePaymentDays,
            $overdueInvoices,
            $totalInvoices,
            $outstandingBalance
        );

        // Get last payment and invoice dates
        $lastPaymentDate = null;
        $lastInvoiceDate = null;

        if ($invoices->isNotEmpty()) {
            $lastInvoiceDate = $invoices->max('invoice_date');
            
            $allPayments = $invoices->flatMap->payments;
            if ($allPayments->isNotEmpty()) {
                $lastPaymentDate = $allPayments->max('payment_date');
            }
        }

        return self::updateOrCreate(
            ['customer_id' => $customerId],
            [
                'total_invoiced' => $totalInvoiced,
                'total_paid' => $totalPaid,
                'outstanding_balance' => $outstandingBalance,
                'total_invoices' => $totalInvoices,
                'paid_invoices' => $paidInvoices,
                'overdue_invoices' => $overdueInvoices,
                'average_payment_days' => $averagePaymentDays,
                'payment_behavior' => $paymentBehavior,
                'last_payment_date' => $lastPaymentDate,
                'last_invoice_date' => $lastInvoiceDate,
            ]
        );
    }

    /**
     * Calculate payment behavior based on metrics.
     */
    private static function calculatePaymentBehavior(
        float $averagePaymentDays,
        int $overdueInvoices,
        int $totalInvoices,
        float $outstandingBalance
    ): string {
        // New customer
        if ($totalInvoices === 0) {
            return 'new';
        }

        // Calculate overdue percentage
        $overduePercentage = $totalInvoices > 0 ? ($overdueInvoices / $totalInvoices) * 100 : 0;

        // Excellent: Pays within 7 days on average, less than 5% overdue
        if ($averagePaymentDays <= 7 && $overduePercentage < 5) {
            return 'excellent';
        }

        // Good: Pays within 15 days on average, less than 15% overdue
        if ($averagePaymentDays <= 15 && $overduePercentage < 15) {
            return 'good';
        }

        // Poor: Pays after 30 days on average or more than 30% overdue
        if ($averagePaymentDays > 30 || $overduePercentage > 30) {
            return 'poor';
        }

        // Average: Everything else
        return 'average';
    }

    /**
     * Check if customer is eligible for credit.
     */
    public function isEligibleForCredit(float $requestedAmount = 0): bool
    {
        // If on credit hold, not eligible
        if ($this->credit_hold) {
            return false;
        }

        // If no credit limit set, assume eligible
        if (!$this->credit_limit) {
            return true;
        }

        // Check if requested amount would exceed credit limit
        $totalExposure = $this->outstanding_balance + $requestedAmount;
        return $totalExposure <= $this->credit_limit;
    }

    /**
     * Get available credit amount.
     */
    public function getAvailableCreditAttribute(): float
    {
        if (!$this->credit_limit || $this->credit_hold) {
            return 0;
        }

        return max(0, $this->credit_limit - $this->outstanding_balance);
    }

    /**
     * Get payment behavior display name.
     */
    public function getPaymentBehaviorDisplayAttribute(): string
    {
        $behaviors = [
            'excellent' => app()->getLocale() === 'ar' ? 'ممتاز' : 'Excellent',
            'good' => app()->getLocale() === 'ar' ? 'جيد' : 'Good',
            'average' => app()->getLocale() === 'ar' ? 'متوسط' : 'Average',
            'poor' => app()->getLocale() === 'ar' ? 'ضعيف' : 'Poor',
            'new' => app()->getLocale() === 'ar' ? 'عميل جديد' : 'New Customer',
        ];

        return $behaviors[$this->payment_behavior] ?? $this->payment_behavior;
    }

    /**
     * Get payment behavior color class for UI.
     */
    public function getPaymentBehaviorColorAttribute(): string
    {
        return match ($this->payment_behavior) {
            'excellent' => 'success',
            'good' => 'primary',
            'average' => 'warning',
            'poor' => 'danger',
            'new' => 'info',
            default => 'secondary',
        };
    }

    /**
     * Get customers with poor payment behavior.
     */
    public static function getPoorPaymentCustomers()
    {
        return self::with('customer')
            ->where('payment_behavior', 'poor')
            ->orWhere('credit_hold', true)
            ->orWhere('overdue_invoices', '>', 0)
            ->orderBy('outstanding_balance', 'desc')
            ->get();
    }

    /**
     * Get customers eligible for credit increase.
     */
    public static function getEligibleForCreditIncrease()
    {
        return self::with('customer')
            ->whereIn('payment_behavior', ['excellent', 'good'])
            ->where('credit_hold', false)
            ->where('outstanding_balance', '>', 0)
            ->whereRaw('outstanding_balance >= credit_limit * 0.8') // Using 80% of credit limit
            ->orderBy('total_invoiced', 'desc')
            ->get();
    }

    /**
     * Update all customer payment summaries.
     */
    public static function updateAllSummaries(): void
    {
        $customerIds = Customer::pluck('id');
        
        foreach ($customerIds as $customerId) {
            self::updateForCustomer($customerId);
        }
    }
}
