<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Customer;
use App\Models\RepairTicket;
use App\Models\RepairStatus;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;

class ApiTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
    }

    /** @test */
    public function api_search_returns_relevant_results()
    {
        Sanctum::actingAs($this->user);
        
        $customer = Customer::factory()->create(['name' => 'John Doe']);
        $brand = Brand::factory()->create(['name' => 'Apple']);
        
        $ticket = RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'brand_id' => $brand->id,
            'device_model' => 'iPhone 13',
            'reported_problem' => 'Screen cracked'
        ]);
        
        $response = $this->getJson('/api/search?q=John');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => [
                'type',
                'title',
                'description',
                'url'
            ]
        ]);
        
        $data = $response->json();
        $this->assertGreaterThan(0, count($data));
        $this->assertStringContainsString('John', $data[0]['title']);
    }

    /** @test */
    public function api_search_requires_minimum_query_length()
    {
        Sanctum::actingAs($this->user);
        
        $response = $this->getJson('/api/search?q=a');
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors('q');
    }

    /** @test */
    public function api_search_returns_mixed_results()
    {
        Sanctum::actingAs($this->user);
        
        $customer = Customer::factory()->create(['name' => 'Apple Store Customer']);
        $brand = Brand::factory()->create(['name' => 'Apple']);
        
        RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'brand_id' => $brand->id,
            'device_model' => 'MacBook Pro'
        ]);
        
        $response = $this->getJson('/api/search?q=Apple');
        
        $response->assertStatus(200);
        
        $data = $response->json();
        $types = array_column($data, 'type');
        
        // Should return both customer and ticket results
        $this->assertContains('customer', $types);
        $this->assertContains('ticket', $types);
    }

    /** @test */
    public function api_dashboard_stats_returns_correct_data()
    {
        Sanctum::actingAs($this->user);
        
        $pendingStatus = RepairStatus::factory()->create(['name' => 'Pending']);
        $completedStatus = RepairStatus::factory()->create(['name' => 'Completed']);
        
        RepairTicket::factory()->count(5)->create(['repair_status_id' => $pendingStatus->id]);
        RepairTicket::factory()->count(3)->create(['repair_status_id' => $completedStatus->id]);
        Customer::factory()->count(10)->create();
        
        $response = $this->getJson('/api/dashboard/stats');
        
        $response->assertStatus(200);
        $response->assertJson([
            'total_tickets' => 8,
            'total_customers' => 10,
            'pending_tickets' => 5,
            'completed_tickets' => 3
        ]);
    }

    /** @test */
    public function api_dashboard_stats_calculates_overdue_tickets()
    {
        Sanctum::actingAs($this->user);
        
        $inProgressStatus = RepairStatus::factory()->create(['name' => 'In Progress']);
        
        // Overdue tickets
        RepairTicket::factory()->count(3)->create([
            'repair_status_id' => $inProgressStatus->id,
            'received_date' => now()->subDays(10)
        ]);
        
        // Recent tickets
        RepairTicket::factory()->count(2)->create([
            'repair_status_id' => $inProgressStatus->id,
            'received_date' => now()->subDays(3)
        ]);
        
        $response = $this->getJson('/api/dashboard/stats');
        
        $response->assertStatus(200);
        $response->assertJson([
            'overdue_tickets' => 3
        ]);
    }

    /** @test */
    public function api_recent_activities_returns_formatted_data()
    {
        Sanctum::actingAs($this->user);
        
        $customer = Customer::factory()->create(['name' => 'John Doe']);
        RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'received_date' => now()->subMinutes(30)
        ]);
        
        $response = $this->getJson('/api/dashboard/recent-activities');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => [
                'type',
                'title',
                'description',
                'time_ago'
            ]
        ]);
        
        $data = $response->json();
        $this->assertGreaterThan(0, count($data));
        $this->assertEquals('ticket_created', $data[0]['type']);
    }

    /** @test */
    public function api_notifications_returns_unread_count()
    {
        Sanctum::actingAs($this->user);
        
        // Create some overdue tickets to generate notifications
        $inProgressStatus = RepairStatus::factory()->create(['name' => 'In Progress']);
        RepairTicket::factory()->count(3)->create([
            'repair_status_id' => $inProgressStatus->id,
            'received_date' => now()->subDays(10)
        ]);
        
        $response = $this->getJson('/api/notifications/unread');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'count',
            'notifications' => [
                '*' => [
                    'type',
                    'message',
                    'created_at'
                ]
            ]
        ]);
        
        $data = $response->json();
        $this->assertGreaterThan(0, $data['count']);
    }

    /** @test */
    public function api_customer_autocomplete_returns_matching_customers()
    {
        Sanctum::actingAs($this->user);
        
        Customer::factory()->create(['name' => 'John Doe', 'phone_number' => '+1234567890']);
        Customer::factory()->create(['name' => 'Jane Smith', 'phone_number' => '+0987654321']);
        Customer::factory()->create(['name' => 'Bob Johnson', 'phone_number' => '+1122334455']);
        
        $response = $this->getJson('/api/customers/autocomplete?term=John');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => [
                'id',
                'name',
                'phone_number',
                'email'
            ]
        ]);
        
        $data = $response->json();
        $this->assertCount(2, $data); // John Doe and Bob Johnson
        $this->assertStringContainsString('John', $data[0]['name']);
    }

    /** @test */
    public function api_brand_autocomplete_returns_matching_brands()
    {
        Sanctum::actingAs($this->user);
        
        Brand::factory()->create(['name' => 'Apple']);
        Brand::factory()->create(['name' => 'Samsung']);
        Brand::factory()->create(['name' => 'Huawei']);
        
        $response = $this->getJson('/api/brands/autocomplete?term=a');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => [
                'id',
                'name'
            ]
        ]);
        
        $data = $response->json();
        $this->assertCount(2, $data); // Apple and Samsung
    }

    /** @test */
    public function api_ticket_status_update_works()
    {
        Sanctum::actingAs($this->user);
        
        $ticket = RepairTicket::factory()->create();
        $newStatus = RepairStatus::factory()->create(['name' => 'Completed']);
        
        $response = $this->patchJson("/api/tickets/{$ticket->id}/status", [
            'repair_status_id' => $newStatus->id
        ]);
        
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Ticket status updated successfully'
        ]);
        
        $this->assertDatabaseHas('repair_tickets', [
            'id' => $ticket->id,
            'repair_status_id' => $newStatus->id
        ]);
    }

    /** @test */
    public function api_bulk_status_update_works()
    {
        Sanctum::actingAs($this->user);
        
        $tickets = RepairTicket::factory()->count(3)->create();
        $newStatus = RepairStatus::factory()->create(['name' => 'In Progress']);
        
        $response = $this->patchJson('/api/tickets/bulk-status', [
            'ticket_ids' => $tickets->pluck('id')->toArray(),
            'repair_status_id' => $newStatus->id
        ]);
        
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'updated_count' => 3
        ]);
        
        foreach ($tickets as $ticket) {
            $this->assertDatabaseHas('repair_tickets', [
                'id' => $ticket->id,
                'repair_status_id' => $newStatus->id
            ]);
        }
    }

    /** @test */
    public function api_requires_authentication()
    {
        $response = $this->getJson('/api/dashboard/stats');
        
        $response->assertStatus(401);
    }

    /** @test */
    public function api_validates_input_parameters()
    {
        Sanctum::actingAs($this->user);
        
        // Test invalid ticket ID
        $response = $this->patchJson('/api/tickets/999/status', [
            'repair_status_id' => 1
        ]);
        
        $response->assertStatus(404);
    }

    /** @test */
    public function api_handles_rate_limiting()
    {
        Sanctum::actingAs($this->user);
        
        // Make multiple requests quickly
        for ($i = 0; $i < 65; $i++) {
            $response = $this->getJson('/api/dashboard/stats');
            
            if ($response->status() === 429) {
                $this->assertEquals(429, $response->status());
                break;
            }
        }
    }

    /** @test */
    public function api_returns_proper_error_responses()
    {
        Sanctum::actingAs($this->user);
        
        $response = $this->patchJson('/api/tickets/999/status', [
            'repair_status_id' => 999 // Invalid status ID
        ]);
        
        $response->assertStatus(422);
        $response->assertJsonStructure([
            'message',
            'errors'
        ]);
    }

    /** @test */
    public function api_search_handles_special_characters()
    {
        Sanctum::actingAs($this->user);
        
        Customer::factory()->create(['name' => 'José María']);
        
        $response = $this->getJson('/api/search?q=' . urlencode('José'));
        
        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertGreaterThan(0, count($data));
    }

    /** @test */
    public function api_pagination_works_correctly()
    {
        Sanctum::actingAs($this->user);
        
        Customer::factory()->count(25)->create(['name' => 'Test Customer']);
        
        $response = $this->getJson('/api/search?q=Test&per_page=10&page=1');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data',
            'current_page',
            'per_page',
            'total'
        ]);
        
        $data = $response->json();
        $this->assertCount(10, $data['data']);
        $this->assertEquals(1, $data['current_page']);
        $this->assertEquals(25, $data['total']);
    }
}
