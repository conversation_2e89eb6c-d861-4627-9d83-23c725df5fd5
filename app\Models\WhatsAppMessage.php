<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WhatsAppMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'whatsapp_message_id',
        'conversation_id',
        'customer_phone',
        'customer_id',
        'direction',
        'message_type',
        'message_content',
        'message_data',
        'media_id',
        'media_url',
        'media_mime_type',
        'media_filename',
        'media_file_size',
        'status',
        'delivered_at',
        'read_at',
        'error_message',
        'context_message_id',
        'context_type',
        'intent',
        'extracted_data',
        'requires_human_response',
        'auto_responded',
        'repair_ticket_id',
        'session_id',
        'session_expires_at',
        'user_agent',
        'ip_address',
        'language',
        'whatsapp_timestamp',
    ];

    protected $casts = [
        'message_data' => 'array',
        'extracted_data' => 'array',
        'requires_human_response' => 'boolean',
        'auto_responded' => 'boolean',
        'delivered_at' => 'datetime',
        'read_at' => 'datetime',
        'session_expires_at' => 'datetime',
        'whatsapp_timestamp' => 'datetime',
    ];

    /**
     * Get the customer that owns the message.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the repair ticket associated with the message.
     */
    public function repairTicket(): BelongsTo
    {
        return $this->belongsTo(RepairTicket::class);
    }

    /**
     * Scope for inbound messages.
     */
    public function scopeInbound($query)
    {
        return $query->where('direction', 'inbound');
    }

    /**
     * Scope for outbound messages.
     */
    public function scopeOutbound($query)
    {
        return $query->where('direction', 'outbound');
    }

    /**
     * Scope for messages requiring human response.
     */
    public function scopeRequiringHumanResponse($query)
    {
        return $query->where('requires_human_response', true);
    }

    /**
     * Scope for auto-responded messages.
     */
    public function scopeAutoResponded($query)
    {
        return $query->where('auto_responded', true);
    }

    /**
     * Scope for messages by intent.
     */
    public function scopeByIntent($query, $intent)
    {
        return $query->where('intent', $intent);
    }

    /**
     * Scope for messages in a specific conversation.
     */
    public function scopeInConversation($query, $conversationId)
    {
        return $query->where('conversation_id', $conversationId);
    }

    /**
     * Scope for messages from a specific customer phone.
     */
    public function scopeFromCustomer($query, $phone)
    {
        return $query->where('customer_phone', $phone);
    }

    /**
     * Check if message is inbound.
     */
    public function isInbound(): bool
    {
        return $this->direction === 'inbound';
    }

    /**
     * Check if message is outbound.
     */
    public function isOutbound(): bool
    {
        return $this->direction === 'outbound';
    }

    /**
     * Check if message has media.
     */
    public function hasMedia(): bool
    {
        return !is_null($this->media_id);
    }

    /**
     * Check if message is delivered.
     */
    public function isDelivered(): bool
    {
        return $this->status === 'delivered' || $this->status === 'read';
    }

    /**
     * Check if message is read.
     */
    public function isRead(): bool
    {
        return $this->status === 'read';
    }

    /**
     * Mark message as delivered.
     */
    public function markAsDelivered(): void
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    /**
     * Mark message as read.
     */
    public function markAsRead(): void
    {
        $this->update([
            'status' => 'read',
            'read_at' => now(),
        ]);
    }

    /**
     * Mark message as failed.
     */
    public function markAsFailed(string $errorMessage = null): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Get formatted message content for display.
     */
    public function getFormattedContentAttribute(): string
    {
        if ($this->message_type === 'text') {
            return $this->message_content;
        }

        if ($this->hasMedia()) {
            return "📎 {$this->message_type}: {$this->media_filename}";
        }

        if ($this->message_type === 'location') {
            return "📍 موقع جغرافي";
        }

        if ($this->message_type === 'interactive') {
            return "🔘 رسالة تفاعلية";
        }

        return $this->message_content ?? "رسالة {$this->message_type}";
    }

    /**
     * Get conversation thread.
     */
    public function getConversationThread()
    {
        return static::where('conversation_id', $this->conversation_id)
            ->orderBy('created_at')
            ->get();
    }
}
