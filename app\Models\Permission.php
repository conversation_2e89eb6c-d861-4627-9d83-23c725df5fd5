<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Permission extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'category',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the roles that have this permission.
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'role_permissions');
    }

    /**
     * Get the users that have this permission directly.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_permissions');
    }

    /**
     * Get permissions by category.
     */
    public static function getByCategory(string $category): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('category', $category)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();
    }

    /**
     * Get all permission categories.
     */
    public static function getCategories(): array
    {
        return static::distinct('category')
            ->where('is_active', true)
            ->pluck('category')
            ->toArray();
    }

    /**
     * Create default permissions.
     */
    public static function createDefaults(): void
    {
        $permissions = [
            // Dashboard & Reports
            ['name' => 'View Dashboard', 'slug' => 'dashboard.view', 'category' => 'dashboard', 'description' => 'Access main dashboard'],
            ['name' => 'View Reports', 'slug' => 'reports.view', 'category' => 'reports', 'description' => 'View all reports'],
            ['name' => 'Export Reports', 'slug' => 'reports.export', 'category' => 'reports', 'description' => 'Export reports to PDF/Excel'],
            ['name' => 'Advanced Reports', 'slug' => 'reports.advanced', 'category' => 'reports', 'description' => 'Access advanced analytics'],

            // Customer Management
            ['name' => 'View Customers', 'slug' => 'customers.view', 'category' => 'customers', 'description' => 'View customer list'],
            ['name' => 'Create Customers', 'slug' => 'customers.create', 'category' => 'customers', 'description' => 'Add new customers'],
            ['name' => 'Edit Customers', 'slug' => 'customers.edit', 'category' => 'customers', 'description' => 'Edit customer information'],
            ['name' => 'Delete Customers', 'slug' => 'customers.delete', 'category' => 'customers', 'description' => 'Delete customers'],
            ['name' => 'View Customer History', 'slug' => 'customers.history', 'category' => 'customers', 'description' => 'View customer repair history'],

            // Repair Tickets
            ['name' => 'View Tickets', 'slug' => 'tickets.view', 'category' => 'tickets', 'description' => 'View repair tickets'],
            ['name' => 'Create Tickets', 'slug' => 'tickets.create', 'category' => 'tickets', 'description' => 'Create new repair tickets'],
            ['name' => 'Edit Tickets', 'slug' => 'tickets.edit', 'category' => 'tickets', 'description' => 'Edit repair tickets'],
            ['name' => 'Delete Tickets', 'slug' => 'tickets.delete', 'category' => 'tickets', 'description' => 'Delete repair tickets'],
            ['name' => 'Assign Tickets', 'slug' => 'tickets.assign', 'category' => 'tickets', 'description' => 'Assign tickets to technicians'],
            ['name' => 'Update Status', 'slug' => 'tickets.status', 'category' => 'tickets', 'description' => 'Update ticket status'],
            ['name' => 'View All Tickets', 'slug' => 'tickets.view_all', 'category' => 'tickets', 'description' => 'View all tickets (not just assigned)'],

            // Inventory Management
            ['name' => 'View Inventory', 'slug' => 'inventory.view', 'category' => 'inventory', 'description' => 'View inventory items'],
            ['name' => 'Create Items', 'slug' => 'inventory.create', 'category' => 'inventory', 'description' => 'Add new inventory items'],
            ['name' => 'Edit Items', 'slug' => 'inventory.edit', 'category' => 'inventory', 'description' => 'Edit inventory items'],
            ['name' => 'Delete Items', 'slug' => 'inventory.delete', 'category' => 'inventory', 'description' => 'Delete inventory items'],
            ['name' => 'Adjust Stock', 'slug' => 'inventory.adjust', 'category' => 'inventory', 'description' => 'Adjust stock levels'],
            ['name' => 'View Stock Reports', 'slug' => 'inventory.reports', 'category' => 'inventory', 'description' => 'View inventory reports'],

            // Financial Management
            ['name' => 'View Invoices', 'slug' => 'invoices.view', 'category' => 'financial', 'description' => 'View invoices'],
            ['name' => 'Create Invoices', 'slug' => 'invoices.create', 'category' => 'financial', 'description' => 'Create new invoices'],
            ['name' => 'Edit Invoices', 'slug' => 'invoices.edit', 'category' => 'financial', 'description' => 'Edit invoices'],
            ['name' => 'Delete Invoices', 'slug' => 'invoices.delete', 'category' => 'financial', 'description' => 'Delete invoices'],
            ['name' => 'Process Payments', 'slug' => 'payments.process', 'category' => 'financial', 'description' => 'Process payments'],
            ['name' => 'View Payments', 'slug' => 'payments.view', 'category' => 'financial', 'description' => 'View payment history'],
            ['name' => 'Refund Payments', 'slug' => 'payments.refund', 'category' => 'financial', 'description' => 'Process refunds'],
            ['name' => 'Financial Reports', 'slug' => 'financial.reports', 'category' => 'financial', 'description' => 'View financial reports'],

            // POS System
            ['name' => 'Access POS', 'slug' => 'pos.access', 'category' => 'pos', 'description' => 'Access POS system'],
            ['name' => 'Process Sales', 'slug' => 'pos.sales', 'category' => 'pos', 'description' => 'Process sales transactions'],
            ['name' => 'Apply Discounts', 'slug' => 'pos.discounts', 'category' => 'pos', 'description' => 'Apply discounts to sales'],
            ['name' => 'Void Transactions', 'slug' => 'pos.void', 'category' => 'pos', 'description' => 'Void transactions'],
            ['name' => 'View POS Reports', 'slug' => 'pos.reports', 'category' => 'pos', 'description' => 'View POS reports'],

            // User Management
            ['name' => 'View Users', 'slug' => 'users.view', 'category' => 'users', 'description' => 'View user list'],
            ['name' => 'Create Users', 'slug' => 'users.create', 'category' => 'users', 'description' => 'Create new users'],
            ['name' => 'Edit Users', 'slug' => 'users.edit', 'category' => 'users', 'description' => 'Edit user information'],
            ['name' => 'Delete Users', 'slug' => 'users.delete', 'category' => 'users', 'description' => 'Delete users'],
            ['name' => 'Manage Roles', 'slug' => 'users.roles', 'category' => 'users', 'description' => 'Assign roles to users'],
            ['name' => 'Manage Permissions', 'slug' => 'users.permissions', 'category' => 'users', 'description' => 'Manage user permissions'],

            // System Settings
            ['name' => 'View Settings', 'slug' => 'settings.view', 'category' => 'settings', 'description' => 'View system settings'],
            ['name' => 'Edit Settings', 'slug' => 'settings.edit', 'category' => 'settings', 'description' => 'Edit system settings'],
            ['name' => 'Backup System', 'slug' => 'settings.backup', 'category' => 'settings', 'description' => 'Create system backups'],
            ['name' => 'View Logs', 'slug' => 'settings.logs', 'category' => 'settings', 'description' => 'View system logs'],

            // WhatsApp & Communications
            ['name' => 'Send WhatsApp', 'slug' => 'whatsapp.send', 'category' => 'communications', 'description' => 'Send WhatsApp messages'],
            ['name' => 'Manage Templates', 'slug' => 'whatsapp.templates', 'category' => 'communications', 'description' => 'Manage WhatsApp templates'],
            ['name' => 'Send SMS', 'slug' => 'sms.send', 'category' => 'communications', 'description' => 'Send SMS messages'],
            ['name' => 'Send Emails', 'slug' => 'email.send', 'category' => 'communications', 'description' => 'Send email notifications'],

            // Suppliers
            ['name' => 'View Suppliers', 'slug' => 'suppliers.view', 'category' => 'suppliers', 'description' => 'View supplier list'],
            ['name' => 'Create Suppliers', 'slug' => 'suppliers.create', 'category' => 'suppliers', 'description' => 'Add new suppliers'],
            ['name' => 'Edit Suppliers', 'slug' => 'suppliers.edit', 'category' => 'suppliers', 'description' => 'Edit supplier information'],
            ['name' => 'Delete Suppliers', 'slug' => 'suppliers.delete', 'category' => 'suppliers', 'description' => 'Delete suppliers'],
        ];

        foreach ($permissions as $permission) {
            static::firstOrCreate(
                ['slug' => $permission['slug']],
                $permission + ['is_active' => true]
            );
        }
    }
}
