<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class InventoryReorderRule extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'inventory_item_id',
        'supplier_id',
        'reorder_point',
        'reorder_quantity',
        'maximum_stock',
        'lead_time_days',
        'unit_cost',
        'minimum_order_quantity',
        'is_active',
        'auto_reorder',
        'last_reorder_date',
        'reorder_frequency_days',
        'seasonal_adjustments',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'unit_cost' => 'decimal:2',
        'is_active' => 'boolean',
        'auto_reorder' => 'boolean',
        'last_reorder_date' => 'date',
        'seasonal_adjustments' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the inventory item.
     */
    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(InventoryItem::class);
    }

    /**
     * Get the supplier.
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Check if item needs reordering.
     */
    public function needsReorder(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $currentStock = $this->inventoryItem->current_stock;
        $adjustedReorderPoint = $this->getAdjustedReorderPoint();

        return $currentStock <= $adjustedReorderPoint;
    }

    /**
     * Get adjusted reorder point based on seasonal factors.
     */
    public function getAdjustedReorderPoint(): int
    {
        $baseReorderPoint = $this->reorder_point;
        
        if (!$this->seasonal_adjustments) {
            return $baseReorderPoint;
        }

        $currentMonth = now()->month;
        $seasonalFactor = $this->seasonal_adjustments[$currentMonth] ?? 1.0;

        return (int) round($baseReorderPoint * $seasonalFactor);
    }

    /**
     * Get adjusted reorder quantity based on seasonal factors.
     */
    public function getAdjustedReorderQuantity(): int
    {
        $baseQuantity = $this->reorder_quantity;
        
        if (!$this->seasonal_adjustments) {
            return $baseQuantity;
        }

        $currentMonth = now()->month;
        $seasonalFactor = $this->seasonal_adjustments[$currentMonth] ?? 1.0;

        $adjustedQuantity = (int) round($baseQuantity * $seasonalFactor);
        
        // Ensure minimum order quantity is met
        return max($adjustedQuantity, $this->minimum_order_quantity);
    }

    /**
     * Calculate safety stock level.
     */
    public function getSafetyStockLevel(): int
    {
        // Safety stock = (Max daily usage × Max lead time) - (Average daily usage × Average lead time)
        // Simplified calculation using lead time and historical usage
        
        $averageDailyUsage = $this->calculateAverageDailyUsage();
        $safetyDays = max(2, $this->lead_time_days * 0.2); // 20% of lead time as safety buffer
        
        return (int) round($averageDailyUsage * $safetyDays);
    }

    /**
     * Calculate average daily usage.
     */
    private function calculateAverageDailyUsage(): float
    {
        // Get usage data from the last 90 days
        $usageData = $this->inventoryItem->stockMovements()
            ->where('movement_type', 'out')
            ->where('created_at', '>=', now()->subDays(90))
            ->sum('quantity');

        return $usageData / 90; // Average per day
    }

    /**
     * Check if it's time for scheduled reorder check.
     */
    public function isTimeForReorderCheck(): bool
    {
        if (!$this->reorder_frequency_days) {
            return true; // Check every time if no frequency set
        }

        if (!$this->last_reorder_date) {
            return true; // Never checked before
        }

        $daysSinceLastCheck = $this->last_reorder_date->diffInDays(now());
        return $daysSinceLastCheck >= $this->reorder_frequency_days;
    }

    /**
     * Create automatic purchase order.
     */
    public function createAutomaticPurchaseOrder(): ?PurchaseOrder
    {
        if (!$this->auto_reorder || !$this->needsReorder()) {
            return null;
        }

        $quantity = $this->getAdjustedReorderQuantity();
        $expectedDeliveryDate = now()->addDays($this->lead_time_days);

        $purchaseOrder = PurchaseOrder::create([
            'supplier_id' => $this->supplier_id,
            'status' => 'draft',
            'order_date' => now(),
            'expected_delivery_date' => $expectedDeliveryDate,
            'priority' => 'normal',
            'notes' => 'Auto-generated reorder for ' . $this->inventoryItem->name,
            'created_by' => 1, // System user
        ]);

        // Add item to purchase order
        $purchaseOrder->items()->create([
            'inventory_item_id' => $this->inventory_item_id,
            'quantity_ordered' => $quantity,
            'unit_cost' => $this->unit_cost,
            'total_cost' => $quantity * $this->unit_cost,
            'item_name' => $this->inventoryItem->name,
            'item_sku' => $this->inventoryItem->sku,
            'expected_delivery_date' => $expectedDeliveryDate,
        ]);

        $purchaseOrder->calculateTotals();

        // Update last reorder date
        $this->update(['last_reorder_date' => now()]);

        return $purchaseOrder;
    }

    /**
     * Get reorder recommendation.
     */
    public function getReorderRecommendation(): array
    {
        $currentStock = $this->inventoryItem->current_stock;
        $adjustedReorderPoint = $this->getAdjustedReorderPoint();
        $recommendedQuantity = $this->getAdjustedReorderQuantity();
        $safetyStock = $this->getSafetyStockLevel();
        $averageDailyUsage = $this->calculateAverageDailyUsage();
        
        // Calculate days of stock remaining
        $daysOfStockRemaining = $averageDailyUsage > 0 ? $currentStock / $averageDailyUsage : 999;
        
        // Calculate total cost
        $totalCost = $recommendedQuantity * $this->unit_cost;
        
        // Determine urgency
        $urgency = 'low';
        if ($currentStock <= 0) {
            $urgency = 'critical';
        } elseif ($currentStock <= $adjustedReorderPoint * 0.5) {
            $urgency = 'high';
        } elseif ($currentStock <= $adjustedReorderPoint) {
            $urgency = 'medium';
        }

        return [
            'item_info' => [
                'name' => $this->inventoryItem->name,
                'sku' => $this->inventoryItem->sku,
                'current_stock' => $currentStock,
                'reorder_point' => $adjustedReorderPoint,
                'maximum_stock' => $this->maximum_stock,
                'safety_stock' => $safetyStock,
            ],
            'supplier_info' => [
                'name' => $this->supplier->name,
                'lead_time_days' => $this->lead_time_days,
                'unit_cost' => $this->unit_cost,
                'minimum_order_quantity' => $this->minimum_order_quantity,
            ],
            'recommendation' => [
                'needs_reorder' => $this->needsReorder(),
                'recommended_quantity' => $recommendedQuantity,
                'total_cost' => $totalCost,
                'urgency' => $urgency,
                'days_of_stock_remaining' => round($daysOfStockRemaining, 1),
                'expected_delivery_date' => now()->addDays($this->lead_time_days),
                'auto_reorder_enabled' => $this->auto_reorder,
            ],
            'analysis' => [
                'average_daily_usage' => round($averageDailyUsage, 2),
                'seasonal_factor' => $this->seasonal_adjustments[now()->month] ?? 1.0,
                'last_reorder_date' => $this->last_reorder_date,
                'time_for_check' => $this->isTimeForReorderCheck(),
            ]
        ];
    }

    /**
     * Set seasonal adjustments.
     */
    public function setSeasonalAdjustments(array $adjustments): void
    {
        // Ensure we have 12 months of data
        $seasonalData = [];
        for ($month = 1; $month <= 12; $month++) {
            $seasonalData[$month] = $adjustments[$month] ?? 1.0;
        }

        $this->update(['seasonal_adjustments' => $seasonalData]);
    }

    /**
     * Get items that need reordering.
     */
    public static function getItemsNeedingReorder(): \Illuminate\Database\Eloquent\Collection
    {
        return static::with(['inventoryItem', 'supplier'])
            ->where('is_active', true)
            ->get()
            ->filter(function ($rule) {
                return $rule->needsReorder() && $rule->isTimeForReorderCheck();
            });
    }

    /**
     * Get auto-reorder candidates.
     */
    public static function getAutoReorderCandidates(): \Illuminate\Database\Eloquent\Collection
    {
        return static::with(['inventoryItem', 'supplier'])
            ->where('is_active', true)
            ->where('auto_reorder', true)
            ->get()
            ->filter(function ($rule) {
                return $rule->needsReorder() && $rule->isTimeForReorderCheck();
            });
    }

    /**
     * Process automatic reorders.
     */
    public static function processAutomaticReorders(): array
    {
        $candidates = static::getAutoReorderCandidates();
        $results = [
            'processed' => 0,
            'failed' => 0,
            'purchase_orders' => [],
            'errors' => []
        ];

        foreach ($candidates as $rule) {
            try {
                $purchaseOrder = $rule->createAutomaticPurchaseOrder();
                if ($purchaseOrder) {
                    $results['processed']++;
                    $results['purchase_orders'][] = $purchaseOrder->po_number;
                }
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'item' => $rule->inventoryItem->name,
                    'supplier' => $rule->supplier->name,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Scope for active rules.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for auto-reorder enabled rules.
     */
    public function scopeAutoReorder($query)
    {
        return $query->where('auto_reorder', true);
    }

    /**
     * Scope for rules by supplier.
     */
    public function scopeBySupplier($query, $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    /**
     * Get urgency color for UI.
     */
    public function getUrgencyColor(): string
    {
        $recommendation = $this->getReorderRecommendation();
        
        return match ($recommendation['recommendation']['urgency']) {
            'critical' => 'danger',
            'high' => 'warning',
            'medium' => 'info',
            'low' => 'success',
            default => 'secondary',
        };
    }

    /**
     * Get urgency display text.
     */
    public function getUrgencyDisplay(): string
    {
        $recommendation = $this->getReorderRecommendation();
        $urgency = $recommendation['recommendation']['urgency'];
        
        $displays = [
            'critical' => app()->getLocale() === 'ar' ? 'حرج' : 'Critical',
            'high' => app()->getLocale() === 'ar' ? 'عالي' : 'High',
            'medium' => app()->getLocale() === 'ar' ? 'متوسط' : 'Medium',
            'low' => app()->getLocale() === 'ar' ? 'منخفض' : 'Low',
        ];

        return $displays[$urgency] ?? $urgency;
    }
}
