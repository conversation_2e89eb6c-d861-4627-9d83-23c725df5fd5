#!/bin/bash

# NJ Repair Shop Monitoring Setup
# Sets up basic monitoring and alerting

set -e

echo "📊 Setting up monitoring for NJ Repair Shop..."

# Install monitoring tools
sudo apt update
sudo apt install -y htop iotop nethogs fail2ban logwatch

# Configure fail2ban for security
echo "🔒 Configuring fail2ban..."
sudo tee /etc/fail2ban/jail.local > /dev/null <<EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3
backend = systemd

[sshd]
enabled = true
port = ssh
logpath = %(sshd_log)s
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 3

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 3

[nginx-botsearch]
enabled = true
filter = nginx-botsearch
logpath = /var/log/nginx/access.log
maxretry = 2
EOF

sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Create monitoring script
echo "📈 Creating monitoring script..."
sudo tee /usr/local/bin/njrepair-monitor.sh > /dev/null <<'EOF'
#!/bin/bash

# NJ Repair Shop System Monitor
LOG_FILE="/var/log/njrepair-monitor.log"
APP_PATH="/var/www/njrepair"
ALERT_EMAIL="<EMAIL>"

# Function to log with timestamp
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# Function to send alert
send_alert() {
    local subject="$1"
    local message="$2"
    
    # Log the alert
    log_message "ALERT: $subject - $message"
    
    # Send email if configured
    if command -v mail &> /dev/null; then
        echo "$message" | mail -s "$subject" $ALERT_EMAIL
    fi
    
    # Send to webhook if configured
    if [ ! -z "$ALERT_WEBHOOK_URL" ]; then
        curl -X POST "$ALERT_WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"subject\": \"$subject\", \"message\": \"$message\"}" \
            2>/dev/null || true
    fi
}

# Check disk space
check_disk_space() {
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $usage -gt 85 ]; then
        send_alert "High Disk Usage" "Disk usage is at ${usage}% on $(hostname)"
    fi
}

# Check memory usage
check_memory() {
    local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ $mem_usage -gt 90 ]; then
        send_alert "High Memory Usage" "Memory usage is at ${mem_usage}% on $(hostname)"
    fi
}

# Check MySQL status
check_mysql() {
    if ! systemctl is-active --quiet mysql; then
        send_alert "MySQL Down" "MySQL service is not running on $(hostname)"
    fi
}

# Check Nginx status
check_nginx() {
    if ! systemctl is-active --quiet nginx; then
        send_alert "Nginx Down" "Nginx service is not running on $(hostname)"
    fi
}

# Check PHP-FPM status
check_php_fpm() {
    if ! systemctl is-active --quiet php8.2-fpm; then
        send_alert "PHP-FPM Down" "PHP-FPM service is not running on $(hostname)"
    fi
}

# Check Laravel application
check_laravel() {
    if [ -f "$APP_PATH/artisan" ]; then
        cd $APP_PATH
        if ! php artisan inspire &>/dev/null; then
            send_alert "Laravel App Issue" "Laravel application is not responding properly"
        fi
    fi
}

# Check SSL certificate expiry
check_ssl() {
    local domain="njrepair.com"
    local expiry_date=$(echo | openssl s_client -servername $domain -connect $domain:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
    local expiry_epoch=$(date -d "$expiry_date" +%s)
    local current_epoch=$(date +%s)
    local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
    
    if [ $days_until_expiry -lt 30 ]; then
        send_alert "SSL Certificate Expiring" "SSL certificate for $domain expires in $days_until_expiry days"
    fi
}

# Check backup status
check_backup() {
    local backup_file="/var/backups/njrepair/database/njrepair_db_$(date +%Y%m%d)*.sql.gz"
    if ! ls $backup_file 1> /dev/null 2>&1; then
        send_alert "Backup Missing" "No backup found for today on $(hostname)"
    fi
}

# Check log errors
check_logs() {
    local error_count=$(grep -c "ERROR" $APP_PATH/storage/logs/laravel-$(date +%Y-%m-%d).log 2>/dev/null || echo 0)
    if [ $error_count -gt 10 ]; then
        send_alert "High Error Rate" "Found $error_count errors in today's Laravel log"
    fi
}

# Run all checks
log_message "Starting system monitoring checks"

check_disk_space
check_memory
check_mysql
check_nginx
check_php_fpm
check_laravel
check_ssl
check_backup
check_logs

log_message "Monitoring checks completed"

# Generate daily report
if [ "$(date +%H:%M)" = "08:00" ]; then
    {
        echo "NJ Repair Shop Daily System Report - $(date)"
        echo "============================================"
        echo ""
        echo "System Status:"
        echo "- Uptime: $(uptime)"
        echo "- Load Average: $(uptime | awk -F'load average:' '{print $2}')"
        echo "- Disk Usage: $(df -h / | awk 'NR==2 {print $5}')"
        echo "- Memory Usage: $(free -h | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
        echo ""
        echo "Service Status:"
        echo "- MySQL: $(systemctl is-active mysql)"
        echo "- Nginx: $(systemctl is-active nginx)"
        echo "- PHP-FPM: $(systemctl is-active php8.2-fpm)"
        echo "- Redis: $(systemctl is-active redis-server)"
        echo ""
        echo "Recent Activity:"
        echo "- Failed Login Attempts: $(grep "authentication failure" /var/log/auth.log | grep "$(date +%b\ %d)" | wc -l)"
        echo "- Nginx Requests: $(grep "$(date +%d/%b/%Y)" /var/log/nginx/access.log | wc -l)"
        echo "- Laravel Errors: $(grep "ERROR" $APP_PATH/storage/logs/laravel-$(date +%Y-%m-%d).log 2>/dev/null | wc -l)"
    } | mail -s "NJ Repair Shop Daily Report" $ALERT_EMAIL 2>/dev/null || true
fi
EOF

sudo chmod +x /usr/local/bin/njrepair-monitor.sh

# Setup cron jobs
echo "⏰ Setting up monitoring cron jobs..."
(crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/njrepair-monitor.sh") | crontab -
(crontab -l 2>/dev/null; echo "0 2 * * * /var/www/njrepair/deployment/backup-script.sh") | crontab -

# Create log rotation for monitoring
sudo tee /etc/logrotate.d/njrepair-monitor > /dev/null <<EOF
/var/log/njrepair-monitor.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}
EOF

# Setup performance monitoring
echo "⚡ Setting up performance monitoring..."
sudo tee /usr/local/bin/njrepair-performance.sh > /dev/null <<'EOF'
#!/bin/bash

# Performance monitoring script
PERF_LOG="/var/log/njrepair-performance.log"
APP_PATH="/var/www/njrepair"

# Log performance metrics
{
    echo "$(date '+%Y-%m-%d %H:%M:%S')"
    echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)"
    echo "Memory: $(free | awk 'NR==2{printf "%.1f", $3*100/$2}')"
    echo "Disk I/O: $(iostat -x 1 1 | awk 'NR==4{print $10}')"
    echo "Network: $(cat /proc/net/dev | awk 'NR==3{print $2,$10}')"
    echo "MySQL Queries: $(mysql -e "SHOW GLOBAL STATUS LIKE 'Queries';" | awk 'NR==2{print $2}')"
    echo "---"
} >> $PERF_LOG
EOF

sudo chmod +x /usr/local/bin/njrepair-performance.sh
(crontab -l 2>/dev/null; echo "*/10 * * * * /usr/local/bin/njrepair-performance.sh") | crontab -

echo "✅ Monitoring setup completed!"
echo "📋 Monitoring features enabled:"
echo "- System resource monitoring (every 5 minutes)"
echo "- Service health checks"
echo "- SSL certificate expiry monitoring"
echo "- Backup verification"
echo "- Security monitoring with fail2ban"
echo "- Daily system reports"
echo "- Performance metrics logging"
echo ""
echo "📧 Configure email alerts by setting ALERT_EMAIL in the monitoring script"
echo "🔗 Configure webhook alerts by setting ALERT_WEBHOOK_URL environment variable"
