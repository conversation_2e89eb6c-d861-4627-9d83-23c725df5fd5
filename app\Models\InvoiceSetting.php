<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class InvoiceSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'description'
    ];

    // Cache settings for better performance
    public static function getSettings(): array
    {
        return Cache::remember('invoice_settings', 3600, function () {
            $settings = static::all();
            $result = [];

            foreach ($settings as $setting) {
                $result[$setting->key] = static::castValue($setting->value, $setting->type);
            }

            return $result;
        });
    }

    public static function getSetting(string $key, $default = null)
    {
        $settings = static::getSettings();
        return $settings[$key] ?? $default;
    }

    public static function setSetting(string $key, $value, string $type = 'string', ?string $description = null): void
    {
        static::updateOrCreate(
            ['key' => $key],
            [
                'value' => static::prepareValue($value, $type),
                'type' => $type,
                'description' => $description
            ]
        );

        // Clear cache
        Cache::forget('invoice_settings');
    }

    protected static function castValue($value, string $type)
    {
        return match($type) {
            'boolean' => (bool) $value,
            'number' => is_numeric($value) ? (float) $value : $value,
            'json' => json_decode($value, true),
            default => $value
        };
    }

    protected static function prepareValue($value, string $type): string
    {
        return match($type) {
            'boolean' => $value ? '1' : '0',
            'json' => json_encode($value),
            default => (string) $value
        };
    }

    public static function getCompanyDetails(): array
    {
        return static::getSetting('company_details', [
            'name' => 'ورشة الإصلاح NJ',
            'address' => 'الرياض، المملكة العربية السعودية',
            'phone' => '+966 50 123 4567',
            'email' => '<EMAIL>',
            'tax_number' => '*********'
        ]);
    }

    public static function getInvoicePrefix(): string
    {
        return static::getSetting('invoice_prefix', 'INV');
    }

    public static function getInvoiceNumberLength(): int
    {
        return static::getSetting('invoice_number_length', 6);
    }

    public static function getDefaultTaxRate(): float
    {
        return static::getSetting('default_tax_rate', 15);
    }

    public static function getPaymentTermsDays(): int
    {
        return static::getSetting('payment_terms_days', 30);
    }

    public static function getInvoiceTerms(): string
    {
        return static::getSetting('invoice_terms', 'يرجى الدفع خلال 30 يوم من تاريخ الفاتورة. شكراً لثقتكم بنا.');
    }
}
