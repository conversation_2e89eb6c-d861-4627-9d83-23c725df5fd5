<?php

namespace App\Console\Commands;

use App\Models\Role;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class SetupSecurity extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'security:setup 
                            {--force : Force setup even if data exists}
                            {--admin-email= : Admin email address}
                            {--admin-password= : Admin password}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up the security system with roles, permissions, and admin user';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔐 Setting up Security System...');
        $this->newLine();

        // Check if setup already exists
        if (!$this->option('force') && $this->isAlreadySetup()) {
            $this->warn('Security system appears to be already set up.');
            $this->info('Use --force to override existing setup.');
            return Command::FAILURE;
        }

        try {
            DB::beginTransaction();

            // Step 1: Create permissions
            $this->createPermissions();

            // Step 2: Create roles
            $this->createRoles();

            // Step 3: Create admin user
            $this->createAdminUser();

            // Step 4: Run migrations if needed
            $this->runMigrations();

            DB::commit();

            $this->newLine();
            $this->info('✅ Security system setup completed successfully!');
            $this->displaySummary();

            return Command::SUCCESS;

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('❌ Security setup failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Check if security system is already set up.
     */
    private function isAlreadySetup(): bool
    {
        return Permission::exists() && Role::exists() && User::where('role', 'admin')->exists();
    }

    /**
     * Create default permissions.
     */
    private function createPermissions(): void
    {
        $this->info('📋 Creating permissions...');
        
        $bar = $this->output->createProgressBar(1);
        $bar->start();

        Permission::createDefaults();

        $bar->finish();
        $this->newLine();
        
        $count = Permission::count();
        $this->info("   Created {$count} permissions");
    }

    /**
     * Create default roles.
     */
    private function createRoles(): void
    {
        $this->info('👥 Creating roles...');
        
        $bar = $this->output->createProgressBar(1);
        $bar->start();

        Role::createDefaults();

        $bar->finish();
        $this->newLine();
        
        $count = Role::count();
        $this->info("   Created {$count} roles");
    }

    /**
     * Create admin user.
     */
    private function createAdminUser(): void
    {
        $this->info('👤 Creating admin user...');

        $email = $this->option('admin-email') ?? $this->ask('Admin email address', '<EMAIL>');
        $password = $this->option('admin-password') ?? $this->secret('Admin password (leave empty to generate)');

        if (empty($password)) {
            $password = $this->generateSecurePassword();
            $this->warn("Generated password: {$password}");
            $this->warn('Please save this password securely!');
        }

        // Check if admin user already exists
        $existingAdmin = User::where('email', $email)->first();
        
        if ($existingAdmin) {
            if ($this->option('force')) {
                $existingAdmin->update([
                    'password' => Hash::make($password),
                    'role' => 'admin',
                    'is_active' => true,
                ]);
                $this->info("   Updated existing admin user: {$email}");
            } else {
                $this->warn("   Admin user already exists: {$email}");
            }
        } else {
            User::create([
                'name' => 'System Administrator',
                'email' => $email,
                'password' => Hash::make($password),
                'role' => 'admin',
                'is_active' => true,
                'email_verified_at' => now(),
                'phone_number' => '+966501234567',
                'address' => 'System Administrator',
            ]);
            $this->info("   Created admin user: {$email}");
        }
    }

    /**
     * Run necessary migrations.
     */
    private function runMigrations(): void
    {
        $this->info('🔄 Checking migrations...');
        
        // Check if security tables exist
        $tables = ['permissions', 'roles', 'role_permissions', 'user_permissions', 'security_logs'];
        $missingTables = [];

        foreach ($tables as $table) {
            if (!DB::getSchemaBuilder()->hasTable($table)) {
                $missingTables[] = $table;
            }
        }

        if (!empty($missingTables)) {
            $this->warn('   Missing tables: ' . implode(', ', $missingTables));
            $this->info('   Running migrations...');
            $this->call('migrate', ['--force' => true]);
        } else {
            $this->info('   All security tables exist');
        }
    }

    /**
     * Generate a secure password.
     */
    private function generateSecurePassword(int $length = 16): string
    {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

        $password = '';
        
        // Ensure at least one character from each category
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        $password .= $symbols[random_int(0, strlen($symbols) - 1)];

        // Fill the rest randomly
        $allChars = $uppercase . $lowercase . $numbers . $symbols;
        for ($i = 4; $i < $length; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }

        return str_shuffle($password);
    }

    /**
     * Display setup summary.
     */
    private function displaySummary(): void
    {
        $this->newLine();
        $this->info('📊 Setup Summary:');
        $this->table(
            ['Component', 'Count', 'Status'],
            [
                ['Permissions', Permission::count(), '✅ Ready'],
                ['Roles', Role::count(), '✅ Ready'],
                ['Admin Users', User::where('role', 'admin')->count(), '✅ Ready'],
                ['Total Users', User::count(), '✅ Ready'],
            ]
        );

        $this->newLine();
        $this->info('🔑 Available Roles:');
        $roles = Role::orderBy('level', 'desc')->get();
        foreach ($roles as $role) {
            $permissionCount = $role->permissions()->count();
            $this->line("   • {$role->name} ({$role->slug}) - Level {$role->level} - {$permissionCount} permissions");
        }

        $this->newLine();
        $this->info('🛡️  Security Features Enabled:');
        $this->line('   • Role-based access control');
        $this->line('   • Permission-based authorization');
        $this->line('   • Security audit logging');
        $this->line('   • Login attempt monitoring');
        $this->line('   • IP blocking for failed attempts');
        $this->line('   • Password strength validation');

        $this->newLine();
        $this->info('📝 Next Steps:');
        $this->line('   1. Update your .env file with security settings');
        $this->line('   2. Configure trusted IP addresses in config/security.php');
        $this->line('   3. Set up SSL/HTTPS for production');
        $this->line('   4. Review and customize permission assignments');
        $this->line('   5. Test the security system with different user roles');

        $this->newLine();
        $this->warn('⚠️  Important Security Notes:');
        $this->line('   • Change the default admin password immediately');
        $this->line('   • Regularly review security logs');
        $this->line('   • Keep the system updated');
        $this->line('   • Use strong passwords for all users');
        $this->line('   • Enable two-factor authentication when available');
    }
}
