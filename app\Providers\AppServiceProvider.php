<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use App\Helpers\ArabicFormatter;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register WhatsApp services
        $this->app->singleton(\App\Services\WhatsAppService::class, function () {
            return new \App\Services\WhatsAppService();
        });

        $this->app->singleton(\App\Services\WhatsAppBotService::class, function ($app) {
            return new \App\Services\WhatsAppBotService($app->make(\App\Services\WhatsAppService::class));
        });

        $this->app->singleton(\App\Services\WhatsAppTemplateService::class, function ($app) {
            return new \App\Services\WhatsAppTemplateService($app->make(\App\Services\WhatsAppService::class));
        });

        $this->app->singleton(\App\Services\WhatsAppPreferenceService::class, function () {
            return new \App\Services\WhatsAppPreferenceService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register Blade directives for Arabic formatting
        Blade::directive('arabicNumber', function ($expression) {
            return "<?php echo App\Helpers\ArabicFormatter::formatNumber($expression); ?>";
        });

        Blade::directive('arabicCurrency', function ($expression) {
            return "<?php echo App\Helpers\ArabicFormatter::formatCurrency($expression); ?>";
        });

        Blade::directive('arabicPercentage', function ($expression) {
            return "<?php echo App\Helpers\ArabicFormatter::formatPercentage($expression); ?>";
        });

        Blade::directive('arabicNumerals', function ($expression) {
            return "<?php echo App\Helpers\ArabicFormatter::toArabicNumerals($expression); ?>";
        });

        Blade::directive('arabicFileSize', function ($expression) {
            return "<?php echo App\Helpers\ArabicFormatter::formatFileSize($expression); ?>";
        });

        Blade::directive('arabicDuration', function ($expression) {
            return "<?php echo App\Helpers\ArabicFormatter::formatDuration($expression); ?>";
        });

        // Register model observers
        \App\Models\RepairTicket::observe(\App\Observers\RepairTicketObserver::class);
    }
}
