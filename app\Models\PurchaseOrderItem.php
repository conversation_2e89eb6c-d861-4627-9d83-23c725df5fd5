<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PurchaseOrderItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'purchase_order_id',
        'inventory_item_id',
        'quantity_ordered',
        'quantity_received',
        'unit_cost',
        'total_cost',
        'discount_percentage',
        'discount_amount',
        'item_name',
        'item_sku',
        'item_description',
        'expected_delivery_date',
        'actual_delivery_date',
        'quality_status',
        'quantity_approved',
        'quantity_rejected',
        'quality_notes',
        'notes',
        'batch_number',
        'expiry_date',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'quantity_ordered' => 'integer',
        'quantity_received' => 'integer',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'expected_delivery_date' => 'date',
        'actual_delivery_date' => 'date',
        'quantity_approved' => 'integer',
        'quantity_rejected' => 'integer',
        'expiry_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the purchase order that owns this item.
     */
    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    /**
     * Get the inventory item.
     */
    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(InventoryItem::class);
    }

    /**
     * Get quantity pending delivery.
     */
    public function getQuantityPendingAttribute(): int
    {
        return max(0, $this->quantity_ordered - $this->quantity_received);
    }

    /**
     * Get delivery progress percentage.
     */
    public function getDeliveryProgressAttribute(): float
    {
        if ($this->quantity_ordered <= 0) {
            return 0;
        }
        
        return min(100, ($this->quantity_received / $this->quantity_ordered) * 100);
    }

    /**
     * Get quality approval percentage.
     */
    public function getQualityApprovalRateAttribute(): float
    {
        if ($this->quantity_received <= 0) {
            return 0;
        }
        
        return ($this->quantity_approved / $this->quantity_received) * 100;
    }

    /**
     * Get quality status display name.
     */
    public function getQualityStatusDisplayAttribute(): string
    {
        return match($this->quality_status) {
            'pending' => __('app.inventory.quality_status.pending'),
            'approved' => __('app.inventory.quality_status.approved'),
            'rejected' => __('app.inventory.quality_status.rejected'),
            'partial' => __('app.inventory.quality_status.partial'),
            default => $this->quality_status
        };
    }

    /**
     * Get quality status badge class.
     */
    public function getQualityStatusBadgeClassAttribute(): string
    {
        return match($this->quality_status) {
            'pending' => 'bg-warning',
            'approved' => 'bg-success',
            'rejected' => 'bg-danger',
            'partial' => 'bg-info',
            default => 'bg-secondary'
        };
    }

    /**
     * Check if item is fully delivered.
     */
    public function isFullyDelivered(): bool
    {
        return $this->quantity_received >= $this->quantity_ordered;
    }

    /**
     * Check if item is partially delivered.
     */
    public function isPartiallyDelivered(): bool
    {
        return $this->quantity_received > 0 && $this->quantity_received < $this->quantity_ordered;
    }

    /**
     * Check if item is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->expected_delivery_date && 
               $this->expected_delivery_date->isPast() && 
               !$this->isFullyDelivered();
    }

    /**
     * Get net unit cost after discount.
     */
    public function getNetUnitCostAttribute(): float
    {
        $discountPerUnit = $this->discount_amount / max(1, $this->quantity_ordered);
        return $this->unit_cost - $discountPerUnit;
    }

    /**
     * Get net total cost after discount.
     */
    public function getNetTotalCostAttribute(): float
    {
        return $this->total_cost - $this->discount_amount;
    }

    /**
     * Receive items.
     */
    public function receiveItems(int $quantity, array $additionalData = []): void
    {
        $this->quantity_received += $quantity;
        
        if (isset($additionalData['actual_delivery_date'])) {
            $this->actual_delivery_date = $additionalData['actual_delivery_date'];
        }
        
        if (isset($additionalData['batch_number'])) {
            $this->batch_number = $additionalData['batch_number'];
        }
        
        if (isset($additionalData['expiry_date'])) {
            $this->expiry_date = $additionalData['expiry_date'];
        }
        
        $this->save();
        
        // Update inventory
        $this->inventoryItem->updateStock($quantity, 'purchase', [
            'unit_cost' => $this->net_unit_cost,
            'total_cost' => $quantity * $this->net_unit_cost,
            'reference_type' => 'purchase_order',
            'reference_id' => $this->purchase_order_id,
            'reference_number' => $this->purchaseOrder->po_number,
            'batch_number' => $this->batch_number,
            'expiry_date' => $this->expiry_date,
        ]);
        
        // Update purchase order status
        $this->purchaseOrder->updateStatusBasedOnDelivery();
    }

    /**
     * Approve quality inspection.
     */
    public function approveQuality(int $approvedQuantity, int $rejectedQuantity = 0, string $notes = null): void
    {
        $this->quantity_approved = $approvedQuantity;
        $this->quantity_rejected = $rejectedQuantity;
        $this->quality_notes = $notes;
        
        if ($rejectedQuantity > 0) {
            $this->quality_status = $approvedQuantity > 0 ? 'partial' : 'rejected';
        } else {
            $this->quality_status = 'approved';
        }
        
        $this->save();
        
        // If items were rejected, create damage movement
        if ($rejectedQuantity > 0) {
            $this->inventoryItem->updateStock(-$rejectedQuantity, 'damage', [
                'reference_type' => 'purchase_order',
                'reference_id' => $this->purchase_order_id,
                'reference_number' => $this->purchaseOrder->po_number,
                'notes' => "Quality rejection: {$notes}",
            ]);
        }
    }

    /**
     * Get days until expected delivery.
     */
    public function getDaysUntilDeliveryAttribute(): ?int
    {
        if (!$this->expected_delivery_date) {
            return null;
        }
        
        return now()->diffInDays($this->expected_delivery_date, false);
    }

    /**
     * Get item summary.
     */
    public function getSummaryAttribute(): array
    {
        return [
            'delivery_progress' => $this->delivery_progress,
            'quantity_pending' => $this->quantity_pending,
            'is_overdue' => $this->isOverdue(),
            'days_until_delivery' => $this->days_until_delivery,
            'quality_approval_rate' => $this->quality_approval_rate,
            'net_unit_cost' => $this->net_unit_cost,
            'net_total_cost' => $this->net_total_cost,
        ];
    }
}
