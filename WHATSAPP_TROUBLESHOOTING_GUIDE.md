# 🔧 WhatsApp Business API - Troubleshooting Guide

This guide provides solutions to common issues encountered when setting up and using WhatsApp Business API with the NJ Repair Shop system.

## 📋 Quick Diagnostics

### Run System Diagnostics
```bash
# Check overall WhatsApp system status
php artisan whatsapp:status

# Test API connectivity
php artisan whatsapp:test-connection

# Validate configuration
php artisan whatsapp:validate-config

# Check webhook logs
php artisan whatsapp:webhook-logs --recent
```

---

## 🚨 Common Issues and Solutions

### 1. Webhook Verification Failed

#### Error Messages:
```
Webhook verification failed
Invalid verify token
Challenge verification error
```

#### Causes and Solutions:

**A. Incorrect Verify Token**
```bash
# Check your .env file
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_secure_token_here

# Verify token matches in Meta Developer Console
# Webhook Settings > Verify Token
```

**B. Webhook URL Not Accessible**
```bash
# Test webhook URL manually
curl -X GET "https://yourdomain.com/api/whatsapp/webhook?hub.mode=subscribe&hub.challenge=test&hub.verify_token=YOUR_TOKEN"

# Should return: test
```

**C. SSL Certificate Issues**
```bash
# Check SSL certificate
curl -I https://yourdomain.com/api/whatsapp/webhook

# Install/renew SSL certificate
sudo certbot --nginx -d yourdomain.com
```

**D. Route Not Registered**
```php
// Check routes/api.php contains:
Route::get('/whatsapp/webhook', [WhatsAppWebhookController::class, 'verify']);
Route::post('/whatsapp/webhook', [WhatsAppWebhookController::class, 'handle']);
```

---

### 2. Message Sending Failed

#### Error Messages:
```
(#100) Param phone_number is not a valid phone number
(#131000) Recipient phone number not in allowed list
Template not found or not approved
Rate limit exceeded
```

#### Solutions:

**A. Invalid Phone Number Format**
```php
// Correct format (with country code)
$phone = '+966501234567';  // ✅ Correct
$phone = '966501234567';   // ❌ Wrong
$phone = '0501234567';     // ❌ Wrong

// Validation function
function validatePhoneNumber($phone) {
    return preg_match('/^\+[1-9]\d{1,14}$/', $phone);
}
```

**B. Phone Number Not in Test List**
```
During development, only test phone numbers can receive messages.
Add phone numbers in Meta Developer Console:
WhatsApp > API Setup > Add Phone Number
```

**C. Template Not Approved**
```bash
# Check template status
php artisan whatsapp:check-templates

# List all templates with status
php artisan whatsapp:list-templates --status=all
```

**D. Rate Limit Exceeded**
```php
// Implement rate limiting
use Illuminate\Support\Facades\RateLimiter;

if (RateLimiter::tooManyAttempts('whatsapp-send:'.$phone, 5)) {
    $seconds = RateLimiter::availableIn('whatsapp-send:'.$phone);
    throw new Exception("Rate limit exceeded. Try again in {$seconds} seconds.");
}

RateLimiter::hit('whatsapp-send:'.$phone, 60); // 5 attempts per minute
```

---

### 3. Template Message Issues

#### Error Messages:
```
Template not found
Invalid template parameters
Template language not supported
Template not approved
```

#### Solutions:

**A. Template Not Found**
```bash
# Check if template exists in database
php artisan tinker
>>> App\Models\WhatsAppTemplate::where('name', 'repair_received_ar')->first();

# Reseed templates if missing
php artisan db:seed --class=WhatsAppTemplateSeeder
```

**B. Invalid Parameters**
```php
// Correct parameter format
$parameters = [
    'أحمد محمد',        // Customer name
    'iPhone 13 Pro',    // Device model
    'TKT-001'          // Ticket number
];

// Send template message
$whatsapp->sendTemplateMessage($phone, 'repair_received_ar', $parameters);
```

**C. Template Not Approved**
```
1. Go to Meta Business Manager
2. Navigate to WhatsApp Manager > Message Templates
3. Check template status
4. Resubmit if rejected
5. Wait 24-48 hours for approval
```

---

### 4. Webhook Processing Errors

#### Error Messages:
```
Webhook signature validation failed
Invalid webhook payload
Webhook processing timeout
Duplicate webhook received
```

#### Solutions:

**A. Signature Validation Failed**
```php
// In WhatsAppWebhookController
private function validateSignature($payload, $signature)
{
    $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, config('whatsapp.webhook.secret'));
    return hash_equals($expectedSignature, $signature);
}
```

**B. Invalid Payload**
```php
// Log webhook payload for debugging
Log::info('WhatsApp Webhook Payload', [
    'headers' => request()->headers->all(),
    'payload' => request()->getContent()
]);
```

**C. Processing Timeout**
```php
// Use queued jobs for webhook processing
dispatch(new ProcessWhatsAppWebhook($webhookData))->onQueue('whatsapp');
```

---

### 5. Database and Migration Issues

#### Error Messages:
```
Table 'whatsapp_messages' doesn't exist
Column not found
Migration failed
```

#### Solutions:

**A. Missing Tables**
```bash
# Run WhatsApp migrations
php artisan migrate --path=database/migrations/2025_08_01_18*

# Check migration status
php artisan migrate:status | grep whatsapp
```

**B. Migration Rollback**
```bash
# Rollback WhatsApp migrations
php artisan migrate:rollback --step=4

# Re-run migrations
php artisan migrate
```

**C. Database Connection Issues**
```bash
# Test database connection
php artisan tinker
>>> DB::connection()->getPdo();

# Check database configuration
php artisan config:show database
```

---

### 6. Queue Processing Issues

#### Error Messages:
```
Queue worker not running
Job failed after maximum attempts
Redis connection refused
```

#### Solutions:

**A. Queue Worker Not Running**
```bash
# Start queue worker
php artisan queue:work --queue=whatsapp,default

# For production, use Supervisor
sudo supervisorctl start laravel-worker:*
```

**B. Redis Connection Issues**
```bash
# Check Redis status
redis-cli ping

# Start Redis service
sudo systemctl start redis-server

# Check Redis configuration in .env
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

**C. Failed Jobs**
```bash
# View failed jobs
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all

# Clear failed jobs
php artisan queue:flush
```

---

### 7. API Authentication Issues

#### Error Messages:
```
Invalid access token
Token expired
Insufficient permissions
App not approved
```

#### Solutions:

**A. Invalid/Expired Token**
```
1. Go to Meta Developer Console
2. Navigate to WhatsApp > Getting Started
3. Generate new permanent access token
4. Update .env file with new token
```

**B. Insufficient Permissions**
```
Required permissions:
- whatsapp_business_messaging
- whatsapp_business_management
- business_management

Add permissions in Meta Developer Console > App Review
```

**C. App Not Approved**
```
1. Submit app for review in Meta Developer Console
2. Provide required business verification documents
3. Wait for approval (can take several days)
```

---

### 8. SSL and HTTPS Issues

#### Error Messages:
```
SSL certificate verification failed
Webhook URL must use HTTPS
Certificate expired
```

#### Solutions:

**A. Install SSL Certificate**
```bash
# Using Let's Encrypt
sudo certbot --nginx -d yourdomain.com

# Verify SSL
curl -I https://yourdomain.com
```

**B. Certificate Renewal**
```bash
# Check certificate expiry
openssl x509 -in /etc/ssl/certs/yourdomain.com.crt -text -noout | grep "Not After"

# Renew certificate
sudo certbot renew
```

**C. Force HTTPS**
```php
// In AppServiceProvider boot method
if (app()->environment('production')) {
    URL::forceScheme('https');
}
```

---

## 🔍 Debugging Tools

### Enable Debug Logging
```php
// In config/whatsapp.php
'logging' => [
    'enabled' => true,
    'level' => 'debug',
    'log_api_calls' => true,
    'log_webhooks' => true,
],
```

### Custom Debug Commands
```bash
# View recent WhatsApp logs
php artisan whatsapp:logs --lines=50

# Test specific template
php artisan whatsapp:test-template repair_received_ar +966501234567

# Validate webhook signature
php artisan whatsapp:validate-webhook --payload="webhook_payload.json"

# Check API rate limits
php artisan whatsapp:rate-limits
```

### Database Queries for Debugging
```sql
-- Check recent messages
SELECT * FROM whatsapp_messages 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) 
ORDER BY created_at DESC;

-- Check failed messages
SELECT * FROM whatsapp_messages 
WHERE status = 'failed' 
ORDER BY created_at DESC;

-- Check webhook processing
SELECT * FROM whatsapp_webhooks 
WHERE status IN ('failed', 'processing') 
ORDER BY created_at DESC;

-- Check template usage
SELECT name, usage_count, success_rate, last_used_at 
FROM whatsapp_templates 
WHERE is_active = 1;
```

---

## 📞 Getting Help

### Meta Support Resources
- **WhatsApp Business API Documentation**: https://developers.facebook.com/docs/whatsapp
- **Meta Business Support**: Available through Business Manager
- **Developer Community**: https://developers.facebook.com/community/

### System Logs Location
```bash
# Laravel logs
tail -f storage/logs/laravel.log

# WhatsApp specific logs (if configured)
tail -f storage/logs/whatsapp.log

# Nginx error logs
sudo tail -f /var/log/nginx/error.log

# System logs
sudo journalctl -f -u nginx
```

### Emergency Fallback
```php
// Fallback to SMS when WhatsApp fails
if (config('whatsapp.features.fallback_to_sms')) {
    $smsService = app(SmsService::class);
    $smsService->sendMessage($phone, $message);
}
```

---

## ✅ Prevention Checklist

### Before Going Live
- [ ] All templates approved by Meta
- [ ] Webhook URL accessible via HTTPS
- [ ] SSL certificate valid and not expiring soon
- [ ] Queue workers running and monitored
- [ ] Rate limiting implemented
- [ ] Error handling and logging configured
- [ ] Fallback mechanisms in place
- [ ] Test messages sent successfully
- [ ] Webhook processing tested
- [ ] Database backups configured

### Regular Maintenance
- [ ] Monitor API usage and rate limits
- [ ] Check template approval status
- [ ] Review failed message logs
- [ ] Update SSL certificates before expiry
- [ ] Monitor queue processing
- [ ] Check webhook endpoint accessibility
- [ ] Review and clean old logs
- [ ] Test fallback mechanisms

---

**Remember**: WhatsApp Business API has strict policies. Always test thoroughly in development before deploying to production, and ensure compliance with WhatsApp's terms of service.
