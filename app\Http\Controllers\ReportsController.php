<?php

namespace App\Http\Controllers;

use App\Models\RepairTicket;
use App\Models\Customer;
use App\Models\Brand;
use App\Models\RepairStatus;
use App\Models\User;
use App\Models\Invoice;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportsController extends Controller
{
    /**
     * Display the main dashboard with analytics
     */
    public function dashboard(Request $request): View
    {
        $dateRange = $this->getDateRange($request);

        // Key Performance Indicators
        $kpis = $this->getKPIs($dateRange);

        // Charts data
        $chartsData = [
            'ticketsOverTime' => $this->getTicketsOverTime($dateRange),
            'statusDistribution' => $this->getStatusDistribution($dateRange),
            'brandDistribution' => $this->getBrandDistribution($dateRange),
            'technicianPerformance' => $this->getTechnicianPerformance($dateRange),
            'averageRepairTime' => $this->getAverageRepairTime($dateRange),
        ];

        return view('reports.dashboard', compact('kpis', 'chartsData', 'dateRange'));
    }

    /**
     * Customer analytics report
     */
    public function customerAnalytics(Request $request): View
    {
        $dateRange = $this->getDateRange($request);

        $analytics = [
            'topCustomers' => $this->getTopCustomers($dateRange),
            'customerGrowth' => $this->getCustomerGrowth($dateRange),
            'repeatCustomers' => $this->getRepeatCustomers($dateRange),
            'customerSatisfaction' => $this->getCustomerSatisfactionMetrics($dateRange),
        ];

        return view('reports.customer-analytics', compact('analytics', 'dateRange'));
    }

    /**
     * Business intelligence report
     */
    public function businessIntelligence(Request $request): View
    {
        $dateRange = $this->getDateRange($request);

        $intelligence = [
            'revenueAnalysis' => $this->getRevenueAnalysis($dateRange),
            'profitabilityByBrand' => $this->getProfitabilityByBrand($dateRange),
            'seasonalTrends' => $this->getSeasonalTrends($dateRange),
            'deviceFailurePatterns' => $this->getDeviceFailurePatterns($dateRange),
        ];

        return view('reports.business-intelligence', compact('intelligence', 'dateRange'));
    }

    /**
     * Get date range from request or default to last 30 days
     */
    private function getDateRange(Request $request): array
    {
        $startDate = $request->get('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        return [
            'start' => Carbon::parse($startDate),
            'end' => Carbon::parse($endDate),
            'start_formatted' => $startDate,
            'end_formatted' => $endDate,
        ];
    }

    /**
     * Get Key Performance Indicators
     */
    private function getKPIs(array $dateRange): array
    {
        $query = RepairTicket::whereBetween('received_date', [$dateRange['start'], $dateRange['end']]);

        $totalTickets = $query->count();
        $completedTickets = $query->completed()->count();
        $overdueTickets = $query->overdue()->count();
        $avgRepairTime = $this->calculateAverageRepairTime($dateRange);

        // Calculate completion rate
        $completionRate = $totalTickets > 0 ? round(($completedTickets / $totalTickets) * 100, 1) : 0;

        // Calculate revenue
        $totalRevenue = RepairTicket::whereBetween('received_date', [$dateRange['start'], $dateRange['end']])
            ->whereNotNull('final_cost')
            ->sum('final_cost');

        return [
            'total_tickets' => $totalTickets,
            'completed_tickets' => $completedTickets,
            'overdue_tickets' => $overdueTickets,
            'completion_rate' => $completionRate,
            'avg_repair_time' => $avgRepairTime,
            'total_revenue' => $totalRevenue,
        ];
    }

    /**
     * Get tickets over time for chart
     */
    private function getTicketsOverTime(array $dateRange): array
    {
        $tickets = RepairTicket::selectRaw('DATE(received_date) as date, COUNT(*) as count')
            ->whereBetween('received_date', [$dateRange['start'], $dateRange['end']])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'labels' => $tickets->pluck('date')->map(fn($date) => Carbon::parse($date)->format('M d'))->toArray(),
            'data' => $tickets->pluck('count')->toArray(),
        ];
    }

    /**
     * Get status distribution for pie chart
     */
    private function getStatusDistribution(array $dateRange): array
    {
        $distribution = RepairTicket::select('repair_statuses.name', 'repair_statuses.color', DB::raw('COUNT(*) as count'))
            ->join('repair_statuses', 'repair_tickets.repair_status_id', '=', 'repair_statuses.id')
            ->whereBetween('repair_tickets.received_date', [$dateRange['start'], $dateRange['end']])
            ->groupBy('repair_statuses.id', 'repair_statuses.name', 'repair_statuses.color')
            ->get();

        return [
            'labels' => $distribution->pluck('name')->toArray(),
            'data' => $distribution->pluck('count')->toArray(),
            'colors' => $distribution->pluck('color')->toArray(),
        ];
    }

    /**
     * Get brand distribution
     */
    private function getBrandDistribution(array $dateRange): array
    {
        $distribution = RepairTicket::select('brands.name', DB::raw('COUNT(*) as count'))
            ->join('brands', 'repair_tickets.brand_id', '=', 'brands.id')
            ->whereBetween('repair_tickets.received_date', [$dateRange['start'], $dateRange['end']])
            ->groupBy('brands.id', 'brands.name')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        return [
            'labels' => $distribution->pluck('name')->toArray(),
            'data' => $distribution->pluck('count')->toArray(),
        ];
    }

    /**
     * Get technician performance metrics
     */
    private function getTechnicianPerformance(array $dateRange): array
    {
        $performance = RepairTicket::select(
                'users.name',
                DB::raw('COUNT(*) as total_tickets'),
                DB::raw('COUNT(CASE WHEN repair_statuses.is_final = 1 THEN 1 END) as completed_tickets'),
                DB::raw('AVG(CASE WHEN completed_date IS NOT NULL THEN DATEDIFF(completed_date, received_date) END) as avg_days')
            )
            ->join('users', 'repair_tickets.assigned_technician_id', '=', 'users.id')
            ->join('repair_statuses', 'repair_tickets.repair_status_id', '=', 'repair_statuses.id')
            ->whereBetween('repair_tickets.received_date', [$dateRange['start'], $dateRange['end']])
            ->whereNotNull('repair_tickets.assigned_technician_id')
            ->groupBy('users.id', 'users.name')
            ->get();

        return [
            'technicians' => $performance->pluck('name')->toArray(),
            'total_tickets' => $performance->pluck('total_tickets')->toArray(),
            'completed_tickets' => $performance->pluck('completed_tickets')->toArray(),
            'avg_days' => $performance->pluck('avg_days')->map(fn($days) => round($days, 1))->toArray(),
        ];
    }

    /**
     * Calculate average repair time
     */
    private function calculateAverageRepairTime(array $dateRange): float
    {
        $avgDays = RepairTicket::whereBetween('received_date', [$dateRange['start'], $dateRange['end']])
            ->whereNotNull('completed_date')
            ->selectRaw('AVG(DATEDIFF(completed_date, received_date)) as avg_days')
            ->value('avg_days');

        return round($avgDays ?? 0, 1);
    }

    /**
     * Get average repair time over time
     */
    private function getAverageRepairTime(array $dateRange): array
    {
        $repairTimes = RepairTicket::selectRaw('
                DATE_FORMAT(completed_date, "%Y-%m") as month,
                AVG(DATEDIFF(completed_date, received_date)) as avg_days
            ')
            ->whereBetween('received_date', [$dateRange['start'], $dateRange['end']])
            ->whereNotNull('completed_date')
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return [
            'labels' => $repairTimes->pluck('month')->map(fn($month) => Carbon::parse($month)->format('M Y'))->toArray(),
            'data' => $repairTimes->pluck('avg_days')->map(fn($days) => round($days ?? 0, 1))->toArray(),
        ];
    }

    /**
     * Get top customers by ticket count
     */
    private function getTopCustomers(array $dateRange): array
    {
        $topCustomers = RepairTicket::select(
                'customers.name',
                'customers.phone_number',
                DB::raw('COUNT(*) as ticket_count'),
                DB::raw('SUM(COALESCE(final_cost, 0)) as total_spent')
            )
            ->join('customers', 'repair_tickets.customer_id', '=', 'customers.id')
            ->whereBetween('repair_tickets.received_date', [$dateRange['start'], $dateRange['end']])
            ->groupBy('customers.id', 'customers.name', 'customers.phone_number')
            ->orderBy('ticket_count', 'desc')
            ->limit(10)
            ->get();

        return $topCustomers->toArray();
    }

    /**
     * Get customer growth over time
     */
    private function getCustomerGrowth(array $dateRange): array
    {
        $growth = Customer::selectRaw('DATE_FORMAT(created_at, "%Y-%m") as month, COUNT(*) as new_customers')
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return [
            'labels' => $growth->pluck('month')->map(fn($month) => Carbon::parse($month)->format('M Y'))->toArray(),
            'data' => $growth->pluck('new_customers')->toArray(),
        ];
    }

    /**
     * Get repeat customers analysis
     */
    private function getRepeatCustomers(array $dateRange): array
    {
        $repeatCustomers = RepairTicket::select('customer_id', DB::raw('COUNT(*) as visit_count'))
            ->whereBetween('received_date', [$dateRange['start'], $dateRange['end']])
            ->groupBy('customer_id')
            ->having('visit_count', '>', 1)
            ->get();

        $totalCustomers = RepairTicket::whereBetween('received_date', [$dateRange['start'], $dateRange['end']])
            ->distinct('customer_id')
            ->count();

        $repeatRate = $totalCustomers > 0 ? round(($repeatCustomers->count() / $totalCustomers) * 100, 1) : 0;

        return [
            'repeat_customers' => $repeatCustomers->count(),
            'total_customers' => $totalCustomers,
            'repeat_rate' => $repeatRate,
            'distribution' => $repeatCustomers->groupBy('visit_count')->map->count()->toArray(),
        ];
    }

    /**
     * Get customer satisfaction metrics
     */
    private function getCustomerSatisfactionMetrics(array $dateRange): array
    {
        // Since we don't have a satisfaction rating system, we'll use completion time and repeat customers as proxies
        $avgCompletionTime = $this->calculateAverageRepairTime($dateRange);
        $repeatCustomerRate = $this->getRepeatCustomers($dateRange)['repeat_rate'];

        // Calculate on-time completion rate (tickets completed within estimated time)
        $onTimeTickets = RepairTicket::whereBetween('received_date', [$dateRange['start'], $dateRange['end']])
            ->whereNotNull('completed_date')
            ->whereNotNull('estimated_completion_date')
            ->whereRaw('completed_date <= estimated_completion_date')
            ->count();

        $totalCompletedTickets = RepairTicket::whereBetween('received_date', [$dateRange['start'], $dateRange['end']])
            ->whereNotNull('completed_date')
            ->whereNotNull('estimated_completion_date')
            ->count();

        $onTimeRate = $totalCompletedTickets > 0 ? round(($onTimeTickets / $totalCompletedTickets) * 100, 1) : 0;

        return [
            'avg_completion_time' => $avgCompletionTime,
            'repeat_customer_rate' => $repeatCustomerRate,
            'on_time_completion_rate' => $onTimeRate,
            'total_completed_tickets' => $totalCompletedTickets,
            'on_time_tickets' => $onTimeTickets,
        ];
    }

    /**
     * Get revenue analysis
     */
    private function getRevenueAnalysis(array $dateRange): array
    {
        $revenueByMonth = RepairTicket::selectRaw('
                DATE_FORMAT(completed_date, "%Y-%m") as month,
                SUM(final_cost) as revenue,
                COUNT(*) as completed_tickets
            ')
            ->whereBetween('received_date', [$dateRange['start'], $dateRange['end']])
            ->whereNotNull('final_cost')
            ->whereNotNull('completed_date')
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $totalRevenue = $revenueByMonth->sum('revenue');
        $avgTicketValue = $revenueByMonth->sum('completed_tickets') > 0
            ? round($totalRevenue / $revenueByMonth->sum('completed_tickets'), 2)
            : 0;

        return [
            'monthly_revenue' => [
                'labels' => $revenueByMonth->pluck('month')->map(fn($month) => Carbon::parse($month)->format('M Y'))->toArray(),
                'data' => $revenueByMonth->pluck('revenue')->toArray(),
            ],
            'total_revenue' => $totalRevenue,
            'avg_ticket_value' => $avgTicketValue,
        ];
    }

    /**
     * Get profitability by brand
     */
    private function getProfitabilityByBrand(array $dateRange): array
    {
        $profitability = RepairTicket::select(
                'brands.name',
                DB::raw('COUNT(*) as ticket_count'),
                DB::raw('SUM(COALESCE(final_cost, 0)) as total_revenue'),
                DB::raw('AVG(COALESCE(final_cost, 0)) as avg_revenue')
            )
            ->join('brands', 'repair_tickets.brand_id', '=', 'brands.id')
            ->whereBetween('repair_tickets.received_date', [$dateRange['start'], $dateRange['end']])
            ->whereNotNull('repair_tickets.final_cost')
            ->groupBy('brands.id', 'brands.name')
            ->orderBy('total_revenue', 'desc')
            ->get();

        return $profitability->toArray();
    }

    /**
     * Get seasonal trends analysis
     */
    private function getSeasonalTrends(array $dateRange): array
    {
        $monthlyData = RepairTicket::selectRaw('
                MONTH(received_date) as month,
                YEAR(received_date) as year,
                COUNT(*) as ticket_count,
                SUM(COALESCE(final_cost, 0)) as revenue
            ')
            ->whereBetween('received_date', [$dateRange['start'], $dateRange['end']])
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return [
            'monthly_tickets' => [
                'labels' => $monthlyData->map(fn($item) => Carbon::create($item->year, $item->month)->format('M Y'))->toArray(),
                'data' => $monthlyData->pluck('ticket_count')->toArray(),
            ],
            'monthly_revenue' => [
                'labels' => $monthlyData->map(fn($item) => Carbon::create($item->year, $item->month)->format('M Y'))->toArray(),
                'data' => $monthlyData->pluck('revenue')->toArray(),
            ],
        ];
    }

    /**
     * Get device failure patterns
     */
    private function getDeviceFailurePatterns(array $dateRange): array
    {
        $patterns = RepairTicket::select(
                'brands.name as brand',
                'device_model',
                DB::raw('COUNT(*) as failure_count'),
                DB::raw('GROUP_CONCAT(DISTINCT SUBSTRING(reported_problem, 1, 50) SEPARATOR "; ") as common_problems')
            )
            ->join('brands', 'repair_tickets.brand_id', '=', 'brands.id')
            ->whereBetween('repair_tickets.received_date', [$dateRange['start'], $dateRange['end']])
            ->groupBy('brands.id', 'brands.name', 'device_model')
            ->having('failure_count', '>', 1)
            ->orderBy('failure_count', 'desc')
            ->limit(20)
            ->get();

        return $patterns->toArray();
    }

    /**
     * Financial reports dashboard.
     */
    public function financial(): View
    {
        return view('reports.financial');
    }

    /**
     * Revenue reports.
     */
    public function revenue(Request $request): View
    {
        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));
        $groupBy = $request->get('group_by', 'day');

        // Daily revenue data
        $revenueData = $this->getRevenueData($dateFrom, $dateTo, $groupBy);

        // Summary statistics
        $stats = [
            'total_revenue' => Payment::where('status', 'completed')
                ->whereBetween('payment_date', [$dateFrom, $dateTo])
                ->sum('amount'),
            'total_invoices' => Invoice::whereBetween('invoice_date', [$dateFrom, $dateTo])->count(),
            'paid_invoices' => Invoice::where('payment_status', 'paid')
                ->whereBetween('invoice_date', [$dateFrom, $dateTo])
                ->count(),
            'average_invoice' => Invoice::whereBetween('invoice_date', [$dateFrom, $dateTo])
                ->avg('total_amount'),
        ];

        // Payment methods breakdown
        $paymentMethods = Payment::where('status', 'completed')
            ->whereBetween('payment_date', [$dateFrom, $dateTo])
            ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as total')
            ->groupBy('payment_method')
            ->get();

        return view('reports.revenue', compact('revenueData', 'stats', 'paymentMethods', 'dateFrom', 'dateTo', 'groupBy'));
    }

    /**
     * Outstanding invoices report.
     */
    public function outstanding(): View
    {
        $outstandingInvoices = Invoice::with(['customer'])
            ->whereIn('payment_status', ['pending', 'partial'])
            ->orderBy('due_date', 'asc')
            ->get();

        $overdueInvoices = $outstandingInvoices->filter(function($invoice) {
            return $invoice->due_date < now();
        });

        $stats = [
            'total_outstanding' => $outstandingInvoices->sum(function($invoice) {
                return $invoice->total_amount - $invoice->paid_amount;
            }),
            'overdue_amount' => $overdueInvoices->sum(function($invoice) {
                return $invoice->total_amount - $invoice->paid_amount;
            }),
            'outstanding_count' => $outstandingInvoices->count(),
            'overdue_count' => $overdueInvoices->count(),
        ];

        return view('reports.outstanding', compact('outstandingInvoices', 'overdueInvoices', 'stats'));
    }

    /**
     * Customer analysis report.
     */
    public function customers(Request $request): View
    {
        $dateFrom = $request->get('date_from', now()->startOfYear()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        $customerStats = Customer::with(['invoices' => function($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('invoice_date', [$dateFrom, $dateTo]);
            }, 'payments' => function($query) use ($dateFrom, $dateTo) {
                $query->where('status', 'completed')
                      ->whereBetween('payment_date', [$dateFrom, $dateTo]);
            }])
            ->get()
            ->map(function($customer) {
                return [
                    'customer' => $customer,
                    'total_invoices' => $customer->invoices->count(),
                    'total_amount' => $customer->invoices->sum('total_amount'),
                    'total_paid' => $customer->payments->sum('amount'),
                    'outstanding' => $customer->invoices->sum('total_amount') - $customer->payments->sum('amount'),
                ];
            })
            ->sortByDesc('total_amount')
            ->take(50);

        return view('reports.customers', compact('customerStats', 'dateFrom', 'dateTo'));
    }

    /**
     * Profit and loss report.
     */
    public function profitLoss(Request $request): View
    {
        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        // Revenue
        $revenue = Payment::where('status', 'completed')
            ->whereBetween('payment_date', [$dateFrom, $dateTo])
            ->sum('amount');

        // Cost of goods sold (from invoice items)
        $cogs = DB::table('invoice_items')
            ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->join('inventory_items', 'invoice_items.inventory_item_id', '=', 'inventory_items.id')
            ->whereBetween('invoices.invoice_date', [$dateFrom, $dateTo])
            ->where('invoice_items.item_type', 'part')
            ->sum(DB::raw('invoice_items.quantity * inventory_items.cost_price'));

        // Expenses (this would need an expenses table in a real system)
        $expenses = 0; // Placeholder

        $grossProfit = $revenue - $cogs;
        $netProfit = $grossProfit - $expenses;

        $data = [
            'revenue' => $revenue,
            'cogs' => $cogs,
            'gross_profit' => $grossProfit,
            'expenses' => $expenses,
            'net_profit' => $netProfit,
            'gross_margin' => $revenue > 0 ? ($grossProfit / $revenue) * 100 : 0,
            'net_margin' => $revenue > 0 ? ($netProfit / $revenue) * 100 : 0,
        ];

        return view('reports.profit-loss', compact('data', 'dateFrom', 'dateTo'));
    }

    /**
     * Get revenue data for charts.
     */
    private function getRevenueData(string $dateFrom, string $dateTo, string $groupBy): array
    {
        $dateFormat = match($groupBy) {
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            'year' => '%Y',
            default => '%Y-%m-%d'
        };

        $data = Payment::where('status', 'completed')
            ->whereBetween('payment_date', [$dateFrom, $dateTo])
            ->selectRaw("DATE_FORMAT(payment_date, '{$dateFormat}') as period, SUM(amount) as total")
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        return $data->pluck('total', 'period')->toArray();
    }

    /**
     * Get financial dashboard statistics.
     */
    public function financialStats(): JsonResponse
    {
        $today = now()->format('Y-m-d');
        $thisMonth = now()->format('Y-m');
        $thisYear = now()->format('Y');

        $stats = [
            'today' => [
                'revenue' => Payment::where('status', 'completed')
                    ->whereDate('payment_date', $today)
                    ->sum('amount'),
                'invoices' => Invoice::whereDate('invoice_date', $today)->count(),
                'payments' => Payment::where('status', 'completed')
                    ->whereDate('payment_date', $today)
                    ->count(),
            ],
            'this_month' => [
                'revenue' => Payment::where('status', 'completed')
                    ->where('payment_date', 'like', $thisMonth . '%')
                    ->sum('amount'),
                'invoices' => Invoice::where('invoice_date', 'like', $thisMonth . '%')->count(),
                'payments' => Payment::where('status', 'completed')
                    ->where('payment_date', 'like', $thisMonth . '%')
                    ->count(),
            ],
            'this_year' => [
                'revenue' => Payment::where('status', 'completed')
                    ->where('payment_date', 'like', $thisYear . '%')
                    ->sum('amount'),
                'invoices' => Invoice::where('invoice_date', 'like', $thisYear . '%')->count(),
                'payments' => Payment::where('status', 'completed')
                    ->where('payment_date', 'like', $thisYear . '%')
                    ->count(),
            ],
            'outstanding' => [
                'amount' => Invoice::whereIn('payment_status', ['pending', 'partial'])
                    ->sum(DB::raw('total_amount - paid_amount')),
                'count' => Invoice::whereIn('payment_status', ['pending', 'partial'])->count(),
            ],
            'overdue' => [
                'amount' => Invoice::whereIn('payment_status', ['pending', 'partial'])
                    ->where('due_date', '<', now())
                    ->sum(DB::raw('total_amount - paid_amount')),
                'count' => Invoice::whereIn('payment_status', ['pending', 'partial'])
                    ->where('due_date', '<', now())
                    ->count(),
            ]
        ];

        return response()->json($stats);
    }
}
