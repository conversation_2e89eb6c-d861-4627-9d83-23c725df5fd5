@extends('layouts.app')

@section('title', __('app.repair_statuses.edit'))

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ __('app.repair_statuses.edit') }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('repair-statuses.index') }}">{{ __('app.repair_statuses.title') }}</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('repair-statuses.show', $repairStatus) }}">{{ $repairStatus->name }}</a></li>
                    <li class="breadcrumb-item active">{{ __('app.repair_statuses.edit') }}</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="{{ route('repair-statuses.show', $repairStatus) }}" class="btn btn-secondary">
                <i class="fas fa-eye me-2"></i>{{ __('app.common.view') }}
            </a>
            <a href="{{ route('repair-statuses.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>{{ __('app.common.back') }}
            </a>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.repair_statuses.status_details') }}</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('repair-statuses.update', $repairStatus) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Status Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label">{{ __('app.repair_statuses.name') }} <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $repairStatus->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">{{ __('app.repair_statuses.description') }}</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description', $repairStatus->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Color -->
                        <div class="mb-3">
                            <label for="color" class="form-label">{{ __('app.repair_statuses.color') }}</label>
                            <div class="input-group">
                                <input type="color" class="form-control form-control-color @error('color') is-invalid @enderror" 
                                       id="color" name="color" value="{{ old('color', $repairStatus->color) }}" title="{{ __('app.repair_statuses.choose_color') }}">
                                <input type="text" class="form-control @error('color') is-invalid @enderror" 
                                       id="color_text" name="color_text" value="{{ old('color', $repairStatus->color) }}" placeholder="#6B7280">
                            </div>
                            @error('color')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('app.repair_statuses.color_help') }}</div>
                        </div>

                        <!-- Sort Order -->
                        <div class="mb-3">
                            <label for="sort_order" class="form-label">{{ __('app.common.sort_order') }}</label>
                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                   id="sort_order" name="sort_order" value="{{ old('sort_order', $repairStatus->sort_order) }}" min="0">
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('app.common.sort_order_help') }}</div>
                        </div>

                        <!-- Status Options -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input @error('is_active') is-invalid @enderror" 
                                               type="checkbox" id="is_active" name="is_active" value="1" 
                                               {{ old('is_active', $repairStatus->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            {{ __('app.common.is_active') }}
                                        </label>
                                    </div>
                                    @error('is_active')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">{{ __('app.repair_statuses.is_active_help') }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input @error('is_final') is-invalid @enderror" 
                                               type="checkbox" id="is_final" name="is_final" value="1" 
                                               {{ old('is_final', $repairStatus->is_final) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_final">
                                            {{ __('app.repair_statuses.is_final') }}
                                        </label>
                                    </div>
                                    @error('is_final')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">{{ __('app.repair_statuses.is_final_help') }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('repair-statuses.show', $repairStatus) }}" class="btn btn-secondary">
                                {{ __('app.common.cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ __('app.common.update') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview Card -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.common.preview') }}</h5>
                </div>
                <div class="card-body">
                    <div class="preview-badge">
                        <span class="badge" id="status-preview" style="background-color: {{ $repairStatus->color }}; color: white;">
                            {{ $repairStatus->name }}
                        </span>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">{{ __('app.repair_statuses.preview_help') }}</small>
                    </div>
                </div>
            </div>

            <!-- Usage Statistics -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.common.usage_statistics') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12">
                            <div class="mb-2">
                                <h4 class="text-primary mb-0">{{ $repairStatus->repair_tickets_count ?? 0 }}</h4>
                                <small class="text-muted">{{ __('app.common.tickets_using_status') }}</small>
                            </div>
                        </div>
                    </div>
                    @if($repairStatus->repair_tickets_count > 0)
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {{ __('app.repair_statuses.has_tickets_warning') }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const colorInput = document.getElementById('color');
    const colorTextInput = document.getElementById('color_text');
    const preview = document.getElementById('status-preview');

    // Update preview when name changes
    nameInput.addEventListener('input', function() {
        const name = this.value || '{{ $repairStatus->name }}';
        preview.textContent = name;
    });

    // Sync color inputs
    colorInput.addEventListener('input', function() {
        colorTextInput.value = this.value;
        preview.style.backgroundColor = this.value;
    });

    colorTextInput.addEventListener('input', function() {
        if (this.value.match(/^#[0-9A-F]{6}$/i)) {
            colorInput.value = this.value;
            preview.style.backgroundColor = this.value;
        }
    });
});
</script>
@endpush
@endsection
