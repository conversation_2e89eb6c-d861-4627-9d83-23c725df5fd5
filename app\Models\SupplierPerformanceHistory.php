<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class SupplierPerformanceHistory extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'supplier_id',
        'evaluation_date',
        'delivery_performance',
        'quality_rating',
        'price_competitiveness',
        'communication_rating',
        'overall_rating',
        'orders_evaluated',
        'total_order_value',
        'strengths',
        'weaknesses',
        'notes',
        'evaluated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'evaluation_date' => 'date',
        'delivery_performance' => 'decimal:2',
        'quality_rating' => 'decimal:2',
        'price_competitiveness' => 'decimal:2',
        'communication_rating' => 'decimal:2',
        'overall_rating' => 'decimal:2',
        'total_order_value' => 'decimal:2',
        'strengths' => 'array',
        'weaknesses' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the supplier that owns this performance record.
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the user who evaluated this performance.
     */
    public function evaluatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'evaluated_by');
    }

    /**
     * Create performance evaluation for supplier.
     */
    public static function evaluateSupplier(Supplier $supplier, Carbon $startDate, Carbon $endDate, User $evaluator): self
    {
        // Get orders in the evaluation period
        $orders = $supplier->purchaseOrders()
            ->whereBetween('order_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->get();

        if ($orders->isEmpty()) {
            throw new \Exception('No orders found for evaluation period');
        }

        // Calculate delivery performance
        $completedOrders = $orders->where('status', 'completed');
        $onTimeDeliveries = $completedOrders->where('delivered_on_time', true)->count();
        $deliveryPerformance = $completedOrders->count() > 0 
            ? ($onTimeDeliveries / $completedOrders->count()) * 100 
            : 0;

        // Calculate average quality rating
        $qualityRatings = $completedOrders->whereNotNull('quality_score');
        $averageQuality = $qualityRatings->avg('quality_score') ?? 0;

        // Calculate price competitiveness (simplified - could be enhanced with market data)
        $priceCompetitiveness = static::calculatePriceCompetitiveness($orders);

        // Calculate communication rating (based on response times, issue resolution, etc.)
        $communicationRating = static::calculateCommunicationRating($supplier, $startDate, $endDate);

        // Calculate overall rating
        $overallRating = (
            $deliveryPerformance * 0.3 +
            $averageQuality * 20 * 0.25 +  // Convert 5-point scale to 100-point
            $priceCompetitiveness * 0.25 +
            $communicationRating * 0.2
        ) / 20; // Convert back to 5-point scale

        // Identify strengths and weaknesses
        $strengths = static::identifyStrengths($deliveryPerformance, $averageQuality, $priceCompetitiveness, $communicationRating);
        $weaknesses = static::identifyWeaknesses($deliveryPerformance, $averageQuality, $priceCompetitiveness, $communicationRating);

        return static::create([
            'supplier_id' => $supplier->id,
            'evaluation_date' => $endDate,
            'delivery_performance' => $deliveryPerformance,
            'quality_rating' => $averageQuality,
            'price_competitiveness' => $priceCompetitiveness / 20, // Convert to 5-point scale
            'communication_rating' => $communicationRating / 20, // Convert to 5-point scale
            'overall_rating' => $overallRating,
            'orders_evaluated' => $orders->count(),
            'total_order_value' => $orders->sum('total_amount'),
            'strengths' => $strengths,
            'weaknesses' => $weaknesses,
            'evaluated_by' => $evaluator->id,
        ]);
    }

    /**
     * Calculate price competitiveness score.
     */
    private static function calculatePriceCompetitiveness($orders): float
    {
        // Simplified calculation - in reality, this would compare with market prices
        $totalEstimated = $orders->sum('estimated_cost');
        $totalActual = $orders->sum('actual_cost');

        if ($totalEstimated <= 0) {
            return 75; // Default score if no estimates available
        }

        $variance = (($totalActual - $totalEstimated) / $totalEstimated) * 100;

        // Score based on cost variance (lower actual cost = higher score)
        if ($variance <= -10) return 100; // 10% or more under estimate
        if ($variance <= -5) return 90;   // 5-10% under estimate
        if ($variance <= 0) return 80;    // At or under estimate
        if ($variance <= 5) return 70;    // Up to 5% over estimate
        if ($variance <= 10) return 60;   // 5-10% over estimate
        if ($variance <= 20) return 50;   // 10-20% over estimate
        return 40; // More than 20% over estimate
    }

    /**
     * Calculate communication rating.
     */
    private static function calculateCommunicationRating(Supplier $supplier, Carbon $startDate, Carbon $endDate): float
    {
        // This is a simplified calculation
        // In reality, this would consider:
        // - Response times to inquiries
        // - Proactive communication about delays
        // - Quality of documentation provided
        // - Issue resolution effectiveness

        // For now, return a default score that could be manually adjusted
        return 75; // Default communication score
    }

    /**
     * Identify supplier strengths.
     */
    private static function identifyStrengths(float $delivery, float $quality, float $price, float $communication): array
    {
        $strengths = [];

        if ($delivery >= 90) {
            $strengths[] = app()->getLocale() === 'ar' ? 'التسليم في الوقت المحدد' : 'Excellent on-time delivery';
        }

        if ($quality >= 4.0) {
            $strengths[] = app()->getLocale() === 'ar' ? 'جودة عالية للمنتجات' : 'High product quality';
        }

        if ($price >= 80) {
            $strengths[] = app()->getLocale() === 'ar' ? 'أسعار تنافسية' : 'Competitive pricing';
        }

        if ($communication >= 80) {
            $strengths[] = app()->getLocale() === 'ar' ? 'تواصل ممتاز' : 'Excellent communication';
        }

        return $strengths;
    }

    /**
     * Identify areas for improvement.
     */
    private static function identifyWeaknesses(float $delivery, float $quality, float $price, float $communication): array
    {
        $weaknesses = [];

        if ($delivery < 70) {
            $weaknesses[] = app()->getLocale() === 'ar' ? 'تأخير في التسليم' : 'Delivery delays';
        }

        if ($quality < 3.0) {
            $weaknesses[] = app()->getLocale() === 'ar' ? 'مشاكل في الجودة' : 'Quality issues';
        }

        if ($price < 60) {
            $weaknesses[] = app()->getLocale() === 'ar' ? 'أسعار مرتفعة' : 'High pricing';
        }

        if ($communication < 60) {
            $weaknesses[] = app()->getLocale() === 'ar' ? 'ضعف في التواصل' : 'Poor communication';
        }

        return $weaknesses;
    }

    /**
     * Get performance trend for supplier.
     */
    public static function getPerformanceTrend(Supplier $supplier, int $periods = 6): array
    {
        $evaluations = static::where('supplier_id', $supplier->id)
            ->orderBy('evaluation_date', 'desc')
            ->limit($periods)
            ->get()
            ->reverse();

        return [
            'dates' => $evaluations->pluck('evaluation_date')->map(fn($date) => $date->format('M Y'))->toArray(),
            'overall_rating' => $evaluations->pluck('overall_rating')->toArray(),
            'delivery_performance' => $evaluations->pluck('delivery_performance')->toArray(),
            'quality_rating' => $evaluations->pluck('quality_rating')->map(fn($rating) => $rating * 20)->toArray(), // Convert to 100-point scale for chart
            'price_competitiveness' => $evaluations->pluck('price_competitiveness')->map(fn($rating) => $rating * 20)->toArray(),
            'communication_rating' => $evaluations->pluck('communication_rating')->map(fn($rating) => $rating * 20)->toArray(),
        ];
    }

    /**
     * Get latest evaluation for supplier.
     */
    public static function getLatestEvaluation(Supplier $supplier): ?self
    {
        return static::where('supplier_id', $supplier->id)
            ->orderBy('evaluation_date', 'desc')
            ->first();
    }

    /**
     * Get performance summary.
     */
    public function getPerformanceSummary(): array
    {
        return [
            'evaluation_info' => [
                'date' => $this->evaluation_date,
                'evaluator' => $this->evaluatedBy->name,
                'orders_evaluated' => $this->orders_evaluated,
                'total_value' => $this->total_order_value,
            ],
            'ratings' => [
                'overall' => $this->overall_rating,
                'delivery' => $this->delivery_performance,
                'quality' => $this->quality_rating,
                'price' => $this->price_competitiveness,
                'communication' => $this->communication_rating,
            ],
            'strengths' => $this->strengths,
            'weaknesses' => $this->weaknesses,
            'notes' => $this->notes,
        ];
    }

    /**
     * Get rating color for UI.
     */
    public function getRatingColor(float $rating, bool $isPercentage = false): string
    {
        $threshold = $isPercentage ? [90, 75, 60, 40] : [4.5, 3.5, 2.5, 1.5];

        if ($rating >= $threshold[0]) return 'success';
        if ($rating >= $threshold[1]) return 'primary';
        if ($rating >= $threshold[2]) return 'warning';
        if ($rating >= $threshold[3]) return 'danger';
        return 'dark';
    }

    /**
     * Get overall rating display.
     */
    public function getOverallRatingDisplayAttribute(): string
    {
        $ratings = [
            4.5 => app()->getLocale() === 'ar' ? 'ممتاز' : 'Excellent',
            3.5 => app()->getLocale() === 'ar' ? 'جيد جداً' : 'Very Good',
            2.5 => app()->getLocale() === 'ar' ? 'جيد' : 'Good',
            1.5 => app()->getLocale() === 'ar' ? 'مقبول' : 'Fair',
            0 => app()->getLocale() === 'ar' ? 'ضعيف' : 'Poor',
        ];

        foreach ($ratings as $threshold => $label) {
            if ($this->overall_rating >= $threshold) {
                return $label;
            }
        }

        return $ratings[0];
    }

    /**
     * Scope for recent evaluations.
     */
    public function scopeRecent($query, int $months = 12)
    {
        return $query->where('evaluation_date', '>=', now()->subMonths($months));
    }

    /**
     * Scope for evaluations by rating range.
     */
    public function scopeByRatingRange($query, float $minRating, float $maxRating)
    {
        return $query->whereBetween('overall_rating', [$minRating, $maxRating]);
    }

    /**
     * Get top performing suppliers.
     */
    public static function getTopPerformers(int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return static::with(['supplier', 'evaluatedBy'])
            ->whereHas('supplier', function ($query) {
                $query->where('is_active', true);
            })
            ->orderBy('overall_rating', 'desc')
            ->orderBy('evaluation_date', 'desc')
            ->limit($limit)
            ->get()
            ->unique('supplier_id')
            ->take($limit);
    }
}
