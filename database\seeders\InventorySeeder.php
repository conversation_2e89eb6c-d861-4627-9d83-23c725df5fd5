<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Supplier;
use App\Models\InventoryCategory;
use App\Models\InventoryItem;

class InventorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create suppliers
        $suppliers = [
            [
                'name' => 'شركة قطع الغيار المتقدمة',
                'company_name' => 'Advanced Parts Company',
                'contact_person' => 'أحمد محمد',
                'phone_number' => '0501234567',
                'email' => '<EMAIL>',
                'address' => 'شارع الملك فهد، الرياض',
                'city' => 'الرياض',
                'payment_terms' => 'credit_30',
                'credit_limit' => 50000,
                'is_active' => true,
            ],
            [
                'name' => 'مؤسسة الشاشات الذكية',
                'company_name' => 'Smart Screens Est.',
                'contact_person' => 'سارة أحمد',
                'phone_number' => '0509876543',
                'email' => '<EMAIL>',
                'address' => 'طريق الأمير محمد بن عبدالعزيز، جدة',
                'city' => 'جدة',
                'payment_terms' => 'credit_15',
                'credit_limit' => 30000,
                'is_active' => true,
            ],
            [
                'name' => 'متجر البطاريات الأصلية',
                'company_name' => 'Original Batteries Store',
                'contact_person' => 'محمد علي',
                'phone_number' => '0555555555',
                'email' => '<EMAIL>',
                'address' => 'شارع التحلية، الخبر',
                'city' => 'الخبر',
                'payment_terms' => 'cash',
                'credit_limit' => 0,
                'is_active' => true,
            ],
        ];

        foreach ($suppliers as $supplierData) {
            Supplier::create($supplierData);
        }

        // Create inventory categories
        $categories = [
            [
                'name' => 'شاشات',
                'slug' => 'screens',
                'description' => 'شاشات الهواتف الذكية والأجهزة اللوحية',
                'icon' => 'fas fa-tv',
                'color' => '#3B82F6',
                'sort_order' => 1,
            ],
            [
                'name' => 'بطاريات',
                'slug' => 'batteries',
                'description' => 'بطاريات الهواتف الذكية والأجهزة الإلكترونية',
                'icon' => 'fas fa-battery-full',
                'color' => '#10B981',
                'sort_order' => 2,
            ],
            [
                'name' => 'كاميرات',
                'slug' => 'cameras',
                'description' => 'كاميرات الهواتف الذكية الأمامية والخلفية',
                'icon' => 'fas fa-camera',
                'color' => '#8B5CF6',
                'sort_order' => 3,
            ],
            [
                'name' => 'مكبرات الصوت',
                'slug' => 'speakers',
                'description' => 'مكبرات الصوت والسماعات',
                'icon' => 'fas fa-volume-up',
                'color' => '#F59E0B',
                'sort_order' => 4,
            ],
            [
                'name' => 'أدوات الإصلاح',
                'slug' => 'repair-tools',
                'description' => 'أدوات ومعدات الإصلاح',
                'icon' => 'fas fa-tools',
                'color' => '#6B7280',
                'sort_order' => 5,
            ],
            [
                'name' => 'قطع غيار أخرى',
                'slug' => 'other-parts',
                'description' => 'قطع غيار متنوعة',
                'icon' => 'fas fa-cogs',
                'color' => '#EF4444',
                'sort_order' => 6,
            ],
        ];

        foreach ($categories as $categoryData) {
            InventoryCategory::create($categoryData);
        }

        // Create subcategories for screens
        $screenCategory = InventoryCategory::where('slug', 'screens')->first();
        $screenSubcategories = [
            [
                'name' => 'شاشات iPhone',
                'slug' => 'iphone-screens',
                'description' => 'شاشات هواتف آيفون',
                'icon' => 'fab fa-apple',
                'color' => '#000000',
                'parent_id' => $screenCategory->id,
                'sort_order' => 1,
            ],
            [
                'name' => 'شاشات Samsung',
                'slug' => 'samsung-screens',
                'description' => 'شاشات هواتف سامسونج',
                'icon' => 'fas fa-mobile-alt',
                'color' => '#1f4e79',
                'parent_id' => $screenCategory->id,
                'sort_order' => 2,
            ],
            [
                'name' => 'شاشات Huawei',
                'slug' => 'huawei-screens',
                'description' => 'شاشات هواتف هواوي',
                'icon' => 'fas fa-mobile-alt',
                'color' => '#FF0000',
                'parent_id' => $screenCategory->id,
                'sort_order' => 3,
            ],
        ];

        foreach ($screenSubcategories as $subcategoryData) {
            InventoryCategory::create($subcategoryData);
        }

        // Create sample inventory items
        $items = [
            [
                'name' => 'شاشة iPhone 14 Pro أصلية',
                'sku' => 'IP14P-SCR-001',
                'barcode' => '1234567890123',
                'description' => 'شاشة أصلية لهاتف iPhone 14 Pro مع اللمس',
                'category_id' => InventoryCategory::where('slug', 'iphone-screens')->first()->id,
                'brand_id' => 1, // Apple
                'model_compatibility' => 'iPhone 14 Pro',
                'part_number' => 'A2890-SCR',
                'current_stock' => 25,
                'minimum_stock' => 5,
                'maximum_stock' => 50,
                'reorder_quantity' => 20,
                'unit_of_measure' => 'قطعة',
                'cost_price' => 800.00,
                'selling_price' => 1200.00,
                'markup_percentage' => 50.00,
                'location' => 'رف A1',
                'warehouse_section' => 'شاشات آيفون',
                'primary_supplier_id' => 1,
                'lead_time_days' => 7,
                'condition' => 'new',
                'warranty_months' => 6,
                'is_active' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'بطارية iPhone 14 Pro أصلية',
                'sku' => 'IP14P-BAT-001',
                'barcode' => '1234567890124',
                'description' => 'بطارية أصلية لهاتف iPhone 14 Pro',
                'category_id' => InventoryCategory::where('slug', 'batteries')->first()->id,
                'brand_id' => 1, // Apple
                'model_compatibility' => 'iPhone 14 Pro',
                'part_number' => 'A2890-BAT',
                'current_stock' => 15,
                'minimum_stock' => 10,
                'maximum_stock' => 40,
                'reorder_quantity' => 25,
                'unit_of_measure' => 'قطعة',
                'cost_price' => 150.00,
                'selling_price' => 250.00,
                'markup_percentage' => 66.67,
                'location' => 'رف B2',
                'warehouse_section' => 'بطاريات',
                'primary_supplier_id' => 3,
                'lead_time_days' => 5,
                'condition' => 'new',
                'warranty_months' => 12,
                'is_active' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'شاشة Samsung Galaxy S23 Ultra',
                'sku' => 'SGS23U-SCR-001',
                'barcode' => '1234567890125',
                'description' => 'شاشة أصلية لهاتف Samsung Galaxy S23 Ultra',
                'category_id' => InventoryCategory::where('slug', 'samsung-screens')->first()->id,
                'brand_id' => 2, // Samsung
                'model_compatibility' => 'Galaxy S23 Ultra',
                'part_number' => 'SM-S918-SCR',
                'current_stock' => 8,
                'minimum_stock' => 5,
                'maximum_stock' => 30,
                'reorder_quantity' => 15,
                'unit_of_measure' => 'قطعة',
                'cost_price' => 900.00,
                'selling_price' => 1400.00,
                'markup_percentage' => 55.56,
                'location' => 'رف A2',
                'warehouse_section' => 'شاشات سامسونج',
                'primary_supplier_id' => 2,
                'lead_time_days' => 10,
                'condition' => 'new',
                'warranty_months' => 6,
                'is_active' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'مفك براغي دقيق مجموعة',
                'sku' => 'TOOL-SCR-001',
                'barcode' => '1234567890126',
                'description' => 'مجموعة مفكات براغي دقيقة للإصلاح',
                'category_id' => InventoryCategory::where('slug', 'repair-tools')->first()->id,
                'brand_id' => null,
                'model_compatibility' => 'جميع الأجهزة',
                'current_stock' => 50,
                'minimum_stock' => 20,
                'maximum_stock' => 100,
                'reorder_quantity' => 30,
                'unit_of_measure' => 'مجموعة',
                'cost_price' => 25.00,
                'selling_price' => 45.00,
                'markup_percentage' => 80.00,
                'location' => 'رف C1',
                'warehouse_section' => 'أدوات',
                'primary_supplier_id' => 1,
                'lead_time_days' => 3,
                'condition' => 'new',
                'warranty_months' => 0,
                'is_active' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'كاميرا iPhone 14 Pro خلفية',
                'sku' => 'IP14P-CAM-001',
                'barcode' => '1234567890127',
                'description' => 'كاميرا خلفية أصلية لهاتف iPhone 14 Pro',
                'category_id' => InventoryCategory::where('slug', 'cameras')->first()->id,
                'brand_id' => 1, // Apple
                'model_compatibility' => 'iPhone 14 Pro',
                'part_number' => 'A2890-CAM',
                'current_stock' => 3,
                'minimum_stock' => 5,
                'maximum_stock' => 20,
                'reorder_quantity' => 10,
                'unit_of_measure' => 'قطعة',
                'cost_price' => 400.00,
                'selling_price' => 650.00,
                'markup_percentage' => 62.50,
                'location' => 'رف D1',
                'warehouse_section' => 'كاميرات',
                'primary_supplier_id' => 1,
                'lead_time_days' => 14,
                'condition' => 'new',
                'warranty_months' => 3,
                'is_active' => true,
                'created_by' => 1,
            ],
        ];

        foreach ($items as $itemData) {
            InventoryItem::create($itemData);
        }

        $this->command->info('Created inventory data: ' . count($suppliers) . ' suppliers, ' . 
                           (count($categories) + count($screenSubcategories)) . ' categories, ' . 
                           count($items) . ' items.');
    }
}
