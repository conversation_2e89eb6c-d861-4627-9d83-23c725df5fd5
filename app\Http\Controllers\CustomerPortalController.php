<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\RepairTicket;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class CustomerPortalController extends Controller
{
    /**
     * Show customer login form
     */
    public function login(): View
    {
        return view('customer-portal.login');
    }

    /**
     * Handle customer login
     */
    public function authenticate(Request $request): RedirectResponse
    {
        $request->validate([
            'phone_number' => 'required|string',
            'verification_code' => 'nullable|string|size:4'
        ]);

        $phoneNumber = $this->cleanPhoneNumber($request->phone_number);
        
        // Find customer by phone number
        $customer = Customer::where('phone_number', $phoneNumber)
                           ->orWhere('phone_number', 'like', '%' . substr($phoneNumber, -9))
                           ->first();

        if (!$customer) {
            return back()->withErrors(['phone_number' => 'رقم الهاتف غير مسجل لدينا']);
        }

        // If verification code is provided, verify it
        if ($request->verification_code) {
            $storedCode = Session::get('verification_code_' . $customer->id);
            $codeTimestamp = Session::get('verification_code_time_' . $customer->id);
            
            // Check if code is valid and not expired (5 minutes)
            if (!$storedCode || 
                $storedCode !== $request->verification_code || 
                (time() - $codeTimestamp) > 300) {
                return back()->withErrors(['verification_code' => 'رمز التحقق غير صحيح أو منتهي الصلاحية']);
            }

            // Login successful
            Session::put('customer_id', $customer->id);
            Session::forget(['verification_code_' . $customer->id, 'verification_code_time_' . $customer->id]);
            
            return redirect()->route('customer.dashboard');
        }

        // Generate and send verification code
        $verificationCode = str_pad(random_int(0, 9999), 4, '0', STR_PAD_LEFT);
        
        // Store verification code in session
        Session::put('verification_code_' . $customer->id, $verificationCode);
        Session::put('verification_code_time_' . $customer->id, time());
        
        // Send SMS with verification code
        $smsService = app(\App\Services\SmsService::class);
        $message = "رمز التحقق الخاص بك في ورشة NJ: {$verificationCode}\nصالح لمدة 5 دقائق";
        
        if ($smsService->sendSms($customer->phone_number, $message)) {
            return back()->with([
                'verification_sent' => true,
                'customer_id' => $customer->id,
                'phone_number' => $phoneNumber
            ])->withInput(['phone_number']);
        } else {
            return back()->withErrors(['phone_number' => 'فشل في إرسال رمز التحقق. يرجى المحاولة مرة أخرى']);
        }
    }

    /**
     * Show customer dashboard
     */
    public function dashboard(): View
    {
        $customerId = Session::get('customer_id');
        
        if (!$customerId) {
            return redirect()->route('customer.login');
        }

        $customer = Customer::with(['repairTickets.repairStatus', 'repairTickets.brand'])
                           ->findOrFail($customerId);

        $tickets = $customer->repairTickets()
                           ->orderBy('created_at', 'desc')
                           ->paginate(10);

        $stats = [
            'total_tickets' => $customer->repairTickets()->count(),
            'pending_tickets' => $customer->repairTickets()
                                        ->whereHas('repairStatus', function($q) {
                                            $q->where('is_final', false);
                                        })->count(),
            'completed_tickets' => $customer->repairTickets()
                                          ->whereHas('repairStatus', function($q) {
                                              $q->where('is_final', true);
                                          })->count(),
        ];

        return view('customer-portal.dashboard', compact('customer', 'tickets', 'stats'));
    }

    /**
     * Show ticket details
     */
    public function showTicket(string $ticketNumber): View
    {
        $customerId = Session::get('customer_id');
        
        if (!$customerId) {
            return redirect()->route('customer.login');
        }

        $ticket = RepairTicket::with(['customer', 'brand', 'repairStatus', 'deviceCondition'])
                             ->where('ticket_number', $ticketNumber)
                             ->where('customer_id', $customerId)
                             ->firstOrFail();

        return view('customer-portal.ticket-details', compact('ticket'));
    }

    /**
     * Update customer contact information
     */
    public function updateContact(Request $request): RedirectResponse
    {
        $customerId = Session::get('customer_id');
        
        if (!$customerId) {
            return redirect()->route('customer.login');
        }

        $request->validate([
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:500',
        ]);

        $customer = Customer::findOrFail($customerId);
        $customer->update($request->only(['email', 'address']));

        return back()->with('success', 'تم تحديث معلومات الاتصال بنجاح');
    }

    /**
     * Request callback
     */
    public function requestCallback(Request $request): RedirectResponse
    {
        $customerId = Session::get('customer_id');
        
        if (!$customerId) {
            return redirect()->route('customer.login');
        }

        $request->validate([
            'ticket_id' => 'required|exists:repair_tickets,id',
            'preferred_time' => 'required|string|max:100',
            'message' => 'nullable|string|max:500'
        ]);

        $ticket = RepairTicket::where('id', $request->ticket_id)
                             ->where('customer_id', $customerId)
                             ->firstOrFail();

        // Create notification for staff
        \App\Models\Notification::create([
            'title' => 'طلب اتصال من العميل',
            'message' => "العميل {$ticket->customer->name} يطلب اتصال بخصوص التذكرة {$ticket->ticket_number}",
            'type' => 'callback_request',
            'data' => [
                'customer_id' => $customerId,
                'ticket_id' => $ticket->id,
                'preferred_time' => $request->preferred_time,
                'customer_message' => $request->message
            ],
            'created_by' => null // System generated
        ]);

        return back()->with('success', 'تم إرسال طلب الاتصال. سنتواصل معك قريباً');
    }

    /**
     * Rate service
     */
    public function rateService(Request $request): RedirectResponse
    {
        $customerId = Session::get('customer_id');
        
        if (!$customerId) {
            return redirect()->route('customer.login');
        }

        $request->validate([
            'ticket_id' => 'required|exists:repair_tickets,id',
            'rating' => 'required|integer|min:1|max:5',
            'feedback' => 'nullable|string|max:1000'
        ]);

        $ticket = RepairTicket::where('id', $request->ticket_id)
                             ->where('customer_id', $customerId)
                             ->firstOrFail();

        // Update ticket with rating
        $ticket->update([
            'customer_rating' => $request->rating,
            'customer_feedback' => $request->feedback,
            'rated_at' => now()
        ]);

        return back()->with('success', 'شكراً لك على تقييمك. رأيك مهم لنا');
    }

    /**
     * Customer logout
     */
    public function logout(): RedirectResponse
    {
        Session::forget('customer_id');
        return redirect()->route('customer.login')->with('success', 'تم تسجيل الخروج بنجاح');
    }

    /**
     * Clean phone number format
     */
    private function cleanPhoneNumber(string $phoneNumber): string
    {
        // Remove all non-numeric characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);
        
        // Ensure it starts with country code or add it
        if (strlen($cleaned) === 10 && substr($cleaned, 0, 1) === '0') {
            $cleaned = '966' . substr($cleaned, 1);
        } elseif (strlen($cleaned) === 9) {
            $cleaned = '966' . $cleaned;
        }
        
        return $cleaned;
    }

    /**
     * Check if customer is authenticated
     */
    private function isAuthenticated(): bool
    {
        return Session::has('customer_id');
    }
}
