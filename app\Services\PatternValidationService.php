<?php

namespace App\Services;

class PatternValidationService
{
    /**
     * Validate a visual pattern sequence.
     *
     * @param string $pattern Pattern string like "1-2-3-6-9"
     * @return array Validation result with success status and errors
     */
    public static function validateVisualPattern(string $pattern): array
    {
        $errors = [];

        // Check if pattern is empty
        if (empty($pattern)) {
            return ['valid' => false, 'errors' => [__('app.patterns.validation.empty')]];
        }

        // Split pattern into dots
        $dots = explode('-', $pattern);

        // Check minimum length (at least 4 dots)
        if (count($dots) < 4) {
            $errors[] = __('app.patterns.validation.min_dots');
        }

        // Check maximum length (at most 9 dots)
        if (count($dots) > 9) {
            $errors[] = __('app.patterns.validation.max_dots');
        }

        // Validate each dot ID
        foreach ($dots as $dot) {
            if (!is_numeric($dot) || $dot < 1 || $dot > 9) {
                $errors[] = __('app.patterns.validation.invalid_dot', ['dot' => $dot]);
            }
        }

        // Check for duplicate dots
        if (count($dots) !== count(array_unique($dots))) {
            $errors[] = __('app.patterns.validation.duplicate_dots');
        }

        // Validate connections between consecutive dots
        for ($i = 0; $i < count($dots) - 1; $i++) {
            $from = (int)$dots[$i];
            $to = (int)$dots[$i + 1];

            if (!self::isValidConnection($from, $to, array_slice($dots, 0, $i + 1))) {
                $errors[] = __('app.patterns.validation.invalid_connection', ['from' => $from, 'to' => $to]);
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Check if a connection between two dots is valid.
     *
     * @param int $from Starting dot (1-9)
     * @param int $to Ending dot (1-9)
     * @param array $activatedDots Previously activated dots
     * @return bool
     */
    private static function isValidConnection(int $from, int $to, array $activatedDots): bool
    {
        // Convert dot IDs to grid coordinates (0-based)
        $fromPos = self::dotIdToPosition($from);
        $toPos = self::dotIdToPosition($to);

        // Calculate the difference
        $rowDiff = $toPos['row'] - $fromPos['row'];
        $colDiff = $toPos['col'] - $fromPos['col'];

        // Check if there's an intermediate dot that should be activated
        if (abs($rowDiff) > 1 || abs($colDiff) > 1) {
            // Calculate step direction
            $stepRow = $rowDiff === 0 ? 0 : $rowDiff / abs($rowDiff);
            $stepCol = $colDiff === 0 ? 0 : $colDiff / abs($colDiff);

            // Check each intermediate position
            $currentRow = $fromPos['row'] + $stepRow;
            $currentCol = $fromPos['col'] + $stepCol;

            while ($currentRow !== $toPos['row'] || $currentCol !== $toPos['col']) {
                $intermediateDotId = self::positionToDotId($currentRow, $currentCol);

                // If there's a dot at this position and it's not activated, connection is invalid
                if ($intermediateDotId !== null && !in_array((string)$intermediateDotId, $activatedDots)) {
                    return false;
                }

                $currentRow += $stepRow;
                $currentCol += $stepCol;
            }
        }

        return true;
    }

    /**
     * Convert dot ID (1-9) to grid position.
     *
     * @param int $dotId Dot ID (1-9)
     * @return array Position with 'row' and 'col' keys
     */
    private static function dotIdToPosition(int $dotId): array
    {
        $dotId--; // Convert to 0-based
        return [
            'row' => intval($dotId / 3),
            'col' => $dotId % 3
        ];
    }

    /**
     * Convert grid position to dot ID.
     *
     * @param int $row Row (0-2)
     * @param int $col Column (0-2)
     * @return int|null Dot ID (1-9) or null if invalid position
     */
    private static function positionToDotId(int $row, int $col): ?int
    {
        if ($row < 0 || $row > 2 || $col < 0 || $col > 2) {
            return null;
        }

        return $row * 3 + $col + 1;
    }

    /**
     * Generate a visual representation of the pattern for display.
     *
     * @param string $pattern Pattern string like "1-2-3-6-9"
     * @return array 3x3 grid with pattern information
     */
    public static function generatePatternGrid(string $pattern): array
    {
        $grid = array_fill(0, 3, array_fill(0, 3, ['active' => false, 'order' => null]));

        if (empty($pattern)) {
            return $grid;
        }

        $dots = explode('-', $pattern);

        foreach ($dots as $index => $dotId) {
            $pos = self::dotIdToPosition((int)$dotId);
            $grid[$pos['row']][$pos['col']] = [
                'active' => true,
                'order' => $index + 1
            ];
        }

        return $grid;
    }

    /**
     * Get pattern connections for drawing lines.
     *
     * @param string $pattern Pattern string like "1-2-3-6-9"
     * @return array Array of connections with from/to coordinates
     */
    public static function getPatternConnections(string $pattern): array
    {
        $connections = [];

        if (empty($pattern)) {
            return $connections;
        }

        $dots = explode('-', $pattern);

        for ($i = 0; $i < count($dots) - 1; $i++) {
            $fromPos = self::dotIdToPosition((int)$dots[$i]);
            $toPos = self::dotIdToPosition((int)$dots[$i + 1]);

            $connections[] = [
                'from' => $fromPos,
                'to' => $toPos
            ];
        }

        return $connections;
    }

    /**
     * Convert pattern to a more secure hash for comparison.
     *
     * @param string $pattern Pattern string
     * @return string Hashed pattern
     */
    public static function hashPattern(string $pattern): string
    {
        return hash('sha256', $pattern . config('app.key'));
    }

    /**
     * Get pattern complexity score (1-10).
     *
     * @param string $pattern Pattern string
     * @return int Complexity score
     */
    public static function getPatternComplexity(string $pattern): int
    {
        if (empty($pattern)) {
            return 0;
        }

        $dots = explode('-', $pattern);
        $score = 0;

        // Base score for length
        $score += min(count($dots), 9);

        // Bonus for direction changes
        $directionChanges = 0;
        for ($i = 1; $i < count($dots) - 1; $i++) {
            $prev = self::dotIdToPosition((int)$dots[$i - 1]);
            $curr = self::dotIdToPosition((int)$dots[$i]);
            $next = self::dotIdToPosition((int)$dots[$i + 1]);

            $dir1 = [$curr['row'] - $prev['row'], $curr['col'] - $prev['col']];
            $dir2 = [$next['row'] - $curr['row'], $next['col'] - $curr['col']];

            if ($dir1[0] !== $dir2[0] || $dir1[1] !== $dir2[1]) {
                $directionChanges++;
            }
        }

        $score += min($directionChanges, 3);

        return min($score, 10);
    }

    /**
     * Get pattern type based on content.
     *
     * @param string|null $textPattern Text pattern
     * @param string|null $visualPattern Visual pattern
     * @return string Pattern type: 'none', 'text', 'visual', 'both'
     */
    public static function determinePatternType(?string $textPattern, ?string $visualPattern): string
    {
        $hasText = !empty($textPattern);
        $hasVisual = !empty($visualPattern);

        if ($hasText && $hasVisual) {
            return 'both';
        } elseif ($hasText) {
            return 'text';
        } elseif ($hasVisual) {
            return 'visual';
        } else {
            return 'none';
        }
    }
}
