<?php

namespace App\Events;

use App\Models\Invoice;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PaymentReminder
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Invoice $invoice;
    public string $reminderType;

    /**
     * Create a new event instance.
     */
    public function __construct(Invoice $invoice, string $reminderType = 'gentle')
    {
        $this->invoice = $invoice;
        $this->reminderType = $reminderType;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
