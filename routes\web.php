<?php

use App\Http\Controllers\CustomerController;
use App\Http\Controllers\RepairTicketController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\RepairStatusController;
use App\Http\Controllers\DeviceConditionController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ReportsController;
use App\Http\Controllers\PrintExportController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Redirect root to dashboard
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Public routes
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Temporary: Comment out auth routes until migration is run
// require __DIR__.'/auth.php';

// Temporary: Remove auth middleware until setup is complete
Route::group([], function () {
    // Dashboard - All roles
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Customer Management
    Route::resource('customers', CustomerController::class);
    Route::get('/api/customers/search', [CustomerController::class, 'search'])->name('customers.search');
    Route::get('/api/customers/{customer}', [CustomerController::class, 'getCustomer'])->name('customers.get');

    // Repair Ticket Management
    Route::resource('repair-tickets', RepairTicketController::class);
    Route::patch('/repair-tickets/{repairTicket}/status', [RepairTicketController::class, 'updateStatus'])->name('repair-tickets.update-status');
    Route::post('/repair-tickets/bulk-action', [RepairTicketController::class, 'bulkAction'])->name('repair-tickets.bulk-action');

    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/dashboard', [ReportsController::class, 'dashboard'])->name('dashboard');
        Route::get('/customer-analytics', [ReportsController::class, 'customerAnalytics'])->name('customer-analytics');
        Route::get('/business-intelligence', [ReportsController::class, 'businessIntelligence'])->name('business-intelligence');
        Route::get('/financial', [ReportsController::class, 'financial'])->name('financial');
        Route::get('/profit-loss', [ReportsController::class, 'profitLoss'])->name('profit-loss');
        Route::get('/revenue', [ReportsController::class, 'revenue'])->name('revenue');
        Route::get('/outstanding', [ReportsController::class, 'outstanding'])->name('outstanding');
        Route::get('/customers', [ReportsController::class, 'customers'])->name('customers');
    });

    // System Configuration
    Route::resource('brands', BrandController::class);
    Route::resource('repair-statuses', RepairStatusController::class);
    Route::resource('device-conditions', DeviceConditionController::class);

    // WhatsApp Dashboard
    Route::get('/whatsapp/dashboard', function () {
        return view('whatsapp.dashboard');
    })->name('whatsapp.dashboard')->middleware('auth');

    // WhatsApp Template Management
    Route::prefix('whatsapp/templates')->name('whatsapp.templates.')->middleware('auth')->group(function () {
        Route::get('/', [App\Http\Controllers\WhatsAppTemplateController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\WhatsAppTemplateController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\WhatsAppTemplateController::class, 'store'])->name('store');
        Route::get('/{template}', [App\Http\Controllers\WhatsAppTemplateController::class, 'show'])->name('show');
        Route::get('/{template}/edit', [App\Http\Controllers\WhatsAppTemplateController::class, 'edit'])->name('edit');
        Route::put('/{template}', [App\Http\Controllers\WhatsAppTemplateController::class, 'update'])->name('update');
        Route::delete('/{template}', [App\Http\Controllers\WhatsAppTemplateController::class, 'destroy'])->name('destroy');

        // Template workflow actions
        Route::post('/{template}/submit-approval', [App\Http\Controllers\WhatsAppTemplateController::class, 'submitForApproval'])->name('submit-approval');
        Route::post('/{template}/approve', [App\Http\Controllers\WhatsAppTemplateController::class, 'approve'])->name('approve');
        Route::post('/{template}/reject', [App\Http\Controllers\WhatsAppTemplateController::class, 'reject'])->name('reject');
        Route::post('/{template}/submit-whatsapp', [App\Http\Controllers\WhatsAppTemplateController::class, 'submitToWhatsApp'])->name('submit-whatsapp');

        // Template utilities
        Route::post('/{template}/preview', [App\Http\Controllers\WhatsAppTemplateController::class, 'preview'])->name('preview');
        Route::post('/{template}/test-send', [App\Http\Controllers\WhatsAppTemplateController::class, 'testSend'])->name('test-send');
        Route::get('/{template}/analytics', [App\Http\Controllers\WhatsAppTemplateController::class, 'analytics'])->name('analytics');
        Route::get('/{template}/duplicate', [App\Http\Controllers\WhatsAppTemplateController::class, 'duplicate'])->name('duplicate');
    });

// Customer Management
Route::resource('customers', CustomerController::class);
Route::get('/api/customers/search', [CustomerController::class, 'search'])->name('customers.search');
Route::get('/api/customers/{customer}', [CustomerController::class, 'getCustomer'])->name('customers.get');

// Repair Ticket Management
Route::resource('repair-tickets', RepairTicketController::class);
Route::patch('/repair-tickets/{repairTicket}/status', [RepairTicketController::class, 'updateStatus'])->name('repair-tickets.update-status');
Route::post('/repair-tickets/bulk-action', [RepairTicketController::class, 'bulkAction'])->name('repair-tickets.bulk-action');

// Reports and Analytics
Route::prefix('reports')->name('reports.')->group(function () {
    Route::get('/dashboard', [ReportsController::class, 'dashboard'])->name('dashboard');
    Route::get('/customer-analytics', [ReportsController::class, 'customerAnalytics'])->name('customer-analytics');
    Route::get('/business-intelligence', [ReportsController::class, 'businessIntelligence'])->name('business-intelligence');
});

// Notifications Management
Route::resource('notifications', App\Http\Controllers\NotificationController::class)->except(['edit', 'update', 'destroy']);
Route::post('/notifications/{notification}/resend', [App\Http\Controllers\NotificationController::class, 'resend'])->name('notifications.resend');
Route::get('/notifications/template-preview', [App\Http\Controllers\NotificationController::class, 'templatePreview'])->name('notifications.template-preview');
Route::get('/notifications/statistics', [App\Http\Controllers\NotificationController::class, 'statistics'])->name('notifications.statistics');
Route::post('/notifications/process-scheduled', [App\Http\Controllers\NotificationController::class, 'processScheduled'])->name('notifications.process-scheduled');

// Inventory Management
Route::resource('inventory', App\Http\Controllers\InventoryController::class);
Route::get('/inventory/{inventory}/adjust-stock', [App\Http\Controllers\InventoryController::class, 'adjustStock'])->name('inventory.adjust-stock');
Route::post('/inventory/{inventory}/adjust-stock', [App\Http\Controllers\InventoryController::class, 'processStockAdjustment'])->name('inventory.process-adjustment');
Route::get('/inventory/{inventory}/movements', [App\Http\Controllers\InventoryController::class, 'movements'])->name('inventory.movements');
Route::get('/inventory/alerts/low-stock', [App\Http\Controllers\InventoryController::class, 'lowStockAlerts'])->name('inventory.low-stock-alerts');
Route::get('/inventory/statistics', [App\Http\Controllers\InventoryController::class, 'statistics'])->name('inventory.statistics');

// Invoice Management
Route::resource('invoices', App\Http\Controllers\InvoiceController::class);
Route::post('/invoices/{invoice}/send', [App\Http\Controllers\InvoiceController::class, 'send'])->name('invoices.send');
Route::get('/invoices/{invoice}/pdf', [App\Http\Controllers\InvoiceController::class, 'pdf'])->name('invoices.pdf');
Route::get('/invoices/{invoice}/print', [App\Http\Controllers\InvoiceController::class, 'print'])->name('invoices.print');
Route::get('/invoices/statistics', [App\Http\Controllers\InvoiceController::class, 'statistics'])->name('invoices.statistics');

// Payment Management
Route::resource('payments', App\Http\Controllers\PaymentController::class);
Route::get('/payments/statistics', [App\Http\Controllers\PaymentController::class, 'statistics'])->name('payments.statistics');

// Financial Reports
Route::get('/reports/financial', [App\Http\Controllers\ReportsController::class, 'financial'])->name('reports.financial');
Route::get('/reports/revenue', [App\Http\Controllers\ReportsController::class, 'revenue'])->name('reports.revenue');
Route::get('/reports/outstanding', [App\Http\Controllers\ReportsController::class, 'outstanding'])->name('reports.outstanding');
Route::get('/reports/profit-loss', [App\Http\Controllers\ReportsController::class, 'profitLoss'])->name('reports.profit-loss');
Route::get('/reports/customers', [App\Http\Controllers\ReportsController::class, 'customers'])->name('reports.customers');
Route::get('/reports/financial-stats', [App\Http\Controllers\ReportsController::class, 'financialStats'])->name('reports.financial-stats');

// Print and Export
Route::prefix('print-export')->name('print-export.')->group(function () {
    // Ticket printing
    Route::get('/ticket/{repairTicket}/print', [PrintExportController::class, 'printTicket'])->name('ticket.print');
    Route::get('/ticket/{repairTicket}/preview', [PrintExportController::class, 'previewTicket'])->name('ticket.preview');
    Route::post('/tickets/print-multiple', [PrintExportController::class, 'printMultipleTickets'])->name('tickets.print-multiple');

    // Excel exports
    Route::get('/tickets/export-excel', [PrintExportController::class, 'exportTicketsExcel'])->name('tickets.export-excel');
    Route::get('/customers/export-excel', [PrintExportController::class, 'exportCustomersExcel'])->name('customers.export-excel');

    // PDF reports
    Route::get('/reports/export-pdf', [PrintExportController::class, 'exportReportsPdf'])->name('reports.export-pdf');
});

// Configuration Management
Route::resource('brands', BrandController::class);
Route::resource('repair-statuses', RepairStatusController::class);
Route::resource('device-conditions', DeviceConditionController::class);

}); // End of protected routes group

// Temporary routes until authentication is fully set up
Route::get('/profile/edit', function () {
    return view('profile.edit');
})->name('profile.edit');

Route::post('/logout', function () {
    Auth::logout();
    request()->session()->invalidate();
    request()->session()->regenerateToken();
    return redirect()->route('login')->with('success', 'تم تسجيل الخروج بنجاح');
})->name('logout');

// Include authentication routes
require __DIR__.'/auth.php';

// Temporary: Direct login for development
Route::get('/dev-login', function () {
    $user = App\Models\User::first();
    if ($user) {
        Auth::login($user);
        return redirect()->route('whatsapp.templates.index')->with('success', 'تم تسجيل الدخول بنجاح');
    }
    return redirect('/')->with('error', 'لا يوجد مستخدمين في النظام');
})->name('dev-login');
