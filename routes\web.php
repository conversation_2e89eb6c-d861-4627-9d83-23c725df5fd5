<?php

use App\Http\Controllers\CustomerController;
use App\Http\Controllers\RepairTicketController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\RepairStatusController;
use App\Http\Controllers\DeviceConditionController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ReportsController;
use App\Http\Controllers\PrintExportController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Redirect root to dashboard
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Public routes
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Temporary: Comment out auth routes until migration is run
// require __DIR__.'/auth.php';

// Temporary: Remove auth middleware until setup is complete
Route::group([], function () {
    // Dashboard - All roles
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Customer Management
    Route::resource('customers', CustomerController::class);
    Route::get('/api/customers/search', [CustomerController::class, 'search'])->name('customers.search');
    Route::get('/api/customers/{customer}', [CustomerController::class, 'getCustomer'])->name('customers.get');

    // Repair Ticket Management
    Route::resource('repair-tickets', RepairTicketController::class);
    Route::patch('/repair-tickets/{repairTicket}/status', [RepairTicketController::class, 'updateStatus'])->name('repair-tickets.update-status');
    Route::post('/repair-tickets/bulk-action', [RepairTicketController::class, 'bulkAction'])->name('repair-tickets.bulk-action');

    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/dashboard', [ReportsController::class, 'dashboard'])->name('dashboard');
        Route::get('/customer-analytics', [ReportsController::class, 'customerAnalytics'])->name('customer-analytics');
        Route::get('/business-intelligence', [ReportsController::class, 'businessIntelligence'])->name('business-intelligence');
        Route::get('/financial', [ReportsController::class, 'financial'])->name('financial');
        Route::get('/profit-loss', [ReportsController::class, 'profitLoss'])->name('profit-loss');
        Route::get('/revenue', [ReportsController::class, 'revenue'])->name('revenue');
        Route::get('/outstanding', [ReportsController::class, 'outstanding'])->name('outstanding');
        Route::get('/customers', [ReportsController::class, 'customers'])->name('customers');
    });

    // System Configuration
    Route::resource('brands', BrandController::class);
    Route::resource('repair-statuses', RepairStatusController::class);
    Route::resource('device-conditions', DeviceConditionController::class);

    // WhatsApp Dashboard
    Route::get('/whatsapp/dashboard', function () {
        return view('whatsapp.dashboard');
    })->name('whatsapp.dashboard')->middleware('auth');

    // WhatsApp Template Management
    Route::prefix('whatsapp/templates')->name('whatsapp.templates.')->middleware('auth')->group(function () {
        Route::get('/', [App\Http\Controllers\WhatsAppTemplateController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\WhatsAppTemplateController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\WhatsAppTemplateController::class, 'store'])->name('store');
        Route::get('/{template}', [App\Http\Controllers\WhatsAppTemplateController::class, 'show'])->name('show');
        Route::get('/{template}/edit', [App\Http\Controllers\WhatsAppTemplateController::class, 'edit'])->name('edit');
        Route::put('/{template}', [App\Http\Controllers\WhatsAppTemplateController::class, 'update'])->name('update');
        Route::delete('/{template}', [App\Http\Controllers\WhatsAppTemplateController::class, 'destroy'])->name('destroy');

        // Template workflow actions
        Route::post('/{template}/submit-approval', [App\Http\Controllers\WhatsAppTemplateController::class, 'submitForApproval'])->name('submit-approval');
        Route::post('/{template}/approve', [App\Http\Controllers\WhatsAppTemplateController::class, 'approve'])->name('approve');
        Route::post('/{template}/reject', [App\Http\Controllers\WhatsAppTemplateController::class, 'reject'])->name('reject');
        Route::post('/{template}/submit-whatsapp', [App\Http\Controllers\WhatsAppTemplateController::class, 'submitToWhatsApp'])->name('submit-whatsapp');

        // Template utilities
        Route::post('/{template}/preview', [App\Http\Controllers\WhatsAppTemplateController::class, 'preview'])->name('preview');
        Route::post('/{template}/test-send', [App\Http\Controllers\WhatsAppTemplateController::class, 'testSend'])->name('test-send');
        Route::get('/{template}/analytics', [App\Http\Controllers\WhatsAppTemplateController::class, 'analytics'])->name('analytics');
        Route::get('/{template}/duplicate', [App\Http\Controllers\WhatsAppTemplateController::class, 'duplicate'])->name('duplicate');
    });

    // WhatsApp Payment Notifications
    Route::prefix('whatsapp/payments')->name('whatsapp.payments.')->middleware('auth')->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\WhatsAppPaymentController::class, 'dashboard'])->name('dashboard');
        Route::post('/invoices/{invoice}/send-reminder', [App\Http\Controllers\WhatsAppPaymentController::class, 'sendPaymentReminder'])->name('send-reminder');
        Route::post('/send-bulk-reminders', [App\Http\Controllers\WhatsAppPaymentController::class, 'sendBulkReminders'])->name('send-bulk-reminders');
        Route::post('/payments/{payment}/resend-confirmation', [App\Http\Controllers\WhatsAppPaymentController::class, 'resendPaymentConfirmation'])->name('resend-confirmation');
        Route::post('/payments/{payment}/send-receipt', [App\Http\Controllers\WhatsAppPaymentController::class, 'sendPaymentReceipt'])->name('send-receipt');
        Route::post('/invoices/{invoice}/send-notification', [App\Http\Controllers\WhatsAppPaymentController::class, 'sendInvoiceNotification'])->name('send-notification');
        Route::post('/invoices/{invoice}/send-payment-plan', [App\Http\Controllers\WhatsAppPaymentController::class, 'sendPaymentPlan'])->name('send-payment-plan');
        Route::get('/overdue-invoices', [App\Http\Controllers\WhatsAppPaymentController::class, 'getOverdueInvoices'])->name('overdue-invoices');
        Route::post('/test-notification', [App\Http\Controllers\WhatsAppPaymentController::class, 'testNotification'])->name('test-notification');
    });

// Customer Management
Route::resource('customers', CustomerController::class);
Route::get('/api/customers/search', [CustomerController::class, 'search'])->name('customers.search');
Route::get('/api/customers/{customer}', [CustomerController::class, 'getCustomer'])->name('customers.get');

// Repair Ticket Management
Route::resource('repair-tickets', RepairTicketController::class);
Route::patch('/repair-tickets/{repairTicket}/status', [RepairTicketController::class, 'updateStatus'])->name('repair-tickets.update-status');
Route::post('/repair-tickets/bulk-action', [RepairTicketController::class, 'bulkAction'])->name('repair-tickets.bulk-action');

// Reports and Analytics
Route::prefix('reports')->name('reports.')->group(function () {
    Route::get('/dashboard', [ReportsController::class, 'dashboard'])->name('dashboard');
    Route::get('/customer-analytics', [ReportsController::class, 'customerAnalytics'])->name('customer-analytics');
    Route::get('/business-intelligence', [ReportsController::class, 'businessIntelligence'])->name('business-intelligence');
});

// Notifications Management
Route::resource('notifications', App\Http\Controllers\NotificationController::class)->except(['edit', 'update', 'destroy']);
Route::post('/notifications/{notification}/resend', [App\Http\Controllers\NotificationController::class, 'resend'])->name('notifications.resend');
Route::get('/notifications/template-preview', [App\Http\Controllers\NotificationController::class, 'templatePreview'])->name('notifications.template-preview');
Route::get('/notifications/statistics', [App\Http\Controllers\NotificationController::class, 'statistics'])->name('notifications.statistics');
Route::post('/notifications/process-scheduled', [App\Http\Controllers\NotificationController::class, 'processScheduled'])->name('notifications.process-scheduled');

// Inventory Management
Route::resource('inventory', App\Http\Controllers\InventoryController::class);
Route::get('/inventory/{inventory}/adjust-stock', [App\Http\Controllers\InventoryController::class, 'adjustStock'])->name('inventory.adjust-stock');
Route::post('/inventory/{inventory}/adjust-stock', [App\Http\Controllers\InventoryController::class, 'processStockAdjustment'])->name('inventory.process-adjustment');
Route::get('/inventory/{inventory}/movements', [App\Http\Controllers\InventoryController::class, 'movements'])->name('inventory.movements');
Route::get('/inventory/alerts/low-stock', [App\Http\Controllers\InventoryController::class, 'lowStockAlerts'])->name('inventory.low-stock-alerts');
Route::get('/inventory/statistics', [App\Http\Controllers\InventoryController::class, 'statistics'])->name('inventory.statistics');

// Invoice Management
Route::resource('invoices', App\Http\Controllers\InvoiceController::class);
Route::post('/invoices/{invoice}/send', [App\Http\Controllers\InvoiceController::class, 'send'])->name('invoices.send');
Route::get('/invoices/{invoice}/pdf', [App\Http\Controllers\InvoiceController::class, 'pdf'])->name('invoices.pdf');
Route::get('/invoices/{invoice}/print', [App\Http\Controllers\InvoiceController::class, 'print'])->name('invoices.print');
Route::get('/invoices/statistics', [App\Http\Controllers\InvoiceController::class, 'statistics'])->name('invoices.statistics');

// Payment Management
Route::resource('payments', App\Http\Controllers\PaymentController::class);
Route::get('/payments/statistics', [App\Http\Controllers\PaymentController::class, 'statistics'])->name('payments.statistics');

// Financial Reports
Route::get('/reports/financial', [App\Http\Controllers\ReportsController::class, 'financial'])->name('reports.financial');
Route::get('/reports/revenue', [App\Http\Controllers\ReportsController::class, 'revenue'])->name('reports.revenue');
Route::get('/reports/outstanding', [App\Http\Controllers\ReportsController::class, 'outstanding'])->name('reports.outstanding');
Route::get('/reports/profit-loss', [App\Http\Controllers\ReportsController::class, 'profitLoss'])->name('reports.profit-loss');
Route::get('/reports/customers', [App\Http\Controllers\ReportsController::class, 'customers'])->name('reports.customers');

// Advanced Reports and Analytics
Route::prefix('advanced-reports')->name('advanced-reports.')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\AdvancedReportsController::class, 'dashboard'])->name('dashboard');
    Route::get('/profit-loss', [App\Http\Controllers\AdvancedReportsController::class, 'profitLoss'])->name('profit-loss');
    Route::get('/kpi-dashboard', [App\Http\Controllers\AdvancedReportsController::class, 'kpiDashboard'])->name('kpi-dashboard');
    Route::get('/sales-performance', [App\Http\Controllers\AdvancedReportsController::class, 'salesPerformance'])->name('sales-performance');
    Route::get('/inventory-analysis', [App\Http\Controllers\AdvancedReportsController::class, 'inventoryAnalysis'])->name('inventory-analysis');
    Route::get('/customer-payment-history', [App\Http\Controllers\AdvancedReportsController::class, 'customerPaymentHistory'])->name('customer-payment-history');
    Route::post('/generate-custom-report', [App\Http\Controllers\AdvancedReportsController::class, 'generateCustomReport'])->name('generate-custom-report');
});

// Enhanced Sales Management
Route::prefix('sales')->name('sales.')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\SalesController::class, 'dashboard'])->name('dashboard');
    Route::get('/analytics', [App\Http\Controllers\SalesController::class, 'analytics'])->name('analytics');
    Route::get('/customer-payment-analysis', [App\Http\Controllers\SalesController::class, 'customerPaymentAnalysis'])->name('customer-payment-analysis');
    Route::post('/generate-report', [App\Http\Controllers\SalesController::class, 'generateReport'])->name('generate-report');
    Route::put('/customers/{customer}/credit', [App\Http\Controllers\SalesController::class, 'updateCustomerCredit'])->name('update-customer-credit');
});

// Payment Method Configuration
Route::resource('payment-methods', App\Http\Controllers\PaymentMethodConfigController::class);
Route::post('/payment-methods/seed-defaults', [App\Http\Controllers\PaymentMethodConfigController::class, 'seedDefaults'])->name('payment-methods.seed-defaults');

// Point of Sale (POS) System
Route::prefix('pos')->name('pos.')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\PosController::class, 'dashboard'])->name('dashboard');
    Route::get('/terminals/{terminal}/interface', [App\Http\Controllers\PosController::class, 'interface'])->name('interface');

    // Shift Management
    Route::match(['GET', 'POST'], '/terminals/{terminal}/open-shift', [App\Http\Controllers\PosController::class, 'openShift'])->name('open-shift');
    Route::match(['GET', 'POST'], '/terminals/{terminal}/close-shift', [App\Http\Controllers\PosController::class, 'closeShift'])->name('close-shift');

    // Transaction Management
    Route::post('/terminals/{terminal}/transactions', [App\Http\Controllers\PosController::class, 'createTransaction'])->name('create-transaction');
    Route::post('/transactions/{transaction}/items', [App\Http\Controllers\PosController::class, 'addItem'])->name('add-item');
    Route::put('/transaction-items/{item}/quantity', [App\Http\Controllers\PosController::class, 'updateItemQuantity'])->name('update-item-quantity');
    Route::delete('/transaction-items/{item}', [App\Http\Controllers\PosController::class, 'removeItem'])->name('remove-item');
    Route::post('/transactions/{transaction}/payments', [App\Http\Controllers\PosController::class, 'addPayment'])->name('add-payment');
    Route::post('/transactions/{transaction}/complete', [App\Http\Controllers\PosController::class, 'completeTransaction'])->name('complete-transaction');
    Route::post('/transactions/{transaction}/cancel', [App\Http\Controllers\PosController::class, 'cancelTransaction'])->name('cancel-transaction');
    Route::post('/transactions/{transaction}/print-receipt', [App\Http\Controllers\PosController::class, 'printReceipt'])->name('print-receipt');

    // Item Search and Barcode
    Route::get('/search-items', [App\Http\Controllers\PosController::class, 'searchItems'])->name('search-items');
    Route::get('/barcode-item', [App\Http\Controllers\PosController::class, 'getItemByBarcode'])->name('barcode-item');
});

// POS Terminal Management
Route::resource('pos-terminals', App\Http\Controllers\PosTerminalController::class);
Route::post('/pos-terminals/{terminal}/toggle-status', [App\Http\Controllers\PosTerminalController::class, 'toggleStatus'])->name('pos-terminals.toggle-status');

// POS Quick Sale Templates
Route::resource('pos-templates', App\Http\Controllers\PosQuickSaleTemplateController::class);
Route::post('/pos-templates/seed-defaults', [App\Http\Controllers\PosQuickSaleTemplateController::class, 'seedDefaults'])->name('pos-templates.seed-defaults');
Route::get('/reports/financial-stats', [App\Http\Controllers\ReportsController::class, 'financialStats'])->name('reports.financial-stats');

// Print and Export
Route::prefix('print-export')->name('print-export.')->group(function () {
    // Ticket printing
    Route::get('/ticket/{repairTicket}/print', [PrintExportController::class, 'printTicket'])->name('ticket.print');
    Route::get('/ticket/{repairTicket}/preview', [PrintExportController::class, 'previewTicket'])->name('ticket.preview');
    Route::post('/tickets/print-multiple', [PrintExportController::class, 'printMultipleTickets'])->name('tickets.print-multiple');

    // Excel exports
    Route::get('/tickets/export-excel', [PrintExportController::class, 'exportTicketsExcel'])->name('tickets.export-excel');
    Route::get('/customers/export-excel', [PrintExportController::class, 'exportCustomersExcel'])->name('customers.export-excel');

    // PDF reports
    Route::get('/reports/export-pdf', [PrintExportController::class, 'exportReportsPdf'])->name('reports.export-pdf');
});

// Configuration Management
Route::resource('brands', BrandController::class);
Route::resource('repair-statuses', RepairStatusController::class);
Route::resource('device-conditions', DeviceConditionController::class);

// Communications Routes
Route::prefix('communications')->name('communications.')->group(function () {
    Route::get('/whatsapp', function () {
        return view('communications.whatsapp.index');
    })->name('whatsapp');

    Route::get('/sms', function () {
        return view('communications.sms.index');
    })->name('sms');

    Route::get('/email', function () {
        return view('communications.email.index');
    })->name('email');

    Route::get('/templates', function () {
        return view('communications.templates.index');
    })->name('templates');
});

// User Management Routes
Route::prefix('users')->name('users.')->group(function () {
    Route::get('/', function () {
        return view('users.index');
    })->name('index');

    Route::get('/create', function () {
        return view('users.create');
    })->name('create');

    Route::get('/{id}', function ($id) {
        return view('users.show', compact('id'));
    })->name('show');

    Route::get('/{id}/edit', function ($id) {
        return view('users.edit', compact('id'));
    })->name('edit');
});

// Roles and Permissions Routes
Route::get('/roles-permissions', function () {
    return view('admin.roles-permissions');
})->name('roles-permissions.index');

// Security Dashboard Routes
Route::prefix('security')->name('security.')->group(function () {
    Route::get('/dashboard', function () {
        return view('admin.security-dashboard');
    })->name('dashboard');

    Route::get('/logs', function () {
        return view('admin.security-logs');
    })->name('logs');
});

// Suppliers Routes
Route::prefix('suppliers')->name('suppliers.')->group(function () {
    Route::get('/', function () {
        return view('suppliers.index');
    })->name('index');

    Route::get('/create', function () {
        return view('suppliers.create');
    })->name('create');

    Route::get('/{id}', function ($id) {
        return view('suppliers.show', compact('id'));
    })->name('show');

    Route::get('/{id}/edit', function ($id) {
        return view('suppliers.edit', compact('id'));
    })->name('edit');
});

// Additional Reports Routes
Route::get('/reports/advanced-dashboard', function () {
    return view('reports.advanced-dashboard');
})->name('reports.advanced-dashboard');

}); // End of protected routes group

// Temporary routes until authentication is fully set up
Route::get('/profile/edit', function () {
    return view('profile.edit');
})->name('profile.edit');

Route::post('/logout', function () {
    Auth::logout();
    request()->session()->invalidate();
    request()->session()->regenerateToken();
    return redirect()->route('login')->with('success', 'تم تسجيل الخروج بنجاح');
})->name('logout');

// Include authentication routes
require __DIR__.'/auth.php';

// Temporary: Direct login for development
Route::get('/dev-login', function () {
    $user = App\Models\User::first();
    if ($user) {
        Auth::login($user);
        return redirect()->route('whatsapp.templates.index')->with('success', 'تم تسجيل الدخول بنجاح');
    }
    return redirect('/')->with('error', 'لا يوجد مستخدمين في النظام');
})->name('dev-login');
