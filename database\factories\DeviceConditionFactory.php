<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DeviceCondition>
 */
class DeviceConditionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->randomElement([
                'Excellent', 'Good', 'Fair', 'Poor',
                'Water Damaged', 'Cracked Screen', 'Battery Issues',
                'Screen Broken', 'Won\'t Turn On', 'Charging Issues',
                'Speaker Problems', 'Camera Issues', 'Button Problems'
            ]),
            'description' => $this->faker->optional(0.8)->sentence(),
        ];
    }
}
