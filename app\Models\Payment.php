<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'payment_number',
        'invoice_id',
        'customer_id',
        'amount',
        'payment_date',
        'payment_method',
        'payment_reference',
        'card_last_four',
        'bank_name',
        'check_number',
        'payment_details',
        'status',
        'is_verified',
        'verified_at',
        'verified_by',
        'refunded_amount',
        'refunded_at',
        'refund_reason',
        'notes',
        'receipt_number',
        'attachments',
        'created_by'
    ];

    protected $casts = [
        'payment_date' => 'date',
        'amount' => 'decimal:2',
        'refunded_amount' => 'decimal:2',
        'payment_details' => 'array',
        'attachments' => 'array',
        'is_verified' => 'boolean',
        'verified_at' => 'datetime',
        'refunded_at' => 'datetime'
    ];

    // العلاقات
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    // Accessors
    protected function paymentMethodDisplay(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->payment_method) {
                'cash' => 'نقدي',
                'card' => 'بطاقة',
                'bank_transfer' => 'تحويل بنكي',
                'check' => 'شيك',
                'mobile_payment' => 'دفع عبر الجوال',
                'installment' => 'قسط',
                'other' => 'أخرى',
                default => $this->payment_method
            }
        );
    }

    protected function paymentMethodIcon(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->payment_method) {
                'cash' => 'fas fa-money-bill-wave',
                'card' => 'fas fa-credit-card',
                'bank_transfer' => 'fas fa-university',
                'check' => 'fas fa-money-check',
                'online' => 'fas fa-globe',
                'other' => 'fas fa-coins',
                default => 'fas fa-coins'
            }
        );
    }

    protected function statusDisplay(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->status) {
                'pending' => 'معلق',
                'completed' => 'مكتمل',
                'failed' => 'فاشل',
                'cancelled' => 'ملغي',
                default => $this->status
            }
        );
    }

    protected function statusColor(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->status) {
                'pending' => 'warning',
                'completed' => 'success',
                'failed' => 'danger',
                'cancelled' => 'secondary',
                default => 'secondary'
            }
        );
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeByMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('payment_date', today());
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('payment_date', now()->month)
                    ->whereYear('payment_date', now()->year);
    }

    // Methods
    public static function generatePaymentNumber(): string
    {
        $lastPayment = static::latest('id')->first();
        $nextNumber = $lastPayment ? $lastPayment->id + 1 : 1;

        return 'PAY-' . str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
    }

    public function canEdit(): bool
    {
        return $this->status === 'pending';
    }

    public function canDelete(): bool
    {
        return $this->status === 'pending';
    }

    public function markAsCompleted(): void
    {
        $this->update(['status' => 'completed']);
    }

    public function markAsFailed(): void
    {
        $this->update(['status' => 'failed']);
    }
}
