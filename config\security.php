<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains security-related configuration options for the
    | repair shop management system.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Login Security
    |--------------------------------------------------------------------------
    |
    | Configuration for login attempt limits and lockout settings.
    |
    */
    'login' => [
        'max_attempts' => env('LOGIN_MAX_ATTEMPTS', 5),
        'lockout_duration' => env('LOGIN_LOCKOUT_DURATION', 30), // minutes
        'track_ip' => env('LOGIN_TRACK_IP', true),
        'require_email_verification' => env('LOGIN_REQUIRE_EMAIL_VERIFICATION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Password Security
    |--------------------------------------------------------------------------
    |
    | Password strength requirements and policies.
    |
    */
    'password' => [
        'min_length' => env('PASSWORD_MIN_LENGTH', 8),
        'require_uppercase' => env('PASSWORD_REQUIRE_UPPERCASE', true),
        'require_lowercase' => env('PASSWORD_REQUIRE_LOWERCASE', true),
        'require_numbers' => env('PASSWORD_REQUIRE_NUMBERS', true),
        'require_symbols' => env('PASSWORD_REQUIRE_SYMBOLS', true),
        'prevent_common' => env('PASSWORD_PREVENT_COMMON', true),
        'history_limit' => env('PASSWORD_HISTORY_LIMIT', 5), // Remember last N passwords
        'expiry_days' => env('PASSWORD_EXPIRY_DAYS', 90), // Force change after N days
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Security
    |--------------------------------------------------------------------------
    |
    | Session timeout and security settings.
    |
    */
    'session' => [
        'timeout' => env('SESSION_TIMEOUT', 120), // minutes
        'concurrent_limit' => env('SESSION_CONCURRENT_LIMIT', 3), // Max concurrent sessions per user
        'ip_validation' => env('SESSION_IP_VALIDATION', true),
        'user_agent_validation' => env('SESSION_USER_AGENT_VALIDATION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Trusted IP Addresses
    |--------------------------------------------------------------------------
    |
    | List of IP addresses that are considered trusted and may bypass
    | certain security restrictions.
    |
    */
    'trusted_ips' => [
        '127.0.0.1',
        '::1',
        // Add your office/admin IP addresses here
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Headers
    |--------------------------------------------------------------------------
    |
    | HTTP security headers to be sent with responses.
    |
    */
    'headers' => [
        'x_frame_options' => 'DENY',
        'x_content_type_options' => 'nosniff',
        'x_xss_protection' => '1; mode=block',
        'referrer_policy' => 'strict-origin-when-cross-origin',
        'content_security_policy' => [
            'default-src' => "'self'",
            'script-src' => "'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://code.jquery.com https://cdnjs.cloudflare.com",
            'style-src' => "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com",
            'font-src' => "'self' https://fonts.gstatic.com https://cdn.jsdelivr.net",
            'img-src' => "'self' data: https:",
            'connect-src' => "'self'",
            'frame-ancestors' => "'none'",
        ],
        'permissions_policy' => [
            'camera' => '()',
            'microphone' => '()',
            'geolocation' => '()',
            'payment' => '()',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | API Security
    |--------------------------------------------------------------------------
    |
    | Security settings for API endpoints.
    |
    */
    'api' => [
        'rate_limit' => env('API_RATE_LIMIT', 60), // requests per minute
        'require_https' => env('API_REQUIRE_HTTPS', true),
        'cors_origins' => explode(',', env('API_CORS_ORIGINS', '')),
        'token_expiry' => env('API_TOKEN_EXPIRY', 60), // minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Security
    |--------------------------------------------------------------------------
    |
    | Security settings for file uploads.
    |
    */
    'uploads' => [
        'max_size' => env('UPLOAD_MAX_SIZE', 10240), // KB
        'allowed_extensions' => [
            'images' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'documents' => ['pdf', 'doc', 'docx', 'txt'],
            'spreadsheets' => ['xls', 'xlsx', 'csv'],
        ],
        'scan_for_malware' => env('UPLOAD_SCAN_MALWARE', false),
        'quarantine_suspicious' => env('UPLOAD_QUARANTINE_SUSPICIOUS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Audit Logging
    |--------------------------------------------------------------------------
    |
    | Settings for security audit logging.
    |
    */
    'audit' => [
        'enabled' => env('AUDIT_ENABLED', true),
        'log_all_requests' => env('AUDIT_LOG_ALL_REQUESTS', false),
        'log_failed_attempts' => env('AUDIT_LOG_FAILED_ATTEMPTS', true),
        'log_permission_denials' => env('AUDIT_LOG_PERMISSION_DENIALS', true),
        'log_data_access' => env('AUDIT_LOG_DATA_ACCESS', true),
        'log_data_modifications' => env('AUDIT_LOG_DATA_MODIFICATIONS', true),
        'retention_days' => env('AUDIT_RETENTION_DAYS', 365),
    ],

    /*
    |--------------------------------------------------------------------------
    | Encryption
    |--------------------------------------------------------------------------
    |
    | Encryption settings for sensitive data.
    |
    */
    'encryption' => [
        'algorithm' => env('ENCRYPTION_ALGORITHM', 'AES-256-CBC'),
        'encrypt_pii' => env('ENCRYPT_PII', true), // Personally Identifiable Information
        'encrypt_financial' => env('ENCRYPT_FINANCIAL', true), // Financial data
        'key_rotation_days' => env('KEY_ROTATION_DAYS', 90),
    ],

    /*
    |--------------------------------------------------------------------------
    | Two-Factor Authentication
    |--------------------------------------------------------------------------
    |
    | Settings for 2FA implementation.
    |
    */
    'two_factor' => [
        'enabled' => env('TWO_FACTOR_ENABLED', false),
        'required_for_admin' => env('TWO_FACTOR_REQUIRED_ADMIN', true),
        'required_for_roles' => explode(',', env('TWO_FACTOR_REQUIRED_ROLES', 'admin,manager')),
        'backup_codes_count' => env('TWO_FACTOR_BACKUP_CODES', 8),
        'totp_window' => env('TWO_FACTOR_TOTP_WINDOW', 1), // Time windows to accept
    ],

    /*
    |--------------------------------------------------------------------------
    | Suspicious Activity Detection
    |--------------------------------------------------------------------------
    |
    | Settings for detecting and responding to suspicious activities.
    |
    */
    'suspicious_activity' => [
        'enabled' => env('SUSPICIOUS_ACTIVITY_ENABLED', true),
        'multiple_locations_threshold' => env('SUSPICIOUS_MULTIPLE_LOCATIONS', 3),
        'rapid_requests_threshold' => env('SUSPICIOUS_RAPID_REQUESTS', 50), // per 5 minutes
        'unusual_hours_detection' => env('SUSPICIOUS_UNUSUAL_HOURS', true),
        'auto_block_suspicious' => env('SUSPICIOUS_AUTO_BLOCK', false),
        'notify_admin' => env('SUSPICIOUS_NOTIFY_ADMIN', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup and Recovery
    |--------------------------------------------------------------------------
    |
    | Security settings for backup and recovery operations.
    |
    */
    'backup' => [
        'encrypt_backups' => env('BACKUP_ENCRYPT', true),
        'verify_integrity' => env('BACKUP_VERIFY_INTEGRITY', true),
        'offsite_storage' => env('BACKUP_OFFSITE_STORAGE', false),
        'retention_policy' => [
            'daily' => env('BACKUP_RETAIN_DAILY', 7),
            'weekly' => env('BACKUP_RETAIN_WEEKLY', 4),
            'monthly' => env('BACKUP_RETAIN_MONTHLY', 12),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Compliance
    |--------------------------------------------------------------------------
    |
    | Settings for regulatory compliance.
    |
    */
    'compliance' => [
        'gdpr_enabled' => env('COMPLIANCE_GDPR', false),
        'data_retention_days' => env('COMPLIANCE_DATA_RETENTION', 2555), // ~7 years
        'anonymize_after_days' => env('COMPLIANCE_ANONYMIZE_AFTER', 1825), // ~5 years
        'audit_trail_immutable' => env('COMPLIANCE_IMMUTABLE_AUDIT', true),
        'right_to_be_forgotten' => env('COMPLIANCE_RIGHT_TO_BE_FORGOTTEN', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Emergency Access
    |--------------------------------------------------------------------------
    |
    | Emergency access settings for critical situations.
    |
    */
    'emergency' => [
        'enabled' => env('EMERGENCY_ACCESS_ENABLED', true),
        'master_key' => env('EMERGENCY_MASTER_KEY'), // Should be very secure
        'notification_required' => env('EMERGENCY_NOTIFICATION_REQUIRED', true),
        'audit_all_actions' => env('EMERGENCY_AUDIT_ALL', true),
        'time_limit_hours' => env('EMERGENCY_TIME_LIMIT', 24),
    ],
];
