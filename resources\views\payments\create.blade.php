@extends('layouts.app')

@section('title', 'تسجيل دفعة جديدة')

@push('styles')
<style>
.payment-header {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    margin-bottom: 1.5rem;
}

.form-section-header {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
    margin: -1px -1px 0 -1px;
}

.form-section-body {
    padding: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.required-field::after {
    content: " *";
    color: #e74a3b;
    font-weight: bold;
}

.help-text {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.invoice-details {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-top: 0.5rem;
    display: none;
}

.invoice-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e3e6f0;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #5a5c69;
}

.info-value {
    color: #858796;
    font-weight: 600;
}

.amount-input-group {
    position: relative;
}

.remaining-amount {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 0.35rem;
    padding: 0.75rem;
    margin-top: 0.5rem;
    text-align: center;
}

.payment-method-details {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-top: 1rem;
    display: none;
}

.btn-submit {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 0.35rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.btn-submit:hover {
    background: linear-gradient(45deg, #20c997, #17a2b8);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    color: white;
}

.alert {
    border-radius: 0.35rem;
    border: none;
    padding: 1rem 1.5rem;
}

@media (max-width: 768px) {
    .payment-header {
        padding: 1rem;
    }
    
    .form-section-body {
        padding: 1rem;
    }
    
    .invoice-info {
        grid-template-columns: 1fr;
    }
    
    .btn-submit {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="payment-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">تسجيل دفعة جديدة</h1>
                <p class="mb-0 opacity-75">تسجيل دفعة جديدة لفاتورة</p>
            </div>
            <div>
                <a href="{{ route('payments.index') }}" class="btn btn-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للمدفوعات
                </a>
            </div>
        </div>
    </div>

    <!-- Alert Info -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle me-2"></i>
        <strong>تنبيه:</strong> الحقول المميزة بعلامة (*) مطلوبة ويجب ملؤها.
    </div>

    <!-- Form -->
    <form action="{{ route('payments.store') }}" method="POST" id="paymentForm">
        @csrf
        
        <!-- Invoice Selection -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-invoice me-2"></i>
                    اختيار الفاتورة
                </h5>
            </div>
            <div class="form-section-body">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="invoice_id" class="form-label required-field">الفاتورة</label>
                        <select class="form-select @error('invoice_id') is-invalid @enderror" 
                                id="invoice_id" 
                                name="invoice_id" 
                                required>
                            <option value="">اختر الفاتورة</option>
                            @foreach($invoices as $invoice)
                                <option value="{{ $invoice->id }}" 
                                        data-customer="{{ $invoice->customer->name }}"
                                        data-phone="{{ $invoice->customer->phone_number }}"
                                        data-total="{{ $invoice->total_amount }}"
                                        data-paid="{{ $invoice->paid_amount }}"
                                        data-remaining="{{ $invoice->total_amount - $invoice->paid_amount }}"
                                        data-due-date="{{ $invoice->due_date->format('Y-m-d') }}"
                                        {{ old('invoice_id', $selectedInvoice?->id) == $invoice->id ? 'selected' : '' }}>
                                    {{ $invoice->invoice_number }} - {{ $invoice->customer->name }} - @arabicCurrency($invoice->total_amount - $invoice->paid_amount) متبقي
                                </option>
                            @endforeach
                        </select>
                        @error('invoice_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">اختر الفاتورة التي تريد تسجيل دفعة لها</div>
                        
                        <div class="invoice-details" id="invoiceDetails">
                            <div class="invoice-info">
                                <div class="info-item">
                                    <span class="info-label">العميل:</span>
                                    <span class="info-value" id="invoiceCustomer">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">الهاتف:</span>
                                    <span class="info-value" id="invoicePhone">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">إجمالي الفاتورة:</span>
                                    <span class="info-value" id="invoiceTotal">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">المبلغ المدفوع:</span>
                                    <span class="info-value" id="invoicePaid">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">المبلغ المتبقي:</span>
                                    <span class="info-value text-danger" id="invoiceRemaining">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">تاريخ الاستحقاق:</span>
                                    <span class="info-value" id="invoiceDueDate">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Details -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    تفاصيل الدفعة
                </h5>
            </div>
            <div class="form-section-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="amount" class="form-label required-field">مبلغ الدفعة</label>
                        <div class="input-group">
                            <input type="number" 
                                   class="form-control @error('amount') is-invalid @enderror" 
                                   id="amount" 
                                   name="amount" 
                                   value="{{ old('amount') }}" 
                                   min="0.01" 
                                   step="0.01"
                                   placeholder="0.00"
                                   required>
                            <span class="input-group-text">ريال</span>
                        </div>
                        @error('amount')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        
                        <div class="remaining-amount" id="remainingAmount" style="display: none;">
                            <strong>المبلغ المتبقي بعد هذه الدفعة: <span id="newRemaining">0.00</span> ريال</strong>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="payment_date" class="form-label required-field">تاريخ الدفع</label>
                        <input type="date" 
                               class="form-control @error('payment_date') is-invalid @enderror" 
                               id="payment_date" 
                               name="payment_date" 
                               value="{{ old('payment_date', date('Y-m-d')) }}" 
                               required>
                        @error('payment_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="payment_method" class="form-label required-field">طريقة الدفع</label>
                        <select class="form-select @error('payment_method') is-invalid @enderror" 
                                id="payment_method" 
                                name="payment_method" 
                                required>
                            <option value="">اختر طريقة الدفع</option>
                            <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>نقدي</option>
                            <option value="card" {{ old('payment_method') == 'card' ? 'selected' : '' }}>بطاقة</option>
                            <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>تحويل بنكي</option>
                            <option value="check" {{ old('payment_method') == 'check' ? 'selected' : '' }}>شيك</option>
                            <option value="mobile_payment" {{ old('payment_method') == 'mobile_payment' ? 'selected' : '' }}>دفع عبر الجوال</option>
                            <option value="other" {{ old('payment_method') == 'other' ? 'selected' : '' }}>أخرى</option>
                        </select>
                        @error('payment_method')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="payment_reference" class="form-label">رقم المرجع</label>
                        <input type="text" 
                               class="form-control @error('payment_reference') is-invalid @enderror" 
                               id="payment_reference" 
                               name="payment_reference" 
                               value="{{ old('payment_reference') }}" 
                               placeholder="رقم المرجع أو الإيصال">
                        @error('payment_reference')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">رقم المرجع للتحويل البنكي أو رقم الإيصال</div>
                    </div>
                </div>

                <!-- Payment Method Specific Fields -->
                <div class="payment-method-details" id="cardDetails">
                    <h6 class="text-muted mb-3">تفاصيل البطاقة</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="card_last_four" class="form-label">آخر 4 أرقام من البطاقة</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="card_last_four" 
                                   name="card_last_four" 
                                   value="{{ old('card_last_four') }}" 
                                   maxlength="4"
                                   placeholder="1234">
                        </div>
                    </div>
                </div>

                <div class="payment-method-details" id="bankDetails">
                    <h6 class="text-muted mb-3">تفاصيل التحويل البنكي</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="bank_name" class="form-label">اسم البنك</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="bank_name" 
                                   name="bank_name" 
                                   value="{{ old('bank_name') }}" 
                                   placeholder="اسم البنك">
                        </div>
                    </div>
                </div>

                <div class="payment-method-details" id="checkDetails">
                    <h6 class="text-muted mb-3">تفاصيل الشيك</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="check_number" class="form-label">رقم الشيك</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="check_number" 
                                   name="check_number" 
                                   value="{{ old('check_number') }}" 
                                   placeholder="رقم الشيك">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="receipt_number" class="form-label">رقم الإيصال</label>
                        <input type="text" 
                               class="form-control @error('receipt_number') is-invalid @enderror" 
                               id="receipt_number" 
                               name="receipt_number" 
                               value="{{ old('receipt_number') }}" 
                               placeholder="رقم الإيصال الداخلي">
                        @error('receipt_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">رقم الإيصال الداخلي للمرجع</div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control @error('notes') is-invalid @enderror" 
                              id="notes" 
                              name="notes" 
                              rows="3" 
                              placeholder="أي ملاحظات إضافية">{{ old('notes') }}</textarea>
                    @error('notes')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="text-center mt-4">
            <button type="submit" class="btn-submit">
                <i class="fas fa-save me-2"></i>تسجيل الدفعة
            </button>
            <a href="{{ route('payments.index') }}" class="btn btn-secondary ms-2">
                <i class="fas fa-times me-2"></i>إلغاء
            </a>
        </div>
    </form>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentInvoiceRemaining = 0;
    
    // Invoice selection handler
    document.getElementById('invoice_id').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const invoiceDetails = document.getElementById('invoiceDetails');
        
        if (this.value) {
            // Update invoice details
            document.getElementById('invoiceCustomer').textContent = selectedOption.dataset.customer || '-';
            document.getElementById('invoicePhone').textContent = selectedOption.dataset.phone || '-';
            document.getElementById('invoiceTotal').textContent = parseFloat(selectedOption.dataset.total || 0).toFixed(2) + ' ريال';
            document.getElementById('invoicePaid').textContent = parseFloat(selectedOption.dataset.paid || 0).toFixed(2) + ' ريال';
            document.getElementById('invoiceRemaining').textContent = parseFloat(selectedOption.dataset.remaining || 0).toFixed(2) + ' ريال';
            document.getElementById('invoiceDueDate').textContent = selectedOption.dataset.dueDate || '-';
            
            currentInvoiceRemaining = parseFloat(selectedOption.dataset.remaining || 0);
            
            // Set max amount to remaining amount
            document.getElementById('amount').max = currentInvoiceRemaining;
            document.getElementById('amount').value = currentInvoiceRemaining.toFixed(2);
            
            invoiceDetails.style.display = 'block';
            updateRemainingAmount();
        } else {
            invoiceDetails.style.display = 'none';
            document.getElementById('remainingAmount').style.display = 'none';
            currentInvoiceRemaining = 0;
        }
    });
    
    // Amount input handler
    document.getElementById('amount').addEventListener('input', updateRemainingAmount);
    
    // Payment method change handler
    document.getElementById('payment_method').addEventListener('change', function() {
        // Hide all method details
        document.querySelectorAll('.payment-method-details').forEach(el => {
            el.style.display = 'none';
        });
        
        // Show relevant details
        switch(this.value) {
            case 'card':
                document.getElementById('cardDetails').style.display = 'block';
                break;
            case 'bank_transfer':
                document.getElementById('bankDetails').style.display = 'block';
                break;
            case 'check':
                document.getElementById('checkDetails').style.display = 'block';
                break;
        }
    });
    
    function updateRemainingAmount() {
        const amount = parseFloat(document.getElementById('amount').value) || 0;
        const newRemaining = Math.max(0, currentInvoiceRemaining - amount);
        
        document.getElementById('newRemaining').textContent = newRemaining.toFixed(2);
        
        if (amount > 0 && currentInvoiceRemaining > 0) {
            document.getElementById('remainingAmount').style.display = 'block';
        } else {
            document.getElementById('remainingAmount').style.display = 'none';
        }
        
        // Validate amount
        const amountInput = document.getElementById('amount');
        if (amount > currentInvoiceRemaining) {
            amountInput.setCustomValidity('المبلغ أكبر من المبلغ المتبقي');
        } else {
            amountInput.setCustomValidity('');
        }
    }
    
    // Trigger invoice details if pre-selected
    if (document.getElementById('invoice_id').value) {
        document.getElementById('invoice_id').dispatchEvent(new Event('change'));
    }
    
    // Trigger payment method details if pre-selected
    if (document.getElementById('payment_method').value) {
        document.getElementById('payment_method').dispatchEvent(new Event('change'));
    }
    
    // Form validation
    document.getElementById('paymentForm').addEventListener('submit', function(e) {
        const amount = parseFloat(document.getElementById('amount').value) || 0;
        
        if (amount <= 0) {
            e.preventDefault();
            alert('يجب إدخال مبلغ صحيح');
            return false;
        }
        
        if (amount > currentInvoiceRemaining) {
            e.preventDefault();
            alert('المبلغ أكبر من المبلغ المتبقي للفاتورة');
            return false;
        }
        
        return confirm('هل أنت متأكد من تسجيل هذه الدفعة؟');
    });
});
</script>
@endpush
@endsection
