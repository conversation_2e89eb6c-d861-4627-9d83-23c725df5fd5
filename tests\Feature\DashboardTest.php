<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Customer;
use App\Models\RepairTicket;
use App\Models\RepairStatus;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class DashboardTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
    }

    /** @test */
    public function authenticated_user_can_view_dashboard()
    {
        $this->actingAs($this->user);
        
        $response = $this->get(route('dashboard'));
        
        $response->assertStatus(200);
        $response->assertViewIs('dashboard');
    }

    /** @test */
    public function guest_cannot_view_dashboard()
    {
        $response = $this->get(route('dashboard'));
        
        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function dashboard_displays_correct_statistics()
    {
        $this->actingAs($this->user);
        
        // Create test data
        $pendingStatus = RepairStatus::factory()->create(['name' => 'Pending']);
        $completedStatus = RepairStatus::factory()->create(['name' => 'Completed']);
        
        RepairTicket::factory()->count(5)->create(['repair_status_id' => $pendingStatus->id]);
        RepairTicket::factory()->count(3)->create(['repair_status_id' => $completedStatus->id]);
        Customer::factory()->count(10)->create();
        
        $response = $this->get(route('dashboard'));
        
        $response->assertStatus(200);
        $response->assertSee('8'); // Total tickets
        $response->assertSee('5'); // Pending tickets
        $response->assertSee('3'); // Completed tickets
        $response->assertSee('10'); // Total customers
    }

    /** @test */
    public function dashboard_shows_overdue_tickets_count()
    {
        $this->actingAs($this->user);
        
        $inProgressStatus = RepairStatus::factory()->create(['name' => 'In Progress']);
        
        // Create overdue tickets (more than 7 days old)
        RepairTicket::factory()->count(3)->create([
            'repair_status_id' => $inProgressStatus->id,
            'received_date' => Carbon::now()->subDays(10)
        ]);
        
        // Create recent tickets
        RepairTicket::factory()->count(2)->create([
            'repair_status_id' => $inProgressStatus->id,
            'received_date' => Carbon::now()->subDays(3)
        ]);
        
        $response = $this->get(route('dashboard'));
        
        $response->assertStatus(200);
        $response->assertSee('3'); // Overdue tickets count
    }

    /** @test */
    public function dashboard_displays_recent_tickets()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create(['name' => 'John Doe']);
        $ticket = RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'received_date' => Carbon::now()->subHours(2)
        ]);
        
        $response = $this->get(route('dashboard'));
        
        $response->assertStatus(200);
        $response->assertSee($ticket->ticket_number);
        $response->assertSee('John Doe');
    }

    /** @test */
    public function dashboard_shows_monthly_revenue()
    {
        $this->actingAs($this->user);
        
        RepairTicket::factory()->count(3)->create([
            'final_cost' => 100.00,
            'completed_date' => Carbon::now()->subDays(5)
        ]);
        
        RepairTicket::factory()->count(2)->create([
            'final_cost' => 150.00,
            'completed_date' => Carbon::now()->subDays(10)
        ]);
        
        $response = $this->get(route('dashboard'));
        
        $response->assertStatus(200);
        // Should show total revenue for current month
        $response->assertSee('$600.00'); // 3 * 100 + 2 * 150
    }

    /** @test */
    public function dashboard_displays_top_brands()
    {
        $this->actingAs($this->user);
        
        $appleBrand = Brand::factory()->create(['name' => 'Apple']);
        $samsungBrand = Brand::factory()->create(['name' => 'Samsung']);
        
        RepairTicket::factory()->count(5)->create(['brand_id' => $appleBrand->id]);
        RepairTicket::factory()->count(3)->create(['brand_id' => $samsungBrand->id]);
        
        $response = $this->get(route('dashboard'));
        
        $response->assertStatus(200);
        $response->assertSee('Apple');
        $response->assertSee('Samsung');
        $response->assertSee('5'); // Apple count
        $response->assertSee('3'); // Samsung count
    }

    /** @test */
    public function dashboard_shows_technician_workload()
    {
        $this->actingAs($this->user);
        
        $technician1 = User::factory()->create(['name' => 'Tech One']);
        $technician2 = User::factory()->create(['name' => 'Tech Two']);
        
        RepairTicket::factory()->count(4)->create(['assigned_to' => $technician1->id]);
        RepairTicket::factory()->count(2)->create(['assigned_to' => $technician2->id]);
        RepairTicket::factory()->count(1)->create(['assigned_to' => null]); // Unassigned
        
        $response = $this->get(route('dashboard'));
        
        $response->assertStatus(200);
        $response->assertSee('Tech One');
        $response->assertSee('Tech Two');
        $response->assertSee('4'); // Tech One workload
        $response->assertSee('2'); // Tech Two workload
        $response->assertSee('1'); // Unassigned count
    }

    /** @test */
    public function dashboard_displays_completion_rate()
    {
        $this->actingAs($this->user);
        
        $completedStatus = RepairStatus::factory()->create(['name' => 'Completed']);
        $pendingStatus = RepairStatus::factory()->create(['name' => 'Pending']);
        
        RepairTicket::factory()->count(7)->create(['repair_status_id' => $completedStatus->id]);
        RepairTicket::factory()->count(3)->create(['repair_status_id' => $pendingStatus->id]);
        
        $response = $this->get(route('dashboard'));
        
        $response->assertStatus(200);
        // Completion rate should be 70% (7 out of 10)
        $response->assertSee('70%');
    }

    /** @test */
    public function dashboard_shows_average_repair_time()
    {
        $this->actingAs($this->user);
        
        $completedStatus = RepairStatus::factory()->create(['name' => 'Completed']);
        
        // Ticket completed in 3 days
        RepairTicket::factory()->create([
            'repair_status_id' => $completedStatus->id,
            'received_date' => Carbon::now()->subDays(5),
            'completed_date' => Carbon::now()->subDays(2)
        ]);
        
        // Ticket completed in 7 days
        RepairTicket::factory()->create([
            'repair_status_id' => $completedStatus->id,
            'received_date' => Carbon::now()->subDays(10),
            'completed_date' => Carbon::now()->subDays(3)
        ]);
        
        $response = $this->get(route('dashboard'));
        
        $response->assertStatus(200);
        // Average should be 5 days
        $response->assertSee('5 days');
    }

    /** @test */
    public function dashboard_displays_priority_distribution()
    {
        $this->actingAs($this->user);
        
        RepairTicket::factory()->count(2)->create(['priority' => 'high']);
        RepairTicket::factory()->count(5)->create(['priority' => 'medium']);
        RepairTicket::factory()->count(8)->create(['priority' => 'normal']);
        
        $response = $this->get(route('dashboard'));
        
        $response->assertStatus(200);
        $response->assertSee('2'); // High priority count
        $response->assertSee('5'); // Medium priority count
        $response->assertSee('8'); // Normal priority count
    }

    /** @test */
    public function dashboard_shows_customer_satisfaction_metrics()
    {
        $this->actingAs($this->user);
        
        $completedStatus = RepairStatus::factory()->create(['name' => 'Completed']);
        
        // Tickets completed on time
        RepairTicket::factory()->count(8)->create([
            'repair_status_id' => $completedStatus->id,
            'received_date' => Carbon::now()->subDays(5),
            'completed_date' => Carbon::now()->subDays(2),
            'estimated_completion_date' => Carbon::now()->subDays(1)
        ]);
        
        // Tickets completed late
        RepairTicket::factory()->count(2)->create([
            'repair_status_id' => $completedStatus->id,
            'received_date' => Carbon::now()->subDays(10),
            'completed_date' => Carbon::now()->subDays(1),
            'estimated_completion_date' => Carbon::now()->subDays(3)
        ]);
        
        $response = $this->get(route('dashboard'));
        
        $response->assertStatus(200);
        // On-time completion rate should be 80%
        $response->assertSee('80%');
    }

    /** @test */
    public function dashboard_api_returns_real_time_stats()
    {
        $this->actingAs($this->user);
        
        RepairTicket::factory()->count(5)->create();
        Customer::factory()->count(3)->create();
        
        $response = $this->get('/api/dashboard/stats');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total_tickets',
            'total_customers',
            'pending_tickets',
            'completed_tickets',
            'overdue_tickets'
        ]);
        
        $data = $response->json();
        $this->assertEquals(5, $data['total_tickets']);
        $this->assertEquals(3, $data['total_customers']);
    }

    /** @test */
    public function dashboard_api_returns_recent_activities()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create(['name' => 'John Doe']);
        RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'received_date' => Carbon::now()->subMinutes(30)
        ]);
        
        $response = $this->get('/api/dashboard/recent-activities');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => [
                'type',
                'title',
                'description',
                'time_ago'
            ]
        ]);
    }

    /** @test */
    public function dashboard_chart_data_is_properly_formatted()
    {
        $this->actingAs($this->user);
        
        $pendingStatus = RepairStatus::factory()->create(['name' => 'Pending', 'color' => '#ffc107']);
        $completedStatus = RepairStatus::factory()->create(['name' => 'Completed', 'color' => '#28a745']);
        
        RepairTicket::factory()->count(3)->create(['repair_status_id' => $pendingStatus->id]);
        RepairTicket::factory()->count(7)->create(['repair_status_id' => $completedStatus->id]);
        
        $response = $this->get(route('dashboard'));
        
        $response->assertStatus(200);
        
        // Check that chart data is passed to the view
        $response->assertViewHas('ticketStatusData');
        $response->assertViewHas('revenueData');
        $response->assertViewHas('brandData');
        
        $ticketStatusData = $response->viewData('ticketStatusData');
        $this->assertCount(2, $ticketStatusData);
        $this->assertEquals('Pending', $ticketStatusData[0]['name']);
        $this->assertEquals(3, $ticketStatusData[0]['count']);
        $this->assertEquals('#ffc107', $ticketStatusData[0]['color']);
    }

    /** @test */
    public function dashboard_handles_empty_data_gracefully()
    {
        $this->actingAs($this->user);
        
        // No data in database
        $response = $this->get(route('dashboard'));
        
        $response->assertStatus(200);
        $response->assertSee('0'); // Should show zeros for all counts
    }

    /** @test */
    public function dashboard_filters_data_by_date_range()
    {
        $this->actingAs($this->user);
        
        // Old tickets
        RepairTicket::factory()->count(3)->create([
            'received_date' => Carbon::now()->subMonths(2)
        ]);
        
        // Recent tickets
        RepairTicket::factory()->count(5)->create([
            'received_date' => Carbon::now()->subDays(5)
        ]);
        
        $response = $this->get(route('dashboard', [
            'start_date' => Carbon::now()->subDays(30)->format('Y-m-d'),
            'end_date' => Carbon::now()->format('Y-m-d')
        ]));
        
        $response->assertStatus(200);
        // Should only show recent tickets in the count
        $response->assertSee('5');
    }
}
