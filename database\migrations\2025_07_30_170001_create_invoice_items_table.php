<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            
            // Item information
            $table->enum('item_type', ['service', 'part', 'labor', 'other'])->default('service');
            $table->string('item_name'); // Item/service name
            $table->text('item_description')->nullable(); // Item description
            $table->string('item_sku')->nullable(); // SKU if it's a part
            
            // Reference to inventory item if applicable
            $table->foreignId('inventory_item_id')->nullable()->constrained()->onDelete('set null');
            
            // Quantity and pricing
            $table->decimal('quantity', 8, 2)->default(1); // Quantity
            $table->string('unit_of_measure')->default('قطعة'); // Unit of measure
            $table->decimal('unit_price', 10, 2); // Price per unit
            $table->decimal('total_price', 10, 2); // Total price for this line item
            
            // Discount information
            $table->decimal('discount_percentage', 5, 2)->default(0); // Discount percentage
            $table->decimal('discount_amount', 10, 2)->default(0); // Discount amount
            $table->decimal('net_amount', 10, 2); // Net amount after discount
            
            // Tax information
            $table->decimal('tax_rate', 5, 2)->default(15); // Tax rate for this item
            $table->decimal('tax_amount', 10, 2)->default(0); // Tax amount for this item
            $table->boolean('is_taxable')->default(true); // Whether this item is taxable
            
            // Additional information
            $table->text('notes')->nullable(); // Line item notes
            $table->integer('sort_order')->default(0); // Display order
            
            $table->timestamps();

            // Indexes
            $table->index('invoice_id');
            $table->index('inventory_item_id');
            $table->index('item_type');
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_items');
    }
};
