<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class RepairTicket extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'ticket_number',
        'customer_id',
        'brand_id',
        'device_model',
        'device_pattern',
        'security_pattern',
        'visual_pattern',
        'pattern_type',
        'device_condition_id',
        'reported_problem',
        'technician_comments',
        'repair_status_id',
        'received_date',
        'estimated_completion_date',
        'completed_date',
        'estimated_cost',
        'final_cost',
        'created_by',
        'assigned_technician_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'received_date' => 'datetime',
        'estimated_completion_date' => 'datetime',
        'completed_date' => 'datetime',
        'estimated_cost' => 'decimal:2',
        'final_cost' => 'decimal:2',
        'device_pattern' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($ticket) {
            if (empty($ticket->ticket_number)) {
                $ticket->ticket_number = static::generateTicketNumber();
            }
            if (empty($ticket->received_date)) {
                $ticket->received_date = now();
            }
        });
    }

    /**
     * Generate a unique ticket number.
     */
    public static function generateTicketNumber(): string
    {
        $prefix = 'NJ';
        $date = now()->format('Ymd');

        // Get the last ticket number for today
        $lastTicket = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = 1;
        if ($lastTicket) {
            // Extract sequence number from last ticket
            $lastNumber = substr($lastTicket->ticket_number, -4);
            $sequence = intval($lastNumber) + 1;
        }

        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get the customer that owns the repair ticket.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the brand that owns the repair ticket.
     */
    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * Get the device condition that owns the repair ticket.
     */
    public function deviceCondition(): BelongsTo
    {
        return $this->belongsTo(DeviceCondition::class);
    }

    /**
     * Get the repair status that owns the repair ticket.
     */
    public function repairStatus(): BelongsTo
    {
        return $this->belongsTo(RepairStatus::class);
    }

    /**
     * Get the user who created the repair ticket.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the technician assigned to the repair ticket.
     */
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_technician_id');
    }

    /**
     * Get the assigned technician for the repair ticket.
     */
    public function assignedTechnician(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_technician_id');
    }

    /**
     * Get the invoice for this repair ticket.
     */
    public function invoice(): HasOne
    {
        return $this->hasOne(Invoice::class, 'repair_ticket_id');
    }

    /**
     * Scope a query to search tickets by ticket number, customer name, or device model.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('ticket_number', 'like', "%{$search}%")
              ->orWhere('device_model', 'like', "%{$search}%")
              ->orWhere('reported_problem', 'like', "%{$search}%")
              ->orWhere('technician_comments', 'like', "%{$search}%")
              ->orWhereHas('customer', function ($customerQuery) use ($search) {
                  $customerQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('phone_number', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
              })
              ->orWhereHas('brand', function ($brandQuery) use ($search) {
                  $brandQuery->where('name', 'like', "%{$search}%");
              });
        });
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeByStatus($query, $statusId)
    {
        return $query->where('repair_status_id', $statusId);
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('received_date', [$startDate, $endDate]);
    }

    /**
     * Get the days since the ticket was received.
     */
    public function getDaysSinceReceivedAttribute(): int
    {
        return $this->received_date->diffInDays(now());
    }

    /**
     * Get the days since the ticket was received (method version).
     */
    public function daysSinceReceived(): int
    {
        return $this->received_date->diffInDays(now());
    }

    /**
     * Check if the ticket is overdue based on estimated completion date.
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->estimated_completion_date &&
               $this->estimated_completion_date->isPast() &&
               !$this->completed_date;
    }

    /**
     * Check if the ticket is overdue (method version).
     */
    public function isOverdue(): bool
    {
        return $this->estimated_completion_date &&
               $this->estimated_completion_date->isPast() &&
               !$this->completed_date;
    }

    /**
     * Check if the device has a security pattern/password.
     */
    public function hasSecurityPattern(): bool
    {
        return !empty($this->security_pattern);
    }

    /**
     * Get a masked version of the security pattern for display.
     */
    public function getMaskedSecurityPattern(): string
    {
        if (!$this->hasSecurityPattern()) {
            return 'لا يوجد';
        }

        $pattern = $this->security_pattern;
        if (strlen($pattern) <= 2) {
            return str_repeat('*', strlen($pattern));
        }

        return substr($pattern, 0, 1) . str_repeat('*', strlen($pattern) - 2) . substr($pattern, -1);
    }





    /**
     * Scope for overdue tickets
     */
    public function scopeOverdue($query)
    {
        return $query->whereNotNull('estimated_completion_date')
                    ->where('estimated_completion_date', '<', now())
                    ->whereHas('repairStatus', function ($q) {
                        $q->where('is_final', false);
                    });
    }

    /**
     * Scope for pending tickets (not final status)
     */
    public function scopePending($query)
    {
        return $query->whereHas('repairStatus', function ($q) {
            $q->where('is_final', false);
        });
    }

    /**
     * Scope for completed tickets
     */
    public function scopeCompleted($query)
    {
        return $query->whereHas('repairStatus', function ($q) {
            $q->where('is_final', true);
        });
    }

    /**
     * Scope for tickets assigned to a specific technician
     */
    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_technician_id', $userId);
    }

    /**
     * Scope for unassigned tickets
     */
    public function scopeUnassigned($query)
    {
        return $query->whereNull('assigned_technician_id');
    }

    /**
     * Scope for urgent tickets (overdue or due soon)
     */
    public function scopeUrgent($query, $days = 1)
    {
        return $query->where(function ($q) use ($days) {
            $q->whereNotNull('estimated_completion_date')
              ->where('estimated_completion_date', '<=', now()->addDays($days))
              ->whereHas('repairStatus', function ($statusQuery) {
                  $statusQuery->where('is_final', false);
              });
        });
    }

    /**
     * Get priority level based on days since received and status
     */
    public function getPriorityAttribute(): string
    {
        if ($this->isOverdue()) {
            return 'high';
        }

        if ($this->daysSinceReceived() > 7) {
            return 'medium';
        }

        return 'normal';
    }

    /**
     * Get status badge color class
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->priority) {
            'high' => 'bg-danger',
            'medium' => 'bg-warning',
            default => 'bg-secondary'
        };
    }

    /**
     * Set the security pattern attribute (encrypt it).
     */
    public function setSecurityPatternAttribute($value): void
    {
        $this->attributes['security_pattern'] = $value ? encrypt($value) : null;
    }

    /**
     * Get the security pattern attribute (decrypt it).
     */
    public function getSecurityPatternAttribute($value): ?string
    {
        return $value ? decrypt($value) : null;
    }

    /**
     * Set the visual pattern attribute (encrypt it).
     */
    public function setVisualPatternAttribute($value): void
    {
        $this->attributes['visual_pattern'] = $value ? encrypt($value) : null;
    }

    /**
     * Get the visual pattern attribute (decrypt it).
     */
    public function getVisualPatternAttribute($value): ?string
    {
        return $value ? decrypt($value) : null;
    }

    /**
     * Check if the ticket has a visual pattern.
     */
    public function hasVisualPattern(): bool
    {
        return !empty($this->visual_pattern);
    }

    /**
     * Check if the ticket has any security pattern (text or visual).
     */
    public function hasAnySecurityPattern(): bool
    {
        return $this->hasSecurityPattern() || $this->hasVisualPattern();
    }

    /**
     * Get the visual pattern as an array of dot IDs.
     */
    public function getVisualPatternArray(): array
    {
        if (!$this->hasVisualPattern()) {
            return [];
        }

        return explode('-', $this->visual_pattern);
    }

    /**
     * Get masked visual pattern for display (shows only first and last dots).
     */
    public function getMaskedVisualPattern(): string
    {
        if (!$this->hasVisualPattern()) {
            return 'لا يوجد';
        }

        $patternArray = $this->getVisualPatternArray();
        if (count($patternArray) <= 2) {
            return str_repeat('*', count($patternArray));
        }

        return $patternArray[0] . '-' . str_repeat('*', count($patternArray) - 2) . '-' . end($patternArray);
    }

    /**
     * Get security pattern display based on pattern type.
     */
    public function getSecurityPatternDisplay(): string
    {
        switch ($this->pattern_type) {
            case 'text':
                return $this->getMaskedSecurityPattern();
            case 'visual':
                return 'نمط بصري: ' . $this->getMaskedVisualPattern();
            case 'both':
                return $this->getMaskedSecurityPattern() . ' + نمط بصري';
            default:
                return 'لا يوجد';
        }
    }
}
