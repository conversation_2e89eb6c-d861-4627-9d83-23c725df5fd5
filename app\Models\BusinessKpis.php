<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class BusinessKpis extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'kpi_date',
        'period_type',
        'revenue_per_customer',
        'average_repair_value',
        'customer_acquisition_cost',
        'customer_lifetime_value',
        'inventory_turnover_ratio',
        'average_repair_time_hours',
        'first_time_fix_rate',
        'customer_satisfaction_score',
        'employee_productivity_score',
        'repeat_customer_count',
        'repeat_customer_percentage',
        'new_customer_count',
        'customer_retention_rate',
        'gross_margin_percentage',
        'operating_margin_percentage',
        'cash_conversion_cycle_days',
        'accounts_receivable_turnover',
        'working_capital',
        'debt_to_equity_ratio',
        'return_on_assets',
        'return_on_equity',
        'additional_metrics',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'kpi_date' => 'date',
        'revenue_per_customer' => 'decimal:2',
        'average_repair_value' => 'decimal:2',
        'customer_acquisition_cost' => 'decimal:2',
        'customer_lifetime_value' => 'decimal:2',
        'inventory_turnover_ratio' => 'decimal:2',
        'first_time_fix_rate' => 'decimal:2',
        'customer_satisfaction_score' => 'decimal:2',
        'employee_productivity_score' => 'decimal:2',
        'repeat_customer_percentage' => 'decimal:2',
        'customer_retention_rate' => 'decimal:2',
        'gross_margin_percentage' => 'decimal:2',
        'operating_margin_percentage' => 'decimal:2',
        'cash_conversion_cycle_days' => 'decimal:2',
        'accounts_receivable_turnover' => 'decimal:2',
        'working_capital' => 'decimal:2',
        'debt_to_equity_ratio' => 'decimal:2',
        'return_on_assets' => 'decimal:2',
        'return_on_equity' => 'decimal:2',
        'additional_metrics' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Generate KPIs for a specific date and period.
     */
    public static function generateKPIs(Carbon $date, string $periodType): self
    {
        $startDate = self::getStartDateForPeriod($date, $periodType);
        $endDate = self::getEndDateForPeriod($date, $periodType);

        // Calculate customer metrics
        $customerMetrics = self::calculateCustomerMetrics($startDate, $endDate);
        
        // Calculate operational metrics
        $operationalMetrics = self::calculateOperationalMetrics($startDate, $endDate);
        
        // Calculate financial metrics
        $financialMetrics = self::calculateFinancialMetrics($startDate, $endDate);
        
        // Calculate inventory metrics
        $inventoryMetrics = self::calculateInventoryMetrics($startDate, $endDate);

        return self::updateOrCreate(
            [
                'kpi_date' => $date->toDateString(),
                'period_type' => $periodType
            ],
            array_merge($customerMetrics, $operationalMetrics, $financialMetrics, $inventoryMetrics)
        );
    }

    /**
     * Calculate customer-related metrics.
     */
    private static function calculateCustomerMetrics(Carbon $startDate, Carbon $endDate): array
    {
        // Total revenue and customer count
        $totalRevenue = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->sum('total_amount');

        $uniqueCustomers = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->distinct('customer_id')
            ->count('customer_id');

        $revenuePerCustomer = $uniqueCustomers > 0 ? $totalRevenue / $uniqueCustomers : 0;

        // Average repair value
        $repairRevenue = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->whereHas('repairTicket')
            ->sum('total_amount');

        $repairCount = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->whereHas('repairTicket')
            ->count();

        $averageRepairValue = $repairCount > 0 ? $repairRevenue / $repairCount : 0;

        // New customers
        $newCustomerCount = Customer::whereBetween('created_at', [$startDate, $endDate])->count();

        // Repeat customers (customers with more than one invoice in period)
        $repeatCustomerCount = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->select('customer_id')
            ->groupBy('customer_id')
            ->havingRaw('COUNT(*) > 1')
            ->get()
            ->count();

        $repeatCustomerPercentage = $uniqueCustomers > 0 ? ($repeatCustomerCount / $uniqueCustomers) * 100 : 0;

        // Customer retention rate (customers who made purchases in both this period and previous period)
        $previousStartDate = self::getPreviousPeriodStart($startDate, $periodType);
        $previousEndDate = self::getPreviousPeriodEnd($startDate, $periodType);

        $previousCustomers = Invoice::whereBetween('invoice_date', [$previousStartDate, $previousEndDate])
            ->where('status', '!=', 'cancelled')
            ->distinct('customer_id')
            ->pluck('customer_id');

        $currentCustomers = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->distinct('customer_id')
            ->pluck('customer_id');

        $retainedCustomers = $previousCustomers->intersect($currentCustomers)->count();
        $customerRetentionRate = $previousCustomers->count() > 0 ? ($retainedCustomers / $previousCustomers->count()) * 100 : 0;

        // Customer Lifetime Value (simplified calculation)
        $averageOrderValue = $uniqueCustomers > 0 ? $totalRevenue / $uniqueCustomers : 0;
        $averageOrderFrequency = 2; // Assume 2 orders per year (could be calculated from historical data)
        $averageCustomerLifespan = 3; // Assume 3 years (could be calculated from historical data)
        $customerLifetimeValue = $averageOrderValue * $averageOrderFrequency * $averageCustomerLifespan;

        // Customer Acquisition Cost (simplified - would need marketing spend data)
        $customerAcquisitionCost = $newCustomerCount > 0 ? 50 : 0; // Placeholder value

        return [
            'revenue_per_customer' => $revenuePerCustomer,
            'average_repair_value' => $averageRepairValue,
            'customer_acquisition_cost' => $customerAcquisitionCost,
            'customer_lifetime_value' => $customerLifetimeValue,
            'repeat_customer_count' => $repeatCustomerCount,
            'repeat_customer_percentage' => $repeatCustomerPercentage,
            'new_customer_count' => $newCustomerCount,
            'customer_retention_rate' => $customerRetentionRate,
        ];
    }

    /**
     * Calculate operational metrics.
     */
    private static function calculateOperationalMetrics(Carbon $startDate, Carbon $endDate): array
    {
        // Average repair time
        $completedTickets = RepairTicket::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')
            ->get();

        $totalRepairHours = 0;
        $ticketCount = $completedTickets->count();

        foreach ($completedTickets as $ticket) {
            if ($ticket->completed_at) {
                $repairHours = $ticket->created_at->diffInHours($ticket->completed_at);
                $totalRepairHours += $repairHours;
            }
        }

        $averageRepairTimeHours = $ticketCount > 0 ? $totalRepairHours / $ticketCount : 0;

        // First time fix rate
        $firstTimeFixCount = RepairTicket::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')
            ->where('repair_attempts', 1)
            ->count();

        $firstTimeFixRate = $ticketCount > 0 ? ($firstTimeFixCount / $ticketCount) * 100 : 0;

        // Customer satisfaction score (would come from surveys/feedback)
        $customerSatisfactionScore = 4.2; // Placeholder - would be calculated from actual feedback

        // Employee productivity score (simplified calculation)
        $totalTicketsHandled = RepairTicket::whereBetween('created_at', [$startDate, $endDate])->count();
        $activeEmployees = User::where('is_active', true)->count();
        $employeeProductivityScore = $activeEmployees > 0 ? ($totalTicketsHandled / $activeEmployees) * 10 : 0;

        return [
            'average_repair_time_hours' => $averageRepairTimeHours,
            'first_time_fix_rate' => $firstTimeFixRate,
            'customer_satisfaction_score' => $customerSatisfactionScore,
            'employee_productivity_score' => min(100, $employeeProductivityScore), // Cap at 100
        ];
    }

    /**
     * Calculate financial metrics.
     */
    private static function calculateFinancialMetrics(Carbon $startDate, Carbon $endDate): array
    {
        // Get P&L data
        $profitLoss = ProfitLossStatement::where('statement_date', $endDate)
            ->where('period_type', 'monthly')
            ->first();

        $grossMarginPercentage = $profitLoss ? $profitLoss->gross_profit_margin : 0;
        $operatingMarginPercentage = $profitLoss ? $profitLoss->operating_profit_margin : 0;

        // Accounts receivable turnover
        $totalRevenue = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->sum('total_amount');

        $averageAccountsReceivable = Invoice::where('payment_status', '!=', 'paid')
            ->avg('remaining_amount') ?? 0;

        $accountsReceivableTurnover = $averageAccountsReceivable > 0 ? $totalRevenue / $averageAccountsReceivable : 0;

        // Cash conversion cycle (simplified)
        $averageInventoryValue = InventoryItem::avg(DB::raw('current_stock * cost_price')) ?? 0;
        $costOfGoodsSold = $profitLoss ? $profitLoss->cost_of_goods_sold : 0;
        
        $inventoryTurnoverDays = $costOfGoodsSold > 0 ? ($averageInventoryValue / $costOfGoodsSold) * 365 : 0;
        $receivablesDays = $accountsReceivableTurnover > 0 ? 365 / $accountsReceivableTurnover : 0;
        $payablesDays = 30; // Assume 30 days average payment to suppliers
        
        $cashConversionCycleDays = $inventoryTurnoverDays + $receivablesDays - $payablesDays;

        // Working capital (simplified)
        $currentAssets = $averageInventoryValue + ($averageAccountsReceivable * 12); // Rough estimate
        $currentLiabilities = 50000; // Placeholder - would need actual liability data
        $workingCapital = $currentAssets - $currentLiabilities;

        // Return ratios (simplified - would need balance sheet data)
        $totalAssets = $currentAssets + 100000; // Placeholder for fixed assets
        $totalEquity = $totalAssets - $currentLiabilities;
        $netIncome = $profitLoss ? $profitLoss->net_income : 0;

        $returnOnAssets = $totalAssets > 0 ? ($netIncome / $totalAssets) * 100 : 0;
        $returnOnEquity = $totalEquity > 0 ? ($netIncome / $totalEquity) * 100 : 0;
        $debtToEquityRatio = $totalEquity > 0 ? ($currentLiabilities / $totalEquity) : 0;

        return [
            'gross_margin_percentage' => $grossMarginPercentage,
            'operating_margin_percentage' => $operatingMarginPercentage,
            'cash_conversion_cycle_days' => $cashConversionCycleDays,
            'accounts_receivable_turnover' => $accountsReceivableTurnover,
            'working_capital' => $workingCapital,
            'debt_to_equity_ratio' => $debtToEquityRatio,
            'return_on_assets' => $returnOnAssets,
            'return_on_equity' => $returnOnEquity,
        ];
    }

    /**
     * Calculate inventory metrics.
     */
    private static function calculateInventoryMetrics(Carbon $startDate, Carbon $endDate): array
    {
        // Inventory turnover ratio
        $averageInventoryValue = InventoryItem::avg(DB::raw('current_stock * cost_price')) ?? 0;
        
        $costOfGoodsSold = DB::table('invoice_items')
            ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->join('inventory_items', 'invoice_items.inventory_item_id', '=', 'inventory_items.id')
            ->whereBetween('invoices.invoice_date', [$startDate, $endDate])
            ->where('invoices.status', '!=', 'cancelled')
            ->sum(DB::raw('invoice_items.quantity * inventory_items.cost_price'));

        $inventoryTurnoverRatio = $averageInventoryValue > 0 ? $costOfGoodsSold / $averageInventoryValue : 0;

        return [
            'inventory_turnover_ratio' => $inventoryTurnoverRatio,
        ];
    }

    /**
     * Get start date for period type.
     */
    private static function getStartDateForPeriod(Carbon $date, string $periodType): Carbon
    {
        return match ($periodType) {
            'daily' => $date->copy()->startOfDay(),
            'weekly' => $date->copy()->startOfWeek(),
            'monthly' => $date->copy()->startOfMonth(),
            'quarterly' => $date->copy()->startOfQuarter(),
            'yearly' => $date->copy()->startOfYear(),
            default => $date->copy()->startOfMonth(),
        };
    }

    /**
     * Get end date for period type.
     */
    private static function getEndDateForPeriod(Carbon $date, string $periodType): Carbon
    {
        return match ($periodType) {
            'daily' => $date->copy()->endOfDay(),
            'weekly' => $date->copy()->endOfWeek(),
            'monthly' => $date->copy()->endOfMonth(),
            'quarterly' => $date->copy()->endOfQuarter(),
            'yearly' => $date->copy()->endOfYear(),
            default => $date->copy()->endOfMonth(),
        };
    }

    /**
     * Get previous period start date.
     */
    private static function getPreviousPeriodStart(Carbon $startDate, string $periodType): Carbon
    {
        return match ($periodType) {
            'daily' => $startDate->copy()->subDay()->startOfDay(),
            'weekly' => $startDate->copy()->subWeek()->startOfWeek(),
            'monthly' => $startDate->copy()->subMonth()->startOfMonth(),
            'quarterly' => $startDate->copy()->subQuarter()->startOfQuarter(),
            'yearly' => $startDate->copy()->subYear()->startOfYear(),
            default => $startDate->copy()->subMonth()->startOfMonth(),
        };
    }

    /**
     * Get previous period end date.
     */
    private static function getPreviousPeriodEnd(Carbon $startDate, string $periodType): Carbon
    {
        return match ($periodType) {
            'daily' => $startDate->copy()->subDay()->endOfDay(),
            'weekly' => $startDate->copy()->subWeek()->endOfWeek(),
            'monthly' => $startDate->copy()->subMonth()->endOfMonth(),
            'quarterly' => $startDate->copy()->subQuarter()->endOfQuarter(),
            'yearly' => $startDate->copy()->subYear()->endOfYear(),
            default => $startDate->copy()->subMonth()->endOfMonth(),
        };
    }

    /**
     * Get KPI trends for dashboard.
     */
    public static function getKPITrends(string $periodType, int $periods = 12): array
    {
        $kpis = self::where('period_type', $periodType)
            ->orderBy('kpi_date', 'desc')
            ->limit($periods)
            ->get()
            ->reverse();

        return [
            'labels' => $kpis->pluck('kpi_date')->map(function ($date) use ($periodType) {
                return match ($periodType) {
                    'daily' => $date->format('M d'),
                    'weekly' => 'Week ' . $date->weekOfYear,
                    'monthly' => $date->format('M Y'),
                    'quarterly' => 'Q' . $date->quarter . ' ' . $date->year,
                    'yearly' => $date->format('Y'),
                    default => $date->format('M Y'),
                };
            })->toArray(),
            'revenue_per_customer' => $kpis->pluck('revenue_per_customer')->toArray(),
            'customer_retention_rate' => $kpis->pluck('customer_retention_rate')->toArray(),
            'first_time_fix_rate' => $kpis->pluck('first_time_fix_rate')->toArray(),
            'gross_margin_percentage' => $kpis->pluck('gross_margin_percentage')->toArray(),
            'inventory_turnover_ratio' => $kpis->pluck('inventory_turnover_ratio')->toArray(),
        ];
    }

    /**
     * Get KPI summary for dashboard.
     */
    public function getKPISummary(): array
    {
        return [
            'customer_metrics' => [
                'revenue_per_customer' => $this->revenue_per_customer,
                'customer_retention_rate' => $this->customer_retention_rate,
                'repeat_customer_percentage' => $this->repeat_customer_percentage,
                'customer_lifetime_value' => $this->customer_lifetime_value,
                'new_customer_count' => $this->new_customer_count,
            ],
            'operational_metrics' => [
                'average_repair_time_hours' => $this->average_repair_time_hours,
                'first_time_fix_rate' => $this->first_time_fix_rate,
                'customer_satisfaction_score' => $this->customer_satisfaction_score,
                'employee_productivity_score' => $this->employee_productivity_score,
            ],
            'financial_metrics' => [
                'gross_margin_percentage' => $this->gross_margin_percentage,
                'operating_margin_percentage' => $this->operating_margin_percentage,
                'return_on_assets' => $this->return_on_assets,
                'return_on_equity' => $this->return_on_equity,
                'working_capital' => $this->working_capital,
            ],
            'efficiency_metrics' => [
                'inventory_turnover_ratio' => $this->inventory_turnover_ratio,
                'cash_conversion_cycle_days' => $this->cash_conversion_cycle_days,
                'accounts_receivable_turnover' => $this->accounts_receivable_turnover,
            ],
        ];
    }

    /**
     * Get performance indicators with status.
     */
    public function getPerformanceIndicators(): array
    {
        return [
            'customer_retention' => [
                'value' => $this->customer_retention_rate,
                'status' => $this->customer_retention_rate >= 80 ? 'excellent' : 
                           ($this->customer_retention_rate >= 60 ? 'good' : 'needs_improvement'),
                'target' => 80,
            ],
            'first_time_fix' => [
                'value' => $this->first_time_fix_rate,
                'status' => $this->first_time_fix_rate >= 85 ? 'excellent' : 
                           ($this->first_time_fix_rate >= 70 ? 'good' : 'needs_improvement'),
                'target' => 85,
            ],
            'gross_margin' => [
                'value' => $this->gross_margin_percentage,
                'status' => $this->gross_margin_percentage >= 40 ? 'excellent' : 
                           ($this->gross_margin_percentage >= 25 ? 'good' : 'needs_improvement'),
                'target' => 40,
            ],
            'inventory_turnover' => [
                'value' => $this->inventory_turnover_ratio,
                'status' => $this->inventory_turnover_ratio >= 6 ? 'excellent' : 
                           ($this->inventory_turnover_ratio >= 4 ? 'good' : 'needs_improvement'),
                'target' => 6,
            ],
        ];
    }
}
