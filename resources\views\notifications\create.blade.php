@extends('layouts.app')

@section('title', __('app.notifications.create_notification'))

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ __('app.notifications.create_notification') }}</h1>
            <p class="text-muted">{{ __('app.send_notification_to_customer') }}</p>
        </div>
        <a href="{{ route('notifications.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> {{ __('app.back') }}
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Notification Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.notification_details') }}</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('notifications.store') }}" id="notificationForm">
                        @csrf

                        <!-- Customer Selection -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="customer_id" class="form-label">{{ __('app.customer') }} <span class="text-danger">*</span></label>
                                <select name="customer_id" id="customer_id" class="form-select @error('customer_id') is-invalid @enderror" required>
                                    <option value="">{{ __('app.select_customer') }}</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}"
                                                data-phone="{{ $customer->phone_number }}"
                                                data-email="{{ $customer->email }}"
                                                {{ (old('customer_id', $selectedCustomer?->id) == $customer->id) ? 'selected' : '' }}>
                                            {{ $customer->name }} - {{ $customer->phone_number }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('customer_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="repair_ticket_id" class="form-label">{{ __('app.repair_tickets.ticket') }} ({{ __('app.optional') }})</label>
                                <select name="repair_ticket_id" id="repair_ticket_id" class="form-select @error('repair_ticket_id') is-invalid @enderror">
                                    <option value="">{{ __('app.select_ticket') }}</option>
                                    @foreach($repairTickets as $ticket)
                                        <option value="{{ $ticket->id }}"
                                                data-customer="{{ $ticket->customer_id }}"
                                                {{ (old('repair_ticket_id', $selectedTicket?->id) == $ticket->id) ? 'selected' : '' }}>
                                            {{ $ticket->ticket_number }} - {{ $ticket->customer->name }} - {{ $ticket->device_model }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('repair_ticket_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Type and Category -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="type" class="form-label">{{ __('app.notifications.type') }} <span class="text-danger">*</span></label>
                                <select name="type" id="type" class="form-select @error('type') is-invalid @enderror" required>
                                    <option value="">{{ __('app.select_type') }}</option>
                                    <option value="sms" {{ old('type') === 'sms' ? 'selected' : '' }}>
                                        <i class="fas fa-sms"></i> {{ __('app.notifications.sms') }}
                                    </option>
                                    <option value="whatsapp" {{ old('type') === 'whatsapp' ? 'selected' : '' }}>
                                        <i class="fab fa-whatsapp"></i> {{ __('app.notifications.whatsapp') }}
                                    </option>
                                    <option value="email" {{ old('type') === 'email' ? 'selected' : '' }}>
                                        <i class="fas fa-envelope"></i> {{ __('app.notifications.email') }}
                                    </option>
                                </select>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="category" class="form-label">{{ __('app.notifications.category') }} <span class="text-danger">*</span></label>
                                <select name="category" id="category" class="form-select @error('category') is-invalid @enderror" required>
                                    <option value="">{{ __('app.select_category') }}</option>
                                    <option value="status_update" {{ old('category') === 'status_update' ? 'selected' : '' }}>
                                        {{ __('app.notifications.status_update') }}
                                    </option>
                                    <option value="pickup_ready" {{ old('category') === 'pickup_ready' ? 'selected' : '' }}>
                                        {{ __('app.notifications.pickup_ready') }}
                                    </option>
                                    <option value="appointment_reminder" {{ old('category') === 'appointment_reminder' ? 'selected' : '' }}>
                                        {{ __('app.notifications.appointment_reminder') }}
                                    </option>
                                    <option value="payment_reminder" {{ old('category') === 'payment_reminder' ? 'selected' : '' }}>
                                        {{ __('app.notifications.payment_reminder') }}
                                    </option>
                                    <option value="satisfaction_survey" {{ old('category') === 'satisfaction_survey' ? 'selected' : '' }}>
                                        {{ __('app.notifications.satisfaction_survey') }}
                                    </option>
                                    <option value="general" {{ old('category') === 'general' ? 'selected' : '' }}>
                                        {{ __('app.notifications.general') }}
                                    </option>
                                </select>
                                @error('category')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="template_id" class="form-label">{{ __('app.notifications.template') }} ({{ __('app.optional') }})</label>
                                <select name="template_id" id="template_id" class="form-select">
                                    <option value="">{{ __('app.notifications.custom_message') }}</option>
                                    @foreach($templates as $template)
                                        <option value="{{ $template->id }}"
                                                data-type="{{ $template->type }}"
                                                data-category="{{ $template->category }}"
                                                data-message="{{ $template->message_ar }}"
                                                data-subject="{{ $template->subject }}">
                                            {{ $template->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Subject (for email) -->
                        <div class="mb-3" id="subject-field" style="display: none;">
                            <label for="subject" class="form-label">{{ __('app.subject') }}</label>
                            <input type="text" name="subject" id="subject" class="form-control @error('subject') is-invalid @enderror"
                                   value="{{ old('subject') }}" maxlength="255">
                            @error('subject')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Message -->
                        <div class="mb-3">
                            <label for="message" class="form-label">{{ __('app.notifications.message') }} <span class="text-danger">*</span></label>
                            <textarea name="message" id="message" rows="5"
                                      class="form-control @error('message') is-invalid @enderror"
                                      required maxlength="1000" placeholder="{{ __('app.enter_message') }}">{{ old('message') }}</textarea>
                            <div class="form-text">
                                <span id="char-count">0</span>/1000 {{ __('app.characters') }}
                            </div>
                            @error('message')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Scheduling -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="schedule_later" name="schedule_later">
                                <label class="form-check-label" for="schedule_later">
                                    {{ __('app.notifications.schedule_for_later') }}
                                </label>
                            </div>
                        </div>

                        <div class="mb-3" id="schedule-field" style="display: none;">
                            <label for="scheduled_at" class="form-label">{{ __('app.notifications.scheduled_at') }}</label>
                            <input type="datetime-local" name="scheduled_at" id="scheduled_at"
                                   class="form-control @error('scheduled_at') is-invalid @enderror"
                                   value="{{ old('scheduled_at') }}" min="{{ now()->format('Y-m-d\TH:i') }}">
                            @error('scheduled_at')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Send Immediately -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="send_immediately" name="send_immediately" value="1" checked>
                                <label class="form-check-label" for="send_immediately">
                                    {{ __('app.notifications.send_immediately') }}
                                </label>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> {{ __('app.send') }}
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="previewMessage()">
                                <i class="fas fa-eye"></i> {{ __('app.notifications.preview') }}
                            </button>
                            <a href="{{ route('notifications.index') }}" class="btn btn-outline-secondary">
                                {{ __('app.cancel') }}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Preview Card -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.notifications.preview') }}</h5>
                </div>
                <div class="card-body">
                    <div id="preview-content">
                        <p class="text-muted">{{ __('app.select_template_or_enter_message') }}</p>
                    </div>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('app.help') }}</h5>
                </div>
                <div class="card-body">
                    <h6>{{ __('app.available_variables') }}:</h6>
                    <ul class="list-unstyled small">
                        <li><code>{customer_name}</code> - {{ __('app.customer_name') }}</li>
                        <li><code>{ticket_number}</code> - {{ __('app.ticket_number') }}</li>
                        <li><code>{device_model}</code> - {{ __('app.device_model') }}</li>
                        <li><code>{status}</code> - {{ __('app.repair_status') }}</li>
                        <li><code>{cost}</code> - {{ __('app.repair_cost') }}</li>
                        <li><code>{shop_name}</code> - {{ __('app.shop_name') }}</li>
                        <li><code>{shop_phone}</code> - {{ __('app.shop_phone') }}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    const categorySelect = document.getElementById('category');
    const templateSelect = document.getElementById('template_id');
    const messageTextarea = document.getElementById('message');
    const subjectField = document.getElementById('subject-field');
    const subjectInput = document.getElementById('subject');
    const scheduleLaterCheckbox = document.getElementById('schedule_later');
    const scheduleField = document.getElementById('schedule-field');
    const sendImmediatelyCheckbox = document.getElementById('send_immediately');
    const customerSelect = document.getElementById('customer_id');
    const ticketSelect = document.getElementById('repair_ticket_id');

    // Character count
    messageTextarea.addEventListener('input', function() {
        document.getElementById('char-count').textContent = this.value.length;
    });

    // Show/hide subject field for email
    typeSelect.addEventListener('change', function() {
        if (this.value === 'email') {
            subjectField.style.display = 'block';
            subjectInput.required = true;
        } else {
            subjectField.style.display = 'none';
            subjectInput.required = false;
        }
        filterTemplates();
    });

    // Filter templates by type and category
    function filterTemplates() {
        const selectedType = typeSelect.value;
        const selectedCategory = categorySelect.value;

        Array.from(templateSelect.options).forEach(option => {
            if (option.value === '') return;

            const templateType = option.dataset.type;
            const templateCategory = option.dataset.category;

            const typeMatch = !selectedType || templateType === selectedType;
            const categoryMatch = !selectedCategory || templateCategory === selectedCategory;

            option.style.display = (typeMatch && categoryMatch) ? 'block' : 'none';
        });

        // Reset template selection if current selection is hidden
        const currentOption = templateSelect.options[templateSelect.selectedIndex];
        if (currentOption && currentOption.style.display === 'none') {
            templateSelect.value = '';
        }
    }

    categorySelect.addEventListener('change', filterTemplates);

    // Load template content
    templateSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            messageTextarea.value = selectedOption.dataset.message || '';
            subjectInput.value = selectedOption.dataset.subject || '';

            // Update character count
            document.getElementById('char-count').textContent = messageTextarea.value.length;
        }
    });

    // Schedule later functionality
    scheduleLaterCheckbox.addEventListener('change', function() {
        if (this.checked) {
            scheduleField.style.display = 'block';
            sendImmediatelyCheckbox.checked = false;
            sendImmediatelyCheckbox.disabled = true;
        } else {
            scheduleField.style.display = 'none';
            sendImmediatelyCheckbox.disabled = false;
            sendImmediatelyCheckbox.checked = true;
        }
    });

    // Filter tickets by customer
    customerSelect.addEventListener('change', function() {
        const customerId = this.value;
        Array.from(ticketSelect.options).forEach(option => {
            if (option.value === '') return;

            const ticketCustomerId = option.dataset.customer;
            option.style.display = (!customerId || ticketCustomerId === customerId) ? 'block' : 'none';
        });

        // Reset ticket selection if current selection is hidden
        const currentTicketOption = ticketSelect.options[ticketSelect.selectedIndex];
        if (currentTicketOption && currentTicketOption.style.display === 'none') {
            ticketSelect.value = '';
        }
    });

    // Initial character count
    document.getElementById('char-count').textContent = messageTextarea.value.length;
});

function previewMessage() {
    const customerId = document.getElementById('customer_id').value;
    const ticketId = document.getElementById('repair_ticket_id').value;
    const templateId = document.getElementById('template_id').value;
    const message = document.getElementById('message').value;

    if (!message && !templateId) {
        alert('{{ __("app.please_enter_message_or_select_template") }}');
        return;
    }

    const previewContent = document.getElementById('preview-content');

    if (templateId) {
        // Preview template
        fetch('{{ route("notifications.template-preview") }}?' + new URLSearchParams({
            template_id: templateId,
            customer_id: customerId,
            repair_ticket_id: ticketId
        }))
        .then(response => response.json())
        .then(data => {
            previewContent.innerHTML = `
                ${data.subject ? `<strong>{{ __('app.subject') }}:</strong> ${data.subject}<br><br>` : ''}
                <strong>{{ __('app.notifications.message') }}:</strong><br>
                <div class="border p-2 bg-light rounded">${data.message.replace(/\n/g, '<br>')}</div>
            `;
        })
        .catch(error => {
            console.error('Error:', error);
            previewContent.innerHTML = '<p class="text-danger">{{ __("app.error_loading_preview") }}</p>';
        });
    } else {
        // Preview custom message
        const subject = document.getElementById('subject').value;
        previewContent.innerHTML = `
            ${subject ? `<strong>{{ __('app.subject') }}:</strong> ${subject}<br><br>` : ''}
            <strong>{{ __('app.notifications.message') }}:</strong><br>
            <div class="border p-2 bg-light rounded">${message.replace(/\n/g, '<br>')}</div>
        `;
    }
}
</script>
@endsection
