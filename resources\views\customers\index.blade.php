@extends('layouts.app')

@section('title', 'العملاء')

@push('styles')
<style>
.customers-header {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.search-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.customers-table {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    overflow: hidden;
}

.table th {
    background: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.customer-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
}

.contact-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #495057;
    text-decoration: none;
    transition: color 0.2s ease;
}

.contact-info:hover {
    color: #007bff;
}

.tickets-badge {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-weight: 600;
    font-size: 0.875rem;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
}

.btn-action {
    padding: 0.375rem 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid;
    transition: all 0.2s ease;
    font-size: 0.75rem;
    font-weight: 500;
    min-width: auto;
    text-align: center;
    white-space: nowrap;
    flex-shrink: 0;
}

.btn-action:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.15);
}

.btn-action:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-action i {
    font-size: 0.75rem;
}

/* إصلاح أزرار التنقل */
.pagination {
    margin: 0;
    justify-content: center;
}

.pagination .page-link {
    color: #1cc88a;
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
    margin: 0 0.125rem;
    border-radius: 0.375rem;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination .page-link:hover {
    color: #17a673;
    background-color: #f8f9fc;
    border-color: #1cc88a;
}

.pagination .page-item.active .page-link {
    background-color: #1cc88a;
    border-color: #1cc88a;
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

.pagination-wrapper {
    background: #fff;
    padding: 1rem;
    border-radius: 0.5rem;
    border-top: 1px solid #e3e6f0;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .customers-header {
        padding: 1rem;
    }

    .search-card {
        padding: 1rem;
    }

    .action-buttons {
        flex-direction: column;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="customers-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">العملاء</h1>
                <p class="mb-0 opacity-75">إدارة وتتبع جميع عملاء الورشة</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('customers.create') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>عميل جديد
                </a>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-light dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-2"></i>تصدير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ route('print-export.customers.export-excel', request()->query()) }}">
                            <i class="fas fa-file-excel me-2"></i>تصدير إكسل
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Form -->
    <div class="search-card">
        <h5 class="mb-3">
            <i class="fas fa-search me-2"></i>البحث في العملاء
        </h5>
        <form method="GET" action="{{ route('customers.index') }}" class="row g-3">
            <div class="col-md-8">
                <div class="input-group">
                    <input type="text"
                           class="form-control"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="البحث بالاسم، الهاتف، أو البريد الإلكتروني...">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                @if(request('search'))
                    <a href="{{ route('customers.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>مسح البحث
                    </a>
                @endif
            </div>
        </form>
    </div>

    <!-- Customers Table -->
    <div class="customers-table">
        <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
            <h5 class="mb-0">
                قائمة العملاء
                <span class="badge bg-secondary">@arabicNumber($customers->total()) إجمالي</span>
            </h5>
        </div>

        @if($customers->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>رقم الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>بطاقات الإصلاح</th>
                            <th>تاريخ التسجيل</th>
                            <th style="width: 150px">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($customers as $customer)
                            <tr>
                                <td>
                                    <div class="customer-name">{{ $customer->name }}</div>
                                </td>
                                <td>
                                    <a href="tel:{{ $customer->phone_number }}" class="contact-info">
                                        <i class="fas fa-phone"></i>
                                        <span>{{ $customer->phone_number }}</span>
                                    </a>
                                </td>
                                <td>
                                    @if($customer->email)
                                        <a href="mailto:{{ $customer->email }}" class="contact-info">
                                            <i class="fas fa-envelope"></i>
                                            <span>{{ $customer->email }}</span>
                                        </a>
                                    @else
                                        <span class="text-muted">لا يوجد بريد إلكتروني</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="tickets-badge">
                                        @arabicNumber($customer->repairTickets()->count()) بطاقة
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ $customer->created_at->format('Y-m-d') }}
                                    </small>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ route('customers.show', $customer) }}"
                                           class="btn btn-sm btn-outline-primary btn-action"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="عرض تفاصيل العميل">
                                            <i class="fas fa-eye"></i>
                                            <span class="d-none d-xl-inline ms-1">عرض</span>
                                        </a>
                                        <a href="{{ route('customers.edit', $customer) }}"
                                           class="btn btn-sm btn-outline-secondary btn-action"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="تعديل معلومات العميل">
                                            <i class="fas fa-edit"></i>
                                            <span class="d-none d-xl-inline ms-1">تعديل</span>
                                        </a>
                                        <a href="{{ route('repair-tickets.create', ['customer_id' => $customer->id]) }}"
                                           class="btn btn-sm btn-outline-success btn-action"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="إنشاء بطاقة إصلاح جديدة للعميل">
                                            <i class="fas fa-plus"></i>
                                            <span class="d-none d-xl-inline ms-1">بطاقة</span>
                                        </a>
                                        @if($customer->repairTickets()->count() == 0)
                                            <form method="POST"
                                                  action="{{ route('customers.destroy', $customer) }}"
                                                  class="d-inline"
                                                  onsubmit="return confirm('هل أنت متأكد من حذف هذا العميل؟ لا يمكن التراجع عن هذا الإجراء.')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit"
                                                        class="btn btn-sm btn-outline-danger btn-action"
                                                        data-bs-toggle="tooltip"
                                                        data-bs-placement="top"
                                                        title="حذف العميل نهائياً">
                                                    <i class="fas fa-trash"></i>
                                                    <span class="d-none d-xl-inline ms-1">حذف</span>
                                                </button>
                                            </form>
                                        @else
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-secondary btn-action"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="لا يمكن حذف العميل لوجود {{ $customer->repairTickets()->count() }} بطاقة إصلاح"
                                                    disabled>
                                                <i class="fas fa-lock"></i>
                                                <span class="d-none d-xl-inline ms-1">محمي</span>
                                            </button>
                                        @endif
                                    </div>
                                            </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <h4>لا يوجد عملاء</h4>
                <p>
                    @if(request('search'))
                        لا يوجد عملاء يطابقون معايير البحث المحددة.
                    @else
                        ابدأ بإضافة أول عميل للورشة.
                    @endif
                </p>
                @if(!request('search'))
                    <a href="{{ route('customers.create') }}" class="btn btn-success">
                        <i class="fas fa-plus-circle me-2"></i>إضافة أول عميل
                    </a>
                @else
                    <a href="{{ route('customers.index') }}" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>عرض جميع العملاء
                    </a>
                @endif
            </div>
        @endif

        @if($customers->hasPages())
            <div class="pagination-wrapper">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="pagination-info">
                        <small class="text-muted">
                            عرض {{ $customers->firstItem() }} إلى {{ $customers->lastItem() }}
                            من أصل {{ $customers->total() }} نتيجة
                        </small>
                    </div>
                    <div class="pagination-links">
                        {{ $customers->withQueryString()->links('custom-pagination') }}
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // تحسين تجربة المستخدم للأزرار
    document.querySelectorAll('.btn-action').forEach(function(button) {
        button.addEventListener('mouseenter', function() {
            if (!this.disabled) {
                this.style.transform = 'translateY(-2px)';
            }
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
@endpush

@endsection
