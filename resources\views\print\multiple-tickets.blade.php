<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('app.repair_tickets.multiple_tickets') }} - {{ now()->format('Y-m-d') }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-size: 11px;
            line-height: 1.3;
            color: #333;
        }

        @if(app()->getLocale() == 'ar')
        body {
            font-family: "Cairo", Arial, sans-serif;
            direction: rtl;
            text-align: right;
        }
        @else
        body {
            font-family: Arial, sans-serif;
            direction: ltr;
            text-align: left;
        }
        @endif

        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
        }

        .company-name {
            font-size: 20px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .company-info {
            font-size: 10px;
            color: #666;
        }

        .ticket-container {
            margin-bottom: 30px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            page-break-inside: avoid;
        }

        .ticket-header {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .ticket-number {
            font-size: 14px;
            font-weight: bold;
            color: #007bff;
        }

        .ticket-date {
            font-size: 10px;
            color: #666;
        }

        .ticket-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .info-section {
            margin-bottom: 10px;
        }

        .section-title {
            font-size: 11px;
            font-weight: bold;
            color: #007bff;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 3px;
            margin-bottom: 8px;
        }

        .info-item {
            margin-bottom: 5px;
            display: flex;
        }

        .info-label {
            font-weight: bold;
            color: #495057;
            width: 100px;
            flex-shrink: 0;
        }

        .info-value {
            color: #212529;
            flex: 1;
        }

        .status-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-in-progress {
            background-color: #cce5ff;
            color: #004085;
        }

        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }

        .status-cancelled {
            background-color: #f8d7da;
            color: #721c24;
        }

        .priority-high {
            color: #dc3545;
            font-weight: bold;
        }

        .priority-medium {
            color: #fd7e14;
            font-weight: bold;
        }

        .priority-normal {
            color: #28a745;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 9px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
            }

            .ticket-container {
                page-break-after: always;
            }

            .ticket-container:last-child {
                page-break-after: auto;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ __('app.app_name') }}</div>
        <div class="company-info">
            {{ __('app.repair_tickets.multiple_tickets') }} {{ __('app.reports.title') }} - {{ now()->toArabicDateTimeString() }}<br>
            {{ __('app.total') }} {{ __('app.repair_tickets.title') }}: @arabicNumber(count($tickets))
        </div>
    </div>

    @foreach($tickets as $ticket)
    <div class="ticket-container">
        <div class="ticket-header">
            <div class="ticket-number">{{ $ticket->ticket_number }}</div>
            <div class="ticket-date">{{ $ticket->received_date->format('M d, Y') }}</div>
        </div>

        <div class="ticket-info">
            <div class="info-section">
                <div class="section-title">Customer</div>
                <div class="info-item">
                    <span class="info-label">Name:</span>
                    <span class="info-value">{{ $ticket->customer->name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Phone:</span>
                    <span class="info-value">{{ $ticket->customer->phone_number }}</span>
                </div>
            </div>

            <div class="info-section">
                <div class="section-title">Device</div>
                <div class="info-item">
                    <span class="info-label">Brand:</span>
                    <span class="info-value">{{ $ticket->brand->name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Model:</span>
                    <span class="info-value">{{ $ticket->device_model }}</span>
                </div>
            </div>

            <div class="info-section full-width">
                <div class="section-title">Problem</div>
                <div class="info-item">
                    <span class="info-value">{{ $ticket->reported_problem }}</span>
                </div>
            </div>

            <div class="info-section">
                <div class="section-title">Status</div>
                <div class="info-item">
                    <span class="info-label">Current:</span>
                    <span class="status-badge status-{{ strtolower(str_replace(' ', '-', $ticket->repairStatus->name)) }}">
                        {{ $ticket->repairStatus->name }}
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">Priority:</span>
                    <span class="priority-{{ $ticket->priority }}">
                        {{ ucfirst($ticket->priority) }}
                    </span>
                </div>
            </div>

            <div class="info-section">
                <div class="section-title">Assignment</div>
                <div class="info-item">
                    <span class="info-label">Technician:</span>
                    <span class="info-value">{{ $ticket->assignedTo->name ?? 'Unassigned' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Days:</span>
                    <span class="info-value">{{ $ticket->daysSinceReceived() }} days</span>
                </div>
            </div>

            @if($ticket->final_cost)
            <div class="info-section full-width">
                <div class="section-title">Cost</div>
                <div class="info-item">
                    <span class="info-label">Final Cost:</span>
                    <span class="info-value">${{ number_format($ticket->final_cost, 2) }}</span>
                </div>
            </div>
            @endif

            @if($ticket->technician_comments)
            <div class="info-section full-width">
                <div class="section-title">Comments</div>
                <div class="info-item">
                    <span class="info-value">{{ $ticket->technician_comments }}</span>
                </div>
            </div>
            @endif
        </div>
    </div>
    @endforeach

    <div class="footer">
        <p>{{ __('app.app_name') }} - {{ __('app.company.services') }}</p>
        <p>{{ __('app.generated_on') }} {{ now()->toArabicDateTimeString() }} | {{ __('app.total') }} {{ __('app.repair_tickets.title') }}: @arabicNumber(count($tickets))</p>
    </div>
</body>
</html>
