<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class SecurityLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'action',
        'resource_type',
        'resource_id',
        'ip_address',
        'user_agent',
        'details',
        'severity',
        'status',
        'session_id',
    ];

    protected $casts = [
        'details' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    const SEVERITY_LOW = 'low';
    const SEVERITY_MEDIUM = 'medium';
    const SEVERITY_HIGH = 'high';
    const SEVERITY_CRITICAL = 'critical';

    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_BLOCKED = 'blocked';

    /**
     * Get the user that performed the action.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Log a security event.
     */
    public static function logEvent(
        string $action,
        string $resourceType = null,
        int $resourceId = null,
        array $details = [],
        string $severity = self::SEVERITY_LOW,
        string $status = self::STATUS_SUCCESS,
        int $userId = null
    ): self {
        return static::create([
            'user_id' => $userId ?? Auth::id(),
            'action' => $action,
            'resource_type' => $resourceType,
            'resource_id' => $resourceId,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'details' => $details,
            'severity' => $severity,
            'status' => $status,
            'session_id' => session()->getId(),
        ]);
    }

    /**
     * Log login attempt.
     */
    public static function logLogin(string $email, bool $successful, array $details = []): self
    {
        return static::logEvent(
            'login_attempt',
            'user',
            null,
            array_merge(['email' => $email], $details),
            $successful ? self::SEVERITY_LOW : self::SEVERITY_MEDIUM,
            $successful ? self::STATUS_SUCCESS : self::STATUS_FAILED
        );
    }

    /**
     * Log logout.
     */
    public static function logLogout(int $userId = null): self
    {
        return static::logEvent(
            'logout',
            'user',
            $userId ?? Auth::id(),
            [],
            self::SEVERITY_LOW,
            self::STATUS_SUCCESS,
            $userId
        );
    }

    /**
     * Log permission denied.
     */
    public static function logPermissionDenied(string $permission, string $resource = null, int $resourceId = null): self
    {
        return static::logEvent(
            'permission_denied',
            $resource,
            $resourceId,
            ['permission' => $permission, 'url' => Request::url()],
            self::SEVERITY_MEDIUM,
            self::STATUS_BLOCKED
        );
    }

    /**
     * Log data access.
     */
    public static function logDataAccess(string $resourceType, int $resourceId, string $action = 'view'): self
    {
        return static::logEvent(
            "data_access_{$action}",
            $resourceType,
            $resourceId,
            ['url' => Request::url()],
            self::SEVERITY_LOW,
            self::STATUS_SUCCESS
        );
    }

    /**
     * Log data modification.
     */
    public static function logDataModification(string $resourceType, int $resourceId, string $action, array $changes = []): self
    {
        return static::logEvent(
            "data_{$action}",
            $resourceType,
            $resourceId,
            ['changes' => $changes, 'url' => Request::url()],
            self::SEVERITY_MEDIUM,
            self::STATUS_SUCCESS
        );
    }

    /**
     * Log security violation.
     */
    public static function logSecurityViolation(string $violation, array $details = []): self
    {
        return static::logEvent(
            'security_violation',
            null,
            null,
            array_merge(['violation' => $violation], $details),
            self::SEVERITY_HIGH,
            self::STATUS_BLOCKED
        );
    }

    /**
     * Log password change.
     */
    public static function logPasswordChange(int $userId = null, bool $forced = false): self
    {
        return static::logEvent(
            'password_change',
            'user',
            $userId ?? Auth::id(),
            ['forced' => $forced],
            self::SEVERITY_MEDIUM,
            self::STATUS_SUCCESS,
            $userId
        );
    }

    /**
     * Log role/permission change.
     */
    public static function logRolePermissionChange(int $targetUserId, string $action, array $details = []): self
    {
        return static::logEvent(
            "role_permission_{$action}",
            'user',
            $targetUserId,
            $details,
            self::SEVERITY_HIGH,
            self::STATUS_SUCCESS
        );
    }

    /**
     * Log failed authentication attempts.
     */
    public static function logFailedAuth(string $type, array $details = []): self
    {
        return static::logEvent(
            "failed_auth_{$type}",
            null,
            null,
            $details,
            self::SEVERITY_HIGH,
            self::STATUS_FAILED
        );
    }

    /**
     * Log suspicious activity.
     */
    public static function logSuspiciousActivity(string $activity, array $details = []): self
    {
        return static::logEvent(
            'suspicious_activity',
            null,
            null,
            array_merge(['activity' => $activity], $details),
            self::SEVERITY_HIGH,
            self::STATUS_BLOCKED
        );
    }

    /**
     * Get logs by severity.
     */
    public static function getBySeverity(string $severity, int $limit = 100)
    {
        return static::where('severity', $severity)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent security events.
     */
    public static function getRecentEvents(int $hours = 24, int $limit = 100)
    {
        return static::where('created_at', '>=', now()->subHours($hours))
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get failed login attempts for IP.
     */
    public static function getFailedLoginsForIp(string $ip, int $minutes = 60): int
    {
        return static::where('action', 'login_attempt')
            ->where('status', self::STATUS_FAILED)
            ->where('ip_address', $ip)
            ->where('created_at', '>=', now()->subMinutes($minutes))
            ->count();
    }

    /**
     * Get user activity summary.
     */
    public static function getUserActivitySummary(int $userId, int $days = 30): array
    {
        $logs = static::where('user_id', $userId)
            ->where('created_at', '>=', now()->subDays($days))
            ->get();

        return [
            'total_actions' => $logs->count(),
            'login_count' => $logs->where('action', 'login_attempt')->where('status', self::STATUS_SUCCESS)->count(),
            'failed_logins' => $logs->where('action', 'login_attempt')->where('status', self::STATUS_FAILED)->count(),
            'permission_denials' => $logs->where('action', 'permission_denied')->count(),
            'data_modifications' => $logs->whereIn('action', ['data_create', 'data_update', 'data_delete'])->count(),
            'security_violations' => $logs->where('severity', self::SEVERITY_HIGH)->count(),
            'last_activity' => $logs->max('created_at'),
        ];
    }
}
