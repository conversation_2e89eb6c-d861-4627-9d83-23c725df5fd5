@extends('layouts.app')

@section('title', 'دفعة رقم ' . $payment->payment_number)

@push('styles')
<style>
.payment-header {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.payment-card {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    margin-bottom: 1.5rem;
}

.payment-card-header {
    background: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
}

.payment-card-body {
    padding: 1.5rem;
}

.payment-amount-display {
    text-align: center;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border-radius: 0.5rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

.amount-value {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.amount-label {
    font-size: 1.2rem;
    opacity: 0.9;
}

.payment-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.detail-section h6 {
    color: #28a745;
    font-weight: bold;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e3e6f0;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fc;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: #5a5c69;
}

.detail-value {
    color: #858796;
    font-weight: 600;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.status-cancelled {
    background: #e2e3e5;
    color: #6c757d;
}

.payment-method-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.875rem;
}

.method-cash {
    background: #d4edda;
    color: #155724;
}

.method-card {
    background: #cce5ff;
    color: #0066cc;
}

.method-bank_transfer {
    background: #fff3cd;
    color: #856404;
}

.method-check {
    background: #e2e3e5;
    color: #6c757d;
}

.method-mobile_payment {
    background: #f8d7da;
    color: #721c24;
}

.method-other {
    background: #e2e3e5;
    color: #6c757d;
}

.invoice-summary {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e3e6f0;
}

.summary-row:last-child {
    border-bottom: none;
    font-weight: bold;
    font-size: 1.1rem;
    color: #28a745;
}

.summary-label {
    font-weight: 600;
}

.summary-value {
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-action {
    padding: 0.75rem 1.5rem;
    border-radius: 0.35rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    text-decoration: none;
}

.btn-edit {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    color: #212529;
}

.btn-print {
    background: linear-gradient(45deg, #6c757d, #5a6268);
    color: white;
}

.btn-invoice {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.verification-info {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 1rem;
    border-radius: 0.35rem;
    margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
    .payment-header {
        padding: 1rem;
    }
    
    .payment-details {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .payment-card-body {
        padding: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn-action {
        width: 100%;
        justify-content: center;
    }
    
    .amount-value {
        font-size: 2rem;
    }
}

@media print {
    .payment-header,
    .action-buttons,
    .btn {
        display: none !important;
    }
    
    .payment-card {
        box-shadow: none;
        border: none;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="payment-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">دفعة رقم {{ $payment->payment_number }}</h1>
                <p class="mb-0 opacity-75">تفاصيل الدفعة الكاملة</p>
            </div>
            <div class="action-buttons">
                @if($payment->status === 'pending')
                    <a href="{{ route('payments.edit', $payment) }}" class="btn-action btn-edit">
                        <i class="fas fa-edit"></i>تعديل
                    </a>
                @endif
                
                <button onclick="window.print()" class="btn-action btn-print">
                    <i class="fas fa-print"></i>طباعة
                </button>
                
                <a href="{{ route('invoices.show', $payment->invoice) }}" class="btn-action btn-invoice">
                    <i class="fas fa-file-invoice"></i>عرض الفاتورة
                </a>
                
                <a href="{{ route('payments.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة
                </a>
            </div>
        </div>
    </div>

    <!-- Verification Info -->
    @if($payment->is_verified)
        <div class="verification-info">
            <i class="fas fa-check-circle me-2"></i>
            <strong>تم التحقق من الدفعة</strong> بواسطة {{ $payment->verifiedBy->name }} في {{ $payment->verified_at->format('Y-m-d H:i') }}
        </div>
    @endif

    <!-- Payment Amount Display -->
    <div class="payment-amount-display">
        <div class="amount-value">@arabicCurrency($payment->amount)</div>
        <div class="amount-label">مبلغ الدفعة</div>
    </div>

    <!-- Payment Details -->
    <div class="payment-card">
        <div class="payment-card-body">
            <div class="payment-details">
                <div class="detail-section">
                    <h6>تفاصيل الدفعة</h6>
                    <div class="detail-item">
                        <span class="detail-label">رقم الدفعة:</span>
                        <span class="detail-value">{{ $payment->payment_number }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تاريخ الدفع:</span>
                        <span class="detail-value">{{ $payment->payment_date->format('Y-m-d') }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">طريقة الدفع:</span>
                        <span class="detail-value">
                            <span class="payment-method-badge method-{{ $payment->payment_method }}">
                                <i class="{{ $payment->payment_method_icon }} me-1"></i>
                                {{ $payment->payment_method_display }}
                            </span>
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الحالة:</span>
                        <span class="detail-value">
                            <span class="status-badge status-{{ $payment->status }}">
                                {{ $payment->status_display }}
                            </span>
                        </span>
                    </div>
                    @if($payment->payment_reference)
                        <div class="detail-item">
                            <span class="detail-label">رقم المرجع:</span>
                            <span class="detail-value">{{ $payment->payment_reference }}</span>
                        </div>
                    @endif
                    @if($payment->receipt_number)
                        <div class="detail-item">
                            <span class="detail-label">رقم الإيصال:</span>
                            <span class="detail-value">{{ $payment->receipt_number }}</span>
                        </div>
                    @endif
                </div>

                <div class="detail-section">
                    <h6>بيانات العميل والفاتورة</h6>
                    <div class="detail-item">
                        <span class="detail-label">رقم الفاتورة:</span>
                        <span class="detail-value">
                            <a href="{{ route('invoices.show', $payment->invoice) }}" class="text-decoration-none">
                                {{ $payment->invoice->invoice_number }}
                            </a>
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">اسم العميل:</span>
                        <span class="detail-value">{{ $payment->customer->name }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">هاتف العميل:</span>
                        <span class="detail-value">
                            <a href="tel:{{ $payment->customer->phone_number }}" class="text-decoration-none">
                                {{ $payment->customer->phone_number }}
                            </a>
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تسجيل بواسطة:</span>
                        <span class="detail-value">{{ $payment->createdBy->name }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تاريخ التسجيل:</span>
                        <span class="detail-value">{{ $payment->created_at->format('Y-m-d H:i') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Method Specific Details -->
    @if($payment->payment_method === 'card' && $payment->card_last_four)
        <div class="payment-card">
            <div class="payment-card-header">
                <h5 class="mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    تفاصيل البطاقة
                </h5>
            </div>
            <div class="payment-card-body">
                <div class="detail-item">
                    <span class="detail-label">آخر 4 أرقام:</span>
                    <span class="detail-value">**** **** **** {{ $payment->card_last_four }}</span>
                </div>
            </div>
        </div>
    @endif

    @if($payment->payment_method === 'bank_transfer' && $payment->bank_name)
        <div class="payment-card">
            <div class="payment-card-header">
                <h5 class="mb-0">
                    <i class="fas fa-university me-2"></i>
                    تفاصيل التحويل البنكي
                </h5>
            </div>
            <div class="payment-card-body">
                <div class="detail-item">
                    <span class="detail-label">اسم البنك:</span>
                    <span class="detail-value">{{ $payment->bank_name }}</span>
                </div>
            </div>
        </div>
    @endif

    @if($payment->payment_method === 'check' && $payment->check_number)
        <div class="payment-card">
            <div class="payment-card-header">
                <h5 class="mb-0">
                    <i class="fas fa-money-check me-2"></i>
                    تفاصيل الشيك
                </h5>
            </div>
            <div class="payment-card-body">
                <div class="detail-item">
                    <span class="detail-label">رقم الشيك:</span>
                    <span class="detail-value">{{ $payment->check_number }}</span>
                </div>
            </div>
        </div>
    @endif

    <!-- Invoice Summary -->
    <div class="invoice-summary">
        <h6 class="text-muted mb-3">ملخص الفاتورة</h6>
        <div class="summary-row">
            <span class="summary-label">إجمالي الفاتورة:</span>
            <span class="summary-value">@arabicCurrency($payment->invoice->total_amount)</span>
        </div>
        <div class="summary-row">
            <span class="summary-label">إجمالي المدفوع:</span>
            <span class="summary-value">@arabicCurrency($payment->invoice->paid_amount)</span>
        </div>
        <div class="summary-row">
            <span class="summary-label">المتبقي:</span>
            <span class="summary-value">
                @if($payment->invoice->total_amount > $payment->invoice->paid_amount)
                    <span class="text-danger">@arabicCurrency($payment->invoice->total_amount - $payment->invoice->paid_amount)</span>
                @else
                    <span class="text-success">مدفوع بالكامل</span>
                @endif
            </span>
        </div>
    </div>

    <!-- Notes -->
    @if($payment->notes)
        <div class="payment-card">
            <div class="payment-card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sticky-note me-2"></i>
                    ملاحظات
                </h5>
            </div>
            <div class="payment-card-body">
                <p class="mb-0">{{ $payment->notes }}</p>
            </div>
        </div>
    @endif

    <!-- Refund Information -->
    @if($payment->refunded_amount > 0)
        <div class="payment-card">
            <div class="payment-card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-undo me-2"></i>
                    معلومات الاسترداد
                </h5>
            </div>
            <div class="payment-card-body">
                <div class="detail-item">
                    <span class="detail-label">المبلغ المسترد:</span>
                    <span class="detail-value text-warning">@arabicCurrency($payment->refunded_amount)</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">تاريخ الاسترداد:</span>
                    <span class="detail-value">{{ $payment->refunded_at?->format('Y-m-d H:i') }}</span>
                </div>
                @if($payment->refund_reason)
                    <div class="detail-item">
                        <span class="detail-label">سبب الاسترداد:</span>
                        <span class="detail-value">{{ $payment->refund_reason }}</span>
                    </div>
                @endif
            </div>
        </div>
    @endif
</div>
@endsection
