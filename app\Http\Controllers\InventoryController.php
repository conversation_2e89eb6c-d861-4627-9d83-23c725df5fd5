<?php

namespace App\Http\Controllers;

use App\Models\InventoryItem;
use App\Models\InventoryCategory;
use App\Models\InventoryMovement;
use App\Models\Brand;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;

class InventoryController extends Controller
{
    /**
     * Display inventory dashboard.
     */
    public function index(Request $request): View
    {
        $query = InventoryItem::with(['category', 'brand', 'primarySupplier']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Filter by category
        if ($request->has('category_id') && $request->category_id) {
            $category = InventoryCategory::find($request->category_id);
            if ($category) {
                $categoryIds = $category->getBranchIds();
                $query->whereIn('category_id', $categoryIds);
            }
        }

        // Filter by brand
        if ($request->has('brand_id') && $request->brand_id) {
            $query->where('brand_id', $request->brand_id);
        }

        // Filter by stock status
        if ($request->has('stock_status') && $request->stock_status) {
            switch ($request->stock_status) {
                case 'low_stock':
                    $query->lowStock();
                    break;
                case 'out_of_stock':
                    $query->outOfStock();
                    break;
                case 'overstocked':
                    $query->overstocked();
                    break;
                case 'expiring_soon':
                    $query->expiringSoon();
                    break;
                case 'expired':
                    $query->expired();
                    break;
            }
        }

        // Filter by supplier
        if ($request->has('supplier_id') && $request->supplier_id) {
            $query->where('primary_supplier_id', $request->supplier_id);
        }

        // Filter by active status
        if ($request->has('is_active') && $request->is_active !== '') {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $items = $query->orderBy('name')->paginate(20)->withQueryString();

        // Get filter options
        $categories = InventoryCategory::active()->ordered()->get();
        $brands = Brand::orderBy('name')->get();
        $suppliers = Supplier::active()->orderBy('name')->get();

        // Get statistics
        $stats = [
            'total_items' => InventoryItem::active()->count(),
            'low_stock_items' => InventoryItem::active()->lowStock()->count(),
            'out_of_stock_items' => InventoryItem::active()->outOfStock()->count(),
            'total_stock_value' => InventoryItem::active()->sum(\DB::raw('current_stock * cost_price')),
            'categories_count' => InventoryCategory::active()->count(),
            'suppliers_count' => Supplier::active()->count(),
        ];

        return view('inventory.index', compact(
            'items',
            'categories',
            'brands',
            'suppliers',
            'stats'
        ));
    }

    /**
     * Show the form for creating a new inventory item.
     */
    public function create(): View
    {
        $categories = InventoryCategory::active()->ordered()->get();
        $brands = Brand::orderBy('name')->get();
        $suppliers = Supplier::active()->orderBy('name')->get();

        return view('inventory.create', compact('categories', 'brands', 'suppliers'));
    }

    /**
     * Store a newly created inventory item.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'sku' => 'required|string|max:255|unique:inventory_items',
            'barcode' => 'nullable|string|max:255|unique:inventory_items',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:inventory_categories,id',
            'brand_id' => 'nullable|exists:brands,id',
            'model_compatibility' => 'nullable|string|max:255',
            'part_number' => 'nullable|string|max:255',
            'oem_number' => 'nullable|string|max:255',
            'current_stock' => 'required|integer|min:0',
            'minimum_stock' => 'required|integer|min:0',
            'maximum_stock' => 'required|integer|min:1',
            'reorder_quantity' => 'required|integer|min:1',
            'unit_of_measure' => 'required|string|max:50',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'wholesale_price' => 'nullable|numeric|min:0',
            'location' => 'nullable|string|max:255',
            'warehouse_section' => 'nullable|string|max:255',
            'storage_conditions' => 'nullable|string',
            'primary_supplier_id' => 'nullable|exists:suppliers,id',
            'lead_time_days' => 'required|integer|min:0',
            'condition' => 'required|in:new,refurbished,used',
            'warranty_months' => 'required|integer|min:0',
            'expiry_date' => 'nullable|date|after:today',
            'is_active' => 'boolean',
            'is_serialized' => 'boolean',
            'allow_backorder' => 'boolean',
            'track_expiry' => 'boolean',
            'notes' => 'nullable|string',
        ]);

        // Calculate markup percentage
        if ($validated['cost_price'] > 0) {
            $validated['markup_percentage'] = (($validated['selling_price'] - $validated['cost_price']) / $validated['cost_price']) * 100;
        }

        $validated['created_by'] = auth()->id() ?? 1;

        $item = InventoryItem::create($validated);

        // Create initial stock movement if stock > 0
        if ($validated['current_stock'] > 0) {
            $item->updateStock($validated['current_stock'], 'adjustment', [
                'notes' => 'Initial stock entry',
                'unit_cost' => $validated['cost_price'],
                'total_cost' => $validated['current_stock'] * $validated['cost_price'],
            ]);
        }

        return redirect()->route('inventory.show', $item)
                        ->with('success', __('app.inventory.item_created_successfully'));
    }

    /**
     * Display the specified inventory item.
     */
    public function show(InventoryItem $inventory): View
    {
        $inventory->load(['category', 'brand', 'primarySupplier', 'createdBy']);

        // Get recent movements
        $recentMovements = $inventory->movements()
            ->with('createdBy')
            ->orderBy('movement_date', 'desc')
            ->limit(10)
            ->get();

        // Get stock statistics
        $stockStats = [
            'total_value' => $inventory->total_stock_value,
            'potential_revenue' => $inventory->potential_revenue,
            'profit_margin' => $inventory->profit_margin,
            'days_until_expiry' => $inventory->days_until_expiry,
            'reorder_suggestion' => $inventory->reorder_suggestion,
        ];

        return view('inventory.show', compact('inventory', 'recentMovements', 'stockStats'));
    }

    /**
     * Show the form for editing the specified inventory item.
     */
    public function edit(InventoryItem $inventory): View
    {
        $categories = InventoryCategory::active()->ordered()->get();
        $brands = Brand::orderBy('name')->get();
        $suppliers = Supplier::active()->orderBy('name')->get();

        return view('inventory.edit', compact('inventory', 'categories', 'brands', 'suppliers'));
    }

    /**
     * Update the specified inventory item.
     */
    public function update(Request $request, InventoryItem $inventory): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'sku' => 'required|string|max:255|unique:inventory_items,sku,' . $inventory->id,
            'barcode' => 'nullable|string|max:255|unique:inventory_items,barcode,' . $inventory->id,
            'description' => 'nullable|string',
            'category_id' => 'required|exists:inventory_categories,id',
            'brand_id' => 'nullable|exists:brands,id',
            'model_compatibility' => 'nullable|string|max:255',
            'part_number' => 'nullable|string|max:255',
            'oem_number' => 'nullable|string|max:255',
            'minimum_stock' => 'required|integer|min:0',
            'maximum_stock' => 'required|integer|min:1',
            'reorder_quantity' => 'required|integer|min:1',
            'unit_of_measure' => 'required|string|max:50',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'wholesale_price' => 'nullable|numeric|min:0',
            'location' => 'nullable|string|max:255',
            'warehouse_section' => 'nullable|string|max:255',
            'storage_conditions' => 'nullable|string',
            'primary_supplier_id' => 'nullable|exists:suppliers,id',
            'lead_time_days' => 'required|integer|min:0',
            'condition' => 'required|in:new,refurbished,used',
            'warranty_months' => 'required|integer|min:0',
            'expiry_date' => 'nullable|date|after:today',
            'is_active' => 'boolean',
            'is_serialized' => 'boolean',
            'allow_backorder' => 'boolean',
            'track_expiry' => 'boolean',
            'notes' => 'nullable|string',
        ]);

        // Calculate markup percentage
        if ($validated['cost_price'] > 0) {
            $validated['markup_percentage'] = (($validated['selling_price'] - $validated['cost_price']) / $validated['cost_price']) * 100;
        }

        $inventory->update($validated);

        return redirect()->route('inventory.show', $inventory)
                        ->with('success', __('app.inventory.item_updated_successfully'));
    }

    /**
     * Remove the specified inventory item.
     */
    public function destroy(InventoryItem $inventory): RedirectResponse
    {
        // Check if item has movements
        if ($inventory->movements()->exists()) {
            return redirect()->back()
                           ->with('error', __('app.inventory.cannot_delete_item_with_movements'));
        }

        $inventory->delete();

        return redirect()->route('inventory.index')
                        ->with('success', __('app.inventory.item_deleted_successfully'));
    }

    /**
     * Show stock adjustment form.
     */
    public function adjustStock(InventoryItem $inventory): View
    {
        return view('inventory.adjust-stock', compact('inventory'));
    }

    /**
     * Process stock adjustment.
     */
    public function processStockAdjustment(Request $request, InventoryItem $inventory): RedirectResponse
    {
        $validated = $request->validate([
            'adjustment_type' => 'required|in:increase,decrease,set',
            'quantity' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string',
            'unit_cost' => 'nullable|numeric|min:0',
        ]);

        $oldStock = $inventory->current_stock;

        switch ($validated['adjustment_type']) {
            case 'increase':
                $adjustmentQuantity = $validated['quantity'];
                break;
            case 'decrease':
                $adjustmentQuantity = -$validated['quantity'];
                break;
            case 'set':
                $adjustmentQuantity = $validated['quantity'] - $oldStock;
                break;
        }

        // Create movement record
        $inventory->updateStock($adjustmentQuantity, 'adjustment', [
            'notes' => $validated['reason'] . ($validated['notes'] ? ' - ' . $validated['notes'] : ''),
            'unit_cost' => $validated['unit_cost'],
            'total_cost' => $validated['unit_cost'] ? abs($adjustmentQuantity) * $validated['unit_cost'] : null,
        ]);

        return redirect()->route('inventory.show', $inventory)
                        ->with('success', __('app.inventory.stock_adjusted_successfully'));
    }

    /**
     * Get low stock alerts.
     */
    public function lowStockAlerts(): JsonResponse
    {
        $lowStockItems = InventoryItem::with(['category', 'brand'])
            ->active()
            ->lowStock()
            ->orderBy('current_stock')
            ->get();

        return response()->json([
            'count' => $lowStockItems->count(),
            'items' => $lowStockItems->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'sku' => $item->sku,
                    'current_stock' => $item->current_stock,
                    'minimum_stock' => $item->minimum_stock,
                    'category' => $item->category->name,
                    'brand' => $item->brand?->name,
                    'reorder_suggestion' => $item->reorder_suggestion,
                ];
            })
        ]);
    }

    /**
     * Get inventory statistics.
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_items' => InventoryItem::active()->count(),
            'total_categories' => InventoryCategory::active()->count(),
            'total_suppliers' => Supplier::active()->count(),
            'stock_status' => [
                'normal' => InventoryItem::active()
                    ->whereRaw('current_stock > minimum_stock')
                    ->whereRaw('current_stock <= maximum_stock')
                    ->count(),
                'low_stock' => InventoryItem::active()->lowStock()->count(),
                'out_of_stock' => InventoryItem::active()->outOfStock()->count(),
                'overstocked' => InventoryItem::active()->overstocked()->count(),
            ],
            'value_metrics' => [
                'total_stock_value' => InventoryItem::active()->sum(\DB::raw('current_stock * cost_price')),
                'potential_revenue' => InventoryItem::active()->sum(\DB::raw('current_stock * selling_price')),
                'average_markup' => InventoryItem::active()->avg('markup_percentage'),
            ],
            'movement_summary' => [
                'today' => InventoryMovement::whereDate('movement_date', today())->count(),
                'this_week' => InventoryMovement::whereBetween('movement_date', [now()->startOfWeek(), now()->endOfWeek()])->count(),
                'this_month' => InventoryMovement::whereMonth('movement_date', now()->month)->count(),
            ]
        ];

        return response()->json($stats);
    }

    /**
     * Show inventory movements history.
     */
    public function movements(Request $request, InventoryItem $inventory): View
    {
        $query = InventoryMovement::where('inventory_item_id', $inventory->id)
            ->with(['createdBy'])
            ->orderBy('movement_date', 'desc');

        // Apply filters
        if ($request->filled('date_from')) {
            $query->whereDate('movement_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('movement_date', '<=', $request->date_to);
        }

        if ($request->filled('movement_type')) {
            $query->where('type', $request->movement_type);
        }

        if ($request->filled('direction')) {
            // For direction filter, we need to check quantity sign
            if ($request->direction === 'in') {
                $query->where('quantity', '>', 0);
            } elseif ($request->direction === 'out') {
                $query->where('quantity', '<', 0);
            }
        }

        $movements = $query->paginate(20);

        // Calculate statistics
        $movementsIn = InventoryMovement::where('inventory_item_id', $inventory->id)
            ->where('quantity', '>', 0)
            ->count();

        $movementsOut = InventoryMovement::where('inventory_item_id', $inventory->id)
            ->where('quantity', '<', 0)
            ->count();

        return view('inventory.movements', compact('inventory', 'movements', 'movementsIn', 'movementsOut'));
    }
}
