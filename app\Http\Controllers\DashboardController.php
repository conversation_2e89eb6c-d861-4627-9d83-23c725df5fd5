<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\RepairTicket;
use App\Models\RepairStatus;
use Illuminate\View\View;

class DashboardController extends Controller
{
    /**
     * Display the dashboard.
     */
    public function index(): View
    {
        // Get basic statistics
        $stats = [
            'total_customers' => Customer::count(),
            'total_tickets' => RepairTicket::count(),
            'pending_tickets' => RepairTicket::whereHas('repairStatus', function($query) {
                $query->where('is_final', false);
            })->count(),
            'completed_today' => RepairTicket::whereDate('completed_date', today())->count(),
        ];

        // Get recent tickets
        $recentTickets = RepairTicket::with(['customer', 'brand', 'repairStatus'])
                                   ->orderBy('created_at', 'desc')
                                   ->limit(10)
                                   ->get();

        // Get tickets by status
        $ticketsByStatus = RepairStatus::withCount('repairTickets')
                                     ->active()
                                     ->ordered()
                                     ->get();

        return view('dashboard', compact('stats', 'recentTickets', 'ticketsByStatus'));
    }
}
