<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\Permission;
use App\Models\User;
use App\Models\SecurityLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RolePermissionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:users.roles')->except(['index', 'getUserPermissions']);
        $this->middleware('permission:users.view')->only(['index', 'getUserPermissions']);
    }

    /**
     * Display roles and permissions management.
     */
    public function index(): View
    {
        $roles = Role::with('permissions')
            ->where('is_active', true)
            ->orderBy('level', 'desc')
            ->get();

        $permissions = Permission::where('is_active', true)
            ->orderBy('category')
            ->orderBy('name')
            ->get()
            ->groupBy('category');

        $users = User::with('userRole')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.roles-permissions', compact('roles', 'permissions', 'users'));
    }

    /**
     * Create a new role.
     */
    public function createRole(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:roles,slug',
            'description' => 'nullable|string|max:1000',
            'level' => 'required|integer|min:1|max:99',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,slug'
        ]);

        try {
            DB::beginTransaction();

            $role = Role::create([
                'name' => $validated['name'],
                'slug' => $validated['slug'],
                'description' => $validated['description'],
                'level' => $validated['level'],
                'color' => $validated['color'],
                'is_active' => true,
            ]);

            if (!empty($validated['permissions'])) {
                $role->syncPermissions($validated['permissions']);
            }

            SecurityLog::logRolePermissionChange(0, 'role_created', [
                'role_name' => $role->name,
                'role_slug' => $role->slug,
                'permissions_count' => count($validated['permissions'] ?? [])
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Role created successfully',
                'role' => $role->load('permissions')
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to create role: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update an existing role.
     */
    public function updateRole(Request $request, Role $role): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'level' => 'required|integer|min:1|max:99',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,slug'
        ]);

        // Prevent editing admin role by non-admins
        if ($role->slug === 'admin' && !Auth::user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot modify the admin role'
            ], 403);
        }

        try {
            DB::beginTransaction();

            $oldPermissions = $role->permissions->pluck('slug')->toArray();

            $role->update([
                'name' => $validated['name'],
                'description' => $validated['description'],
                'level' => $validated['level'],
                'color' => $validated['color'],
            ]);

            $role->syncPermissions($validated['permissions'] ?? []);

            SecurityLog::logRolePermissionChange(0, 'role_updated', [
                'role_name' => $role->name,
                'role_slug' => $role->slug,
                'old_permissions' => $oldPermissions,
                'new_permissions' => $validated['permissions'] ?? [],
                'permissions_added' => array_diff($validated['permissions'] ?? [], $oldPermissions),
                'permissions_removed' => array_diff($oldPermissions, $validated['permissions'] ?? [])
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Role updated successfully',
                'role' => $role->load('permissions')
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to update role: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a role.
     */
    public function deleteRole(Role $role): JsonResponse
    {
        // Prevent deleting admin role
        if ($role->slug === 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete the admin role'
            ], 403);
        }

        // Check if role is in use
        $usersCount = User::where('role', $role->slug)->count();
        if ($usersCount > 0) {
            return response()->json([
                'success' => false,
                'message' => "Cannot delete role. It is assigned to {$usersCount} user(s)"
            ], 400);
        }

        try {
            $role->update(['is_active' => false]);

            SecurityLog::logRolePermissionChange(0, 'role_deleted', [
                'role_name' => $role->name,
                'role_slug' => $role->slug
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Role deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete role: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign role to user.
     */
    public function assignRole(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'role_slug' => 'required|exists:roles,slug'
        ]);

        $user = User::findOrFail($validated['user_id']);
        $role = Role::where('slug', $validated['role_slug'])->firstOrFail();

        // Check if current user can manage target user
        if (!Auth::user()->canManageUser($user)) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot manage this user'
            ], 403);
        }

        // Check role hierarchy
        $currentUserRole = Auth::user()->userRole;
        if ($currentUserRole && !$currentUserRole->isHigherThan($role)) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot assign a role equal or higher than your own'
            ], 403);
        }

        try {
            $oldRole = $user->role;
            $user->update(['role' => $validated['role_slug']]);

            SecurityLog::logRolePermissionChange($user->id, 'role_assigned', [
                'user_name' => $user->name,
                'user_email' => $user->email,
                'old_role' => $oldRole,
                'new_role' => $validated['role_slug'],
                'assigned_by' => Auth::user()->name
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Role assigned successfully',
                'user' => $user->load('userRole')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign role: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Grant direct permission to user.
     */
    public function grantPermission(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'permission_slug' => 'required|exists:permissions,slug',
            'reason' => 'nullable|string|max:500',
            'expires_at' => 'nullable|date|after:now'
        ]);

        $user = User::findOrFail($validated['user_id']);
        $permission = Permission::where('slug', $validated['permission_slug'])->firstOrFail();

        // Check if current user can manage target user
        if (!Auth::user()->canManageUser($user)) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot manage this user'
            ], 403);
        }

        try {
            $user->givePermission(
                $validated['permission_slug'],
                Auth::user()->name,
                $validated['reason'] ?? null,
                isset($validated['expires_at']) ? new \DateTime($validated['expires_at']) : null
            );

            SecurityLog::logRolePermissionChange($user->id, 'permission_granted', [
                'user_name' => $user->name,
                'user_email' => $user->email,
                'permission' => $validated['permission_slug'],
                'reason' => $validated['reason'] ?? null,
                'expires_at' => $validated['expires_at'] ?? null,
                'granted_by' => Auth::user()->name
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Permission granted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to grant permission: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Revoke direct permission from user.
     */
    public function revokePermission(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'permission_slug' => 'required|exists:permissions,slug'
        ]);

        $user = User::findOrFail($validated['user_id']);

        // Check if current user can manage target user
        if (!Auth::user()->canManageUser($user)) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot manage this user'
            ], 403);
        }

        try {
            $user->revokePermission($validated['permission_slug']);

            SecurityLog::logRolePermissionChange($user->id, 'permission_revoked', [
                'user_name' => $user->name,
                'user_email' => $user->email,
                'permission' => $validated['permission_slug'],
                'revoked_by' => Auth::user()->name
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Permission revoked successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to revoke permission: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user permissions (role + direct).
     */
    public function getUserPermissions(User $user): JsonResponse
    {
        $rolePermissions = [];
        $directPermissions = [];

        if ($user->userRole) {
            $rolePermissions = $user->userRole->permissions()
                ->where('is_active', true)
                ->get()
                ->map(function ($permission) {
                    return [
                        'slug' => $permission->slug,
                        'name' => $permission->name,
                        'category' => $permission->category,
                        'source' => 'role'
                    ];
                });
        }

        $directPermissions = $user->directPermissions()
            ->where('granted', true)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->get()
            ->map(function ($permission) {
                return [
                    'slug' => $permission->slug,
                    'name' => $permission->name,
                    'category' => $permission->category,
                    'source' => 'direct',
                    'expires_at' => $permission->pivot->expires_at,
                    'granted_by' => $permission->pivot->granted_by,
                    'reason' => $permission->pivot->reason
                ];
            });

        return response()->json([
            'success' => true,
            'role_permissions' => $rolePermissions,
            'direct_permissions' => $directPermissions,
            'all_permissions' => $user->getAllPermissions()
        ]);
    }
}
