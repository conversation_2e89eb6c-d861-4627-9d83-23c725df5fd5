<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="bi bi-plus-circle text-primary"></i> إنشاء قالب واتساب جديد</h2>
                    <p class="text-muted">إنشاء قالب رسالة جديد للواتساب بيزنس</p>
                </div>
                <a href="<?php echo e(route('whatsapp.templates.index')); ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <form method="POST" action="<?php echo e(route('whatsapp.templates.store')); ?>" id="templateForm">
        <?php echo csrf_field(); ?>
        <div class="row">
            <!-- Form Fields -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-info-circle"></i> معلومات القالب الأساسية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم القالب (تقني) <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="name" name="name" value="<?php echo e(old('name')); ?>" required>
                                    <div class="form-text">اسم فريد للقالب (بالإنجليزية، بدون مسافات)</div>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">الفئة <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            id="category_id" name="category_id" required>
                                        <option value="">اختر الفئة</option>
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($category->id); ?>"
                                                    <?php echo e(old('category_id') == $category->id ? 'selected' : ''); ?>>
                                                <?php echo e($category->name_ar); ?> (<?php echo e($category->whatsapp_category); ?>)
                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="display_name" class="form-label">الاسم المعروض (English) <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['display_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="display_name" name="display_name" value="<?php echo e(old('display_name')); ?>" required>
                                    <?php $__errorArgs = ['display_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="display_name_ar" class="form-label">الاسم المعروض (عربي) <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['display_name_ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="display_name_ar" name="display_name_ar" value="<?php echo e(old('display_name_ar')); ?>" required>
                                    <?php $__errorArgs = ['display_name_ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="description" class="form-label">الوصف (English)</label>
                                    <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                              id="description" name="description" rows="3"><?php echo e(old('description')); ?></textarea>
                                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="description_ar" class="form-label">الوصف (عربي)</label>
                                    <textarea class="form-control <?php $__errorArgs = ['description_ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                              id="description_ar" name="description_ar" rows="3"><?php echo e(old('description_ar')); ?></textarea>
                                    <?php $__errorArgs = ['description_ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="language" class="form-label">اللغة <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['language'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            id="language" name="language" required>
                                        <option value="ar" <?php echo e(old('language', 'ar') === 'ar' ? 'selected' : ''); ?>>عربي</option>
                                        <option value="en" <?php echo e(old('language') === 'en' ? 'selected' : ''); ?>>English</option>
                                    </select>
                                    <?php $__errorArgs = ['language'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">خيارات إضافية</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="has_media" name="has_media"
                                               value="1" <?php echo e(old('has_media') ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="has_media">
                                            يحتوي على وسائط
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requires_opt_in" name="requires_opt_in"
                                               value="1" <?php echo e(old('requires_opt_in') ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="requires_opt_in">
                                            يتطلب موافقة العميل
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="media_type_section" class="mb-3" style="display: none;">
                            <label for="media_type" class="form-label">نوع الوسائط</label>
                            <select class="form-select" id="media_type" name="media_type">
                                <option value="">اختر نوع الوسائط</option>
                                <option value="image" <?php echo e(old('media_type') === 'image' ? 'selected' : ''); ?>>صورة</option>
                                <option value="document" <?php echo e(old('media_type') === 'document' ? 'selected' : ''); ?>>مستند</option>
                                <option value="video" <?php echo e(old('media_type') === 'video' ? 'selected' : ''); ?>>فيديو</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Template Content -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="bi bi-chat-square-text"></i> محتوى القالب</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="header_text" class="form-label">نص الرأس (اختياري)</label>
                            <input type="text" class="form-control <?php $__errorArgs = ['header_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="header_text" name="header_text" value="<?php echo e(old('header_text')); ?>"
                                   maxlength="60" placeholder="مثال: 🎉 تحديث مهم">
                            <div class="form-text">الحد الأقصى 60 حرف</div>
                            <?php $__errorArgs = ['header_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="body_text" class="form-label">نص الرسالة الرئيسي <span class="text-danger">*</span></label>
                            <textarea class="form-control <?php $__errorArgs = ['body_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      id="body_text" name="body_text" rows="8" maxlength="1024" required
                                      placeholder="مثال: عزيزي {{customer_name}}، تم تحديث حالة جهازك..."><?php echo e(old('body_text')); ?></textarea>
                            <div class="form-text">
                                الحد الأقصى 1024 حرف. استخدم {{variable_name}} للمتغيرات
                                <span id="body_char_count" class="float-end">0/1024</span>
                            </div>
                            <?php $__errorArgs = ['body_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="footer_text" class="form-label">نص التذييل (اختياري)</label>
                            <input type="text" class="form-control <?php $__errorArgs = ['footer_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="footer_text" name="footer_text" value="<?php echo e(old('footer_text')); ?>"
                                   maxlength="60" placeholder="مثال: ورشة إصلاح NJ | +966501234567">
                            <div class="form-text">الحد الأقصى 60 حرف</div>
                            <?php $__errorArgs = ['footer_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="bi bi-ui-radios"></i> أزرار التفاعل (اختياري)</h5>
                    </div>
                    <div class="card-body">
                        <div id="buttons-container">
                            <!-- Buttons will be added dynamically -->
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addButton()">
                            <i class="bi bi-plus"></i> إضافة زر
                        </button>
                        <small class="text-muted d-block mt-2">الحد الأقصى 3 أزرار، كل زر 25 حرف كحد أقصى</small>
                    </div>
                </div>

                <!-- Variables -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="bi bi-code-square"></i> المتغيرات</h5>
                    </div>
                    <div class="card-body">
                        <div id="variables-container">
                            <!-- Variables will be detected automatically -->
                        </div>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="detectVariables()">
                            <i class="bi bi-search"></i> اكتشاف المتغيرات تلقائياً
                        </button>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="col-lg-4">
                <div class="card sticky-top">
                    <div class="card-header">
                        <h5><i class="bi bi-eye"></i> معاينة القالب</h5>
                    </div>
                    <div class="card-body">
                        <div id="template-preview" class="border rounded p-3 bg-light">
                            <div class="text-center text-muted">
                                <i class="bi bi-chat-square-text display-4"></i>
                                <p>املأ محتوى القالب لرؤية المعاينة</p>
                            </div>
                        </div>

                        <div class="mt-3">
                            <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="updatePreview()">
                                <i class="bi bi-arrow-clockwise"></i> تحديث المعاينة
                            </button>
                        </div>

                        <hr>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> حفظ القالب
                            </button>
                            <a href="<?php echo e(route('whatsapp.templates.index')); ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-x"></i> إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let buttonCount = 0;
let variableCount = 0;

// Character counter for body text
document.getElementById('body_text').addEventListener('input', function() {
    const count = this.value.length;
    document.getElementById('body_char_count').textContent = count + '/1024';

    if (count > 1024) {
        this.classList.add('is-invalid');
    } else {
        this.classList.remove('is-invalid');
    }
});

// Show/hide media type section
document.getElementById('has_media').addEventListener('change', function() {
    const mediaSection = document.getElementById('media_type_section');
    if (this.checked) {
        mediaSection.style.display = 'block';
    } else {
        mediaSection.style.display = 'none';
        document.getElementById('media_type').value = '';
    }
});

// Add button function
function addButton() {
    if (buttonCount >= 3) {
        alert('الحد الأقصى 3 أزرار');
        return;
    }

    buttonCount++;
    const container = document.getElementById('buttons-container');
    const buttonDiv = document.createElement('div');
    buttonDiv.className = 'mb-3 border rounded p-3';
    buttonDiv.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-2">
            <strong>زر ${buttonCount}</strong>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeButton(this)">
                <i class="bi bi-trash"></i>
            </button>
        </div>
        <div class="row">
            <div class="col-md-6">
                <label class="form-label">نوع الزر</label>
                <select class="form-select" name="buttons[${buttonCount-1}][type]" required>
                    <option value="QUICK_REPLY">رد سريع</option>
                    <option value="PHONE_NUMBER">رقم هاتف</option>
                    <option value="URL">رابط</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">نص الزر</label>
                <input type="text" class="form-control" name="buttons[${buttonCount-1}][text]"
                       maxlength="25" required placeholder="مثال: تواصل معنا">
            </div>
        </div>
    `;
    container.appendChild(buttonDiv);
}

// Remove button function
function removeButton(btn) {
    btn.closest('.mb-3').remove();
    buttonCount--;
}

// Detect variables function
function detectVariables() {
    const headerText = document.getElementById('header_text').value;
    const bodyText = document.getElementById('body_text').value;
    const footerText = document.getElementById('footer_text').value;

    const allText = headerText + ' ' + bodyText + ' ' + footerText;
    const variables = [...new Set(allText.match(/\{\{(\w+)\}\}/g))];

    const container = document.getElementById('variables-container');
    container.innerHTML = '';

    if (variables.length === 0) {
        container.innerHTML = '<p class="text-muted">لم يتم العثور على متغيرات</p>';
        return;
    }

    variables.forEach((variable, index) => {
        const varName = variable.replace(/[{}]/g, '');
        const varDiv = document.createElement('div');
        varDiv.className = 'mb-3 border rounded p-3';
        varDiv.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">اسم المتغير</label>
                    <input type="text" class="form-control" name="variables[${varName}]"
                           value="${varName}" readonly>
                </div>
                <div class="col-md-6">
                    <label class="form-label">قيمة تجريبية</label>
                    <input type="text" class="form-control" name="sample_values[${varName}]"
                           placeholder="مثال: أحمد محمد">
                </div>
            </div>
        `;
        container.appendChild(varDiv);
    });
}

// Update preview function
function updatePreview() {
    const headerText = document.getElementById('header_text').value;
    const bodyText = document.getElementById('body_text').value;
    const footerText = document.getElementById('footer_text').value;

    let preview = '<div class="whatsapp-message">';

    if (headerText) {
        preview += `<div class="fw-bold text-primary mb-2">${headerText}</div>`;
    }

    if (bodyText) {
        preview += `<div class="mb-2">${bodyText.replace(/\n/g, '<br>')}</div>`;
    }

    if (footerText) {
        preview += `<div class="text-muted small">${footerText}</div>`;
    }

    // Add buttons preview
    const buttons = document.querySelectorAll('input[name*="[text]"]');
    if (buttons.length > 0) {
        preview += '<div class="mt-3">';
        buttons.forEach(button => {
            if (button.value) {
                preview += `<button class="btn btn-outline-primary btn-sm me-2 mb-1">${button.value}</button>`;
            }
        });
        preview += '</div>';
    }

    preview += '</div>';

    if (!headerText && !bodyText && !footerText) {
        preview = `
            <div class="text-center text-muted">
                <i class="bi bi-chat-square-text display-4"></i>
                <p>املأ محتوى القالب لرؤية المعاينة</p>
            </div>
        `;
    }

    document.getElementById('template-preview').innerHTML = preview;
}

// Auto-update preview when typing
document.getElementById('header_text').addEventListener('input', updatePreview);
document.getElementById('body_text').addEventListener('input', updatePreview);
document.getElementById('footer_text').addEventListener('input', updatePreview);

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Check if has_media is checked on page load
    if (document.getElementById('has_media').checked) {
        document.getElementById('media_type_section').style.display = 'block';
    }

    updatePreview();
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\nj\resources\views/whatsapp/templates/create.blade.php ENDPATH**/ ?>