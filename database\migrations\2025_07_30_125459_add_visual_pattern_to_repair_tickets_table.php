<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('repair_tickets', function (Blueprint $table) {
            $table->text('visual_pattern')->nullable()->after('security_pattern')
                  ->comment('Encrypted visual pattern sequence (e.g., 1-2-3-6-9)');
            $table->enum('pattern_type', ['none', 'text', 'visual', 'both'])
                  ->default('none')->after('visual_pattern')
                  ->comment('Type of security pattern used');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('repair_tickets', function (Blueprint $table) {
            $table->dropColumn(['visual_pattern', 'pattern_type']);
        });
    }
};
