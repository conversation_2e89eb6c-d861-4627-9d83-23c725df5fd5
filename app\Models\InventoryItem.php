<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class InventoryItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'sku',
        'barcode',
        'description',
        'category_id',
        'brand_id',
        'model_compatibility',
        'part_number',
        'oem_number',
        'current_stock',
        'minimum_stock',
        'maximum_stock',
        'reorder_quantity',
        'unit_of_measure',
        'cost_price',
        'selling_price',
        'markup_percentage',
        'wholesale_price',
        'location',
        'warehouse_section',
        'storage_conditions',
        'primary_supplier_id',
        'alternative_suppliers',
        'lead_time_days',
        'condition',
        'warranty_months',
        'expiry_date',
        'is_active',
        'is_serialized',
        'allow_backorder',
        'track_expiry',
        'specifications',
        'images',
        'notes',
        'last_counted_at',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'current_stock' => 'integer',
        'minimum_stock' => 'integer',
        'maximum_stock' => 'integer',
        'reorder_quantity' => 'integer',
        'cost_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'markup_percentage' => 'decimal:2',
        'wholesale_price' => 'decimal:2',
        'alternative_suppliers' => 'array',
        'lead_time_days' => 'integer',
        'warranty_months' => 'integer',
        'expiry_date' => 'date',
        'is_active' => 'boolean',
        'is_serialized' => 'boolean',
        'allow_backorder' => 'boolean',
        'track_expiry' => 'boolean',
        'specifications' => 'array',
        'images' => 'array',
        'last_counted_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the category that owns the inventory item.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(InventoryCategory::class);
    }

    /**
     * Get the brand that owns the inventory item.
     */
    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * Get the primary supplier.
     */
    public function primarySupplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class, 'primary_supplier_id');
    }

    /**
     * Get the user who created this item.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get inventory movements for this item.
     */
    public function movements(): HasMany
    {
        return $this->hasMany(InventoryMovement::class);
    }

    /**
     * Get purchase order items for this inventory item.
     */
    public function purchaseOrderItems(): HasMany
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    /**
     * Scope for active items.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for low stock items.
     */
    public function scopeLowStock($query)
    {
        return $query->whereRaw('current_stock <= minimum_stock');
    }

    /**
     * Scope for out of stock items.
     */
    public function scopeOutOfStock($query)
    {
        return $query->where('current_stock', 0);
    }

    /**
     * Scope for overstocked items.
     */
    public function scopeOverstocked($query)
    {
        return $query->whereRaw('current_stock > maximum_stock');
    }

    /**
     * Scope for items expiring soon.
     */
    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->where('track_expiry', true)
                    ->whereNotNull('expiry_date')
                    ->where('expiry_date', '<=', now()->addDays($days));
    }

    /**
     * Scope for expired items.
     */
    public function scopeExpired($query)
    {
        return $query->where('track_expiry', true)
                    ->whereNotNull('expiry_date')
                    ->where('expiry_date', '<', now());
    }

    /**
     * Search items by name, SKU, barcode, or description.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('sku', 'like', "%{$search}%")
              ->orWhere('barcode', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('part_number', 'like', "%{$search}%")
              ->orWhere('oem_number', 'like', "%{$search}%");
        });
    }

    /**
     * Check if item is low stock.
     */
    public function isLowStock(): bool
    {
        return $this->current_stock <= $this->minimum_stock;
    }

    /**
     * Check if item is out of stock.
     */
    public function isOutOfStock(): bool
    {
        return $this->current_stock <= 0;
    }

    /**
     * Check if item is overstocked.
     */
    public function isOverstocked(): bool
    {
        return $this->current_stock > $this->maximum_stock;
    }

    /**
     * Check if item is expiring soon.
     */
    public function isExpiringSoon($days = 30): bool
    {
        if (!$this->track_expiry || !$this->expiry_date) {
            return false;
        }
        
        return $this->expiry_date <= now()->addDays($days);
    }

    /**
     * Check if item is expired.
     */
    public function isExpired(): bool
    {
        if (!$this->track_expiry || !$this->expiry_date) {
            return false;
        }
        
        return $this->expiry_date < now();
    }

    /**
     * Get stock status.
     */
    public function getStockStatusAttribute(): string
    {
        if ($this->isExpired()) {
            return 'expired';
        } elseif ($this->isExpiringSoon()) {
            return 'expiring_soon';
        } elseif ($this->isOutOfStock()) {
            return 'out_of_stock';
        } elseif ($this->isLowStock()) {
            return 'low_stock';
        } elseif ($this->isOverstocked()) {
            return 'overstocked';
        } else {
            return 'normal';
        }
    }

    /**
     * Get stock status badge class.
     */
    public function getStockStatusBadgeClassAttribute(): string
    {
        return match($this->stock_status) {
            'expired' => 'bg-dark',
            'expiring_soon' => 'bg-warning',
            'out_of_stock' => 'bg-danger',
            'low_stock' => 'bg-warning',
            'overstocked' => 'bg-info',
            'normal' => 'bg-success',
            default => 'bg-secondary'
        };
    }

    /**
     * Get stock status display name.
     */
    public function getStockStatusDisplayAttribute(): string
    {
        return match($this->stock_status) {
            'expired' => __('app.inventory.status.expired'),
            'expiring_soon' => __('app.inventory.status.expiring_soon'),
            'out_of_stock' => __('app.inventory.status.out_of_stock'),
            'low_stock' => __('app.inventory.status.low_stock'),
            'overstocked' => __('app.inventory.status.overstocked'),
            'normal' => __('app.inventory.status.normal'),
            default => __('app.inventory.status.unknown')
        };
    }

    /**
     * Get total stock value.
     */
    public function getTotalStockValueAttribute(): float
    {
        return $this->current_stock * $this->cost_price;
    }

    /**
     * Get potential revenue.
     */
    public function getPotentialRevenueAttribute(): float
    {
        return $this->current_stock * $this->selling_price;
    }

    /**
     * Get profit margin.
     */
    public function getProfitMarginAttribute(): float
    {
        if ($this->cost_price <= 0) {
            return 0;
        }
        
        return (($this->selling_price - $this->cost_price) / $this->cost_price) * 100;
    }

    /**
     * Get days until expiry.
     */
    public function getDaysUntilExpiryAttribute(): ?int
    {
        if (!$this->track_expiry || !$this->expiry_date) {
            return null;
        }
        
        return now()->diffInDays($this->expiry_date, false);
    }

    /**
     * Get reorder suggestion.
     */
    public function getReorderSuggestionAttribute(): array
    {
        $shouldReorder = $this->isLowStock() && $this->is_active;
        $suggestedQuantity = $shouldReorder ? $this->reorder_quantity : 0;
        
        return [
            'should_reorder' => $shouldReorder,
            'suggested_quantity' => $suggestedQuantity,
            'estimated_cost' => $suggestedQuantity * $this->cost_price,
            'lead_time_days' => $this->lead_time_days,
            'expected_delivery' => now()->addDays($this->lead_time_days)->format('Y-m-d'),
        ];
    }

    /**
     * Update stock level.
     */
    public function updateStock(int $quantity, string $type, array $additionalData = []): InventoryMovement
    {
        $stockBefore = $this->current_stock;
        $this->current_stock += $quantity;
        $this->save();

        return $this->movements()->create(array_merge([
            'type' => $type,
            'quantity' => $quantity,
            'stock_before' => $stockBefore,
            'stock_after' => $this->current_stock,
            'movement_date' => now(),
            'created_by' => auth()->id() ?? 1,
        ], $additionalData));
    }

    /**
     * Get movement history.
     */
    public function getMovementHistory($limit = 10)
    {
        return $this->movements()
            ->with('createdBy')
            ->orderBy('movement_date', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Calculate average cost.
     */
    public function calculateAverageCost(): float
    {
        $purchaseMovements = $this->movements()
            ->where('type', 'purchase')
            ->whereNotNull('unit_cost')
            ->get();

        if ($purchaseMovements->isEmpty()) {
            return $this->cost_price;
        }

        $totalCost = $purchaseMovements->sum('total_cost');
        $totalQuantity = $purchaseMovements->sum('quantity');

        return $totalQuantity > 0 ? $totalCost / $totalQuantity : $this->cost_price;
    }
}
