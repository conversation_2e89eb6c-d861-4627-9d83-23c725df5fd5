/**
 * Navigation Enhancement Script for NJ Repair Shop
 * Handles responsive navigation, active states, and user interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
});

function initializeNavigation() {
    // Initialize all navigation features
    handleActiveStates();
    handleDropdownInteractions();
    handleMobileNavigation();
    handleNotificationBadges();
    handleNavigationLoading();
    handleKeyboardNavigation();
    
    console.log('Navigation system initialized');
}

/**
 * Handle active navigation states
 */
function handleActiveStates() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.startsWith(href) && href !== '/') {
            link.classList.add('active');
            
            // Also mark parent dropdown as active
            const dropdown = link.closest('.dropdown');
            if (dropdown) {
                const dropdownToggle = dropdown.querySelector('.dropdown-toggle');
                if (dropdownToggle) {
                    dropdownToggle.classList.add('active');
                }
            }
        }
    });
}

/**
 * Enhanced dropdown interactions
 */
function handleDropdownInteractions() {
    const dropdowns = document.querySelectorAll('.navbar-nav .dropdown');
    
    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (!toggle || !menu) return;
        
        // Add hover effect for desktop
        if (window.innerWidth > 991) {
            dropdown.addEventListener('mouseenter', function() {
                toggle.classList.add('show');
                menu.classList.add('show');
                toggle.setAttribute('aria-expanded', 'true');
            });
            
            dropdown.addEventListener('mouseleave', function() {
                toggle.classList.remove('show');
                menu.classList.remove('show');
                toggle.setAttribute('aria-expanded', 'false');
            });
        }
        
        // Handle click events
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Close other dropdowns
            dropdowns.forEach(otherDropdown => {
                if (otherDropdown !== dropdown) {
                    const otherToggle = otherDropdown.querySelector('.dropdown-toggle');
                    const otherMenu = otherDropdown.querySelector('.dropdown-menu');
                    if (otherToggle && otherMenu) {
                        otherToggle.classList.remove('show');
                        otherMenu.classList.remove('show');
                        otherToggle.setAttribute('aria-expanded', 'false');
                    }
                }
            });
            
            // Toggle current dropdown
            const isOpen = toggle.classList.contains('show');
            if (isOpen) {
                toggle.classList.remove('show');
                menu.classList.remove('show');
                toggle.setAttribute('aria-expanded', 'false');
            } else {
                toggle.classList.add('show');
                menu.classList.add('show');
                toggle.setAttribute('aria-expanded', 'true');
            }
        });
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            dropdowns.forEach(dropdown => {
                const toggle = dropdown.querySelector('.dropdown-toggle');
                const menu = dropdown.querySelector('.dropdown-menu');
                if (toggle && menu) {
                    toggle.classList.remove('show');
                    menu.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }
            });
        }
    });
}

/**
 * Mobile navigation enhancements
 */
function handleMobileNavigation() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    if (!navbarToggler || !navbarCollapse) return;
    
    navbarToggler.addEventListener('click', function() {
        const isExpanded = navbarToggler.getAttribute('aria-expanded') === 'true';
        navbarToggler.setAttribute('aria-expanded', !isExpanded);
        
        if (isExpanded) {
            navbarCollapse.classList.remove('show');
        } else {
            navbarCollapse.classList.add('show');
        }
    });
    
    // Close mobile menu when clicking on a link
    const mobileNavLinks = navbarCollapse.querySelectorAll('.nav-link:not(.dropdown-toggle)');
    mobileNavLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (window.innerWidth <= 991) {
                navbarCollapse.classList.remove('show');
                navbarToggler.setAttribute('aria-expanded', 'false');
            }
        });
    });
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 991) {
            navbarCollapse.classList.remove('show');
            navbarToggler.setAttribute('aria-expanded', 'false');
        }
    });
}

/**
 * Handle notification badges
 */
function handleNotificationBadges() {
    const notificationBadges = document.querySelectorAll('.navbar-nav .badge');
    
    notificationBadges.forEach(badge => {
        // Add animation for new notifications
        if (parseInt(badge.textContent) > 0) {
            badge.classList.add('animate__animated', 'animate__pulse');
        }
        
        // Update badge periodically (if needed)
        // This would typically be done via AJAX or WebSocket
    });
}

/**
 * Handle navigation loading states
 */
function handleNavigationLoading() {
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link, .dropdown-item');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Don't add loading state for dropdowns or external links
            if (this.classList.contains('dropdown-toggle') || 
                this.getAttribute('href')?.startsWith('http') ||
                this.getAttribute('href') === '#') {
                return;
            }
            
            // Add loading state
            this.classList.add('loading');
            
            // Remove loading state after navigation (or timeout)
            setTimeout(() => {
                this.classList.remove('loading');
            }, 3000);
        });
    });
}

/**
 * Keyboard navigation support
 */
function handleKeyboardNavigation() {
    const navItems = document.querySelectorAll('.navbar-nav .nav-link, .dropdown-item');
    
    navItems.forEach((item, index) => {
        item.addEventListener('keydown', function(e) {
            let targetIndex;
            
            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    targetIndex = (index + 1) % navItems.length;
                    navItems[targetIndex].focus();
                    break;
                    
                case 'ArrowUp':
                    e.preventDefault();
                    targetIndex = (index - 1 + navItems.length) % navItems.length;
                    navItems[targetIndex].focus();
                    break;
                    
                case 'Escape':
                    // Close any open dropdowns
                    const openDropdowns = document.querySelectorAll('.dropdown-menu.show');
                    openDropdowns.forEach(menu => {
                        menu.classList.remove('show');
                        const toggle = menu.parentElement.querySelector('.dropdown-toggle');
                        if (toggle) {
                            toggle.classList.remove('show');
                            toggle.setAttribute('aria-expanded', 'false');
                            toggle.focus();
                        }
                    });
                    break;
                    
                case 'Enter':
                case ' ':
                    if (this.classList.contains('dropdown-toggle')) {
                        e.preventDefault();
                        this.click();
                    }
                    break;
            }
        });
    });
}

/**
 * Update notification count
 */
function updateNotificationCount(count) {
    const badges = document.querySelectorAll('.navbar-nav .nav-link .badge');
    badges.forEach(badge => {
        if (count > 0) {
            badge.textContent = count;
            badge.style.display = 'inline-block';
            badge.classList.add('animate__animated', 'animate__bounce');
        } else {
            badge.style.display = 'none';
        }
    });
}

/**
 * Show navigation toast message
 */
function showNavigationToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    // Add to toast container or create one
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    toastContainer.appendChild(toast);
    
    // Show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

/**
 * Handle navigation errors
 */
function handleNavigationError(error) {
    console.error('Navigation error:', error);
    showNavigationToast('حدث خطأ في التنقل. يرجى المحاولة مرة أخرى.', 'danger');
}

// Export functions for global use
window.NavigationUtils = {
    updateNotificationCount,
    showNavigationToast,
    handleNavigationError
};
