/* NJ Repair Shop - Custom Styles */

/* Root Variables for Consistent Theming */
:root {
    --primary-color: #007bff;
    --primary-dark: #0056b3;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --border-radius: 8px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
}

/* Enhanced Body and Layout */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f6fa;
    line-height: 1.6;
}

/* Custom Navbar Enhancements */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover {
    transform: translateY(-2px);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 80%;
}

/* Enhanced Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    font-weight: 600;
}

.card-header h5 {
    margin: 0;
}

/* Enhanced Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #1e7e34);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #e0a800);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #c82333);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #138496);
    border: none;
}

/* Enhanced Form Controls */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: scale(1.02);
}

/* Enhanced Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: scale(1.01);
}

/* Status Badges Enhancement */
.badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.5em 0.75em;
    border-radius: 50px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Priority Indicators */
.priority-high {
    color: var(--danger-color);
    font-weight: 700;
    animation: pulse 2s infinite;
}

.priority-medium {
    color: var(--warning-color);
    font-weight: 600;
}

.priority-normal {
    color: var(--success-color);
    font-weight: 500;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Statistics Cards Enhancement */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    transform: rotate(45deg);
}

.stats-card .display-6 {
    opacity: 0.8;
    transition: var(--transition);
}

.stats-card:hover .display-6 {
    opacity: 1;
    transform: scale(1.1);
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
}

.toast-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
}

.modal-footer {
    border: none;
    background-color: var(--light-color);
}

/* Dropdown Enhancements */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    animation: fadeInDown 0.3s ease;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    transition: var(--transition);
}

.dropdown-item:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    transform: translateX(5px);
}

/* Search Enhancement */
.search-container {
    position: relative;
}

.search-container .form-control {
    padding-left: 2.5rem;
}

.search-container .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
    z-index: 5;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: var(--border-radius) !important;
        margin-bottom: 0.25rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
    
    .navbar-nav .nav-link::after {
        display: none;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .btn {
        display: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #2d3748;
        --dark-color: #1a202c;
    }
    
    body {
        background-color: var(--dark-color);
        color: #e2e8f0;
    }
    
    .card {
        background-color: var(--light-color);
        color: #e2e8f0;
    }
    
    .table {
        color: #e2e8f0;
    }
    
    .table tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }
}
