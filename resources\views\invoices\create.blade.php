@extends('layouts.app')

@section('title', 'إنشاء فاتورة جديدة')

@push('styles')
<style>
.invoice-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    margin-bottom: 1.5rem;
}

.form-section-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
    margin: -1px -1px 0 -1px;
}

.form-section-body {
    padding: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.required-field::after {
    content: " *";
    color: #e74a3b;
    font-weight: bold;
}

.help-text {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.items-table {
    background: #fff;
    border-radius: 0.5rem;
    border: 1px solid #e3e6f0;
    overflow: hidden;
}

.items-table th {
    background: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

.items-table td {
    padding: 0.75rem;
    vertical-align: middle;
}

.item-row {
    border-bottom: 1px solid #e3e6f0;
}

.item-row:last-child {
    border-bottom: none;
}

.btn-add-item {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.35rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-add-item:hover {
    background: linear-gradient(45deg, #20c997, #17a2b8);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    color: white;
}

.btn-remove-item {
    background: #dc3545;
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.totals-section {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e3e6f0;
}

.total-row:last-child {
    border-bottom: none;
    font-weight: bold;
    font-size: 1.1rem;
    color: #667eea;
}

.total-label {
    font-weight: 600;
}

.total-value {
    font-weight: 600;
    font-size: 1.1rem;
}

.btn-submit {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 0.35rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.btn-submit:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    color: white;
}

.alert {
    border-radius: 0.35rem;
    border: none;
    padding: 1rem 1.5rem;
}

.customer-details {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-top: 0.5rem;
    display: none;
}

.repair-ticket-details {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-top: 0.5rem;
    display: none;
}

@media (max-width: 768px) {
    .invoice-header {
        padding: 1rem;
    }

    .form-section-body {
        padding: 1rem;
    }

    .items-table {
        font-size: 0.875rem;
    }

    .btn-submit {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="invoice-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">إنشاء فاتورة جديدة</h1>
                <p class="mb-0 opacity-75">إنشاء فاتورة جديدة للعميل</p>
            </div>
            <div>
                <a href="{{ route('invoices.index') }}" class="btn btn-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للفواتير
                </a>
            </div>
        </div>
    </div>

    <!-- Alert Info -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle me-2"></i>
        <strong>تنبيه:</strong> الحقول المميزة بعلامة (*) مطلوبة ويجب ملؤها.
    </div>

    <!-- Form -->
    <form action="{{ route('invoices.store') }}" method="POST" id="invoiceForm">
        @csrf

        <!-- Customer Information -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات العميل
                </h5>
            </div>
            <div class="form-section-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="customer_id" class="form-label required-field">العميل</label>
                        <select class="form-select @error('customer_id') is-invalid @enderror"
                                id="customer_id"
                                name="customer_id"
                                required>
                            <option value="">اختر العميل</option>
                            @foreach($customers as $customer)
                                <option value="{{ $customer->id }}"
                                        data-name="{{ $customer->name }}"
                                        data-phone="{{ $customer->phone_number }}"
                                        data-email="{{ $customer->email }}"
                                        data-address="{{ $customer->address }}"
                                        {{ old('customer_id', $selectedRepairTicket?->customer_id) == $customer->id ? 'selected' : '' }}>
                                    {{ $customer->name }} - {{ $customer->phone_number }}
                                </option>
                            @endforeach
                        </select>
                        @error('customer_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror

                        <div class="customer-details" id="customerDetails">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>الاسم:</strong> <span id="customerName">-</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>الهاتف:</strong> <span id="customerPhone">-</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>البريد الإلكتروني:</strong> <span id="customerEmail">-</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>العنوان:</strong> <span id="customerAddress">-</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="repair_ticket_id" class="form-label">بطاقة الإصلاح (اختياري)</label>
                        <select class="form-select @error('repair_ticket_id') is-invalid @enderror"
                                id="repair_ticket_id"
                                name="repair_ticket_id">
                            <option value="">اختر بطاقة الإصلاح</option>
                            @foreach($repairTickets as $ticket)
                                <option value="{{ $ticket->id }}"
                                        data-device="{{ $ticket->device_type }} {{ $ticket->device_model }}"
                                        data-issue="{{ $ticket->issue_description }}"
                                        data-cost="{{ $ticket->estimated_cost }}"
                                        {{ old('repair_ticket_id', $selectedRepairTicket?->id) == $ticket->id ? 'selected' : '' }}>
                                    #{{ $ticket->ticket_number }} - {{ $ticket->customer->name }} - {{ $ticket->device_type }}
                                </option>
                            @endforeach
                        </select>
                        @error('repair_ticket_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="help-text">ربط الفاتورة ببطاقة إصلاح موجودة</div>

                        <div class="repair-ticket-details" id="repairTicketDetails">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>الجهاز:</strong> <span id="ticketDevice">-</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>المشكلة:</strong> <span id="ticketIssue">-</span>
                                </div>
                                <div class="col-md-12">
                                    <strong>التكلفة المقدرة:</strong> <span id="ticketCost">-</span> ريال
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice Details -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-invoice me-2"></i>
                    تفاصيل الفاتورة
                </h5>
            </div>
            <div class="form-section-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="invoice_date" class="form-label required-field">تاريخ الفاتورة</label>
                        <input type="date"
                               class="form-control @error('invoice_date') is-invalid @enderror"
                               id="invoice_date"
                               name="invoice_date"
                               value="{{ old('invoice_date', date('Y-m-d')) }}"
                               required>
                        @error('invoice_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="due_date" class="form-label required-field">تاريخ الاستحقاق</label>
                        <input type="date"
                               class="form-control @error('due_date') is-invalid @enderror"
                               id="due_date"
                               name="due_date"
                               value="{{ old('due_date', date('Y-m-d', strtotime('+30 days'))) }}"
                               required>
                        @error('due_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="tax_rate" class="form-label required-field">نسبة الضريبة (%)</label>
                        <input type="number"
                               class="form-control @error('tax_rate') is-invalid @enderror"
                               id="tax_rate"
                               name="tax_rate"
                               value="{{ old('tax_rate', $settings['default_tax_rate'] ?? 15) }}"
                               min="0"
                               max="100"
                               step="0.01"
                               required>
                        @error('tax_rate')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="discount_percentage" class="form-label">نسبة الخصم (%)</label>
                        <input type="number"
                               class="form-control @error('discount_percentage') is-invalid @enderror"
                               id="discount_percentage"
                               name="discount_percentage"
                               value="{{ old('discount_percentage', 0) }}"
                               min="0"
                               max="100"
                               step="0.01">
                        @error('discount_percentage')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control @error('notes') is-invalid @enderror"
                                  id="notes"
                                  name="notes"
                                  rows="3"
                                  placeholder="أي ملاحظات إضافية">{{ old('notes') }}</textarea>
                        @error('notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="terms_conditions" class="form-label">الشروط والأحكام</label>
                        <textarea class="form-control @error('terms_conditions') is-invalid @enderror"
                                  id="terms_conditions"
                                  name="terms_conditions"
                                  rows="3"
                                  placeholder="شروط وأحكام الفاتورة">{{ old('terms_conditions', $settings['invoice_terms'] ?? '') }}</textarea>
                        @error('terms_conditions')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice Items -->
        <div class="form-section">
            <div class="form-section-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    عناصر الفاتورة
                </h5>
            </div>
            <div class="form-section-body">
                <div class="items-table">
                    <table class="table mb-0" id="itemsTable">
                        <thead>
                            <tr>
                                <th style="width: 20%">النوع</th>
                                <th style="width: 25%">الاسم</th>
                                <th style="width: 15%">الكمية</th>
                                <th style="width: 15%">السعر</th>
                                <th style="width: 15%">الإجمالي</th>
                                <th style="width: 10%">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="itemsTableBody">
                            <!-- Items will be added here dynamically -->
                        </tbody>
                    </table>
                </div>

                <div class="text-center mt-3">
                    <button type="button" class="btn-add-item" onclick="addItem()">
                        <i class="fas fa-plus me-2"></i>إضافة عنصر
                    </button>
                </div>
            </div>
        </div>

        <!-- Totals -->
        <div class="totals-section">
            <div class="total-row">
                <span class="total-label">المجموع الفرعي:</span>
                <span class="total-value" id="subtotalDisplay">0.00 ريال</span>
            </div>
            <div class="total-row">
                <span class="total-label">الخصم:</span>
                <span class="total-value" id="discountDisplay">0.00 ريال</span>
            </div>
            <div class="total-row">
                <span class="total-label">الضريبة:</span>
                <span class="total-value" id="taxDisplay">0.00 ريال</span>
            </div>
            <div class="total-row">
                <span class="total-label">المجموع الإجمالي:</span>
                <span class="total-value" id="totalDisplay">0.00 ريال</span>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="text-center mt-4">
            <button type="submit" class="btn-submit">
                <i class="fas fa-save me-2"></i>إنشاء الفاتورة
            </button>
            <a href="{{ route('invoices.index') }}" class="btn btn-secondary ms-2">
                <i class="fas fa-times me-2"></i>إلغاء
            </a>
        </div>
    </form>
</div>

@push('scripts')
<script>
let itemCounter = 0;
const inventoryItems = @json($inventoryItems);

document.addEventListener('DOMContentLoaded', function() {
    // Add first item by default
    addItem();

    // Customer selection handler
    document.getElementById('customer_id').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const customerDetails = document.getElementById('customerDetails');

        if (this.value) {
            document.getElementById('customerName').textContent = selectedOption.dataset.name || '-';
            document.getElementById('customerPhone').textContent = selectedOption.dataset.phone || '-';
            document.getElementById('customerEmail').textContent = selectedOption.dataset.email || '-';
            document.getElementById('customerAddress').textContent = selectedOption.dataset.address || '-';
            customerDetails.style.display = 'block';
        } else {
            customerDetails.style.display = 'none';
        }
    });

    // Repair ticket selection handler
    document.getElementById('repair_ticket_id').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const ticketDetails = document.getElementById('repairTicketDetails');

        if (this.value) {
            document.getElementById('ticketDevice').textContent = selectedOption.dataset.device || '-';
            document.getElementById('ticketIssue').textContent = selectedOption.dataset.issue || '-';
            document.getElementById('ticketCost').textContent = selectedOption.dataset.cost || '0';
            ticketDetails.style.display = 'block';

            // Auto-add service item if repair ticket is selected
            if (selectedOption.dataset.cost && parseFloat(selectedOption.dataset.cost) > 0) {
                addServiceItem(selectedOption.dataset.device, selectedOption.dataset.cost);
            }
        } else {
            ticketDetails.style.display = 'none';
        }
    });

    // Tax rate and discount change handlers
    document.getElementById('tax_rate').addEventListener('input', calculateTotals);
    document.getElementById('discount_percentage').addEventListener('input', calculateTotals);

    // Trigger customer details if pre-selected
    if (document.getElementById('customer_id').value) {
        document.getElementById('customer_id').dispatchEvent(new Event('change'));
    }

    // Trigger repair ticket details if pre-selected
    if (document.getElementById('repair_ticket_id').value) {
        document.getElementById('repair_ticket_id').dispatchEvent(new Event('change'));
    }
});

function addItem() {
    itemCounter++;
    const tbody = document.getElementById('itemsTableBody');
    const row = document.createElement('tr');
    row.className = 'item-row';
    row.id = `item-${itemCounter}`;

    row.innerHTML = `
        <td>
            <select class="form-control" name="items[${itemCounter}][item_type]" required onchange="handleItemTypeChange(${itemCounter})">
                <option value="">اختر النوع</option>
                <option value="service">خدمة</option>
                <option value="part">قطعة غيار</option>
                <option value="labor">عمالة</option>
                <option value="other">أخرى</option>
            </select>
        </td>
        <td>
            <input type="text" class="form-control" name="items[${itemCounter}][item_name]" placeholder="اسم العنصر" required>
            <select class="form-control mt-1" name="items[${itemCounter}][inventory_item_id]" style="display: none;" onchange="handleInventoryItemChange(${itemCounter})">
                <option value="">اختر من المخزون</option>
                ${inventoryItems.map(item => `<option value="${item.id}" data-price="${item.selling_price}" data-stock="${item.current_stock}">${item.name} - ${item.sku}</option>`).join('')}
            </select>
        </td>
        <td>
            <input type="number" class="form-control" name="items[${itemCounter}][quantity]" value="1" min="0.01" step="0.01" required onchange="calculateItemTotal(${itemCounter})">
        </td>
        <td>
            <input type="number" class="form-control" name="items[${itemCounter}][unit_price]" value="0" min="0" step="0.01" required onchange="calculateItemTotal(${itemCounter})">
        </td>
        <td>
            <span class="item-total" id="item-total-${itemCounter}">0.00</span>
            <input type="hidden" name="items[${itemCounter}][is_taxable]" value="1">
        </td>
        <td>
            <button type="button" class="btn-remove-item" onclick="removeItem(${itemCounter})">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

    tbody.appendChild(row);
    calculateTotals();
}

function addServiceItem(serviceName, price) {
    itemCounter++;
    const tbody = document.getElementById('itemsTableBody');
    const row = document.createElement('tr');
    row.className = 'item-row';
    row.id = `item-${itemCounter}`;

    row.innerHTML = `
        <td>
            <select class="form-control" name="items[${itemCounter}][item_type]" required>
                <option value="service" selected>خدمة</option>
                <option value="part">قطعة غيار</option>
                <option value="labor">عمالة</option>
                <option value="other">أخرى</option>
            </select>
        </td>
        <td>
            <input type="text" class="form-control" name="items[${itemCounter}][item_name]" value="إصلاح ${serviceName}" required>
        </td>
        <td>
            <input type="number" class="form-control" name="items[${itemCounter}][quantity]" value="1" min="0.01" step="0.01" required onchange="calculateItemTotal(${itemCounter})">
        </td>
        <td>
            <input type="number" class="form-control" name="items[${itemCounter}][unit_price]" value="${price}" min="0" step="0.01" required onchange="calculateItemTotal(${itemCounter})">
        </td>
        <td>
            <span class="item-total" id="item-total-${itemCounter}">${price}</span>
            <input type="hidden" name="items[${itemCounter}][is_taxable]" value="1">
        </td>
        <td>
            <button type="button" class="btn-remove-item" onclick="removeItem(${itemCounter})">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

    tbody.appendChild(row);
    calculateTotals();
}

function removeItem(itemId) {
    const row = document.getElementById(`item-${itemId}`);
    if (row) {
        row.remove();
        calculateTotals();
    }
}

function handleItemTypeChange(itemId) {
    const typeSelect = document.querySelector(`#item-${itemId} select[name="items[${itemId}][item_type]"]`);
    const inventorySelect = document.querySelector(`#item-${itemId} select[name="items[${itemId}][inventory_item_id]"]`);

    if (typeSelect.value === 'part') {
        inventorySelect.style.display = 'block';
    } else {
        inventorySelect.style.display = 'none';
        inventorySelect.value = '';
    }
}

function handleInventoryItemChange(itemId) {
    const inventorySelect = document.querySelector(`#item-${itemId} select[name="items[${itemId}][inventory_item_id]"]`);
    const nameInput = document.querySelector(`#item-${itemId} input[name="items[${itemId}][item_name]"]`);
    const priceInput = document.querySelector(`#item-${itemId} input[name="items[${itemId}][unit_price]"]`);

    const selectedOption = inventorySelect.options[inventorySelect.selectedIndex];

    if (inventorySelect.value) {
        nameInput.value = selectedOption.text.split(' - ')[0];
        priceInput.value = selectedOption.dataset.price || 0;
        calculateItemTotal(itemId);
    }
}

function calculateItemTotal(itemId) {
    const quantityInput = document.querySelector(`#item-${itemId} input[name="items[${itemId}][quantity]"]`);
    const priceInput = document.querySelector(`#item-${itemId} input[name="items[${itemId}][unit_price]"]`);
    const totalSpan = document.getElementById(`item-total-${itemId}`);

    const quantity = parseFloat(quantityInput.value) || 0;
    const price = parseFloat(priceInput.value) || 0;
    const total = quantity * price;

    totalSpan.textContent = total.toFixed(2);
    calculateTotals();
}

function calculateTotals() {
    let subtotal = 0;

    // Calculate subtotal
    document.querySelectorAll('.item-total').forEach(span => {
        subtotal += parseFloat(span.textContent) || 0;
    });

    // Calculate discount
    const discountPercentage = parseFloat(document.getElementById('discount_percentage').value) || 0;
    const discountAmount = subtotal * (discountPercentage / 100);

    // Calculate tax
    const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = taxableAmount * (taxRate / 100);

    // Calculate total
    const total = subtotal - discountAmount + taxAmount;

    // Update displays
    document.getElementById('subtotalDisplay').textContent = subtotal.toFixed(2) + ' ريال';
    document.getElementById('discountDisplay').textContent = discountAmount.toFixed(2) + ' ريال';
    document.getElementById('taxDisplay').textContent = taxAmount.toFixed(2) + ' ريال';
    document.getElementById('totalDisplay').textContent = total.toFixed(2) + ' ريال';
}

// Form validation
document.getElementById('invoiceForm').addEventListener('submit', function(e) {
    const items = document.querySelectorAll('.item-row');

    if (items.length === 0) {
        e.preventDefault();
        alert('يجب إضافة عنصر واحد على الأقل للفاتورة');
        return false;
    }

    // Validate each item
    let isValid = true;
    items.forEach((item, index) => {
        const typeSelect = item.querySelector('select[name*="[item_type]"]');
        const nameInput = item.querySelector('input[name*="[item_name]"]');
        const quantityInput = item.querySelector('input[name*="[quantity]"]');
        const priceInput = item.querySelector('input[name*="[unit_price]"]');

        if (!typeSelect.value || !nameInput.value || !quantityInput.value || !priceInput.value) {
            isValid = false;
        }
    });

    if (!isValid) {
        e.preventDefault();
        alert('يرجى ملء جميع حقول العناصر');
        return false;
    }

    return true;
});
</script>
@endpush
@endsection
