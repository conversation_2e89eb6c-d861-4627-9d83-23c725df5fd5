<?php

namespace App\Console\Commands;

use App\Services\WhatsAppPaymentNotificationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendAutomaticPaymentReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'whatsapp:send-payment-reminders 
                            {--dry-run : Show what would be sent without actually sending}
                            {--type=all : Type of reminders to send (gentle|urgent|final|all)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send automatic WhatsApp payment reminders to customers';

    protected WhatsAppPaymentNotificationService $paymentNotificationService;

    /**
     * Create a new command instance.
     */
    public function __construct(WhatsAppPaymentNotificationService $paymentNotificationService)
    {
        parent::__construct();
        $this->paymentNotificationService = $paymentNotificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $dryRun = $this->option('dry-run');
        $type = $this->option('type');

        $this->info('Starting automatic payment reminders...');
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No messages will actually be sent');
        }

        try {
            if ($dryRun) {
                $results = $this->performDryRun($type);
            } else {
                $results = $this->paymentNotificationService->scheduleAutomaticReminders();
            }

            $this->displayResults($results);

            if (!empty($results['errors'])) {
                $this->error('Some reminders failed to send:');
                foreach ($results['errors'] as $error) {
                    $this->line("  - {$error}");
                }
                return Command::FAILURE;
            }

            $this->info('Payment reminders completed successfully!');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("Failed to send payment reminders: {$e->getMessage()}");
            Log::error('Automatic payment reminders command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Perform a dry run to show what would be sent.
     */
    private function performDryRun(string $type): array
    {
        $results = [
            'gentle_reminders' => 0,
            'urgent_reminders' => 0,
            'final_reminders' => 0,
            'errors' => []
        ];

        $this->info('Checking for invoices that need reminders...');

        if ($type === 'all' || $type === 'gentle') {
            $gentleReminderInvoices = \App\Models\Invoice::where('payment_status', '!=', 'paid')
                ->where('due_date', '=', now()->addDays(3)->toDateString())
                ->with('customer')
                ->get();

            $results['gentle_reminders'] = $gentleReminderInvoices->count();
            
            if ($gentleReminderInvoices->count() > 0) {
                $this->line("Gentle reminders (3 days before due): {$gentleReminderInvoices->count()} invoices");
                foreach ($gentleReminderInvoices as $invoice) {
                    $this->line("  - Invoice {$invoice->invoice_number} for {$invoice->customer->name} ({$invoice->customer->phone_number})");
                }
            }
        }

        if ($type === 'all' || $type === 'urgent') {
            $urgentReminderInvoices = \App\Models\Invoice::where('payment_status', '!=', 'paid')
                ->where('due_date', '=', now()->subDay()->toDateString())
                ->with('customer')
                ->get();

            $results['urgent_reminders'] = $urgentReminderInvoices->count();
            
            if ($urgentReminderInvoices->count() > 0) {
                $this->line("Urgent reminders (1 day overdue): {$urgentReminderInvoices->count()} invoices");
                foreach ($urgentReminderInvoices as $invoice) {
                    $this->line("  - Invoice {$invoice->invoice_number} for {$invoice->customer->name} ({$invoice->customer->phone_number})");
                }
            }
        }

        if ($type === 'all' || $type === 'final') {
            $finalReminderInvoices = \App\Models\Invoice::where('payment_status', '!=', 'paid')
                ->where('due_date', '=', now()->subDays(7)->toDateString())
                ->with('customer')
                ->get();

            $results['final_reminders'] = $finalReminderInvoices->count();
            
            if ($finalReminderInvoices->count() > 0) {
                $this->line("Final reminders (7 days overdue): {$finalReminderInvoices->count()} invoices");
                foreach ($finalReminderInvoices as $invoice) {
                    $this->line("  - Invoice {$invoice->invoice_number} for {$invoice->customer->name} ({$invoice->customer->phone_number})");
                }
            }
        }

        return $results;
    }

    /**
     * Display the results of the reminder sending.
     */
    private function displayResults(array $results): void
    {
        $this->info('Payment Reminder Results:');
        $this->table(
            ['Type', 'Count'],
            [
                ['Gentle Reminders', $results['gentle_reminders']],
                ['Urgent Reminders', $results['urgent_reminders']],
                ['Final Reminders', $results['final_reminders']],
                ['Total Sent', $results['gentle_reminders'] + $results['urgent_reminders'] + $results['final_reminders']],
            ]
        );

        if (!empty($results['errors'])) {
            $this->warn("Errors encountered: " . count($results['errors']));
        }
    }
}
