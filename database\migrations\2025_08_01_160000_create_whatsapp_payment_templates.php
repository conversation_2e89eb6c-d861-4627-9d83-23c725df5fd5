<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\WhatsAppMessageTemplate;
use App\Models\WhatsAppTemplateCategory;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create payment notification templates
        $this->createPaymentTemplates();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove payment templates
        WhatsAppMessageTemplate::whereIn('name', [
            'payment_confirmation_ar',
            'invoice_notification_ar',
            'payment_reminder_gentle_ar',
            'payment_reminder_urgent_ar',
            'payment_reminder_final_ar',
            'payment_receipt_ar',
            'payment_plan_ar',
            'payment_overdue_ar',
            'payment_failed_ar',
            'refund_notification_ar'
        ])->delete();
    }

    /**
     * Create payment-related WhatsApp templates.
     */
    private function createPaymentTemplates(): void
    {
        // Get or create payment category
        $paymentCategory = WhatsAppTemplateCategory::firstOrCreate([
            'name' => 'payment_notifications',
            'display_name' => 'Payment Notifications',
            'display_name_ar' => 'إشعارات الدفع',
            'whatsapp_category' => 'UTILITY',
            'description' => 'Templates for payment confirmations, reminders, and receipts',
            'description_ar' => 'قوالب لتأكيدات الدفع والتذكيرات والإيصالات',
            'is_active' => true,
        ]);

        $templates = [
            [
                'name' => 'payment_confirmation_ar',
                'whatsapp_name' => 'payment_confirmation_ar',
                'display_name' => 'Payment Confirmation',
                'display_name_ar' => 'تأكيد الدفع',
                'description' => 'Sent when a payment is successfully received',
                'description_ar' => 'يُرسل عند استلام الدفع بنجاح',
                'category_id' => $paymentCategory->id,
                'language' => 'ar',
                'header_text' => null,
                'body_text' => "عزيزي {{customer_name}} 👋\n\nتم استلام دفعتك بنجاح! 💰\n\n📋 تفاصيل الدفع:\n• المبلغ: {{payment_amount}} ريال\n• طريقة الدفع: {{payment_method}}\n• التاريخ: {{payment_date}} في {{payment_time}}\n• رقم الفاتورة: {{invoice_number}}\n• المبلغ المتبقي: {{remaining_amount}} ريال\n• رقم المرجع: {{payment_reference}}\n\nشكراً لك على ثقتك في {{business_name}} 🙏\n\nللاستفسار: {{business_phone}}",
                'footer_text' => 'ورشة إصلاح NJ - خدمة متميزة',
                'buttons' => null,
                'variables' => ['customer_name', 'payment_amount', 'payment_method', 'payment_date', 'payment_time', 'invoice_number', 'remaining_amount', 'business_name', 'business_phone', 'payment_reference'],
                'sample_values' => [
                    'customer_name' => 'أحمد محمد',
                    'payment_amount' => '500.00',
                    'payment_method' => 'نقداً',
                    'payment_date' => '2024-01-15',
                    'payment_time' => '14:30',
                    'invoice_number' => 'INV-2024-001',
                    'remaining_amount' => '0.00',
                    'business_name' => 'ورشة إصلاح NJ',
                    'business_phone' => '+966501234567',
                    'payment_reference' => 'PAY-001'
                ],
                'status' => 'approved',
                'is_active' => true,
            ],
            [
                'name' => 'invoice_notification_ar',
                'whatsapp_name' => 'invoice_notification_ar',
                'display_name' => 'Invoice Notification',
                'display_name_ar' => 'إشعار الفاتورة',
                'description' => 'Sent when a new invoice is created',
                'description_ar' => 'يُرسل عند إنشاء فاتورة جديدة',
                'category_id' => $paymentCategory->id,
                'language' => 'ar',
                'header_text' => null,
                'body_text' => "عزيزي {{customer_name}} 👋\n\nتم إصدار فاتورة جديدة لك 📄\n\n📋 تفاصيل الفاتورة:\n• رقم الفاتورة: {{invoice_number}}\n• تاريخ الإصدار: {{invoice_date}}\n• تاريخ الاستحقاق: {{due_date}}\n• المبلغ الإجمالي: {{total_amount}} ريال\n• المبلغ المدفوع: {{paid_amount}} ريال\n• المبلغ المتبقي: {{remaining_amount}} ريال\n• حالة الدفع: {{payment_status}}\n\n{{business_name}}\n📍 {{business_address}}\n📞 {{business_phone}}",
                'footer_text' => 'يرجى الدفع في الموعد المحدد',
                'buttons' => null,
                'variables' => ['customer_name', 'invoice_number', 'invoice_date', 'due_date', 'total_amount', 'paid_amount', 'remaining_amount', 'payment_status', 'business_name', 'business_address', 'business_phone'],
                'sample_values' => [
                    'customer_name' => 'أحمد محمد',
                    'invoice_number' => 'INV-2024-001',
                    'invoice_date' => '2024-01-15',
                    'due_date' => '2024-01-30',
                    'total_amount' => '500.00',
                    'paid_amount' => '0.00',
                    'remaining_amount' => '500.00',
                    'payment_status' => 'غير مدفوع',
                    'business_name' => 'ورشة إصلاح NJ',
                    'business_address' => 'شارع الملك فهد، الرياض',
                    'business_phone' => '+966501234567'
                ],
                'status' => 'approved',
                'is_active' => true,
            ],
            [
                'name' => 'payment_reminder_gentle_ar',
                'whatsapp_name' => 'payment_reminder_gentle_ar',
                'display_name' => 'Gentle Payment Reminder',
                'display_name_ar' => 'تذكير دفع لطيف',
                'description' => 'Gentle reminder for upcoming payment due date',
                'description_ar' => 'تذكير لطيف لموعد الدفع القادم',
                'category_id' => $paymentCategory->id,
                'language' => 'ar',
                'header_text' => null,
                'body_text' => "عزيزي {{customer_name}} 👋\n\nتذكير ودي بموعد استحقاق الدفع 📅\n\n📋 تفاصيل الفاتورة:\n• رقم الفاتورة: {{invoice_number}}\n• تاريخ الاستحقاق: {{due_date}}\n• المبلغ المتبقي: {{remaining_amount}} ريال\n• الحالة: {{overdue_status}}\n\n💳 طرق الدفع المتاحة:\n{{payment_methods}}\n\nنقدر تعاملك معنا ونتطلع لخدمتك دائماً 🙏\n\n{{business_name}}\n📞 {{business_phone}}",
                'footer_text' => 'شكراً لحسن تعاملكم',
                'buttons' => null,
                'variables' => ['customer_name', 'invoice_number', 'due_date', 'remaining_amount', 'overdue_status', 'payment_methods', 'business_name', 'business_phone'],
                'sample_values' => [
                    'customer_name' => 'أحمد محمد',
                    'invoice_number' => 'INV-2024-001',
                    'due_date' => '2024-01-30',
                    'remaining_amount' => '500.00',
                    'overdue_status' => 'مستحق قريباً',
                    'payment_methods' => '• نقداً في المحل\n• تحويل بنكي\n• بطاقة ائتمان',
                    'business_name' => 'ورشة إصلاح NJ',
                    'business_phone' => '+966501234567'
                ],
                'status' => 'approved',
                'is_active' => true,
            ],
            [
                'name' => 'payment_reminder_urgent_ar',
                'whatsapp_name' => 'payment_reminder_urgent_ar',
                'display_name' => 'Urgent Payment Reminder',
                'display_name_ar' => 'تذكير دفع عاجل',
                'description' => 'Urgent reminder for overdue payment',
                'description_ar' => 'تذكير عاجل للدفع المتأخر',
                'category_id' => $paymentCategory->id,
                'language' => 'ar',
                'header_text' => null,
                'body_text' => "عزيزي {{customer_name}} 👋\n\n⚠️ تذكير عاجل: الدفع متأخر\n\n📋 تفاصيل الفاتورة:\n• رقم الفاتورة: {{invoice_number}}\n• تاريخ الاستحقاق: {{due_date}}\n• المبلغ المتبقي: {{remaining_amount}} ريال\n• أيام التأخير: {{days_overdue}} يوم\n\nيرجى المبادرة بالدفع لتجنب أي رسوم إضافية 🙏\n\n💳 طرق الدفع المتاحة:\n{{payment_methods}}\n\n{{business_name}}\n📞 {{business_phone}}\n📍 {{business_address}}",
                'footer_text' => 'نقدر تفهمكم وسرعة الاستجابة',
                'buttons' => null,
                'variables' => ['customer_name', 'invoice_number', 'due_date', 'remaining_amount', 'days_overdue', 'payment_methods', 'business_name', 'business_phone', 'business_address'],
                'sample_values' => [
                    'customer_name' => 'أحمد محمد',
                    'invoice_number' => 'INV-2024-001',
                    'due_date' => '2024-01-30',
                    'remaining_amount' => '500.00',
                    'days_overdue' => '3',
                    'payment_methods' => '• نقداً في المحل\n• تحويل بنكي\n• بطاقة ائتمان',
                    'business_name' => 'ورشة إصلاح NJ',
                    'business_phone' => '+966501234567',
                    'business_address' => 'شارع الملك فهد، الرياض'
                ],
                'status' => 'approved',
                'is_active' => true,
            ],
            [
                'name' => 'payment_receipt_ar',
                'whatsapp_name' => 'payment_receipt_ar',
                'display_name' => 'Payment Receipt',
                'display_name_ar' => 'إيصال الدفع',
                'description' => 'Digital receipt for completed payment',
                'description_ar' => 'إيصال رقمي للدفع المكتمل',
                'category_id' => $paymentCategory->id,
                'language' => 'ar',
                'header_text' => null,
                'body_text' => "عزيزي {{customer_name}} 👋\n\n🧾 إيصال الدفع\n\n📋 تفاصيل الإيصال:\n• رقم الإيصال: {{receipt_number}}\n• المبلغ المدفوع: {{payment_amount}} ريال\n• طريقة الدفع: {{payment_method}}\n• تاريخ ووقت الدفع: {{payment_date}}\n• رقم الفاتورة: {{invoice_number}}\n• رقم المرجع: {{payment_reference}}\n\nشكراً لك على الدفع 💚\n\n{{business_name}}\n📞 {{business_phone}}\n📍 {{business_address}}",
                'footer_text' => 'احتفظ بهذا الإيصال للمراجعة',
                'buttons' => null,
                'variables' => ['customer_name', 'receipt_number', 'payment_amount', 'payment_method', 'payment_date', 'invoice_number', 'payment_reference', 'business_name', 'business_phone', 'business_address'],
                'sample_values' => [
                    'customer_name' => 'أحمد محمد',
                    'receipt_number' => 'RCP-2024-001',
                    'payment_amount' => '500.00',
                    'payment_method' => 'نقداً',
                    'payment_date' => '2024-01-15 14:30',
                    'invoice_number' => 'INV-2024-001',
                    'payment_reference' => 'PAY-001',
                    'business_name' => 'ورشة إصلاح NJ',
                    'business_phone' => '+966501234567',
                    'business_address' => 'شارع الملك فهد، الرياض'
                ],
                'status' => 'approved',
                'is_active' => true,
            ],
            [
                'name' => 'payment_plan_ar',
                'whatsapp_name' => 'payment_plan_ar',
                'display_name' => 'Payment Plan',
                'display_name_ar' => 'خطة الدفع',
                'description' => 'Payment installment plan notification',
                'description_ar' => 'إشعار خطة الدفع بالتقسيط',
                'category_id' => $paymentCategory->id,
                'language' => 'ar',
                'header_text' => null,
                'body_text' => "عزيزي {{customer_name}} 👋\n\n📋 خطة الدفع بالتقسيط\n\n• رقم الفاتورة: {{invoice_number}}\n• المبلغ الإجمالي: {{total_amount}} ريال\n• عدد الأقساط: {{installments_count}} قسط\n• قيمة القسط: {{installment_amount}} ريال\n• تاريخ القسط الأول: {{first_due_date}}\n\n📅 تفاصيل الأقساط:\n{{installment_details}}\n\nيرجى الالتزام بمواعيد الأقساط 🙏\n\n{{business_name}}\n📞 {{business_phone}}",
                'footer_text' => 'نشكركم على اختيار خدماتنا',
                'buttons' => null,
                'variables' => ['customer_name', 'invoice_number', 'total_amount', 'installments_count', 'installment_amount', 'first_due_date', 'installment_details', 'business_name', 'business_phone'],
                'sample_values' => [
                    'customer_name' => 'أحمد محمد',
                    'invoice_number' => 'INV-2024-001',
                    'total_amount' => '1500.00',
                    'installments_count' => '3',
                    'installment_amount' => '500.00',
                    'first_due_date' => '2024-02-01',
                    'installment_details' => 'القسط 1: 500.00 ريال - 2024-02-01\nالقسط 2: 500.00 ريال - 2024-03-01\nالقسط 3: 500.00 ريال - 2024-04-01',
                    'business_name' => 'ورشة إصلاح NJ',
                    'business_phone' => '+966501234567'
                ],
                'status' => 'approved',
                'is_active' => true,
            ]
        ];

        foreach ($templates as $templateData) {
            WhatsAppMessageTemplate::create($templateData);
        }
    }
};
