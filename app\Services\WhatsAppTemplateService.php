<?php

namespace App\Services;

use App\Models\WhatsAppMessageTemplate;
use App\Models\WhatsAppTemplateCategory;
use App\Models\WhatsAppTemplateUsage;
use App\Models\WhatsAppCustomerPreference;
use App\Models\Customer;
use App\Models\RepairTicket;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class WhatsAppTemplateService
{
    protected WhatsAppService $whatsappService;

    public function __construct(WhatsAppService $whatsappService)
    {
        $this->whatsappService = $whatsappService;
    }

    /**
     * Send template message to customer
     */
    public function sendTemplate(
        WhatsAppMessageTemplate $template,
        string $customerPhone,
        array $variables = [],
        array $context = []
    ): array {
        try {
            // Get customer preferences
            $preferences = WhatsAppCustomerPreference::getOrCreateForCustomer($customerPhone);
            
            // Check if customer can receive this message
            if (!$this->canSendToCustomer($template, $preferences, $context)) {
                return [
                    'success' => false,
                    'error' => 'Customer cannot receive this message type',
                    'reason' => 'preferences_blocked'
                ];
            }

            // Validate template variables
            $missingVars = $template->validateVariables($variables);
            if (!empty($missingVars)) {
                return [
                    'success' => false,
                    'error' => 'Missing required variables: ' . implode(', ', $missingVars),
                    'missing_variables' => $missingVars
                ];
            }

            // Send via WhatsApp API
            $result = $this->whatsappService->sendTemplateMessage(
                $customerPhone,
                $template->whatsapp_name,
                array_values($variables),
                $template->language
            );

            // Log usage
            $usage = $this->logTemplateUsage($template, $customerPhone, $variables, $context, $result);

            if ($result['success']) {
                $template->incrementUsage(true);
                $preferences->recordInteraction();
                
                return [
                    'success' => true,
                    'message_id' => $result['message_id'],
                    'usage_id' => $usage->id
                ];
            } else {
                $template->incrementUsage(false);
                $usage->markAsFailed($result['error']);
                
                return [
                    'success' => false,
                    'error' => $result['error'],
                    'usage_id' => $usage->id
                ];
            }

        } catch (\Exception $e) {
            Log::error('Template sending failed', [
                'template_id' => $template->id,
                'customer_phone' => $customerPhone,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Internal error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send status update template
     */
    public function sendStatusUpdate(RepairTicket $ticket, string $newStatus): array
    {
        $template = WhatsAppMessageTemplate::where('name', 'status_update_ar')
            ->approved()
            ->active()
            ->first();

        if (!$template) {
            return ['success' => false, 'error' => 'Status update template not found'];
        }

        $variables = [
            'customer_name' => $ticket->customer->name,
            'ticket_number' => $ticket->ticket_number,
            'device_info' => $ticket->brand->name . ' ' . $ticket->device_model,
            'status' => $newStatus,
            'business_name' => config('whatsapp.business_name'),
            'business_phone' => config('whatsapp.business_phone'),
        ];

        if ($ticket->estimated_completion_date) {
            $variables['estimated_date'] = $ticket->estimated_completion_date->format('Y-m-d');
        }

        return $this->sendTemplate(
            $template,
            $ticket->customer->phone_number,
            $variables,
            [
                'trigger_type' => 'auto',
                'trigger_source' => 'system',
                'business_event' => 'status_update',
                'repair_ticket_id' => $ticket->id,
            ]
        );
    }

    /**
     * Send appointment confirmation
     */
    public function sendAppointmentConfirmation(
        Customer $customer,
        \DateTime $appointmentDate,
        string $serviceType
    ): array {
        $template = WhatsAppMessageTemplate::where('name', 'appointment_confirmation_ar')
            ->approved()
            ->active()
            ->first();

        if (!$template) {
            return ['success' => false, 'error' => 'Appointment template not found'];
        }

        $variables = [
            'customer_name' => $customer->name,
            'appointment_date' => $appointmentDate->format('Y-m-d'),
            'appointment_time' => $appointmentDate->format('H:i'),
            'service_type' => $serviceType,
            'business_name' => config('whatsapp.business_name'),
            'business_address' => config('whatsapp.business_address'),
            'business_phone' => config('whatsapp.business_phone'),
        ];

        return $this->sendTemplate(
            $template,
            $customer->phone_number,
            $variables,
            [
                'trigger_type' => 'auto',
                'trigger_source' => 'system',
                'business_event' => 'appointment',
            ]
        );
    }

    /**
     * Send payment reminder
     */
    public function sendPaymentReminder(RepairTicket $ticket, float $amount): array
    {
        $template = WhatsAppMessageTemplate::where('name', 'payment_reminder_ar')
            ->approved()
            ->active()
            ->first();

        if (!$template) {
            return ['success' => false, 'error' => 'Payment reminder template not found'];
        }

        $variables = [
            'customer_name' => $ticket->customer->name,
            'ticket_number' => $ticket->ticket_number,
            'amount' => number_format($amount, 2) . ' ر.س',
            'device_info' => $ticket->brand->name . ' ' . $ticket->device_model,
            'business_name' => config('whatsapp.business_name'),
            'business_phone' => config('whatsapp.business_phone'),
        ];

        return $this->sendTemplate(
            $template,
            $ticket->customer->phone_number,
            $variables,
            [
                'trigger_type' => 'auto',
                'trigger_source' => 'system',
                'business_event' => 'payment_reminder',
                'repair_ticket_id' => $ticket->id,
            ]
        );
    }

    /**
     * Send pickup ready notification
     */
    public function sendPickupReady(RepairTicket $ticket): array
    {
        $template = WhatsAppMessageTemplate::where('name', 'pickup_ready_ar')
            ->approved()
            ->active()
            ->first();

        if (!$template) {
            return ['success' => false, 'error' => 'Pickup ready template not found'];
        }

        $variables = [
            'customer_name' => $ticket->customer->name,
            'ticket_number' => $ticket->ticket_number,
            'device_info' => $ticket->brand->name . ' ' . $ticket->device_model,
            'business_name' => config('whatsapp.business_name'),
            'business_address' => config('whatsapp.business_address'),
            'business_hours' => config('whatsapp.business_hours'),
            'business_phone' => config('whatsapp.business_phone'),
        ];

        return $this->sendTemplate(
            $template,
            $ticket->customer->phone_number,
            $variables,
            [
                'trigger_type' => 'auto',
                'trigger_source' => 'system',
                'business_event' => 'pickup_ready',
                'repair_ticket_id' => $ticket->id,
            ]
        );
    }

    /**
     * Check if template can be sent to customer
     */
    protected function canSendToCustomer(
        WhatsAppMessageTemplate $template,
        WhatsAppCustomerPreference $preferences,
        array $context
    ): bool {
        // Check if customer opted out
        if (!$preferences->canReceiveWhatsApp()) {
            return false;
        }

        // Check if template is blocked
        if ($preferences->isTemplateBlocked($template->id)) {
            return false;
        }

        // Check message type preferences
        $businessEvent = $context['business_event'] ?? 'general';
        if (!$preferences->canReceiveMessageType($businessEvent)) {
            return false;
        }

        // Check quiet hours
        if ($preferences->isQuietHours()) {
            // Allow urgent messages during quiet hours
            $urgentEvents = ['status_update', 'pickup_ready', 'appointment'];
            if (!in_array($businessEvent, $urgentEvents)) {
                return false;
            }
        }

        // Check daily limits
        if ($preferences->isDailyLimitReached()) {
            return false;
        }

        // Check marketing limits
        if ($template->category->whatsapp_category === 'MARKETING') {
            if ($preferences->isWeeklyMarketingLimitReached()) {
                return false;
            }
        }

        // Check preferred days
        if (!$preferences->isPreferredDay()) {
            // Allow urgent messages on non-preferred days
            $urgentEvents = ['status_update', 'pickup_ready', 'appointment'];
            if (!in_array($businessEvent, $urgentEvents)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Log template usage
     */
    protected function logTemplateUsage(
        WhatsAppMessageTemplate $template,
        string $customerPhone,
        array $variables,
        array $context,
        array $result
    ): WhatsAppTemplateUsage {
        $customer = Customer::where('phone_number', 'like', '%' . substr($customerPhone, -9))->first();
        
        return WhatsAppTemplateUsage::create([
            'template_id' => $template->id,
            'customer_phone' => $customerPhone,
            'customer_id' => $customer?->id,
            'trigger_type' => $context['trigger_type'] ?? 'manual',
            'trigger_source' => $context['trigger_source'] ?? 'api',
            'triggered_by' => $context['triggered_by'] ?? null,
            'variables_used' => $variables,
            'final_message' => $this->renderFinalMessage($template, $variables),
            'status' => $result['success'] ? 'sent' : 'failed',
            'whatsapp_message_id' => $result['message_id'] ?? null,
            'sent_at' => now(),
            'repair_ticket_id' => $context['repair_ticket_id'] ?? null,
            'business_event' => $context['business_event'] ?? null,
            'error_message' => $result['success'] ? null : $result['error'],
        ]);
    }

    /**
     * Render final message with variables
     */
    protected function renderFinalMessage(WhatsAppMessageTemplate $template, array $variables): string
    {
        $rendered = $template->render($variables);
        
        $message = '';
        if ($rendered['header']) {
            $message .= $rendered['header'] . "\n\n";
        }
        $message .= $rendered['body'];
        if ($rendered['footer']) {
            $message .= "\n\n" . $rendered['footer'];
        }
        
        return $message;
    }

    /**
     * Submit template to WhatsApp for approval
     */
    public function submitToWhatsApp(WhatsAppMessageTemplate $template): array
    {
        try {
            $payload = [
                'name' => $template->whatsapp_name,
                'language' => $template->language,
                'category' => strtolower($template->category->whatsapp_category),
                'components' => $this->buildTemplateComponents($template),
            ];

            $response = Http::withToken(config('whatsapp.access_token'))
                ->post(config('whatsapp.base_url') . '/' . config('whatsapp.api_version') . '/' . config('whatsapp.business_account_id') . '/message_templates', $payload);

            if ($response->successful()) {
                $data = $response->json();
                $template->update([
                    'whatsapp_template_id' => $data['id'],
                    'status' => 'PENDING',
                ]);

                return ['success' => true, 'template_id' => $data['id']];
            } else {
                $error = $response->json()['error']['message'] ?? 'Unknown error';
                return ['success' => false, 'error' => $error];
            }

        } catch (\Exception $e) {
            Log::error('Failed to submit template to WhatsApp', [
                'template_id' => $template->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Build template components for WhatsApp API
     */
    protected function buildTemplateComponents(WhatsAppMessageTemplate $template): array
    {
        $components = [];

        // Header component
        if ($template->header_text) {
            $components[] = [
                'type' => 'HEADER',
                'format' => 'TEXT',
                'text' => $template->header_text,
            ];
        }

        // Body component (required)
        $components[] = [
            'type' => 'BODY',
            'text' => $template->body_text,
        ];

        // Footer component
        if ($template->footer_text) {
            $components[] = [
                'type' => 'FOOTER',
                'text' => $template->footer_text,
            ];
        }

        // Buttons component
        if ($template->buttons) {
            $buttons = [];
            foreach ($template->buttons as $button) {
                $buttons[] = [
                    'type' => $button['type'] ?? 'QUICK_REPLY',
                    'text' => $button['text'],
                ];
            }
            
            $components[] = [
                'type' => 'BUTTONS',
                'buttons' => $buttons,
            ];
        }

        return $components;
    }

    /**
     * Get template analytics
     */
    public function getTemplateAnalytics(int $templateId, array $dateRange = null): array
    {
        return WhatsAppTemplateUsage::getTemplateAnalytics($templateId, $dateRange);
    }

    /**
     * Get category analytics
     */
    public function getCategoryAnalytics(int $categoryId, array $dateRange = null): array
    {
        $templates = WhatsAppMessageTemplate::where('category_id', $categoryId)->pluck('id');
        
        $totalUsage = WhatsAppTemplateUsage::whereIn('template_id', $templates);
        if ($dateRange) {
            $totalUsage->whereBetween('created_at', $dateRange);
        }

        $total = $totalUsage->count();
        $delivered = $totalUsage->clone()->delivered()->count();
        $failed = $totalUsage->clone()->failed()->count();
        $withResponse = $totalUsage->clone()->withResponse()->count();

        return [
            'total_templates' => $templates->count(),
            'total_sent' => $total,
            'delivered' => $delivered,
            'failed' => $failed,
            'delivery_rate' => $total > 0 ? round(($delivered / $total) * 100, 2) : 0,
            'response_rate' => $delivered > 0 ? round(($withResponse / $delivered) * 100, 2) : 0,
        ];
    }
}
