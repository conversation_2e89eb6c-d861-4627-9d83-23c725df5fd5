<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PosSession extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'session_number',
        'terminal_id',
        'user_id',
        'started_at',
        'ended_at',
        'opening_cash',
        'closing_cash',
        'expected_cash',
        'cash_difference',
        'transactions_count',
        'total_sales',
        'total_cash',
        'total_card',
        'total_other',
        'payment_summary',
        'status',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'opening_cash' => 'decimal:2',
        'closing_cash' => 'decimal:2',
        'expected_cash' => 'decimal:2',
        'cash_difference' => 'decimal:2',
        'total_sales' => 'decimal:2',
        'total_cash' => 'decimal:2',
        'total_card' => 'decimal:2',
        'total_other' => 'decimal:2',
        'payment_summary' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the terminal that owns this session.
     */
    public function terminal(): BelongsTo
    {
        return $this->belongsTo(PosTerminal::class, 'terminal_id');
    }

    /**
     * Get the user who opened this session.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all transactions for this session.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(PosTransaction::class, 'terminal_id', 'terminal_id')
            ->whereBetween('transaction_date', [$this->started_at, $this->ended_at ?? now()]);
    }

    /**
     * Generate unique session number.
     */
    public static function generateSessionNumber(): string
    {
        $prefix = 'SES';
        $date = now()->format('Ymd');

        $lastSession = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = 1;
        if ($lastSession) {
            $lastNumber = substr($lastSession->session_number, -4);
            $sequence = intval($lastNumber) + 1;
        }

        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Update session totals based on transactions.
     */
    public function updateTotals(): void
    {
        $transactions = $this->transactions()->where('status', 'completed')->get();
        
        $this->transactions_count = $transactions->count();
        $this->total_sales = $transactions->sum('total_amount');

        // Calculate payment method totals
        $paymentSummary = [];
        $totalCash = 0;
        $totalCard = 0;
        $totalOther = 0;

        foreach ($transactions as $transaction) {
            foreach ($transaction->payments as $payment) {
                if ($payment->status !== 'completed') continue;

                $method = $payment->payment_method;
                if (!isset($paymentSummary[$method])) {
                    $paymentSummary[$method] = [
                        'count' => 0,
                        'total' => 0
                    ];
                }
                $paymentSummary[$method]['count']++;
                $paymentSummary[$method]['total'] += $payment->amount;

                // Categorize payment methods
                switch ($method) {
                    case 'cash':
                        $totalCash += $payment->amount;
                        break;
                    case 'card':
                        $totalCard += $payment->amount;
                        break;
                    default:
                        $totalOther += $payment->amount;
                        break;
                }
            }
        }

        $this->update([
            'transactions_count' => $this->transactions_count,
            'total_sales' => $this->total_sales,
            'total_cash' => $totalCash,
            'total_card' => $totalCard,
            'total_other' => $totalOther,
            'payment_summary' => $paymentSummary
        ]);
    }

    /**
     * Get session duration in minutes.
     */
    public function getDurationAttribute(): int
    {
        $endTime = $this->ended_at ?? now();
        return $this->started_at->diffInMinutes($endTime);
    }

    /**
     * Get session duration formatted.
     */
    public function getDurationFormattedAttribute(): string
    {
        $duration = $this->duration;
        $hours = intval($duration / 60);
        $minutes = $duration % 60;

        if ($hours > 0) {
            return "{$hours}h {$minutes}m";
        }

        return "{$minutes}m";
    }

    /**
     * Get average transaction value.
     */
    public function getAverageTransactionValueAttribute(): float
    {
        return $this->transactions_count > 0 ? $this->total_sales / $this->transactions_count : 0;
    }

    /**
     * Get transactions per hour.
     */
    public function getTransactionsPerHourAttribute(): float
    {
        $hours = $this->duration / 60;
        return $hours > 0 ? $this->transactions_count / $hours : 0;
    }

    /**
     * Get sales per hour.
     */
    public function getSalesPerHourAttribute(): float
    {
        $hours = $this->duration / 60;
        return $hours > 0 ? $this->total_sales / $hours : 0;
    }

    /**
     * Check if session has cash discrepancy.
     */
    public function hasCashDiscrepancy(): bool
    {
        return abs($this->cash_difference ?? 0) > 0.01; // Allow for 1 cent rounding
    }

    /**
     * Get cash discrepancy status.
     */
    public function getCashDiscrepancyStatusAttribute(): string
    {
        if (!$this->hasCashDiscrepancy()) {
            return app()->getLocale() === 'ar' ? 'متطابق' : 'Balanced';
        }

        if ($this->cash_difference > 0) {
            return app()->getLocale() === 'ar' ? 'زيادة' : 'Over';
        }

        return app()->getLocale() === 'ar' ? 'نقص' : 'Short';
    }

    /**
     * Get cash discrepancy color for UI.
     */
    public function getCashDiscrepancyColorAttribute(): string
    {
        if (!$this->hasCashDiscrepancy()) {
            return 'success';
        }

        return 'warning';
    }

    /**
     * Get status display.
     */
    public function getStatusDisplayAttribute(): string
    {
        $statuses = [
            'active' => app()->getLocale() === 'ar' ? 'نشط' : 'Active',
            'closed' => app()->getLocale() === 'ar' ? 'مغلق' : 'Closed',
            'suspended' => app()->getLocale() === 'ar' ? 'معلق' : 'Suspended',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'active' => 'success',
            'closed' => 'secondary',
            'suspended' => 'warning',
            default => 'secondary',
        };
    }

    /**
     * Suspend the session.
     */
    public function suspend(string $reason = null): void
    {
        $notes = $this->notes;
        if ($reason) {
            $notes .= ($notes ? "\n" : "") . "Suspended: {$reason}";
        }

        $this->update([
            'status' => 'suspended',
            'notes' => $notes
        ]);
    }

    /**
     * Resume the session.
     */
    public function resume(): void
    {
        if ($this->status !== 'suspended') {
            throw new \Exception('Can only resume suspended sessions');
        }

        $this->update(['status' => 'active']);
    }

    /**
     * Close the session.
     */
    public function close(float $closingCash, array $paymentSummary = []): void
    {
        if ($this->status === 'closed') {
            throw new \Exception('Session is already closed');
        }

        $this->updateTotals();

        $expectedCash = $this->opening_cash + $this->total_cash;
        $cashDifference = $closingCash - $expectedCash;

        $this->update([
            'ended_at' => now(),
            'closing_cash' => $closingCash,
            'expected_cash' => $expectedCash,
            'cash_difference' => $cashDifference,
            'payment_summary' => array_merge($this->payment_summary ?? [], $paymentSummary),
            'status' => 'closed'
        ]);
    }

    /**
     * Get session summary for reporting.
     */
    public function getSummary(): array
    {
        $this->updateTotals();

        return [
            'session_info' => [
                'session_number' => $this->session_number,
                'terminal' => $this->terminal->terminal_name,
                'user' => $this->user->name,
                'started_at' => $this->started_at,
                'ended_at' => $this->ended_at,
                'duration' => $this->duration_formatted,
                'status' => $this->status_display
            ],
            'sales_summary' => [
                'transactions_count' => $this->transactions_count,
                'total_sales' => $this->total_sales,
                'average_transaction' => $this->average_transaction_value,
                'transactions_per_hour' => round($this->transactions_per_hour, 1),
                'sales_per_hour' => $this->sales_per_hour
            ],
            'cash_summary' => [
                'opening_cash' => $this->opening_cash,
                'closing_cash' => $this->closing_cash,
                'expected_cash' => $this->expected_cash,
                'cash_difference' => $this->cash_difference,
                'discrepancy_status' => $this->cash_discrepancy_status
            ],
            'payment_breakdown' => [
                'cash' => $this->total_cash,
                'card' => $this->total_card,
                'other' => $this->total_other,
                'detailed' => $this->payment_summary
            ]
        ];
    }

    /**
     * Scope for active sessions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for closed sessions.
     */
    public function scopeClosed($query)
    {
        return $query->where('status', 'closed');
    }

    /**
     * Scope for today's sessions.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('started_at', today());
    }

    /**
     * Scope for sessions by terminal.
     */
    public function scopeByTerminal($query, $terminalId)
    {
        return $query->where('terminal_id', $terminalId);
    }

    /**
     * Scope for sessions by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}
