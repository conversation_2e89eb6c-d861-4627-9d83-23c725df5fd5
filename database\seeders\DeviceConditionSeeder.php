<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class DeviceConditionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $conditions = [
            [
                'name' => 'لا يعمل',
                'description' => 'الجهاز لا يعمل أو لا يستجيب',
                'sort_order' => 1,
            ],
            [
                'name' => 'شاشة مكسورة',
                'description' => 'شاشة العرض مكسورة أو تالفة',
                'sort_order' => 2,
            ],
            [
                'name' => 'تلف بالماء',
                'description' => 'الجهاز تعرض للماء أو السوائل',
                'sort_order' => 3,
            ],
            [
                'name' => 'مشاكل البطارية',
                'description' => 'البطارية لا تشحن أو تفرغ بسرعة',
                'sort_order' => 4,
            ],
            [
                'name' => 'مشاكل البرمجيات',
                'description' => 'مشاكل في نظام التشغيل أو البرامج',
                'sort_order' => 5,
            ],
            [
                'name' => 'تلف جسدي',
                'description' => 'تلف جسدي في الهيكل أو الأزرار أو المنافذ',
                'sort_order' => 6,
            ],
            [
                'name' => 'سخونة زائدة',
                'description' => 'الجهاز يسخن بشكل مفرط أثناء الاستخدام',
                'sort_order' => 7,
            ],
            [
                'name' => 'مشاكل الصوت',
                'description' => 'مشاكل في السماعات أو الميكروفون أو منفذ السماعات',
                'sort_order' => 8,
            ],
            [
                'name' => 'مشاكل الكاميرا',
                'description' => 'الكاميرا لا تعمل أو تنتج صور رديئة الجودة',
                'sort_order' => 9,
            ],
            [
                'name' => 'مشاكل الاتصال',
                'description' => 'مشاكل في الواي فاي أو البلوتوث أو الشبكة الخلوية',
                'sort_order' => 10,
            ],
            [
                'name' => 'يعمل - صيانة وقائية',
                'description' => 'الجهاز يعمل لكن يحتاج تنظيف أو صيانة',
                'sort_order' => 11,
            ],
        ];

        foreach ($conditions as $condition) {
            DB::table('device_conditions')->insert([
                'name' => $condition['name'],
                'slug' => Str::slug($condition['name']),
                'description' => $condition['description'],
                'sort_order' => $condition['sort_order'],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
