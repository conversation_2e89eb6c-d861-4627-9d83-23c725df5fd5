<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_contacts', function (Blueprint $table) {
            $table->id();
            
            // Contact identification
            $table->string('phone_number', 20)->unique(); // WhatsApp phone number
            $table->string('whatsapp_id')->nullable()->unique(); // WhatsApp user ID
            $table->string('display_name')->nullable(); // Name as shown in WhatsApp
            $table->string('profile_name')->nullable(); // WhatsApp profile name
            
            // Contact information
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('email')->nullable();
            $table->string('language', 10)->default('ar'); // Preferred language
            $table->string('timezone')->default('Asia/Riyadh');
            
            // WhatsApp specific data
            $table->boolean('is_whatsapp_user')->default(true); // Has WhatsApp account
            $table->boolean('is_business_account')->default(false); // WhatsApp Business account
            $table->string('profile_picture_url')->nullable();
            $table->timestamp('last_seen_at')->nullable();
            $table->string('status_message')->nullable(); // WhatsApp status
            
            // Contact status and preferences
            $table->enum('status', ['active', 'blocked', 'opted_out', 'invalid'])->default('active')->index();
            $table->boolean('can_receive_messages')->default(true);
            $table->boolean('opted_in_marketing')->default(false);
            $table->timestamp('opted_out_at')->nullable();
            $table->text('opt_out_reason')->nullable();
            
            // Message preferences
            $table->json('notification_preferences')->nullable(); // What notifications to receive
            $table->boolean('prefers_templates')->default(true); // Prefers template messages
            $table->json('preferred_message_times')->nullable(); // Preferred times to receive messages
            $table->boolean('business_hours_only')->default(false);
            
            // Relationship with system entities
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('cascade');
            $table->json('tags')->nullable(); // Contact tags for segmentation
            $table->text('notes')->nullable(); // Internal notes about contact
            
            // Communication history summary
            $table->integer('total_messages_sent')->default(0);
            $table->integer('total_messages_received')->default(0);
            $table->integer('total_templates_sent')->default(0);
            $table->timestamp('first_message_at')->nullable();
            $table->timestamp('last_message_at')->nullable();
            $table->timestamp('last_inbound_message_at')->nullable();
            $table->timestamp('last_outbound_message_at')->nullable();
            
            // Engagement metrics
            $table->decimal('response_rate', 5, 2)->default(0.00); // Percentage
            $table->integer('average_response_time_minutes')->nullable();
            $table->decimal('message_delivery_rate', 5, 2)->default(0.00);
            $table->decimal('message_read_rate', 5, 2)->default(0.00);
            
            // Quality and compliance
            $table->integer('spam_score')->default(0); // Spam detection score
            $table->boolean('is_verified')->default(false); // Phone number verified
            $table->timestamp('verified_at')->nullable();
            $table->integer('failed_delivery_count')->default(0);
            $table->timestamp('last_failed_delivery_at')->nullable();
            
            // Geographic and device information
            $table->string('country_code', 2)->nullable(); // ISO country code
            $table->string('carrier')->nullable(); // Mobile carrier
            $table->string('device_type')->nullable(); // android, iphone, etc.
            $table->string('whatsapp_version')->nullable();
            
            // Business context
            $table->json('custom_fields')->nullable(); // Custom business fields
            $table->string('source')->nullable(); // How contact was acquired
            $table->string('segment')->nullable(); // Customer segment
            $table->decimal('lifetime_value', 10, 2)->nullable(); // Customer lifetime value
            
            // Data privacy and compliance
            $table->boolean('gdpr_consent')->default(false);
            $table->timestamp('gdpr_consent_at')->nullable();
            $table->boolean('data_processing_consent')->default(true);
            $table->json('consent_history')->nullable(); // History of consent changes
            
            // System metadata
            $table->string('created_source')->nullable(); // How record was created
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->json('metadata')->nullable(); // Additional metadata
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['status', 'can_receive_messages']);
            $table->index(['customer_id', 'status']);
            $table->index(['country_code', 'status']);
            $table->index(['is_whatsapp_user', 'status']);
            $table->index(['last_message_at', 'status']);
            $table->index(['opted_in_marketing', 'status']);
            $table->index(['segment', 'status']);
            $table->index(['created_at', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_contacts');
    }
};
