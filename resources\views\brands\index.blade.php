@extends('layouts.app')

@section('title', 'الماركات')

@push('styles')
<style>
.brands-header {
    background: linear-gradient(45deg, #6f42c1, #e83e8c);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.search-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.brands-table {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    overflow: hidden;
}

.table th {
    background: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.brand-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
}

.brand-description {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-weight: 600;
    font-size: 0.875rem;
}

.status-active {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.status-inactive {
    background: linear-gradient(45deg, #6c757d, #495057);
    color: white;
}

.tickets-count {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-weight: 600;
    font-size: 0.875rem;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
}

.btn-action {
    padding: 0.375rem 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid;
    transition: all 0.2s ease;
    font-size: 0.75rem;
    font-weight: 500;
    min-width: auto;
    text-align: center;
    white-space: nowrap;
    flex-shrink: 0;
}

.btn-action:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.15);
}

.btn-action:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-action i {
    font-size: 0.75rem;
}

/* إصلاح أزرار التنقل */
.pagination {
    margin: 0;
    justify-content: center;
}

.pagination .page-link {
    color: #6f42c1;
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
    margin: 0 0.125rem;
    border-radius: 0.375rem;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination .page-link:hover {
    color: #5a2d91;
    background-color: #f8f9fc;
    border-color: #6f42c1;
}

.pagination .page-item.active .page-link {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

/* إخفاء النصوص الافتراضية وإظهار أيقونات مخصصة */
.pagination .page-link[rel="prev"]::before {
    content: "السابق";
    font-family: 'Cairo', sans-serif;
}

.pagination .page-link[rel="next"]::before {
    content: "التالي";
    font-family: 'Cairo', sans-serif;
}

.pagination .page-link[rel="prev"] svg,
.pagination .page-link[rel="next"] svg {
    display: none;
}

/* تحسين مظهر أزرار التنقل */
.pagination-wrapper {
    background: #fff;
    padding: 1rem;
    border-radius: 0.5rem;
    border-top: 1px solid #e3e6f0;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .brands-header {
        padding: 1rem;
    }

    .search-card {
        padding: 1rem;
    }

    .action-buttons {
        gap: 0.125rem;
    }

    .btn-action {
        padding: 0.25rem 0.375rem;
        font-size: 0.7rem;
    }

    .btn-action span {
        display: none !important;
    }

    .btn-action i {
        font-size: 0.7rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="brands-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">الماركات</h1>
                <p class="mb-0 opacity-75">إدارة ماركات الأجهزة المختلفة</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('brands.create') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>ماركة جديدة
                </a>
            </div>
        </div>
    </div>

    <!-- Search Form -->
    <div class="search-card">
        <h5 class="mb-3">
            <i class="fas fa-search me-2"></i>البحث في الماركات
        </h5>
        <form method="GET" action="{{ route('brands.index') }}" class="row g-3">
            <div class="col-md-8">
                <div class="input-group">
                    <input type="text"
                           class="form-control"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="البحث بالاسم أو الوصف...">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                @if(request('search'))
                    <a href="{{ route('brands.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>مسح البحث
                    </a>
                @endif
            </div>
        </form>
    </div>

    <!-- Brands Table -->
    <div class="brands-table">
        <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
            <h5 class="mb-0">
                قائمة الماركات
                <span class="badge bg-secondary">{{ $brands ? $brands->total() : 0 }} إجمالي</span>
            </h5>
        </div>

        @if($brands && $brands->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>الماركة</th>
                            <th>الحالة</th>
                            <th>عدد البطاقات</th>
                            <th>تاريخ الإنشاء</th>
                            <th style="width: 150px">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($brands as $brand)
                            <tr>
                                <td>
                                    <div class="brand-name">{{ $brand->name }}</div>
                                    @if($brand->description)
                                        <div class="brand-description">{{ $brand->description }}</div>
                                    @endif
                                </td>
                                <td>
                                    <span class="status-badge {{ $brand->is_active ? 'status-active' : 'status-inactive' }}">
                                        {{ $brand->is_active ? 'نشط' : 'غير نشط' }}
                                    </span>
                                </td>
                                <td>
                                    <span class="tickets-count">
                                        {{ $brand->repair_tickets_count ?? 0 }} بطاقة
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ $brand->created_at->format('Y-m-d') }}
                                    </small>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ route('brands.show', $brand) }}"
                                           class="btn btn-sm btn-outline-primary btn-action"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="عرض تفاصيل الماركة">
                                            <i class="fas fa-eye"></i>
                                            <span class="d-none d-lg-inline ms-1">عرض</span>
                                        </a>
                                        <a href="{{ route('brands.edit', $brand) }}"
                                           class="btn btn-sm btn-outline-secondary btn-action"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="تعديل معلومات الماركة">
                                            <i class="fas fa-edit"></i>
                                            <span class="d-none d-lg-inline ms-1">تعديل</span>
                                        </a>
                                        @if(($brand->repair_tickets_count ?? 0) == 0)
                                            <form method="POST"
                                                  action="{{ route('brands.destroy', $brand) }}"
                                                  class="d-inline"
                                                  onsubmit="return confirm('هل أنت متأكد من حذف هذه الماركة؟ لا يمكن التراجع عن هذا الإجراء.')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit"
                                                        class="btn btn-sm btn-outline-danger btn-action"
                                                        data-bs-toggle="tooltip"
                                                        data-bs-placement="top"
                                                        title="حذف الماركة نهائياً">
                                                    <i class="fas fa-trash"></i>
                                                    <span class="d-none d-lg-inline ms-1">حذف</span>
                                                </button>
                                            </form>
                                        @else
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-secondary btn-action"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="لا يمكن حذف الماركة لوجود {{ $brand->repair_tickets_count }} بطاقة إصلاح مرتبطة بها"
                                                    disabled>
                                                <i class="fas fa-lock"></i>
                                                <span class="d-none d-lg-inline ms-1">محمي</span>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="empty-state">
                <i class="fas fa-tags"></i>
                <h4>لا توجد ماركات</h4>
                <p>
                    @if(request('search'))
                        لا توجد ماركات تطابق معايير البحث المحددة.
                    @else
                        ابدأ بإضافة أول ماركة للأجهزة.
                    @endif
                </p>
                @if(!request('search'))
                    <a href="{{ route('brands.create') }}" class="btn btn-success">
                        <i class="fas fa-plus-circle me-2"></i>إضافة أول ماركة
                    </a>
                @else
                    <a href="{{ route('brands.index') }}" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>عرض جميع الماركات
                    </a>
                @endif
            </div>
        @endif

        @if($brands && $brands->hasPages())
            <div class="pagination-wrapper">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="pagination-info">
                        <small class="text-muted">
                            عرض {{ $brands->firstItem() }} إلى {{ $brands->lastItem() }}
                            من أصل {{ $brands->total() }} نتيجة
                        </small>
                    </div>
                    <div class="pagination-links">
                        {{ $brands->withQueryString()->links('custom-pagination') }}
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // تحسين تجربة المستخدم للأزرار
    document.querySelectorAll('.btn-action').forEach(function(button) {
        button.addEventListener('mouseenter', function() {
            if (!this.disabled) {
                this.style.transform = 'translateY(-2px)';
            }
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // تأكيد الحذف مع تفاصيل أكثر
    document.querySelectorAll('form[action*="destroy"]').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const brandName = this.closest('tr').querySelector('.brand-name').textContent;

            if (confirm(`هل أنت متأكد من حذف الماركة "${brandName}"؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه.`)) {
                this.submit();
            }
        });
    });
});
</script>
@endpush

@endsection
