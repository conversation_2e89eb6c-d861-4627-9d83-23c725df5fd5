#!/bin/bash

echo "========================================"
echo "NJ Repair Shop - Test Suite Runner"
echo "========================================"
echo

# Check if vendor directory exists
if [ ! -d "vendor" ]; then
    echo "Error: Vendor directory not found. Please run 'composer install' first."
    exit 1
fi

# Check if .env.testing exists, create if not
if [ ! -f ".env.testing" ]; then
    echo "Creating .env.testing file..."
    cp .env .env.testing
    echo "APP_ENV=testing" >> .env.testing
    echo "DB_CONNECTION=sqlite" >> .env.testing
    echo "DB_DATABASE=:memory:" >> .env.testing
    echo
fi

echo "Setting up test environment..."
php artisan config:clear --env=testing
php artisan cache:clear --env=testing

echo
echo "========================================"
echo "Running Unit Tests"
echo "========================================"
php artisan test --testsuite=Unit --stop-on-failure

if [ $? -ne 0 ]; then
    echo
    echo "Unit tests failed! Stopping execution."
    exit 1
fi

echo
echo "========================================"
echo "Running Feature Tests"
echo "========================================"
php artisan test --testsuite=Feature --stop-on-failure

if [ $? -ne 0 ]; then
    echo
    echo "Feature tests failed! Stopping execution."
    exit 1
fi

echo
echo "========================================"
echo "All Tests Completed Successfully!"
echo "========================================"
echo
echo "Test Summary:"
php artisan test --testsuite=All --coverage-text

echo
echo "Tests completed successfully!"
