<?php

namespace App\Services;

use App\Models\WhatsAppMessage;
use App\Models\WhatsAppSession;
use App\Models\Customer;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class WhatsAppService
{
    protected $baseUrl;
    protected $accessToken;
    protected $phoneNumberId;
    protected $apiVersion;

    public function __construct()
    {
        $this->baseUrl = config('whatsapp.base_url');
        $this->accessToken = config('whatsapp.access_token');
        $this->phoneNumberId = config('whatsapp.phone_number_id');
        $this->apiVersion = config('whatsapp.api_version');
    }

    /**
     * Send a text message
     */
    public function sendTextMessage(string $to, string $message, string $messageId = null): array
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $this->formatPhoneNumber($to),
            'type' => 'text',
            'text' => [
                'body' => $message
            ]
        ];

        if ($messageId) {
            $payload['context'] = ['message_id' => $messageId];
        }

        return $this->sendMessage($payload, $to, 'text', $message);
    }

    /**
     * Send an interactive message with buttons
     */
    public function sendInteractiveMessage(string $to, string $text, array $buttons, string $header = null): array
    {
        $interactive = [
            'type' => 'button',
            'body' => ['text' => $text],
            'action' => [
                'buttons' => array_map(function ($button, $index) {
                    return [
                        'type' => 'reply',
                        'reply' => [
                            'id' => $button['id'] ?? "btn_{$index}",
                            'title' => substr($button['title'], 0, 20) // WhatsApp limit
                        ]
                    ];
                }, $buttons, array_keys($buttons))
            ]
        ];

        if ($header) {
            $interactive['header'] = ['type' => 'text', 'text' => $header];
        }

        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $this->formatPhoneNumber($to),
            'type' => 'interactive',
            'interactive' => $interactive
        ];

        return $this->sendMessage($payload, $to, 'interactive', $text);
    }

    /**
     * Send a list message
     */
    public function sendListMessage(string $to, string $text, array $sections, string $buttonText = 'اختر'): array
    {
        $interactive = [
            'type' => 'list',
            'body' => ['text' => $text],
            'action' => [
                'button' => $buttonText,
                'sections' => $sections
            ]
        ];

        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $this->formatPhoneNumber($to),
            'type' => 'interactive',
            'interactive' => $interactive
        ];

        return $this->sendMessage($payload, $to, 'interactive', $text);
    }

    /**
     * Send location message
     */
    public function sendLocationMessage(string $to, float $latitude, float $longitude, string $name = null, string $address = null): array
    {
        $location = [
            'latitude' => $latitude,
            'longitude' => $longitude
        ];

        if ($name) $location['name'] = $name;
        if ($address) $location['address'] = $address;

        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $this->formatPhoneNumber($to),
            'type' => 'location',
            'location' => $location
        ];

        return $this->sendMessage($payload, $to, 'location', "Location: {$name}");
    }

    /**
     * Send template message
     */
    public function sendTemplateMessage(string $to, string $templateName, array $parameters = [], string $language = 'ar'): array
    {
        $template = [
            'name' => $templateName,
            'language' => ['code' => $language]
        ];

        if (!empty($parameters)) {
            $template['components'] = [
                [
                    'type' => 'body',
                    'parameters' => array_map(function ($param) {
                        return ['type' => 'text', 'text' => $param];
                    }, $parameters)
                ]
            ];
        }

        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $this->formatPhoneNumber($to),
            'type' => 'template',
            'template' => $template
        ];

        return $this->sendMessage($payload, $to, 'template', "Template: {$templateName}");
    }

    /**
     * Send media message
     */
    public function sendMediaMessage(string $to, string $mediaType, string $mediaUrl, string $caption = null): array
    {
        $media = ['link' => $mediaUrl];
        if ($caption) $media['caption'] = $caption;

        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $this->formatPhoneNumber($to),
            'type' => $mediaType,
            $mediaType => $media
        ];

        return $this->sendMessage($payload, $to, $mediaType, $caption ?? "Media: {$mediaType}");
    }

    /**
     * Mark message as read
     */
    public function markAsRead(string $messageId): bool
    {
        try {
            $response = Http::withToken($this->accessToken)
                ->post("{$this->baseUrl}/{$this->apiVersion}/{$this->phoneNumberId}/messages", [
                    'messaging_product' => 'whatsapp',
                    'status' => 'read',
                    'message_id' => $messageId
                ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Failed to mark WhatsApp message as read', [
                'message_id' => $messageId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get media URL
     */
    public function getMediaUrl(string $mediaId): ?string
    {
        try {
            $response = Http::withToken($this->accessToken)
                ->get("{$this->baseUrl}/{$this->apiVersion}/{$mediaId}");

            if ($response->successful()) {
                $data = $response->json();
                return $data['url'] ?? null;
            }
        } catch (\Exception $e) {
            Log::error('Failed to get WhatsApp media URL', [
                'media_id' => $mediaId,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Download media file
     */
    public function downloadMedia(string $mediaUrl): ?string
    {
        try {
            $response = Http::withToken($this->accessToken)
                ->get($mediaUrl);

            if ($response->successful()) {
                return $response->body();
            }
        } catch (\Exception $e) {
            Log::error('Failed to download WhatsApp media', [
                'media_url' => $mediaUrl,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Send message via API
     */
    protected function sendMessage(array $payload, string $to, string $type, string $content): array
    {
        try {
            // Check rate limiting
            $session = WhatsAppSession::getOrCreateForCustomer($to);
            if ($session->isRateLimited()) {
                throw new \Exception('Rate limit exceeded for customer');
            }

            $response = Http::withToken($this->accessToken)
                ->timeout(30)
                ->post("{$this->baseUrl}/{$this->apiVersion}/{$this->phoneNumberId}/messages", $payload);

            $responseData = $response->json();

            if ($response->successful() && isset($responseData['messages'][0]['id'])) {
                $whatsappMessageId = $responseData['messages'][0]['id'];

                // Log outbound message
                $message = WhatsAppMessage::create([
                    'whatsapp_message_id' => $whatsappMessageId,
                    'conversation_id' => $this->generateConversationId($to),
                    'customer_phone' => $to,
                    'customer_id' => $session->customer_id,
                    'direction' => 'outbound',
                    'message_type' => $type,
                    'message_content' => $content,
                    'message_data' => $payload,
                    'status' => 'sent',
                    'session_id' => $session->session_id,
                    'language' => 'ar',
                    'whatsapp_timestamp' => now(),
                ]);

                $session->incrementMessageCount();

                Log::info('WhatsApp message sent successfully', [
                    'to' => $to,
                    'type' => $type,
                    'message_id' => $whatsappMessageId
                ]);

                return [
                    'success' => true,
                    'message_id' => $whatsappMessageId,
                    'data' => $responseData
                ];
            } else {
                throw new \Exception('API response error: ' . json_encode($responseData));
            }

        } catch (\Exception $e) {
            Log::error('Failed to send WhatsApp message', [
                'to' => $to,
                'type' => $type,
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Format phone number for WhatsApp API
     */
    protected function formatPhoneNumber(string $phone): string
    {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // Add country code if missing
        if (strlen($phone) === 10 && substr($phone, 0, 1) === '0') {
            $phone = '966' . substr($phone, 1);
        } elseif (strlen($phone) === 9) {
            $phone = '966' . $phone;
        }

        return $phone;
    }

    /**
     * Generate conversation ID
     */
    protected function generateConversationId(string $customerPhone): string
    {
        return 'conv_' . md5($customerPhone . date('Y-m-d'));
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, config('whatsapp.app_secret'));
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Get business profile
     */
    public function getBusinessProfile(): ?array
    {
        try {
            $response = Http::withToken($this->accessToken)
                ->get("{$this->baseUrl}/{$this->apiVersion}/{$this->phoneNumberId}/whatsapp_business_profile");

            if ($response->successful()) {
                return $response->json()['data'][0] ?? null;
            }
        } catch (\Exception $e) {
            Log::error('Failed to get WhatsApp business profile', [
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Update business profile
     */
    public function updateBusinessProfile(array $data): bool
    {
        try {
            $response = Http::withToken($this->accessToken)
                ->post("{$this->baseUrl}/{$this->apiVersion}/{$this->phoneNumberId}/whatsapp_business_profile", $data);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Failed to update WhatsApp business profile', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            return false;
        }
    }

    /**
     * Optimize business profile for better customer experience
     */
    public function optimizeBusinessProfile(): array
    {
        $profileData = [
            'about' => config('whatsapp.business_name') . ' - خدمة عملاء 24/7 عبر واتساب',
            'address' => config('whatsapp.business_address'),
            'description' => 'ورشة متخصصة في إصلاح جميع أنواع الأجهزة الإلكترونية. نقدم خدمات إصلاح الهواتف الذكية وأجهزة الحاسوب والتابلت مع ضمان الجودة وسرعة الإنجاز.',
            'email' => config('mail.from.address'),
            'websites' => [config('whatsapp.business_website')],
            'vertical' => 'OTHER',
            'profile_picture_url' => asset('images/logo.png'),
        ];

        $success = $this->updateBusinessProfile($profileData);

        return [
            'success' => $success,
            'profile_data' => $profileData,
            'message' => $success ? 'تم تحديث الملف التجاري بنجاح' : 'فشل في تحديث الملف التجاري'
        ];
    }

    /**
     * Set business hours
     */
    public function setBusinessHours(array $hours): bool
    {
        try {
            $businessHours = [];

            foreach ($hours as $day => $schedule) {
                if ($schedule['is_open']) {
                    $businessHours[] = [
                        'day_of_week' => strtoupper($day),
                        'open_time' => $schedule['open_time'],
                        'close_time' => $schedule['close_time'],
                    ];
                }
            }

            $response = Http::withToken($this->accessToken)
                ->post("{$this->baseUrl}/{$this->apiVersion}/{$this->phoneNumberId}/whatsapp_business_profile", [
                    'business_hours' => $businessHours
                ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Failed to set business hours', [
                'error' => $e->getMessage(),
                'hours' => $hours
            ]);
            return false;
        }
    }

    /**
     * Get message templates from WhatsApp
     */
    public function getMessageTemplates(): array
    {
        try {
            $response = Http::withToken($this->accessToken)
                ->get("{$this->baseUrl}/{$this->apiVersion}/" . config('whatsapp.business_account_id') . "/message_templates");

            if ($response->successful()) {
                return $response->json()['data'] ?? [];
            }

            return [];
        } catch (\Exception $e) {
            Log::error('Failed to get message templates', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Create message template
     */
    public function createMessageTemplate(array $templateData): array
    {
        try {
            $response = Http::withToken($this->accessToken)
                ->post("{$this->baseUrl}/{$this->apiVersion}/" . config('whatsapp.business_account_id') . "/message_templates", $templateData);

            if ($response->successful()) {
                $data = $response->json();
                return [
                    'success' => true,
                    'template_id' => $data['id'],
                    'status' => $data['status'] ?? 'PENDING'
                ];
            } else {
                $error = $response->json()['error']['message'] ?? 'Unknown error';
                return [
                    'success' => false,
                    'error' => $error
                ];
            }
        } catch (\Exception $e) {
            Log::error('Failed to create message template', [
                'error' => $e->getMessage(),
                'template_data' => $templateData
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete message template
     */
    public function deleteMessageTemplate(string $templateName): bool
    {
        try {
            $response = Http::withToken($this->accessToken)
                ->delete("{$this->baseUrl}/{$this->apiVersion}/" . config('whatsapp.business_account_id') . "/message_templates", [
                    'name' => $templateName
                ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Failed to delete message template', [
                'error' => $e->getMessage(),
                'template_name' => $templateName
            ]);
            return false;
        }
    }

    /**
     * Get analytics data
     */
    public function getAnalytics(string $startDate, string $endDate): array
    {
        try {
            $response = Http::withToken($this->accessToken)
                ->get("{$this->baseUrl}/{$this->apiVersion}/" . config('whatsapp.business_account_id') . "/analytics", [
                    'start' => $startDate,
                    'end' => $endDate,
                    'granularity' => 'DAY',
                    'metric_types' => 'SENT,DELIVERED,READ'
                ]);

            if ($response->successful()) {
                return $response->json()['data'] ?? [];
            }

            return [];
        } catch (\Exception $e) {
            Log::error('Failed to get WhatsApp analytics', [
                'error' => $e->getMessage(),
                'start_date' => $startDate,
                'end_date' => $endDate
            ]);
            return [];
        }
    }

    /**
     * Set webhook configuration
     */
    public function setWebhookConfiguration(array $config): bool
    {
        try {
            $response = Http::withToken($this->accessToken)
                ->post("{$this->baseUrl}/{$this->apiVersion}/" . config('whatsapp.app_id') . "/subscriptions", $config);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Failed to set webhook configuration', [
                'error' => $e->getMessage(),
                'config' => $config
            ]);
            return false;
        }
    }
}
