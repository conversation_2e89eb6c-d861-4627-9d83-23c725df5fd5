<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_templates', function (Blueprint $table) {
            $table->id();
            
            // Template identification
            $table->string('name')->unique(); // Template name in WhatsApp
            $table->string('display_name'); // Human-readable name
            $table->string('category'); // UTILITY, MARKETING, AUTHENTICATION
            $table->string('language', 10)->default('ar'); // Language code
            $table->string('namespace')->nullable(); // WhatsApp namespace
            
            // Template content
            $table->json('components'); // Template components (header, body, footer, buttons)
            $table->text('preview_text')->nullable(); // Preview of the template
            $table->json('example_data')->nullable(); // Example parameter values
            
            // Approval status
            $table->enum('status', ['pending', 'approved', 'rejected', 'disabled'])->default('pending')->index();
            $table->text('rejection_reason')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('submitted_at')->nullable();
            
            // Usage tracking
            $table->integer('usage_count')->default(0);
            $table->timestamp('last_used_at')->nullable();
            $table->decimal('success_rate', 5, 2)->default(0.00); // Percentage
            
            // Business context
            $table->string('business_use_case')->nullable(); // repair_status, payment_reminder, etc.
            $table->json('parameter_mapping')->nullable(); // How to map business data to template parameters
            $table->boolean('is_system_template')->default(false); // System vs custom template
            
            // Rate limiting
            $table->integer('daily_limit')->nullable(); // Max sends per day
            $table->integer('hourly_limit')->nullable(); // Max sends per hour
            $table->integer('daily_usage')->default(0);
            $table->integer('hourly_usage')->default(0);
            $table->timestamp('usage_reset_at')->nullable();
            
            // Quality and performance
            $table->decimal('delivery_rate', 5, 2)->default(0.00);
            $table->decimal('read_rate', 5, 2)->default(0.00);
            $table->integer('total_sent')->default(0);
            $table->integer('total_delivered')->default(0);
            $table->integer('total_read')->default(0);
            $table->integer('total_failed')->default(0);
            
            // Template versioning
            $table->string('version', 10)->default('1.0');
            $table->foreignId('parent_template_id')->nullable()->constrained('whatsapp_templates')->onDelete('set null');
            $table->boolean('is_active')->default(true);
            
            // Metadata
            $table->json('metadata')->nullable(); // Additional template metadata
            $table->text('notes')->nullable(); // Internal notes
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            
            $table->timestamps();
            
            // Indexes
            $table->index(['status', 'language']);
            $table->index(['category', 'is_active']);
            $table->index(['business_use_case', 'is_active']);
            $table->index(['is_system_template', 'is_active']);
            $table->index(['usage_count', 'last_used_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_templates');
    }
};
