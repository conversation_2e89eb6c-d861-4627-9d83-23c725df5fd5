<?php $__env->startSection('title', 'إدارة المخزون'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Action buttons styling */
.d-flex.gap-1 .btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    border: none;
    transition: all 0.2s ease;
}

.d-flex.gap-1 .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.d-flex.gap-1 .btn i {
    font-size: 0.875rem;
}

/* Primary button - View */
.btn-primary.rounded-pill {
    background: linear-gradient(45deg, #4e73df, #224abe);
    color: white;
}

.btn-primary.rounded-pill:hover {
    background: linear-gradient(45deg, #224abe, #1e3a8a);
    color: white;
}

/* Success button - Edit */
.btn-success.rounded-pill {
    background: linear-gradient(45deg, #1cc88a, #17a673);
    color: white;
}

.btn-success.rounded-pill:hover {
    background: linear-gradient(45deg, #17a673, #138d5f);
    color: white;
}

/* Warning button - Adjust Stock */
.btn-warning.rounded-pill {
    background: linear-gradient(45deg, #f6c23e, #dda20a);
    color: white;
}

.btn-warning.rounded-pill:hover {
    background: linear-gradient(45deg, #dda20a, #c69500);
    color: white;
}

/* Secondary button - More options */
.btn-secondary.rounded-pill {
    background: linear-gradient(45deg, #858796, #6c757d);
    color: white;
}

.btn-secondary.rounded-pill:hover {
    background: linear-gradient(45deg, #6c757d, #5a6268);
    color: white;
}

/* Dropdown menu styling */
.dropdown-menu {
    border: 1px solid #e3e6f0;
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
    min-width: 180px;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border: none;
    background: none;
    width: 100%;
    text-align: right;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fc;
    color: #5a5c69;
}

.dropdown-item.text-danger:hover {
    background-color: #f8d7da;
    color: #721c24;
}

.dropdown-item i {
    width: 18px;
    text-align: center;
}

/* Table styling */
.table td {
    vertical-align: middle;
    padding: 1rem 0.75rem;
}

.table th {
    background-color: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

/* Badge styling */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    font-weight: 500;
}

/* Card styling */
.card {
    border: 1px solid #e3e6f0;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .d-flex.gap-1 {
        flex-wrap: wrap;
        gap: 0.25rem !important;
    }

    .d-flex.gap-1 .btn {
        width: 28px;
        height: 28px;
    }

    .d-flex.gap-1 .btn i {
        font-size: 0.75rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إدارة المخزون</h1>
            <p class="text-muted">إدارة عناصر المخزون</p>
        </div>
        <div class="d-flex gap-2">
            <a href="<?php echo e(route('inventory.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة عنصر
            </a>
            <button type="button" class="btn btn-outline-warning" onclick="showLowStockAlerts()">
                <i class="fas fa-exclamation-triangle"></i> تنبيهات المخزون المنخفض
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo App\Helpers\ArabicFormatter::formatNumber($stats['total_items']); ?></h4>
                            <small>إجمالي العناصر</small>
                        </div>
                        <i class="fas fa-boxes display-6"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo App\Helpers\ArabicFormatter::formatNumber($stats['low_stock_items']); ?></h4>
                            <small>عناصر مخزون منخفض</small>
                        </div>
                        <i class="fas fa-exclamation-triangle display-6"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo App\Helpers\ArabicFormatter::formatNumber($stats['out_of_stock_items']); ?></h4>
                            <small>عناصر نفد مخزونها</small>
                        </div>
                        <i class="fas fa-times-circle display-6"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo App\Helpers\ArabicFormatter::formatCurrency($stats['total_stock_value']); ?></h4>
                            <small>إجمالي قيمة المخزون</small>
                        </div>
                        <i class="fas fa-dollar-sign display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('inventory.index')); ?>" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">بحث</label>
                    <input type="text" id="search" name="search" class="form-control"
                           value="<?php echo e(request('search')); ?>"
                           placeholder="البحث في العناصر">
                </div>
                <div class="col-md-2">
                    <label for="category_id" class="form-label">الفئة</label>
                    <select id="category_id" name="category_id" class="form-select">
                        <option value="">الكل</option>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category->id); ?>" <?php echo e(request('category_id') == $category->id ? 'selected' : ''); ?>>
                                <?php echo e($category->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="brand_id" class="form-label">الماركة</label>
                    <select id="brand_id" name="brand_id" class="form-select">
                        <option value="">الكل</option>
                        <?php $__currentLoopData = $brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($brand->id); ?>" <?php echo e(request('brand_id') == $brand->id ? 'selected' : ''); ?>>
                                <?php echo e($brand->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="stock_status" class="form-label">الحالة</label>
                    <select id="stock_status" name="stock_status" class="form-select">
                        <option value="">الكل</option>
                        <option value="low_stock" <?php echo e(request('stock_status') === 'low_stock' ? 'selected' : ''); ?>>
                            مخزون منخفض
                        </option>
                        <option value="out_of_stock" <?php echo e(request('stock_status') === 'out_of_stock' ? 'selected' : ''); ?>>
                            نفد المخزون
                        </option>
                        <option value="overstocked" <?php echo e(request('stock_status') === 'overstocked' ? 'selected' : ''); ?>>
                            مخزون زائد
                        </option>
                        <option value="expiring_soon" <?php echo e(request('stock_status') === 'expiring_soon' ? 'selected' : ''); ?>>
                            ينتهي قريباً
                        </option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="supplier_id" class="form-label">المورد</label>
                    <select id="supplier_id" name="supplier_id" class="form-select">
                        <option value="">الكل</option>
                        <?php $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($supplier->id); ?>" <?php echo e(request('supplier_id') == $supplier->id ? 'selected' : ''); ?>>
                                <?php echo e($supplier->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block w-100" aria-label="بحث">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Items Table -->
    <div class="card">
        <div class="card-body">
            <?php if($items->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th class="text-center" style="width: 25%;">اسم العنصر</th>
                                <th class="text-center" style="width: 12%;">رمز المنتج</th>
                                <th class="text-center" style="width: 12%;">الفئة</th>
                                <th class="text-center" style="width: 15%;">المخزون الحالي</th>
                                <th class="text-center" style="width: 10%;">سعر التكلفة</th>
                                <th class="text-center" style="width: 10%;">سعر البيع</th>
                                <th class="text-center" style="width: 8%;">الحالة</th>
                                <th class="text-center" style="width: 8%;">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="align-middle">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                    <i class="fas fa-box text-white"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-bold"><?php echo e($item->name); ?></h6>
                                                <?php if($item->brand): ?>
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-tag me-1"></i><?php echo e($item->brand->name); ?>

                                                    </small>
                                                <?php endif; ?>
                                                <?php if($item->model_compatibility): ?>
                                                    <small class="text-info d-block">
                                                        <i class="fas fa-mobile-alt me-1"></i><?php echo e($item->model_compatibility); ?>

                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <code class="bg-light px-2 py-1 rounded"><?php echo e($item->sku); ?></code>
                                        <?php if($item->barcode): ?>
                                            <br><small class="text-muted mt-1 d-block">
                                                <i class="fas fa-barcode me-1"></i><?php echo e($item->barcode); ?>

                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge rounded-pill" style="background-color: <?php echo e($item->category->color ?? '#6B7280'); ?>; color: white; font-size: 0.8rem;">
                                            <?php if($item->category->icon): ?>
                                                <i class="<?php echo e($item->category->icon); ?> me-1"></i>
                                            <?php else: ?>
                                                <i class="fas fa-folder me-1"></i>
                                            <?php endif; ?>
                                            <?php echo e($item->category->name); ?>

                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <div class="d-flex flex-column align-items-center">
                                            <div class="mb-1">
                                                <span class="badge bg-info text-white fs-6 px-3 py-2">
                                                    <?php echo App\Helpers\ArabicFormatter::formatNumber($item->current_stock); ?> <?php echo e($item->unit_of_measure); ?>

                                                </span>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-arrow-down text-danger me-1"></i>أدنى: <?php echo App\Helpers\ArabicFormatter::formatNumber($item->minimum_stock); ?>
                                                <span class="mx-1">|</span>
                                                <i class="fas fa-arrow-up text-success me-1"></i>أقصى: <?php echo App\Helpers\ArabicFormatter::formatNumber($item->maximum_stock); ?>
                                            </small>
                                            <?php if($item->location): ?>
                                                <small class="text-info mt-1">
                                                    <i class="fas fa-map-marker-alt me-1"></i><?php echo e($item->location); ?>

                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="fw-bold text-dark"><?php echo App\Helpers\ArabicFormatter::formatCurrency($item->cost_price); ?></div>
                                    </td>
                                    <td class="text-center">
                                        <div class="fw-bold text-success"><?php echo App\Helpers\ArabicFormatter::formatCurrency($item->selling_price); ?></div>
                                        <?php if($item->markup_percentage > 0): ?>
                                            <small class="text-success d-block mt-1">
                                                <i class="fas fa-chart-line me-1"></i>+<?php echo App\Helpers\ArabicFormatter::formatPercentage($item->markup_percentage); ?>
                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge <?php echo e($item->stock_status_badge_class); ?> rounded-pill px-3 py-2">
                                            <?php echo e($item->stock_status_display); ?>

                                        </span>
                                        <?php if($item->isExpiringSoon()): ?>
                                            <small class="text-warning d-block mt-1">
                                                <i class="fas fa-clock me-1"></i><?php echo e($item->days_until_expiry); ?> أيام
                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1 justify-content-center">
                                            <a href="<?php echo e(route('inventory.show', $item)); ?>"
                                               class="btn btn-primary btn-sm rounded-pill"
                                               title="عرض العنصر"
                                               data-bs-toggle="tooltip">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('inventory.edit', $item)); ?>"
                                               class="btn btn-success btn-sm rounded-pill"
                                               title="تعديل العنصر"
                                               data-bs-toggle="tooltip">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?php echo e(route('inventory.adjust-stock', $item)); ?>"
                                               class="btn btn-warning btn-sm rounded-pill"
                                               title="تعديل المخزون"
                                               data-bs-toggle="tooltip">
                                                <i class="fas fa-balance-scale"></i>
                                            </a>
                                            <div class="dropdown">
                                                <button type="button"
                                                        class="btn btn-secondary btn-sm rounded-pill dropdown-toggle"
                                                        data-bs-toggle="dropdown"
                                                        title="المزيد"
                                                        aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end shadow">
                                                    <li>
                                                        <a class="dropdown-item" href="<?php echo e(route('inventory.show', $item)); ?>">
                                                            <i class="fas fa-eye text-primary me-2"></i>
                                                            عرض التفاصيل
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#">
                                                            <i class="fas fa-history text-info me-2"></i>
                                                            سجل الحركات
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#">
                                                            <i class="fas fa-copy text-secondary me-2"></i>
                                                            نسخ العنصر
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <button type="button"
                                                                class="dropdown-item text-danger"
                                                                onclick="confirmDelete('<?php echo e($item->id); ?>', '<?php echo e($item->name); ?>')">
                                                            <i class="fas fa-trash text-danger me-2"></i>
                                                            حذف العنصر
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($items->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لم يتم العثور على عناصر</h5>
                    <p class="text-muted">لا توجد عناصر مخزون حالياً</p>
                    <a href="<?php echo e(route('inventory.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة عنصر
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Low Stock Alerts Modal -->
<div class="modal fade" id="lowStockModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تنبيهات المخزون المنخفض</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="lowStockContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري التحميل</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showLowStockAlerts() {
    const modal = new bootstrap.Modal(document.getElementById('lowStockModal'));
    modal.show();

    fetch('<?php echo e(route("inventory.low-stock-alerts")); ?>')
        .then(response => response.json())
        .then(data => {
            const content = document.getElementById('lowStockContent');

            if (data.count === 0) {
                content.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-success">لا توجد عناصر مخزون منخفض</h5>
                        <p class="text-muted">جميع العناصر لديها مخزون كافي</p>
                    </div>
                `;
            } else {
                let html = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>${data.count}</strong> عنصر يحتاج إعادة طلب
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>اسم العنصر</th>
                                    <th>المخزون الحالي</th>
                                    <th>الحد الأدنى للمخزون</th>
                                    <th>كمية إعادة الطلب</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                data.items.forEach(item => {
                    html += `
                        <tr>
                            <td>
                                <strong>${item.name}</strong><br>
                                <small class="text-muted">${item.sku}</small>
                            </td>
                            <td><span class="badge bg-danger">${item.current_stock}</span></td>
                            <td>${item.minimum_stock}</td>
                            <td><strong>${item.reorder_suggestion.suggested_quantity}</strong></td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;

                content.innerHTML = html;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('lowStockContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    خطأ في تحميل البيانات
                </div>
            `;
        });
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Confirm delete function
function confirmDelete(itemId, itemName) {
    if (confirm(`هل أنت متأكد من حذف العنصر "${itemName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // Create a form to submit delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/inventory/${itemId}`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        form.appendChild(csrfToken);

        // Add method override for DELETE
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        form.appendChild(methodField);

        // Submit form
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\nj\resources\views/inventory/index.blade.php ENDPATH**/ ?>