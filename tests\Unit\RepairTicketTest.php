<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\RepairTicket;
use App\Models\Customer;
use App\Models\Brand;
use App\Models\RepairStatus;
use App\Models\DeviceCondition;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class RepairTicketTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create necessary related models
        $this->customer = Customer::factory()->create();
        $this->brand = Brand::factory()->create();
        $this->status = RepairStatus::factory()->create(['name' => 'Pending']);
        $this->condition = DeviceCondition::factory()->create();
        $this->technician = User::factory()->create();
    }

    /** @test */
    public function it_can_create_a_repair_ticket()
    {
        $ticket = RepairTicket::factory()->create([
            'customer_id' => $this->customer->id,
            'brand_id' => $this->brand->id,
            'repair_status_id' => $this->status->id,
            'device_condition_id' => $this->condition->id,
        ]);

        $this->assertInstanceOf(RepairTicket::class, $ticket);
        $this->assertDatabaseHas('repair_tickets', [
            'id' => $ticket->id,
            'customer_id' => $this->customer->id,
        ]);
    }

    /** @test */
    public function it_generates_unique_ticket_number()
    {
        $ticket1 = RepairTicket::factory()->create();
        $ticket2 = RepairTicket::factory()->create();

        $this->assertNotEquals($ticket1->ticket_number, $ticket2->ticket_number);
        $this->assertStringStartsWith('NJ', $ticket1->ticket_number);
        $this->assertStringStartsWith('NJ', $ticket2->ticket_number);
    }

    /** @test */
    public function it_generates_ticket_number_with_date_format()
    {
        $ticket = RepairTicket::factory()->create([
            'received_date' => Carbon::parse('2024-01-15')
        ]);

        $expectedPrefix = 'NJ20240115';
        $this->assertStringStartsWith($expectedPrefix, $ticket->ticket_number);
    }

    /** @test */
    public function it_belongs_to_customer()
    {
        $ticket = RepairTicket::factory()->create([
            'customer_id' => $this->customer->id
        ]);

        $this->assertInstanceOf(Customer::class, $ticket->customer);
        $this->assertEquals($this->customer->id, $ticket->customer->id);
    }

    /** @test */
    public function it_belongs_to_brand()
    {
        $ticket = RepairTicket::factory()->create([
            'brand_id' => $this->brand->id
        ]);

        $this->assertInstanceOf(Brand::class, $ticket->brand);
        $this->assertEquals($this->brand->id, $ticket->brand->id);
    }

    /** @test */
    public function it_belongs_to_repair_status()
    {
        $ticket = RepairTicket::factory()->create([
            'repair_status_id' => $this->status->id
        ]);

        $this->assertInstanceOf(RepairStatus::class, $ticket->repairStatus);
        $this->assertEquals($this->status->id, $ticket->repairStatus->id);
    }

    /** @test */
    public function it_can_be_assigned_to_technician()
    {
        $ticket = RepairTicket::factory()->create([
            'assigned_to' => $this->technician->id
        ]);

        $this->assertInstanceOf(User::class, $ticket->assignedTo);
        $this->assertEquals($this->technician->id, $ticket->assignedTo->id);
    }

    /** @test */
    public function it_can_calculate_days_since_received()
    {
        $ticket = RepairTicket::factory()->create([
            'received_date' => Carbon::now()->subDays(5)
        ]);

        $this->assertEquals(5, $ticket->daysSinceReceived());
    }

    /** @test */
    public function it_can_check_if_has_security_pattern()
    {
        $ticketWithPattern = RepairTicket::factory()->withSecurityPattern()->create();
        $ticketWithoutPattern = RepairTicket::factory()->withoutSecurityPattern()->create();

        $this->assertTrue($ticketWithPattern->hasSecurityPattern());
        $this->assertFalse($ticketWithoutPattern->hasSecurityPattern());
    }

    /** @test */
    public function it_can_get_masked_security_pattern()
    {
        $ticket = RepairTicket::factory()->create([
            'security_pattern' => '123456'
        ]);

        $masked = $ticket->getMaskedSecurityPattern();
        $this->assertEquals('1****6', $masked);
    }

    /** @test */
    public function it_returns_no_pattern_message_when_empty()
    {
        $ticket = RepairTicket::factory()->withoutSecurityPattern()->create();

        $this->assertEquals('لا يوجد', $ticket->getMaskedSecurityPattern());
    }

    /** @test */
    public function it_masks_short_patterns_completely()
    {
        $ticket = RepairTicket::factory()->create([
            'security_pattern' => '12'
        ]);

        $masked = $ticket->getMaskedSecurityPattern();
        $this->assertEquals('**', $masked);
    }

    /** @test */
    public function security_pattern_is_encrypted_in_database()
    {
        $originalPattern = '123456';
        $ticket = RepairTicket::factory()->create([
            'security_pattern' => $originalPattern
        ]);

        // The pattern should be encrypted in the database
        $rawValue = $ticket->getAttributes()['security_pattern'];
        $this->assertNotEquals($originalPattern, $rawValue);

        // But should be decrypted when accessed through the model
        $this->assertEquals($originalPattern, $ticket->security_pattern);
    }

    /** @test */
    public function it_can_determine_if_overdue()
    {
        // Create overdue ticket (more than 7 days old)
        $overdueTicket = RepairTicket::factory()->create([
            'received_date' => Carbon::now()->subDays(10),
            'repair_status_id' => RepairStatus::factory()->create(['name' => 'In Progress'])->id
        ]);

        // Create non-overdue ticket
        $recentTicket = RepairTicket::factory()->create([
            'received_date' => Carbon::now()->subDays(3),
            'repair_status_id' => RepairStatus::factory()->create(['name' => 'In Progress'])->id
        ]);

        // Create completed ticket (should not be overdue regardless of age)
        $completedTicket = RepairTicket::factory()->create([
            'received_date' => Carbon::now()->subDays(15),
            'repair_status_id' => RepairStatus::factory()->create(['name' => 'Completed'])->id
        ]);

        $this->assertTrue($overdueTicket->isOverdue());
        $this->assertFalse($recentTicket->isOverdue());
        $this->assertFalse($completedTicket->isOverdue());
    }

    /** @test */
    public function it_can_scope_by_status()
    {
        $pendingStatus = RepairStatus::factory()->create(['name' => 'Pending']);
        $completedStatus = RepairStatus::factory()->create(['name' => 'Completed']);

        RepairTicket::factory()->count(3)->create(['repair_status_id' => $pendingStatus->id]);
        RepairTicket::factory()->count(2)->create(['repair_status_id' => $completedStatus->id]);

        $pendingTickets = RepairTicket::byStatus('Pending')->get();
        $completedTickets = RepairTicket::byStatus('Completed')->get();

        $this->assertCount(3, $pendingTickets);
        $this->assertCount(2, $completedTickets);
    }

    /** @test */
    public function it_can_scope_by_brand()
    {
        $appleBrand = Brand::factory()->create(['name' => 'Apple']);
        $samsungBrand = Brand::factory()->create(['name' => 'Samsung']);

        RepairTicket::factory()->count(4)->create(['brand_id' => $appleBrand->id]);
        RepairTicket::factory()->count(2)->create(['brand_id' => $samsungBrand->id]);

        $appleTickets = RepairTicket::byBrand('Apple')->get();
        $samsungTickets = RepairTicket::byBrand('Samsung')->get();

        $this->assertCount(4, $appleTickets);
        $this->assertCount(2, $samsungTickets);
    }

    /** @test */
    public function it_can_scope_overdue_tickets()
    {
        $inProgressStatus = RepairStatus::factory()->create(['name' => 'In Progress']);
        $completedStatus = RepairStatus::factory()->create(['name' => 'Completed']);

        // Create overdue tickets
        RepairTicket::factory()->count(3)->create([
            'received_date' => Carbon::now()->subDays(10),
            'repair_status_id' => $inProgressStatus->id
        ]);

        // Create recent tickets
        RepairTicket::factory()->count(2)->create([
            'received_date' => Carbon::now()->subDays(3),
            'repair_status_id' => $inProgressStatus->id
        ]);

        // Create completed tickets (old but completed)
        RepairTicket::factory()->count(1)->create([
            'received_date' => Carbon::now()->subDays(15),
            'repair_status_id' => $completedStatus->id
        ]);

        $overdueTickets = RepairTicket::overdue()->get();
        $this->assertCount(3, $overdueTickets);
    }

    /** @test */
    public function it_can_scope_by_priority()
    {
        RepairTicket::factory()->count(2)->create(['priority' => 'high']);
        RepairTicket::factory()->count(3)->create(['priority' => 'medium']);
        RepairTicket::factory()->count(4)->create(['priority' => 'normal']);

        $highPriorityTickets = RepairTicket::byPriority('high')->get();
        $mediumPriorityTickets = RepairTicket::byPriority('medium')->get();
        $normalPriorityTickets = RepairTicket::byPriority('normal')->get();

        $this->assertCount(2, $highPriorityTickets);
        $this->assertCount(3, $mediumPriorityTickets);
        $this->assertCount(4, $normalPriorityTickets);
    }

    /** @test */
    public function it_can_scope_assigned_to_technician()
    {
        $technician1 = User::factory()->create();
        $technician2 = User::factory()->create();

        RepairTicket::factory()->count(3)->create(['assigned_technician_id' => $technician1->id]);
        RepairTicket::factory()->count(2)->create(['assigned_technician_id' => $technician2->id]);
        RepairTicket::factory()->count(1)->create(['assigned_technician_id' => null]); // Unassigned

        $tech1Tickets = RepairTicket::assignedTo($technician1->id)->get();
        $tech2Tickets = RepairTicket::assignedTo($technician2->id)->get();
        $unassignedTickets = RepairTicket::unassigned()->get();

        $this->assertCount(3, $tech1Tickets);
        $this->assertCount(2, $tech2Tickets);
        $this->assertCount(1, $unassignedTickets);
    }

    /** @test */
    public function it_can_search_tickets()
    {
        $customer = Customer::factory()->create(['name' => 'John Doe']);
        $brand = Brand::factory()->create(['name' => 'iPhone']);

        $ticket = RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'brand_id' => $brand->id,
            'device_model' => 'iPhone 13',
            'reported_problem' => 'Screen cracked'
        ]);

        // Search by customer name
        $results = RepairTicket::search('John')->get();
        $this->assertCount(1, $results);
        $this->assertEquals($ticket->id, $results->first()->id);

        // Search by device model
        $results = RepairTicket::search('iPhone 13')->get();
        $this->assertCount(1, $results);

        // Search by problem description
        $results = RepairTicket::search('cracked')->get();
        $this->assertCount(1, $results);
    }

    /** @test */
    public function it_can_scope_by_date_range()
    {
        $startDate = Carbon::now()->subDays(10);
        $endDate = Carbon::now()->subDays(5);

        // Tickets within range
        RepairTicket::factory()->count(3)->create([
            'received_date' => Carbon::now()->subDays(7)
        ]);

        // Tickets outside range
        RepairTicket::factory()->count(2)->create([
            'received_date' => Carbon::now()->subDays(15)
        ]);
        RepairTicket::factory()->count(1)->create([
            'received_date' => Carbon::now()->subDays(2)
        ]);

        $ticketsInRange = RepairTicket::byDateRange($startDate, $endDate)->get();
        $this->assertCount(3, $ticketsInRange);
    }

    /** @test */
    public function it_validates_priority_values()
    {
        $validPriorities = ['high', 'medium', 'normal'];

        foreach ($validPriorities as $priority) {
            $ticket = RepairTicket::factory()->create(['priority' => $priority]);
            $this->assertEquals($priority, $ticket->priority);
        }
    }

    /** @test */
    public function it_casts_dates_properly()
    {
        $ticket = RepairTicket::factory()->create([
            'received_date' => '2024-01-15 10:30:00',
            'estimated_completion_date' => '2024-01-20',
            'completed_date' => '2024-01-18 15:45:00'
        ]);

        $this->assertInstanceOf(Carbon::class, $ticket->received_date);
        $this->assertInstanceOf(Carbon::class, $ticket->estimated_completion_date);
        $this->assertInstanceOf(Carbon::class, $ticket->completed_date);
    }

    /** @test */
    public function it_casts_costs_as_decimal()
    {
        $ticket = RepairTicket::factory()->create([
            'initial_cost' => '150.50',
            'final_cost' => '175.75'
        ]);

        $this->assertEquals(150.50, $ticket->initial_cost);
        $this->assertEquals(175.75, $ticket->final_cost);
    }
}
