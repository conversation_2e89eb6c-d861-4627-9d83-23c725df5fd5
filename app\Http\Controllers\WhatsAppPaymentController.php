<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Customer;
use App\Services\WhatsAppPaymentNotificationService;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

class WhatsAppPaymentController extends Controller
{
    protected WhatsAppPaymentNotificationService $paymentNotificationService;

    public function __construct(WhatsAppPaymentNotificationService $paymentNotificationService)
    {
        $this->paymentNotificationService = $paymentNotificationService;
    }

    /**
     * Display WhatsApp payment notifications dashboard.
     */
    public function dashboard(Request $request): View
    {
        // Get recent payment notifications
        $recentNotifications = \App\Models\WhatsAppTemplateUsage::with(['template', 'customer'])
            ->whereHas('template', function ($query) {
                $query->where('name', 'like', 'payment_%')
                      ->orWhere('name', 'like', 'invoice_%');
            })
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        // Get notification statistics
        $stats = $this->getNotificationStatistics();

        // Get pending reminders
        $pendingReminders = $this->getPendingReminders();

        return view('whatsapp.payment-dashboard', compact(
            'recentNotifications',
            'stats',
            'pendingReminders'
        ));
    }

    /**
     * Send manual payment reminder.
     */
    public function sendPaymentReminder(Request $request, Invoice $invoice): JsonResponse
    {
        $validated = $request->validate([
            'reminder_type' => 'required|in:gentle,urgent,final',
            'custom_message' => 'nullable|string|max:1000'
        ]);

        try {
            $result = $this->paymentNotificationService->sendPaymentReminder(
                $invoice,
                $validated['reminder_type']
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إرسال تذكير الدفع بنجاح',
                    'data' => $result
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في إرسال تذكير الدفع: ' . $result['error']
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال التذكير: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send bulk payment reminders.
     */
    public function sendBulkReminders(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'invoice_ids' => 'required|array|min:1',
            'invoice_ids.*' => 'exists:invoices,id',
            'reminder_type' => 'required|in:gentle,urgent,final'
        ]);

        try {
            $results = $this->paymentNotificationService->sendBulkPaymentReminders(
                $validated['invoice_ids'],
                $validated['reminder_type']
            );

            return response()->json([
                'success' => true,
                'message' => "تم إرسال {$results['sent']} تذكير من أصل {$results['total']}",
                'data' => $results
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال التذكيرات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Resend payment confirmation.
     */
    public function resendPaymentConfirmation(Payment $payment): JsonResponse
    {
        try {
            $result = $this->paymentNotificationService->sendPaymentConfirmation($payment);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إعادة إرسال تأكيد الدفع بنجاح'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في إعادة إرسال تأكيد الدفع: ' . $result['error']
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إعادة الإرسال: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send payment receipt.
     */
    public function sendPaymentReceipt(Payment $payment): JsonResponse
    {
        try {
            $result = $this->paymentNotificationService->sendPaymentReceipt($payment);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إرسال إيصال الدفع بنجاح'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في إرسال إيصال الدفع: ' . $result['error']
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال الإيصال: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send invoice notification.
     */
    public function sendInvoiceNotification(Invoice $invoice): JsonResponse
    {
        try {
            $result = $this->paymentNotificationService->sendInvoiceNotification($invoice);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إرسال إشعار الفاتورة بنجاح'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في إرسال إشعار الفاتورة: ' . $result['error']
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال الإشعار: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send payment plan notification.
     */
    public function sendPaymentPlan(Request $request, Invoice $invoice): JsonResponse
    {
        $validated = $request->validate([
            'installments' => 'required|array|min:2',
            'installments.*.amount' => 'required|numeric|min:0.01',
            'installments.*.due_date' => 'required|date|after:today'
        ]);

        try {
            $result = $this->paymentNotificationService->sendPaymentPlanNotification(
                $invoice,
                ['installments' => $validated['installments']]
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إرسال خطة الدفع بنجاح'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في إرسال خطة الدفع: ' . $result['error']
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال خطة الدفع: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get overdue invoices for reminders.
     */
    public function getOverdueInvoices(Request $request): JsonResponse
    {
        $daysOverdue = $request->get('days_overdue', 1);
        
        $invoices = Invoice::where('payment_status', '!=', 'paid')
            ->where('due_date', '<=', now()->subDays($daysOverdue))
            ->with(['customer', 'repairTicket'])
            ->orderBy('due_date')
            ->get()
            ->map(function ($invoice) {
                return [
                    'id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'customer_name' => $invoice->customer->name,
                    'customer_phone' => $invoice->customer->phone_number,
                    'total_amount' => $invoice->total_amount,
                    'remaining_amount' => $invoice->remaining_amount,
                    'due_date' => $invoice->due_date,
                    'days_overdue' => $invoice->due_date ? $invoice->due_date->diffInDays(now(), false) : 0,
                    'can_send_whatsapp' => !empty($invoice->customer->phone_number),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $invoices
        ]);
    }

    /**
     * Get notification statistics.
     */
    private function getNotificationStatistics(): array
    {
        $today = now()->startOfDay();
        $thisWeek = now()->startOfWeek();
        $thisMonth = now()->startOfMonth();

        return [
            'today' => [
                'payment_confirmations' => \App\Models\WhatsAppTemplateUsage::whereHas('template', function ($query) {
                    $query->where('name', 'payment_confirmation_ar');
                })->where('created_at', '>=', $today)->count(),
                'payment_reminders' => \App\Models\WhatsAppTemplateUsage::whereHas('template', function ($query) {
                    $query->where('name', 'like', 'payment_reminder_%');
                })->where('created_at', '>=', $today)->count(),
                'invoice_notifications' => \App\Models\WhatsAppTemplateUsage::whereHas('template', function ($query) {
                    $query->where('name', 'invoice_notification_ar');
                })->where('created_at', '>=', $today)->count(),
            ],
            'this_week' => [
                'total_sent' => \App\Models\WhatsAppTemplateUsage::whereHas('template', function ($query) {
                    $query->where('name', 'like', 'payment_%')
                          ->orWhere('name', 'like', 'invoice_%');
                })->where('created_at', '>=', $thisWeek)->count(),
                'success_rate' => 95, // This would be calculated from actual delivery status
            ],
            'this_month' => [
                'total_sent' => \App\Models\WhatsAppTemplateUsage::whereHas('template', function ($query) {
                    $query->where('name', 'like', 'payment_%')
                          ->orWhere('name', 'like', 'invoice_%');
                })->where('created_at', '>=', $thisMonth)->count(),
                'cost_saved' => 150.00, // Estimated cost savings vs SMS
            ]
        ];
    }

    /**
     * Get pending payment reminders.
     */
    private function getPendingReminders(): array
    {
        return [
            'gentle' => Invoice::where('payment_status', '!=', 'paid')
                ->where('due_date', '=', now()->addDays(3)->toDateString())
                ->count(),
            'urgent' => Invoice::where('payment_status', '!=', 'paid')
                ->where('due_date', '=', now()->subDay()->toDateString())
                ->count(),
            'final' => Invoice::where('payment_status', '!=', 'paid')
                ->where('due_date', '=', now()->subDays(7)->toDateString())
                ->count(),
        ];
    }

    /**
     * Test WhatsApp payment notification.
     */
    public function testNotification(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'phone_number' => 'required|string',
            'template_type' => 'required|in:payment_confirmation,invoice_notification,payment_reminder',
            'test_data' => 'nullable|array'
        ]);

        try {
            // This would send a test notification with sample data
            $testData = $validated['test_data'] ?? [
                'customer_name' => 'عميل تجريبي',
                'invoice_number' => 'TEST-001',
                'payment_amount' => '100.00',
                'payment_method' => 'نقداً',
                'business_name' => config('whatsapp.business_name'),
                'business_phone' => config('whatsapp.business_phone'),
            ];

            // Here you would implement the actual test sending logic
            
            return response()->json([
                'success' => true,
                'message' => 'تم إرسال الإشعار التجريبي بنجاح'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في إرسال الإشعار التجريبي: ' . $e->getMessage()
            ], 500);
        }
    }
}
