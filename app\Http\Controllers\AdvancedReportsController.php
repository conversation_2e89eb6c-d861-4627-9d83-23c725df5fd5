<?php

namespace App\Http\Controllers;

use App\Models\ProfitLossStatement;
use App\Models\BusinessKpis;
use App\Models\SalesAnalytics;
use App\Models\PurchaseAnalytics;
use App\Models\Invoice;
use App\Models\RepairTicket;
use App\Models\Customer;
use App\Models\InventoryItem;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdvancedReportsController extends Controller
{
    /**
     * Display advanced reports dashboard.
     */
    public function dashboard(Request $request): View
    {
        $period = $request->get('period', 'monthly');
        $date = $request->get('date') ? Carbon::parse($request->date) : now();

        // Get latest KPIs
        $latestKpis = BusinessKpis::where('period_type', $period)
            ->orderBy('kpi_date', 'desc')
            ->first();

        if (!$latestKpis) {
            $latestKpis = BusinessKpis::generateKPIs($date, $period);
        }

        // Get latest P&L statement
        $latestProfitLoss = ProfitLossStatement::where('period_type', $period)
            ->orderBy('statement_date', 'desc')
            ->first();

        if (!$latestProfitLoss) {
            $latestProfitLoss = ProfitLossStatement::generateStatement($date, $period);
        }

        // Get KPI trends
        $kpiTrends = BusinessKpis::getKPITrends($period, 6);

        // Get quick stats
        $quickStats = $this->getQuickStats();

        return view('reports.advanced-dashboard', compact(
            'latestKpis',
            'latestProfitLoss',
            'kpiTrends',
            'quickStats',
            'period',
            'date'
        ));
    }

    /**
     * Display profit & loss report.
     */
    public function profitLoss(Request $request): View
    {
        $period = $request->get('period', 'monthly');
        $date = $request->get('date') ? Carbon::parse($request->date) : now();

        // Generate or get existing P&L statement
        $profitLoss = ProfitLossStatement::where('statement_date', $date->toDateString())
            ->where('period_type', $period)
            ->first();

        if (!$profitLoss) {
            $profitLoss = ProfitLossStatement::generateStatement($date, $period);
        }

        // Get comparison with previous period
        $comparison = $profitLoss->getComparisonWithPreviousPeriod();

        // Get financial health indicators
        $healthIndicators = $profitLoss->getFinancialHealthIndicators();

        // Get historical data for trends
        $historicalData = $this->getProfitLossTrends($period, 12);

        return view('reports.profit-loss', compact(
            'profitLoss',
            'comparison',
            'healthIndicators',
            'historicalData',
            'period',
            'date'
        ));
    }

    /**
     * Display KPI dashboard.
     */
    public function kpiDashboard(Request $request): View
    {
        $period = $request->get('period', 'monthly');
        $date = $request->get('date') ? Carbon::parse($request->date) : now();

        // Generate or get existing KPIs
        $kpis = BusinessKpis::where('kpi_date', $date->toDateString())
            ->where('period_type', $period)
            ->first();

        if (!$kpis) {
            $kpis = BusinessKpis::generateKPIs($date, $period);
        }

        // Get KPI summary
        $kpiSummary = $kpis->getKPISummary();

        // Get performance indicators
        $performanceIndicators = $kpis->getPerformanceIndicators();

        // Get KPI trends
        $kpiTrends = BusinessKpis::getKPITrends($period, 12);

        return view('reports.kpi-dashboard', compact(
            'kpis',
            'kpiSummary',
            'performanceIndicators',
            'kpiTrends',
            'period',
            'date'
        ));
    }

    /**
     * Display sales performance report.
     */
    public function salesPerformance(Request $request): View
    {
        $period = $request->get('period', 'monthly');
        $startDate = $request->get('start_date') ? Carbon::parse($request->start_date) : now()->startOfMonth();
        $endDate = $request->get('end_date') ? Carbon::parse($request->end_date) : now()->endOfMonth();

        // Get sales analytics
        $salesAnalytics = SalesAnalytics::getAnalyticsForRange($startDate, $endDate, $period);

        // Get top performing metrics
        $topMetrics = $this->getSalesTopMetrics($startDate, $endDate);

        // Get sales trends
        $salesTrends = $this->getSalesTrendsData($period, $startDate, $endDate);

        // Get customer analysis
        $customerAnalysis = $this->getCustomerAnalysisData($startDate, $endDate);

        return view('reports.sales-performance', compact(
            'salesAnalytics',
            'topMetrics',
            'salesTrends',
            'customerAnalysis',
            'period',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Display inventory analysis report.
     */
    public function inventoryAnalysis(Request $request): View
    {
        $category = $request->get('category');
        $lowStockOnly = $request->boolean('low_stock_only');

        $query = InventoryItem::with(['category', 'brand', 'supplier']);

        if ($category) {
            $query->where('category_id', $category);
        }

        if ($lowStockOnly) {
            $query->whereRaw('current_stock <= minimum_stock');
        }

        $inventoryItems = $query->orderBy('name')->paginate(50)->withQueryString();

        // Get inventory summary
        $inventorySummary = $this->getInventorySummary();

        // Get turnover analysis
        $turnoverAnalysis = $this->getInventoryTurnoverAnalysis();

        // Get categories for filter
        $categories = \App\Models\Category::orderBy('name')->get();

        return view('reports.inventory-analysis', compact(
            'inventoryItems',
            'inventorySummary',
            'turnoverAnalysis',
            'categories',
            'category',
            'lowStockOnly'
        ));
    }

    /**
     * Display customer payment history report.
     */
    public function customerPaymentHistory(Request $request): View
    {
        $customerId = $request->get('customer_id');
        $paymentStatus = $request->get('payment_status');
        $startDate = $request->get('start_date') ? Carbon::parse($request->start_date) : now()->subMonths(3);
        $endDate = $request->get('end_date') ? Carbon::parse($request->end_date) : now();

        $query = Invoice::with(['customer', 'payments'])
            ->whereBetween('invoice_date', [$startDate, $endDate]);

        if ($customerId) {
            $query->where('customer_id', $customerId);
        }

        if ($paymentStatus) {
            $query->where('payment_status', $paymentStatus);
        }

        $invoices = $query->orderBy('invoice_date', 'desc')
            ->paginate(25)
            ->withQueryString();

        // Get payment summary
        $paymentSummary = $this->getPaymentSummary($startDate, $endDate, $customerId);

        // Get customers for filter
        $customers = Customer::orderBy('name')->get();

        return view('reports.customer-payment-history', compact(
            'invoices',
            'paymentSummary',
            'customers',
            'customerId',
            'paymentStatus',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Generate custom report.
     */
    public function generateCustomReport(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'report_type' => 'required|in:profit_loss,kpi_summary,sales_analysis,inventory_turnover,customer_analysis',
            'period_type' => 'required|in:daily,weekly,monthly,quarterly,yearly',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'required|in:json,pdf,excel,csv'
        ]);

        $startDate = Carbon::parse($validated['start_date']);
        $endDate = Carbon::parse($validated['end_date']);

        $reportData = match ($validated['report_type']) {
            'profit_loss' => $this->generateProfitLossReportData($startDate, $endDate, $validated['period_type']),
            'kpi_summary' => $this->generateKPIReportData($startDate, $endDate, $validated['period_type']),
            'sales_analysis' => $this->generateSalesAnalysisReportData($startDate, $endDate, $validated['period_type']),
            'inventory_turnover' => $this->generateInventoryTurnoverReportData($startDate, $endDate),
            'customer_analysis' => $this->generateCustomerAnalysisReportData($startDate, $endDate),
        };

        return response()->json([
            'success' => true,
            'data' => $reportData,
            'meta' => [
                'report_type' => $validated['report_type'],
                'period_type' => $validated['period_type'],
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
                'generated_at' => now()->toISOString()
            ]
        ]);
    }

    /**
     * Get quick stats for dashboard.
     */
    private function getQuickStats(): array
    {
        $today = now();
        $thisMonth = $today->copy()->startOfMonth();

        return [
            'today_revenue' => Invoice::whereDate('invoice_date', $today)
                ->where('status', '!=', 'cancelled')
                ->sum('total_amount'),
            'month_revenue' => Invoice::whereBetween('invoice_date', [$thisMonth, $today])
                ->where('status', '!=', 'cancelled')
                ->sum('total_amount'),
            'pending_invoices' => Invoice::where('payment_status', '!=', 'paid')->count(),
            'active_repairs' => RepairTicket::whereIn('status', ['pending', 'in_progress'])->count(),
            'low_stock_items' => InventoryItem::whereRaw('current_stock <= minimum_stock')->count(),
            'total_customers' => Customer::count(),
        ];
    }

    /**
     * Get profit & loss trends.
     */
    private function getProfitLossTrends(string $period, int $periods): array
    {
        $statements = ProfitLossStatement::where('period_type', $period)
            ->orderBy('statement_date', 'desc')
            ->limit($periods)
            ->get()
            ->reverse();

        return [
            'labels' => $statements->pluck('statement_date')->map(function ($date) use ($period) {
                return match ($period) {
                    'monthly' => $date->format('M Y'),
                    'quarterly' => 'Q' . $date->quarter . ' ' . $date->year,
                    'yearly' => $date->format('Y'),
                    default => $date->format('M Y'),
                };
            })->toArray(),
            'revenue' => $statements->pluck('total_revenue')->toArray(),
            'gross_profit' => $statements->pluck('gross_profit')->toArray(),
            'operating_income' => $statements->pluck('operating_income')->toArray(),
            'net_income' => $statements->pluck('net_income')->toArray(),
        ];
    }

    /**
     * Get sales top metrics.
     */
    private function getSalesTopMetrics(Carbon $startDate, Carbon $endDate): array
    {
        $invoices = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->with(['customer', 'items.inventoryItem'])
            ->get();

        // Top customers by revenue
        $topCustomers = $invoices->groupBy('customer_id')
            ->map(function ($customerInvoices) {
                return [
                    'customer' => $customerInvoices->first()->customer,
                    'total_revenue' => $customerInvoices->sum('total_amount'),
                    'invoice_count' => $customerInvoices->count()
                ];
            })
            ->sortByDesc('total_revenue')
            ->take(10)
            ->values();

        // Top selling items
        $topItems = [];
        foreach ($invoices as $invoice) {
            foreach ($invoice->items as $item) {
                $key = $item->item_name;
                if (!isset($topItems[$key])) {
                    $topItems[$key] = [
                        'name' => $item->item_name,
                        'quantity' => 0,
                        'revenue' => 0
                    ];
                }
                $topItems[$key]['quantity'] += $item->quantity;
                $topItems[$key]['revenue'] += $item->total_price;
            }
        }

        $topItems = collect($topItems)
            ->sortByDesc('revenue')
            ->take(10)
            ->values();

        return [
            'top_customers' => $topCustomers,
            'top_items' => $topItems
        ];
    }

    /**
     * Get sales trends data.
     */
    private function getSalesTrendsData(string $period, Carbon $startDate, Carbon $endDate): array
    {
        $analytics = SalesAnalytics::getAnalyticsForRange($startDate, $endDate, $period);

        return [
            'labels' => $analytics->pluck('analytics_date')->map(function ($date) use ($period) {
                return match ($period) {
                    'daily' => $date->format('M d'),
                    'weekly' => 'Week ' . $date->weekOfYear,
                    'monthly' => $date->format('M Y'),
                    'yearly' => $date->format('Y'),
                    default => $date->format('M d'),
                };
            })->toArray(),
            'sales' => $analytics->pluck('total_sales')->toArray(),
            'profit' => $analytics->pluck('total_profit')->toArray(),
            'invoices' => $analytics->pluck('invoices_count')->toArray()
        ];
    }

    /**
     * Get customer analysis data.
     */
    private function getCustomerAnalysisData(Carbon $startDate, Carbon $endDate): array
    {
        return DB::table('invoices')
            ->join('customers', 'invoices.customer_id', '=', 'customers.id')
            ->whereBetween('invoices.invoice_date', [$startDate, $endDate])
            ->where('invoices.status', '!=', 'cancelled')
            ->select(
                'customers.id',
                'customers.name',
                DB::raw('COUNT(invoices.id) as invoice_count'),
                DB::raw('SUM(invoices.total_amount) as total_revenue'),
                DB::raw('AVG(invoices.total_amount) as avg_invoice_value'),
                DB::raw('SUM(invoices.profit_amount) as total_profit')
            )
            ->groupBy('customers.id', 'customers.name')
            ->orderBy('total_revenue', 'desc')
            ->limit(20)
            ->get()
            ->toArray();
    }

    /**
     * Get inventory summary.
     */
    private function getInventorySummary(): array
    {
        return [
            'total_items' => InventoryItem::count(),
            'low_stock_items' => InventoryItem::whereRaw('current_stock <= minimum_stock')->count(),
            'out_of_stock_items' => InventoryItem::where('current_stock', 0)->count(),
            'total_inventory_value' => InventoryItem::sum(DB::raw('current_stock * cost_price')),
            'total_retail_value' => InventoryItem::sum(DB::raw('current_stock * selling_price')),
        ];
    }

    /**
     * Get inventory turnover analysis.
     */
    private function getInventoryTurnoverAnalysis(): array
    {
        // This would require more complex calculations based on historical data
        // For now, return placeholder data
        return [
            'fast_moving' => InventoryItem::where('current_stock', '>', 0)
                ->orderBy('updated_at', 'desc')
                ->limit(10)
                ->get(),
            'slow_moving' => InventoryItem::where('current_stock', '>', 0)
                ->orderBy('updated_at', 'asc')
                ->limit(10)
                ->get(),
        ];
    }

    /**
     * Get payment summary.
     */
    private function getPaymentSummary(Carbon $startDate, Carbon $endDate, ?int $customerId): array
    {
        $query = Invoice::whereBetween('invoice_date', [$startDate, $endDate]);

        if ($customerId) {
            $query->where('customer_id', $customerId);
        }

        $invoices = $query->get();

        return [
            'total_invoices' => $invoices->count(),
            'total_amount' => $invoices->sum('total_amount'),
            'paid_amount' => $invoices->sum('paid_amount'),
            'outstanding_amount' => $invoices->sum('remaining_amount'),
            'overdue_amount' => $invoices->where('due_date', '<', now())->sum('remaining_amount'),
        ];
    }

    // Additional private methods for report generation would go here...
    // (generateProfitLossReportData, generateKPIReportData, etc.)
}
