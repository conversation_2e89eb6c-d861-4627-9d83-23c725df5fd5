@extends('layouts.app')

@section('title', 'تقرير الأرباح والخسائر')

@push('styles')
<style>
.profit-loss-header {
    background: linear-gradient(45deg, #6f42c1, #5a32a3);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.filter-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.profit-loss-statement {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.statement-header {
    background: linear-gradient(45deg, #6f42c1, #5a32a3);
    color: white;
    padding: 1rem 1.5rem;
}

.statement-body {
    padding: 0;
}

.statement-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e3e6f0;
}

.statement-row:last-child {
    border-bottom: none;
}

.statement-row.total {
    background: #f8f9fc;
    font-weight: bold;
    font-size: 1.1rem;
}

.statement-row.subtotal {
    background: #fff3cd;
    font-weight: 600;
}

.statement-label {
    font-weight: 600;
    color: #5a5c69;
}

.statement-value {
    font-weight: 600;
    font-size: 1.1rem;
}

.value-positive {
    color: #28a745;
}

.value-negative {
    color: #dc3545;
}

.value-neutral {
    color: #6c757d;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.metric-card {
    background: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    text-align: center;
    transition: all 0.2s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    color: #6c757d;
    font-size: 0.875rem;
    text-transform: uppercase;
    margin-bottom: 0.5rem;
}

.metric-description {
    font-size: 0.75rem;
    color: #6c757d;
}

.chart-container {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
    .profit-loss-header {
        padding: 1rem;
    }
    
    .filter-card {
        padding: 1rem;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .statement-row {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="profit-loss-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">تقرير الأرباح والخسائر</h1>
                <p class="mb-0 opacity-75">تحليل مالي شامل للأرباح والخسائر</p>
            </div>
            <div>
                <a href="{{ route('reports.financial') }}" class="btn btn-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="GET" action="{{ route('reports.profit-loss') }}" class="row g-3">
            <div class="col-md-4">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" 
                       class="form-control" 
                       id="date_from" 
                       name="date_from" 
                       value="{{ $dateFrom }}">
            </div>
            
            <div class="col-md-4">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" 
                       class="form-control" 
                       id="date_to" 
                       name="date_to" 
                       value="{{ $dateTo }}">
            </div>
            
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>تطبيق الفلتر
                    </button>
                    <a href="{{ route('reports.profit-loss') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Key Metrics -->
    <div class="metrics-grid">
        <div class="metric-card">
            <div class="metric-value value-positive">{{ number_format($data['gross_margin'], 1) }}%</div>
            <div class="metric-label">هامش الربح الإجمالي</div>
            <div class="metric-description">نسبة الربح الإجمالي من المبيعات</div>
        </div>
        
        <div class="metric-card">
            <div class="metric-value {{ $data['net_margin'] >= 0 ? 'value-positive' : 'value-negative' }}">{{ number_format($data['net_margin'], 1) }}%</div>
            <div class="metric-label">هامش الربح الصافي</div>
            <div class="metric-description">نسبة الربح الصافي من المبيعات</div>
        </div>
        
        <div class="metric-card">
            <div class="metric-value value-positive">@arabicCurrency($data['revenue'])</div>
            <div class="metric-label">إجمالي الإيرادات</div>
            <div class="metric-description">إجمالي المبيعات للفترة</div>
        </div>
        
        <div class="metric-card">
            <div class="metric-value {{ $data['net_profit'] >= 0 ? 'value-positive' : 'value-negative' }}">@arabicCurrency($data['net_profit'])</div>
            <div class="metric-label">صافي الربح</div>
            <div class="metric-description">الربح بعد خصم جميع التكاليف</div>
        </div>
    </div>

    <!-- Profit & Loss Statement -->
    <div class="profit-loss-statement">
        <div class="statement-header">
            <h5 class="mb-0">
                <i class="fas fa-calculator me-2"></i>
                قائمة الأرباح والخسائر
            </h5>
        </div>
        <div class="statement-body">
            <!-- Revenue Section -->
            <div class="statement-row">
                <span class="statement-label">الإيرادات</span>
                <span class="statement-value value-positive">@arabicCurrency($data['revenue'])</span>
            </div>
            
            <!-- Cost of Goods Sold -->
            <div class="statement-row">
                <span class="statement-label">تكلفة البضاعة المباعة</span>
                <span class="statement-value value-negative">(@arabicCurrency($data['cogs']))</span>
            </div>
            
            <!-- Gross Profit -->
            <div class="statement-row subtotal">
                <span class="statement-label">إجمالي الربح</span>
                <span class="statement-value {{ $data['gross_profit'] >= 0 ? 'value-positive' : 'value-negative' }}">
                    @arabicCurrency($data['gross_profit'])
                </span>
            </div>
            
            <!-- Operating Expenses -->
            <div class="statement-row">
                <span class="statement-label">المصروفات التشغيلية</span>
                <span class="statement-value value-negative">(@arabicCurrency($data['expenses']))</span>
            </div>
            
            <!-- Net Profit -->
            <div class="statement-row total">
                <span class="statement-label">صافي الربح</span>
                <span class="statement-value {{ $data['net_profit'] >= 0 ? 'value-positive' : 'value-negative' }}">
                    @arabicCurrency($data['net_profit'])
                </span>
            </div>
        </div>
    </div>

    <!-- Profit Chart -->
    <div class="chart-container">
        <h5 class="mb-3">
            <i class="fas fa-chart-pie me-2"></i>
            توزيع الإيرادات والتكاليف
        </h5>
        <canvas id="profitChart" height="100"></canvas>
    </div>

    <!-- Analysis -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        تحليل الأداء
                    </h6>
                </div>
                <div class="card-body">
                    @if($data['gross_margin'] >= 30)
                        <div class="alert alert-success">
                            <i class="fas fa-thumbs-up me-2"></i>
                            هامش ربح إجمالي ممتاز ({{ number_format($data['gross_margin'], 1) }}%)
                        </div>
                    @elseif($data['gross_margin'] >= 20)
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            هامش ربح إجمالي جيد ({{ number_format($data['gross_margin'], 1) }}%)
                        </div>
                    @else
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            هامش ربح إجمالي منخفض ({{ number_format($data['gross_margin'], 1) }}%)
                        </div>
                    @endif
                    
                    @if($data['net_profit'] > 0)
                        <p class="text-success mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            الشركة تحقق أرباحاً صافية إيجابية
                        </p>
                    @else
                        <p class="text-danger mb-0">
                            <i class="fas fa-times-circle me-2"></i>
                            الشركة تواجه خسائر صافية
                        </p>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        توصيات
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        @if($data['gross_margin'] < 20)
                            <li class="mb-2">
                                <i class="fas fa-arrow-up text-success me-2"></i>
                                زيادة أسعار الخدمات أو تقليل تكلفة المواد
                            </li>
                        @endif
                        
                        @if($data['expenses'] > $data['revenue'] * 0.3)
                            <li class="mb-2">
                                <i class="fas fa-cut text-warning me-2"></i>
                                مراجعة وتقليل المصروفات التشغيلية
                            </li>
                        @endif
                        
                        <li class="mb-2">
                            <i class="fas fa-chart-line text-primary me-2"></i>
                            تحليل الخدمات الأكثر ربحية والتركيز عليها
                        </li>
                        
                        <li class="mb-0">
                            <i class="fas fa-users text-info me-2"></i>
                            تحسين خدمة العملاء لزيادة الولاء والإيرادات
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Period Info -->
    <div class="mt-3">
        <small class="text-muted">
            <i class="fas fa-calendar me-1"></i>
            فترة التقرير: من {{ \Carbon\Carbon::parse($dateFrom)->format('Y-m-d') }} إلى {{ \Carbon\Carbon::parse($dateTo)->format('Y-m-d') }}
        </small>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Profit Chart
    const ctx = document.getElementById('profitChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['الإيرادات', 'تكلفة البضاعة', 'المصروفات', 'صافي الربح'],
            datasets: [{
                data: [
                    {{ $data['revenue'] }},
                    {{ $data['cogs'] }},
                    {{ $data['expenses'] }},
                    {{ max(0, $data['net_profit']) }}
                ],
                backgroundColor: [
                    '#28a745',
                    '#dc3545',
                    '#ffc107',
                    '#17a2b8'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return context.label + ': ' + new Intl.NumberFormat('ar-SA', {
                                style: 'currency',
                                currency: 'SAR'
                            }).format(value) + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });
});
</script>
@endpush
@endsection
