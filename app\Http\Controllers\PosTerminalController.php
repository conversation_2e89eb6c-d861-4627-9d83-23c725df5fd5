<?php

namespace App\Http\Controllers;

use App\Models\PosTerminal;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;

class PosTerminalController extends Controller
{
    /**
     * Display a listing of POS terminals.
     */
    public function index(Request $request): View
    {
        $query = PosTerminal::with(['assignedUser', 'currentSession']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('terminal_name', 'like', "%{$search}%")
                  ->orWhere('terminal_code', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by assigned user
        if ($request->filled('user_id')) {
            $query->where('assigned_user_id', $request->user_id);
        }

        $terminals = $query->orderBy('terminal_name')
            ->paginate(15)
            ->withQueryString();

        // Get users for filter dropdown
        $users = User::orderBy('name')->get();

        return view('pos-terminals.index', compact('terminals', 'users'));
    }

    /**
     * Show the form for creating a new terminal.
     */
    public function create(): View
    {
        $users = User::orderBy('name')->get();
        return view('pos-terminals.create', compact('users'));
    }

    /**
     * Store a newly created terminal.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'terminal_name' => 'required|string|max:255',
            'location' => 'nullable|string|max:255',
            'assigned_user_id' => 'nullable|exists:users,id',
            'is_active' => 'boolean',
            'opening_balance' => 'nullable|numeric|min:0',
            'configuration' => 'nullable|json',
        ]);

        // Generate terminal code
        $validated['terminal_code'] = PosTerminal::generateTerminalCode();
        $validated['is_active'] = $validated['is_active'] ?? true;
        $validated['cash_drawer_balance'] = $validated['opening_balance'] ?? 0;

        // Parse configuration JSON
        if (isset($validated['configuration'])) {
            $validated['configuration'] = json_decode($validated['configuration'], true);
        }

        $terminal = PosTerminal::create($validated);

        return redirect()->route('pos-terminals.show', $terminal)
            ->with('success', 'تم إنشاء نقطة البيع بنجاح');
    }

    /**
     * Display the specified terminal.
     */
    public function show(PosTerminal $posTerminal): View
    {
        $posTerminal->load(['assignedUser', 'currentSession', 'sessions' => function ($query) {
            $query->orderBy('started_at', 'desc')->limit(10);
        }]);

        // Get today's statistics
        $todayStats = $posTerminal->getTodaysSalesSummary();

        // Get recent transactions
        $recentTransactions = $posTerminal->transactions()
            ->with(['customer', 'cashier'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get cash drawer operations for today
        $todayCashOperations = $posTerminal->cashDrawerOperations()
            ->with('performedBy')
            ->whereDate('operation_date', today())
            ->orderBy('operation_date', 'desc')
            ->get();

        return view('pos-terminals.show', compact(
            'posTerminal',
            'todayStats',
            'recentTransactions',
            'todayCashOperations'
        ));
    }

    /**
     * Show the form for editing the specified terminal.
     */
    public function edit(PosTerminal $posTerminal): View
    {
        $users = User::orderBy('name')->get();
        return view('pos-terminals.edit', compact('posTerminal', 'users'));
    }

    /**
     * Update the specified terminal.
     */
    public function update(Request $request, PosTerminal $posTerminal): RedirectResponse
    {
        $validated = $request->validate([
            'terminal_name' => 'required|string|max:255',
            'location' => 'nullable|string|max:255',
            'assigned_user_id' => 'nullable|exists:users,id',
            'is_active' => 'boolean',
            'configuration' => 'nullable|json',
        ]);

        $validated['is_active'] = $validated['is_active'] ?? false;

        // Parse configuration JSON
        if (isset($validated['configuration'])) {
            $validated['configuration'] = json_decode($validated['configuration'], true);
        }

        $posTerminal->update($validated);

        return redirect()->route('pos-terminals.show', $posTerminal)
            ->with('success', 'تم تحديث نقطة البيع بنجاح');
    }

    /**
     * Remove the specified terminal.
     */
    public function destroy(PosTerminal $posTerminal): RedirectResponse
    {
        // Check if terminal has active sessions or transactions
        $hasActiveSession = $posTerminal->currentSession !== null;
        $hasTransactions = $posTerminal->transactions()->count() > 0;

        if ($hasActiveSession) {
            return back()->with('error', 'لا يمكن حذف نقطة البيع لوجود وردية نشطة');
        }

        if ($hasTransactions) {
            return back()->with('error', 'لا يمكن حذف نقطة البيع لوجود معاملات مرتبطة بها');
        }

        $posTerminal->delete();

        return redirect()->route('pos-terminals.index')
            ->with('success', 'تم حذف نقطة البيع بنجاح');
    }

    /**
     * Toggle terminal status.
     */
    public function toggleStatus(PosTerminal $posTerminal): RedirectResponse
    {
        // Don't allow deactivating terminal with active session
        if ($posTerminal->is_active && $posTerminal->currentSession) {
            return back()->with('error', 'لا يمكن إلغاء تفعيل نقطة البيع لوجود وردية نشطة');
        }

        $posTerminal->update(['is_active' => !$posTerminal->is_active]);
        
        $status = $posTerminal->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';
        
        return back()->with('success', $status . ' نقطة البيع بنجاح');
    }

    /**
     * Get terminal statistics for API.
     */
    public function getStatistics(PosTerminal $posTerminal): JsonResponse
    {
        $todayStats = $posTerminal->getTodaysSalesSummary();
        
        return response()->json([
            'success' => true,
            'data' => [
                'terminal_info' => [
                    'id' => $posTerminal->id,
                    'name' => $posTerminal->terminal_name,
                    'code' => $posTerminal->terminal_code,
                    'location' => $posTerminal->location,
                    'status' => $posTerminal->status_display,
                    'is_online' => $posTerminal->isOnline(),
                    'has_active_shift' => $posTerminal->hasActiveShift(),
                    'assigned_user' => $posTerminal->assignedUser?->name,
                    'cash_balance' => $posTerminal->cash_drawer_balance,
                ],
                'today_stats' => $todayStats,
                'current_session' => $posTerminal->currentSession ? [
                    'session_number' => $posTerminal->currentSession->session_number,
                    'started_at' => $posTerminal->currentSession->started_at,
                    'duration' => $posTerminal->currentSession->duration_formatted,
                    'opening_cash' => $posTerminal->currentSession->opening_cash,
                ] : null
            ]
        ]);
    }

    /**
     * Update terminal configuration.
     */
    public function updateConfiguration(Request $request, PosTerminal $posTerminal): JsonResponse
    {
        $validated = $request->validate([
            'configuration' => 'required|array',
        ]);

        $posTerminal->update(['configuration' => $validated['configuration']]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث إعدادات نقطة البيع بنجاح'
        ]);
    }

    /**
     * Assign user to terminal.
     */
    public function assignUser(Request $request, PosTerminal $posTerminal): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'nullable|exists:users,id'
        ]);

        $posTerminal->update(['assigned_user_id' => $validated['user_id']]);

        return response()->json([
            'success' => true,
            'message' => 'تم تعيين المستخدم لنقطة البيع بنجاح',
            'assigned_user' => $posTerminal->fresh()->assignedUser?->name
        ]);
    }

    /**
     * Get terminal activity log.
     */
    public function getActivityLog(Request $request, PosTerminal $posTerminal): JsonResponse
    {
        $startDate = $request->get('start_date', today()->toDateString());
        $endDate = $request->get('end_date', today()->toDateString());

        // Get sessions
        $sessions = $posTerminal->sessions()
            ->with('user')
            ->whereBetween('started_at', [$startDate, $endDate])
            ->orderBy('started_at', 'desc')
            ->get();

        // Get cash operations
        $cashOperations = $posTerminal->cashDrawerOperations()
            ->with('performedBy')
            ->whereBetween('operation_date', [$startDate, $endDate])
            ->orderBy('operation_date', 'desc')
            ->get();

        // Get transactions
        $transactions = $posTerminal->transactions()
            ->with(['customer', 'cashier'])
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->orderBy('transaction_date', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'sessions' => $sessions->map(function ($session) {
                    return [
                        'type' => 'session',
                        'timestamp' => $session->started_at,
                        'description' => "Session {$session->session_number} started by {$session->user->name}",
                        'details' => $session->getSummary()
                    ];
                }),
                'cash_operations' => $cashOperations->map(function ($operation) {
                    return [
                        'type' => 'cash_operation',
                        'timestamp' => $operation->operation_date,
                        'description' => "{$operation->operation_type_display} by {$operation->performedBy->name}",
                        'details' => $operation->getSummary()
                    ];
                }),
                'transactions' => $transactions->map(function ($transaction) {
                    return [
                        'type' => 'transaction',
                        'timestamp' => $transaction->transaction_date,
                        'description' => "Transaction {$transaction->transaction_number} - {$transaction->status_display}",
                        'details' => [
                            'transaction_number' => $transaction->transaction_number,
                            'customer' => $transaction->customer?->name ?? $transaction->customer_name,
                            'cashier' => $transaction->cashier->name,
                            'total_amount' => $transaction->total_amount,
                            'status' => $transaction->status_display
                        ]
                    ];
                })
            ]
        ]);
    }

    /**
     * Reset terminal (clear all data except configuration).
     */
    public function reset(Request $request, PosTerminal $posTerminal): RedirectResponse
    {
        // Check if terminal has active session
        if ($posTerminal->currentSession) {
            return back()->with('error', 'لا يمكن إعادة تعيين نقطة البيع لوجود وردية نشطة');
        }

        $validated = $request->validate([
            'confirm_reset' => 'required|accepted',
            'reset_reason' => 'required|string|max:500'
        ]);

        try {
            DB::transaction(function () use ($posTerminal, $validated) {
                // Reset cash drawer balance
                $posTerminal->update([
                    'cash_drawer_balance' => 0,
                    'opening_balance' => 0,
                    'shift_started_at' => null,
                    'shift_opened_by' => null,
                    'last_activity_at' => null
                ]);

                // Log the reset operation
                $posTerminal->cashDrawerOperations()->create([
                    'operation_type' => 'adjustment',
                    'amount' => -$posTerminal->cash_drawer_balance,
                    'balance_before' => $posTerminal->cash_drawer_balance,
                    'balance_after' => 0,
                    'reason' => 'Terminal Reset',
                    'notes' => $validated['reset_reason'],
                    'performed_by' => auth()->id(),
                    'operation_date' => now()
                ]);
            });

            return back()->with('success', 'تم إعادة تعيين نقطة البيع بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء إعادة تعيين نقطة البيع: ' . $e->getMessage());
        }
    }
}
