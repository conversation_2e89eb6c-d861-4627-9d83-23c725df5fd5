<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" dir="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0d6efd">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="NJ Repair">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- P<PERSON> Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/images/icons/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/images/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/images/icons/icon-192x192.png">

    <title><?php echo e(__('app.app_name')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <?php if(app()->getLocale() == 'ar'): ?>
        <!-- Arabic Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <?php endif; ?>

    <!-- Bootstrap CSS -->
    <?php if(app()->getLocale() == 'ar'): ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <?php else: ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <?php endif; ?>
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="<?php echo e(asset('css/custom.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('css/mobile-responsive.css')); ?>" rel="stylesheet">
    <?php if(app()->getLocale() == 'ar'): ?>
        <link href="<?php echo e(asset('css/rtl.css')); ?>" rel="stylesheet">
    <?php endif; ?>

    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: 600;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .status-badge {
            font-size: 0.875rem;
        }
        .search-form {
            max-width: 400px;
        }
    </style>

    <?php if(app()->getLocale() == 'ar'): ?>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            text-align: right;
        }
        .navbar-brand {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
        }
        .dropdown-menu {
            text-align: right;
        }
        .alert {
            text-align: right;
        }
        .form-control, .form-select {
            text-align: right;
        }
    </style>
    <?php endif; ?>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <div id="app">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?php echo e(route('dashboard')); ?>">
                    <i class="bi bi-tools"></i> <?php echo e(__('app.app_name')); ?>

                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('dashboard')); ?>">
                                <i class="bi bi-house"></i> <?php echo e(__('app.nav.dashboard')); ?>

                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('repair-tickets.index')); ?>">
                                <i class="bi bi-ticket"></i> <?php echo e(__('app.nav.repair_tickets')); ?>

                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('customers.index')); ?>">
                                <i class="bi bi-people"></i> <?php echo e(__('app.nav.customers')); ?>

                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('invoices.index')); ?>">
                                <i class="fas fa-file-invoice"></i> الفواتير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('payments.index')); ?>">
                                <i class="fas fa-credit-card"></i> المدفوعات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('inventory.index')); ?>">
                                <i class="fas fa-boxes"></i> المخزون
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-graph-up"></i> <?php echo e(__('app.nav.reports')); ?>

                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo e(route('reports.dashboard')); ?>">
                                    <i class="bi bi-speedometer2"></i> <?php echo e(__('app.nav.dashboard')); ?>

                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('reports.customer-analytics')); ?>">
                                    <i class="bi bi-person-lines-fill"></i> <?php echo e(__('app.reports.customer_analytics')); ?>

                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('reports.business-intelligence')); ?>">
                                    <i class="bi bi-graph-up-arrow"></i> <?php echo e(__('app.reports.business_intelligence')); ?>

                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('reports.financial')); ?>">
                                    <i class="fas fa-chart-line"></i> التقارير المالية
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('reports.revenue')); ?>">
                                    <i class="fas fa-money-bill-wave"></i> تقرير الإيرادات
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('reports.outstanding')); ?>">
                                    <i class="fas fa-file-invoice-dollar"></i> الفواتير المعلقة
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('notifications.index')); ?>">
                                <i class="bi bi-bell"></i> <?php echo e(__('app.notifications.title')); ?>

                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('inventory.index')); ?>">
                                <i class="bi bi-box-seam"></i> <?php echo e(__('app.inventory.title')); ?>

                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-gear"></i> <?php echo e(__('app.nav.settings')); ?>

                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo e(route('brands.index')); ?>"><?php echo e(__('app.brands.title')); ?></a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('repair-statuses.index')); ?>"><?php echo e(__('app.repair_statuses.title')); ?></a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('device-conditions.index')); ?>"><?php echo e(__('app.device_conditions.title')); ?></a></li>
                            </ul>
                        </li>
                    </ul>

                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle"></i> <?php echo e(Auth::user()->name ?? __('app.nav.user')); ?>

                            </a>
                            <ul class="dropdown-menu">
                                <?php if(Route::has('profile.edit')): ?>
                                    <li><a class="dropdown-item" href="<?php echo e(route('profile.edit')); ?>"><?php echo e(__('app.nav.profile')); ?></a></li>
                                    <li><hr class="dropdown-divider"></li>
                                <?php endif; ?>
                                <?php if(Route::has('logout')): ?>
                                    <li>
                                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="dropdown-item"><?php echo e(__('app.nav.logout')); ?></button>
                                        </form>
                                    </li>
                                <?php else: ?>
                                    <li><a class="dropdown-item" href="#" onclick="alert('Authentication not yet configured')"><?php echo e(__('app.nav.logout')); ?></a></li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container-fluid">
            <div class="row">
                <main class="col-12">
                    <!-- Alerts -->
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                            <?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if($errors->any()): ?>
                        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                            <ul class="mb-0">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Page Content -->
                    <?php echo $__env->yieldContent('content'); ?>
                </main>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="<?php echo e(asset('js/notifications.js')); ?>"></script>
    <script src="<?php echo e(asset('js/ui-enhancements.js')); ?>"></script>
    <script src="<?php echo e(asset('js/dashboard-enhancements.js')); ?>"></script>
    <script src="<?php echo e(asset('js/theme-manager.js')); ?>"></script>

    <!-- PWA Installation Script -->
    <script>
        // Register Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;

            // Show install button
            const installBtn = document.createElement('button');
            installBtn.className = 'btn btn-primary btn-sm position-fixed';
            installBtn.style.cssText = 'bottom: 20px; right: 20px; z-index: 1000; border-radius: 50px;';
            installBtn.innerHTML = '<i class="bi bi-download"></i> تثبيت التطبيق';
            installBtn.onclick = installPWA;
            document.body.appendChild(installBtn);
        });

        function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    }
                    deferredPrompt = null;
                    // Remove install button
                    const installBtn = document.querySelector('button[onclick="installPWA()"]');
                    if (installBtn) installBtn.remove();
                });
            }
        }

        // Handle app installed
        window.addEventListener('appinstalled', (evt) => {
            console.log('PWA was installed');
            // Remove install button if still visible
            const installBtn = document.querySelector('button[onclick="installPWA()"]');
            if (installBtn) installBtn.remove();
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\laragon\www\nj\resources\views/layouts/app.blade.php ENDPATH**/ ?>