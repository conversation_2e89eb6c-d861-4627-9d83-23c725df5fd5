<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" dir="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0d6efd">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="NJ Repair">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- P<PERSON> Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/images/icons/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/images/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/images/icons/icon-192x192.png">

    <title><?php echo e(__('app.app_name')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <?php if(app()->getLocale() == 'ar'): ?>
        <!-- Arabic Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <?php endif; ?>

    <!-- Bootstrap CSS -->
    <?php if(app()->getLocale() == 'ar'): ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <?php else: ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <?php endif; ?>
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="<?php echo e(asset('css/custom.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('css/mobile-responsive.css')); ?>" rel="stylesheet">
    <?php if(app()->getLocale() == 'ar'): ?>
        <link href="<?php echo e(asset('css/rtl.css')); ?>" rel="stylesheet">
    <?php endif; ?>

    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: 600;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .status-badge {
            font-size: 0.875rem;
        }
        .search-form {
            max-width: 400px;
        }
    </style>

    <?php if(app()->getLocale() == 'ar'): ?>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            text-align: right;
        }
        .navbar-brand {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
        }
        .dropdown-menu {
            text-align: right;
        }
        .alert {
            text-align: right;
        }
        .form-control, .form-select {
            text-align: right;
        }
    </style>
    <?php endif; ?>

    <?php echo $__env->yieldPushContent('styles'); ?>

    <!-- Navigation Styles -->
    <style>
        /* RTL Support for Navigation */
        [dir="rtl"] .navbar-nav .dropdown-menu {
            right: 0;
            left: auto;
        }

        [dir="rtl"] .dropdown-menu-end {
            right: auto !important;
            left: 0 !important;
        }

        /* Active Navigation States */
        .navbar-nav .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 0.375rem;
        }

        .dropdown-item.active {
            background-color: var(--bs-primary);
            color: white;
        }

        /* Notification Badge */
        .navbar-nav .badge {
            font-size: 0.7rem;
            padding: 0.25em 0.5em;
        }

        /* User Role Badge */
        .navbar-nav .nav-link .badge {
            margin-left: 0.5rem;
        }

        [dir="rtl"] .navbar-nav .nav-link .badge {
            margin-left: 0;
            margin-right: 0.5rem;
        }

        /* Dropdown Header Styling */
        .dropdown-header {
            padding: 0.75rem 1rem;
            background-color: var(--bs-light);
        }

        /* Mobile Navigation Improvements */
        @media (max-width: 991.98px) {
            .navbar-nav .dropdown-menu {
                border: none;
                box-shadow: none;
                background-color: rgba(255, 255, 255, 0.1);
                margin-left: 1rem;
            }

            .navbar-nav .dropdown-item {
                color: rgba(255, 255, 255, 0.8);
                padding: 0.5rem 1rem;
            }

            .navbar-nav .dropdown-item:hover,
            .navbar-nav .dropdown-item:focus {
                background-color: rgba(255, 255, 255, 0.1);
                color: white;
            }

            .navbar-nav .dropdown-divider {
                border-color: rgba(255, 255, 255, 0.2);
            }
        }

        /* Accessibility Improvements */
        .nav-link:focus,
        .dropdown-item:focus {
            outline: 2px solid #fff;
            outline-offset: 2px;
        }

        /* Loading State for Navigation */
        .nav-link.loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .nav-link.loading::after {
            content: "";
            display: inline-block;
            width: 12px;
            height: 12px;
            margin-left: 8px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?php echo e(route('dashboard')); ?>">
                    <i class="bi bi-tools"></i> <?php echo e(__('app.app_name')); ?>

                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <!-- Dashboard -->
                        <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'dashboard.view')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('dashboard')); ?>">
                                <i class="bi bi-house"></i> <?php echo e(__('app.nav.dashboard')); ?>

                            </a>
                        </li>
                        <?php endif; ?>

                        <!-- Repair Tickets -->
                        <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'tickets.view')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('repair-tickets.*') ? 'active' : ''); ?>" href="<?php echo e(route('repair-tickets.index')); ?>">
                                <i class="bi bi-ticket"></i> <?php echo e(__('app.nav.repair_tickets')); ?>

                            </a>
                        </li>
                        <?php endif; ?>

                        <!-- Customers -->
                        <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'customers.view')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('customers.*') ? 'active' : ''); ?>" href="<?php echo e(route('customers.index')); ?>">
                                <i class="bi bi-people"></i> <?php echo e(__('app.nav.customers')); ?>

                            </a>
                        </li>
                        <?php endif; ?>

                        <!-- Financial Management -->
                        <?php if (\Illuminate\Support\Facades\Blade::check('anypermission', 'invoices.view', 'payments.view')): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs(['invoices.*', 'payments.*']) ? 'active' : ''); ?>" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-currency-dollar"></i> <?php echo e(__('app.nav.financial')); ?>

                            </a>
                            <ul class="dropdown-menu">
                                <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'invoices.view')): ?>
                                <li><a class="dropdown-item <?php echo e(request()->routeIs('invoices.*') ? 'active' : ''); ?>" href="<?php echo e(route('invoices.index')); ?>">
                                    <i class="bi bi-receipt"></i> <?php echo e(__('app.invoices.title')); ?>

                                </a></li>
                                <?php endif; ?>
                                <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'payments.view')): ?>
                                <li><a class="dropdown-item <?php echo e(request()->routeIs('payments.*') ? 'active' : ''); ?>" href="<?php echo e(route('payments.index')); ?>">
                                    <i class="bi bi-credit-card"></i> <?php echo e(__('app.payments.title')); ?>

                                </a></li>
                                <?php endif; ?>
                                <?php if (\Illuminate\Support\Facades\Blade::check('anypermission', 'invoices.view', 'payments.view')): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('reports.financial')); ?>">
                                    <i class="bi bi-graph-up"></i> <?php echo e(__('app.reports.financial')); ?>

                                </a></li>
                                <?php endif; ?>
                            </ul>
                        </li>
                        <?php endif; ?>

                        <!-- Inventory -->
                        <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'inventory.view')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('inventory.*') ? 'active' : ''); ?>" href="<?php echo e(route('inventory.index')); ?>">
                                <i class="bi bi-box-seam"></i> <?php echo e(__('app.inventory.title')); ?>

                            </a>
                        </li>
                        <?php endif; ?>

                        <!-- POS System -->
                        <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'pos.access')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('pos.*') ? 'active' : ''); ?>" href="<?php echo e(route('pos.index')); ?>">
                                <i class="bi bi-shop"></i> <?php echo e(__('app.pos.title')); ?>

                            </a>
                        </li>
                        <?php endif; ?>

                        <!-- Reports -->
                        <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'reports.view')): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('reports.*') ? 'active' : ''); ?>" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-graph-up"></i> <?php echo e(__('app.nav.reports')); ?>

                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo e(route('reports.customer-analytics')); ?>">
                                    <i class="bi bi-person-lines-fill"></i> <?php echo e(__('app.reports.customer_analytics')); ?>

                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('reports.repair-analytics')); ?>">
                                    <i class="bi bi-wrench"></i> <?php echo e(__('app.reports.repair_analytics')); ?>

                                </a></li>
                                <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'reports.advanced')): ?>
                                <li><a class="dropdown-item" href="<?php echo e(route('reports.advanced-dashboard')); ?>">
                                    <i class="bi bi-speedometer2"></i> <?php echo e(__('app.reports.advanced_dashboard')); ?>

                                </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('reports.financial')); ?>">
                                    <i class="bi bi-cash-stack"></i> <?php echo e(__('app.reports.financial')); ?>

                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('reports.inventory')); ?>">
                                    <i class="bi bi-boxes"></i> <?php echo e(__('app.reports.inventory')); ?>

                                </a></li>
                            </ul>
                        </li>
                        <?php endif; ?>

                        <!-- Communications -->
                        <?php if (\Illuminate\Support\Facades\Blade::check('anypermission', 'whatsapp.send', 'sms.send', 'email.send')): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('communications.*') ? 'active' : ''); ?>" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-chat-dots"></i> <?php echo e(__('app.nav.communications')); ?>

                            </a>
                            <ul class="dropdown-menu">
                                <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'whatsapp.send')): ?>
                                <li><a class="dropdown-item" href="<?php echo e(route('communications.whatsapp')); ?>">
                                    <i class="bi bi-whatsapp"></i> <?php echo e(__('app.communications.whatsapp')); ?>

                                </a></li>
                                <?php endif; ?>
                                <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'sms.send')): ?>
                                <li><a class="dropdown-item" href="<?php echo e(route('communications.sms')); ?>">
                                    <i class="bi bi-phone"></i> <?php echo e(__('app.communications.sms')); ?>

                                </a></li>
                                <?php endif; ?>
                                <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'email.send')): ?>
                                <li><a class="dropdown-item" href="<?php echo e(route('communications.email')); ?>">
                                    <i class="bi bi-envelope"></i> <?php echo e(__('app.communications.email')); ?>

                                </a></li>
                                <?php endif; ?>
                                <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'whatsapp.templates')): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('communications.templates')); ?>">
                                    <i class="bi bi-file-text"></i> <?php echo e(__('app.communications.templates')); ?>

                                </a></li>
                                <?php endif; ?>
                            </ul>
                        </li>
                        <?php endif; ?>

                        <!-- Notifications -->
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('notifications.*') ? 'active' : ''); ?>" href="<?php echo e(route('notifications.index')); ?>">
                                <i class="bi bi-bell"></i>
                                <?php echo e(__('app.notifications.title')); ?>

                                <?php if(auth()->user()->unreadNotifications->count() > 0): ?>
                                    <span class="badge bg-danger ms-1"><?php echo e(auth()->user()->unreadNotifications->count()); ?></span>
                                <?php endif; ?>
                            </a>
                        </li>

                        <!-- Settings -->
                        <?php if (\Illuminate\Support\Facades\Blade::check('anypermission', 'settings.view', 'users.view', 'users.roles')): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs(['settings.*', 'users.*', 'brands.*', 'repair-statuses.*', 'device-conditions.*']) ? 'active' : ''); ?>" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-gear"></i> <?php echo e(__('app.nav.settings')); ?>

                            </a>
                            <ul class="dropdown-menu">
                                <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'settings.view')): ?>
                                <li><a class="dropdown-item" href="<?php echo e(route('settings.index')); ?>">
                                    <i class="bi bi-sliders"></i> <?php echo e(__('app.settings.general')); ?>

                                </a></li>
                                <?php endif; ?>

                                <?php if (\Illuminate\Support\Facades\Blade::check('anypermission', 'users.view', 'users.roles')): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li class="dropdown-header"><?php echo e(__('app.nav.user_management')); ?></li>
                                <?php endif; ?>

                                <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'users.view')): ?>
                                <li><a class="dropdown-item" href="<?php echo e(route('users.index')); ?>">
                                    <i class="bi bi-people"></i> <?php echo e(__('app.users.title')); ?>

                                </a></li>
                                <?php endif; ?>

                                <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'users.roles')): ?>
                                <li><a class="dropdown-item" href="<?php echo e(route('roles-permissions.index')); ?>">
                                    <i class="bi bi-shield-check"></i> <?php echo e(__('app.roles.title')); ?>

                                </a></li>
                                <?php endif; ?>

                                <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'settings.logs')): ?>
                                <li><a class="dropdown-item" href="<?php echo e(route('security.dashboard')); ?>">
                                    <i class="bi bi-shield-exclamation"></i> <?php echo e(__('app.security.dashboard')); ?>

                                </a></li>
                                <?php endif; ?>

                                <li><hr class="dropdown-divider"></li>
                                <li class="dropdown-header"><?php echo e(__('app.nav.system_data')); ?></li>

                                <li><a class="dropdown-item" href="<?php echo e(route('brands.index')); ?>">
                                    <i class="bi bi-tags"></i> <?php echo e(__('app.brands.title')); ?>

                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('repair-statuses.index')); ?>">
                                    <i class="bi bi-list-check"></i> <?php echo e(__('app.repair_statuses.title')); ?>

                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('device-conditions.index')); ?>">
                                    <i class="bi bi-clipboard-check"></i> <?php echo e(__('app.device_conditions.title')); ?>

                                </a></li>

                                <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'suppliers.view')): ?>
                                <li><a class="dropdown-item" href="<?php echo e(route('suppliers.index')); ?>">
                                    <i class="bi bi-truck"></i> <?php echo e(__('app.suppliers.title')); ?>

                                </a></li>
                                <?php endif; ?>
                            </ul>
                        </li>
                        <?php endif; ?>
                    </ul>

                    <ul class="navbar-nav">
                        <!-- User Profile Dropdown -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle"></i>
                                <?php echo e(Auth::user()->name ?? __('app.nav.user')); ?>

                                <?php if(auth()->check()): 
                $role = auth()->user()->userRole;
                $color = $role ? $role->color : '#6c757d';
                $name = $role ? $role->name : auth()->user()->role;
                echo '<span class="badge" style="background-color: ' . $color . '">' . $name . '</span>';
            endif; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li class="dropdown-header">
                                    <div class="d-flex flex-column">
                                        <span class="fw-bold"><?php echo e(Auth::user()->name); ?></span>
                                        <small class="text-muted"><?php echo e(Auth::user()->email); ?></small>
                                    </div>
                                </li>
                                <li><hr class="dropdown-divider"></li>

                                <?php if(Route::has('profile.edit')): ?>
                                <li><a class="dropdown-item" href="<?php echo e(route('profile.edit')); ?>">
                                    <i class="bi bi-person"></i> <?php echo e(__('app.nav.profile')); ?>

                                </a></li>
                                <?php endif; ?>

                                <?php if (\Illuminate\Support\Facades\Blade::check('permission', 'settings.view')): ?>
                                <li><a class="dropdown-item" href="<?php echo e(route('settings.index')); ?>">
                                    <i class="bi bi-gear"></i> <?php echo e(__('app.settings.title')); ?>

                                </a></li>
                                <?php endif; ?>

                                <li><hr class="dropdown-divider"></li>

                                <?php if(Route::has('logout')): ?>
                                <li>
                                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="bi bi-box-arrow-right"></i> <?php echo e(__('app.nav.logout')); ?>

                                        </button>
                                    </form>
                                </li>
                                <?php else: ?>
                                <li><a class="dropdown-item text-danger" href="#" onclick="alert('Authentication not yet configured')">
                                    <i class="bi bi-box-arrow-right"></i> <?php echo e(__('app.nav.logout')); ?>

                                </a></li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container-fluid">
            <div class="row">
                <main class="col-12">
                    <!-- Alerts -->
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                            <?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if($errors->any()): ?>
                        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                            <ul class="mb-0">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Page Content -->
                    <?php echo $__env->yieldContent('content'); ?>
                </main>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="<?php echo e(asset('js/notifications.js')); ?>"></script>
    <script src="<?php echo e(asset('js/ui-enhancements.js')); ?>"></script>
    <script src="<?php echo e(asset('js/dashboard-enhancements.js')); ?>"></script>
    <script src="<?php echo e(asset('js/theme-manager.js')); ?>"></script>

    <!-- PWA Installation Script -->
    <script>
        // Register Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;

            // Show install button
            const installBtn = document.createElement('button');
            installBtn.className = 'btn btn-primary btn-sm position-fixed';
            installBtn.style.cssText = 'bottom: 20px; right: 20px; z-index: 1000; border-radius: 50px;';
            installBtn.innerHTML = '<i class="bi bi-download"></i> تثبيت التطبيق';
            installBtn.onclick = installPWA;
            document.body.appendChild(installBtn);
        });

        function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    }
                    deferredPrompt = null;
                    // Remove install button
                    const installBtn = document.querySelector('button[onclick="installPWA()"]');
                    if (installBtn) installBtn.remove();
                });
            }
        }

        // Handle app installed
        window.addEventListener('appinstalled', (evt) => {
            console.log('PWA was installed');
            // Remove install button if still visible
            const installBtn = document.querySelector('button[onclick="installPWA()"]');
            if (installBtn) installBtn.remove();
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- Navigation Enhancement Script -->
    <script src="<?php echo e(asset('js/navigation.js')); ?>"></script>

    <!-- Navigation Initialization -->
    <script>
        // Initialize navigation when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Check for any navigation-specific initialization
            if (typeof initializeNavigation === 'function') {
                initializeNavigation();
            }

            // Handle form submissions in navigation
            document.querySelectorAll('.navbar form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري التحميل...';
                    }
                });
            });

            // Handle navigation link clicks with loading states
            document.querySelectorAll('.nav-link:not(.dropdown-toggle)').forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.getAttribute('href') && !this.getAttribute('href').startsWith('#')) {
                        this.classList.add('loading');
                    }
                });
            });
        });

        // Handle navigation errors
        window.addEventListener('error', function(e) {
            if (e.target.tagName === 'A' && e.target.classList.contains('nav-link')) {
                console.error('Navigation link error:', e);
                if (typeof NavigationUtils !== 'undefined') {
                    NavigationUtils.showNavigationToast('خطأ في تحميل الصفحة', 'danger');
                }
            }
        });
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\nj\resources\views/layouts/app.blade.php ENDPATH**/ ?>