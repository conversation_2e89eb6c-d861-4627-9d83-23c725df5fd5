<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BarcodeInventory extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'barcode',
        'barcode_type',
        'inventory_item_id',
        'item_name',
        'price',
        'is_active',
        'last_scanned_at',
        'scan_count',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
        'last_scanned_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the inventory item.
     */
    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(InventoryItem::class);
    }

    /**
     * Generate barcode based on type.
     */
    public static function generateBarcode(string $type = 'ean13'): string
    {
        return match ($type) {
            'ean13' => static::generateEAN13(),
            'ean8' => static::generateEAN8(),
            'code128' => static::generateCode128(),
            'code39' => static::generateCode39(),
            'qr' => static::generateQRCode(),
            default => static::generateEAN13(),
        };
    }

    /**
     * Generate EAN-13 barcode.
     */
    private static function generateEAN13(): string
    {
        // Generate 12 random digits
        $digits = '';
        for ($i = 0; $i < 12; $i++) {
            $digits .= rand(0, 9);
        }

        // Calculate check digit
        $checkDigit = static::calculateEAN13CheckDigit($digits);
        
        return $digits . $checkDigit;
    }

    /**
     * Calculate EAN-13 check digit.
     */
    private static function calculateEAN13CheckDigit(string $digits): int
    {
        $sum = 0;
        for ($i = 0; $i < 12; $i++) {
            $digit = intval($digits[$i]);
            $sum += ($i % 2 === 0) ? $digit : $digit * 3;
        }

        $remainder = $sum % 10;
        return $remainder === 0 ? 0 : 10 - $remainder;
    }

    /**
     * Generate EAN-8 barcode.
     */
    private static function generateEAN8(): string
    {
        // Generate 7 random digits
        $digits = '';
        for ($i = 0; $i < 7; $i++) {
            $digits .= rand(0, 9);
        }

        // Calculate check digit
        $checkDigit = static::calculateEAN8CheckDigit($digits);
        
        return $digits . $checkDigit;
    }

    /**
     * Calculate EAN-8 check digit.
     */
    private static function calculateEAN8CheckDigit(string $digits): int
    {
        $sum = 0;
        for ($i = 0; $i < 7; $i++) {
            $digit = intval($digits[$i]);
            $sum += ($i % 2 === 0) ? $digit * 3 : $digit;
        }

        $remainder = $sum % 10;
        return $remainder === 0 ? 0 : 10 - $remainder;
    }

    /**
     * Generate Code 128 barcode.
     */
    private static function generateCode128(): string
    {
        // Generate random alphanumeric string
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $length = rand(8, 12);
        $code = '';

        for ($i = 0; $i < $length; $i++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $code;
    }

    /**
     * Generate Code 39 barcode.
     */
    private static function generateCode39(): string
    {
        // Generate random alphanumeric string (Code 39 compatible)
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%';
        $length = rand(6, 10);
        $code = '';

        for ($i = 0; $i < $length; $i++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $code;
    }

    /**
     * Generate QR code data.
     */
    private static function generateQRCode(): string
    {
        // Generate unique identifier for QR code
        return 'NJ-' . now()->format('YmdHis') . '-' . rand(1000, 9999);
    }

    /**
     * Create barcode for inventory item.
     */
    public static function createForInventoryItem(InventoryItem $item, string $barcodeType = 'ean13'): self
    {
        // Check if item already has a barcode
        $existingBarcode = static::where('inventory_item_id', $item->id)->first();
        if ($existingBarcode) {
            return $existingBarcode;
        }

        // Generate unique barcode
        do {
            $barcode = static::generateBarcode($barcodeType);
        } while (static::where('barcode', $barcode)->exists());

        return static::create([
            'barcode' => $barcode,
            'barcode_type' => $barcodeType,
            'inventory_item_id' => $item->id,
            'item_name' => $item->name,
            'price' => $item->selling_price,
            'is_active' => true,
        ]);
    }

    /**
     * Create standalone barcode (not linked to inventory).
     */
    public static function createStandalone(string $itemName, float $price, string $barcodeType = 'ean13'): self
    {
        // Generate unique barcode
        do {
            $barcode = static::generateBarcode($barcodeType);
        } while (static::where('barcode', $barcode)->exists());

        return static::create([
            'barcode' => $barcode,
            'barcode_type' => $barcodeType,
            'inventory_item_id' => null,
            'item_name' => $itemName,
            'price' => $price,
            'is_active' => true,
        ]);
    }

    /**
     * Record barcode scan.
     */
    public function recordScan(): void
    {
        $this->increment('scan_count');
        $this->update(['last_scanned_at' => now()]);
    }

    /**
     * Validate barcode format.
     */
    public function validateBarcodeFormat(): bool
    {
        return match ($this->barcode_type) {
            'ean13' => $this->validateEAN13(),
            'ean8' => $this->validateEAN8(),
            'code128' => $this->validateCode128(),
            'code39' => $this->validateCode39(),
            'qr' => true, // QR codes can contain any data
            default => false,
        };
    }

    /**
     * Validate EAN-13 format.
     */
    private function validateEAN13(): bool
    {
        if (strlen($this->barcode) !== 13 || !ctype_digit($this->barcode)) {
            return false;
        }

        $digits = substr($this->barcode, 0, 12);
        $checkDigit = intval(substr($this->barcode, 12, 1));
        $calculatedCheckDigit = static::calculateEAN13CheckDigit($digits);

        return $checkDigit === $calculatedCheckDigit;
    }

    /**
     * Validate EAN-8 format.
     */
    private function validateEAN8(): bool
    {
        if (strlen($this->barcode) !== 8 || !ctype_digit($this->barcode)) {
            return false;
        }

        $digits = substr($this->barcode, 0, 7);
        $checkDigit = intval(substr($this->barcode, 7, 1));
        $calculatedCheckDigit = static::calculateEAN8CheckDigit($digits);

        return $checkDigit === $calculatedCheckDigit;
    }

    /**
     * Validate Code 128 format.
     */
    private function validateCode128(): bool
    {
        // Code 128 can contain any ASCII character
        return strlen($this->barcode) >= 1 && strlen($this->barcode) <= 48;
    }

    /**
     * Validate Code 39 format.
     */
    private function validateCode39(): bool
    {
        // Code 39 character set
        $validChars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%';
        
        for ($i = 0; $i < strlen($this->barcode); $i++) {
            if (strpos($validChars, $this->barcode[$i]) === false) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get barcode type display name.
     */
    public function getBarcodeTypeDisplayAttribute(): string
    {
        $types = [
            'ean13' => 'EAN-13',
            'ean8' => 'EAN-8',
            'code128' => 'Code 128',
            'code39' => 'Code 39',
            'qr' => 'QR Code',
        ];

        return $types[$this->barcode_type] ?? $this->barcode_type;
    }

    /**
     * Get formatted barcode for display.
     */
    public function getFormattedBarcodeAttribute(): string
    {
        return match ($this->barcode_type) {
            'ean13' => $this->formatEAN13(),
            'ean8' => $this->formatEAN8(),
            default => $this->barcode,
        };
    }

    /**
     * Format EAN-13 barcode for display.
     */
    private function formatEAN13(): string
    {
        if (strlen($this->barcode) !== 13) {
            return $this->barcode;
        }

        return substr($this->barcode, 0, 1) . ' ' .
               substr($this->barcode, 1, 6) . ' ' .
               substr($this->barcode, 7, 6);
    }

    /**
     * Format EAN-8 barcode for display.
     */
    private function formatEAN8(): string
    {
        if (strlen($this->barcode) !== 8) {
            return $this->barcode;
        }

        return substr($this->barcode, 0, 4) . ' ' . substr($this->barcode, 4, 4);
    }

    /**
     * Scope for active barcodes.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for barcodes by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('barcode_type', $type);
    }

    /**
     * Search barcodes.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('barcode', 'like', "%{$search}%")
              ->orWhere('item_name', 'like', "%{$search}%");
        });
    }

    /**
     * Find by barcode.
     */
    public static function findByBarcode(string $barcode): ?self
    {
        return static::where('barcode', $barcode)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Get scan statistics.
     */
    public function getScanStats(): array
    {
        return [
            'total_scans' => $this->scan_count,
            'last_scanned' => $this->last_scanned_at,
            'days_since_last_scan' => $this->last_scanned_at ? 
                $this->last_scanned_at->diffInDays(now()) : null,
            'average_scans_per_day' => $this->created_at ? 
                $this->scan_count / max(1, $this->created_at->diffInDays(now())) : 0,
        ];
    }
}
