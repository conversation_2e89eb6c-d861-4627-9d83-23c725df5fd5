/**
 * Visual Pattern Lock Component for NJ Repair Shop
 * Allows customers to draw unlock patterns similar to Android pattern lock
 */
class PatternLock {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.canvas = null;
        this.ctx = null;
        this.dots = [];
        this.pattern = [];
        this.isDrawing = false;
        this.currentPos = { x: 0, y: 0 };
        
        // Configuration
        this.config = {
            gridSize: 3,
            dotRadius: 20,
            lineWidth: 8,
            dotColor: '#6c757d',
            activeDotColor: '#007bff',
            lineColor: '#007bff',
            backgroundColor: '#f8f9fa',
            canvasSize: 300,
            ...options
        };
        
        this.init();
    }
    
    init() {
        this.createCanvas();
        this.setupDots();
        this.setupEventListeners();
        this.draw();
    }
    
    createCanvas() {
        this.canvas = document.createElement('canvas');
        this.canvas.width = this.config.canvasSize;
        this.canvas.height = this.config.canvasSize;
        this.canvas.style.border = '2px solid #dee2e6';
        this.canvas.style.borderRadius = '8px';
        this.canvas.style.cursor = 'pointer';
        this.canvas.style.touchAction = 'none';
        
        this.ctx = this.canvas.getContext('2d');
        this.container.appendChild(this.canvas);
    }
    
    setupDots() {
        const spacing = this.config.canvasSize / (this.config.gridSize + 1);
        this.dots = [];
        
        for (let row = 0; row < this.config.gridSize; row++) {
            for (let col = 0; col < this.config.gridSize; col++) {
                const x = spacing * (col + 1);
                const y = spacing * (row + 1);
                const id = row * this.config.gridSize + col + 1;
                
                this.dots.push({
                    id: id,
                    x: x,
                    y: y,
                    active: false,
                    row: row,
                    col: col
                });
            }
        }
    }
    
    setupEventListeners() {
        // Mouse events
        this.canvas.addEventListener('mousedown', this.handleStart.bind(this));
        this.canvas.addEventListener('mousemove', this.handleMove.bind(this));
        this.canvas.addEventListener('mouseup', this.handleEnd.bind(this));
        this.canvas.addEventListener('mouseleave', this.handleEnd.bind(this));
        
        // Touch events
        this.canvas.addEventListener('touchstart', this.handleStart.bind(this));
        this.canvas.addEventListener('touchmove', this.handleMove.bind(this));
        this.canvas.addEventListener('touchend', this.handleEnd.bind(this));
        this.canvas.addEventListener('touchcancel', this.handleEnd.bind(this));
    }
    
    getEventPos(event) {
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;
        
        let clientX, clientY;
        
        if (event.touches && event.touches.length > 0) {
            clientX = event.touches[0].clientX;
            clientY = event.touches[0].clientY;
        } else {
            clientX = event.clientX;
            clientY = event.clientY;
        }
        
        return {
            x: (clientX - rect.left) * scaleX,
            y: (clientY - rect.top) * scaleY
        };
    }
    
    getDotAt(x, y) {
        return this.dots.find(dot => {
            const distance = Math.sqrt(Math.pow(x - dot.x, 2) + Math.pow(y - dot.y, 2));
            return distance <= this.config.dotRadius;
        });
    }
    
    handleStart(event) {
        event.preventDefault();
        const pos = this.getEventPos(event);
        const dot = this.getDotAt(pos.x, pos.y);
        
        if (dot && !dot.active) {
            this.isDrawing = true;
            this.activateDot(dot);
            this.currentPos = { x: dot.x, y: dot.y };
        }
    }
    
    handleMove(event) {
        if (!this.isDrawing) return;
        
        event.preventDefault();
        const pos = this.getEventPos(event);
        const dot = this.getDotAt(pos.x, pos.y);
        
        if (dot && !dot.active) {
            // Check if the path is valid (no jumping over dots)
            if (this.isValidConnection(this.pattern[this.pattern.length - 1], dot.id)) {
                this.activateDot(dot);
                this.currentPos = { x: dot.x, y: dot.y };
            }
        } else {
            this.currentPos = pos;
        }
        
        this.draw();
    }
    
    handleEnd(event) {
        if (!this.isDrawing) return;
        
        event.preventDefault();
        this.isDrawing = false;
        this.currentPos = null;
        this.draw();
        
        // Trigger pattern complete event
        if (this.pattern.length >= 4) {
            this.onPatternComplete(this.getPatternString());
        } else {
            this.onPatternTooShort();
        }
    }
    
    activateDot(dot) {
        dot.active = true;
        this.pattern.push(dot.id);
    }
    
    isValidConnection(fromDotId, toDotId) {
        if (!fromDotId) return true;
        
        const fromDot = this.dots.find(d => d.id === fromDotId);
        const toDot = this.dots.find(d => d.id === toDotId);
        
        // Check if there's a dot in between that should be activated
        const rowDiff = toDot.row - fromDot.row;
        const colDiff = toDot.col - fromDot.col;
        
        // If moving more than 1 step in any direction, check for intermediate dots
        if (Math.abs(rowDiff) > 1 || Math.abs(colDiff) > 1) {
            const stepRow = rowDiff === 0 ? 0 : rowDiff / Math.abs(rowDiff);
            const stepCol = colDiff === 0 ? 0 : colDiff / Math.abs(colDiff);
            
            let currentRow = fromDot.row + stepRow;
            let currentCol = fromDot.col + stepCol;
            
            while (currentRow !== toDot.row || currentCol !== toDot.col) {
                const intermediateDot = this.dots.find(d => d.row === currentRow && d.col === currentCol);
                if (intermediateDot && !intermediateDot.active) {
                    // Must activate intermediate dot first
                    this.activateDot(intermediateDot);
                }
                currentRow += stepRow;
                currentCol += stepCol;
            }
        }
        
        return true;
    }
    
    draw() {
        // Clear canvas
        this.ctx.fillStyle = this.config.backgroundColor;
        this.ctx.fillRect(0, 0, this.config.canvasSize, this.config.canvasSize);
        
        // Draw pattern lines
        if (this.pattern.length > 1) {
            this.ctx.strokeStyle = this.config.lineColor;
            this.ctx.lineWidth = this.config.lineWidth;
            this.ctx.lineCap = 'round';
            this.ctx.lineJoin = 'round';
            
            this.ctx.beginPath();
            const firstDot = this.dots.find(d => d.id === this.pattern[0]);
            this.ctx.moveTo(firstDot.x, firstDot.y);
            
            for (let i = 1; i < this.pattern.length; i++) {
                const dot = this.dots.find(d => d.id === this.pattern[i]);
                this.ctx.lineTo(dot.x, dot.y);
            }
            
            // Draw line to current position if drawing
            if (this.isDrawing && this.currentPos) {
                this.ctx.lineTo(this.currentPos.x, this.currentPos.y);
            }
            
            this.ctx.stroke();
        }
        
        // Draw dots
        this.dots.forEach(dot => {
            this.ctx.fillStyle = dot.active ? this.config.activeDotColor : this.config.dotColor;
            this.ctx.beginPath();
            this.ctx.arc(dot.x, dot.y, this.config.dotRadius, 0, 2 * Math.PI);
            this.ctx.fill();
            
            // Draw dot number for active dots
            if (dot.active) {
                this.ctx.fillStyle = 'white';
                this.ctx.font = '14px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                const index = this.pattern.indexOf(dot.id) + 1;
                this.ctx.fillText(index.toString(), dot.x, dot.y);
            }
        });
    }
    
    getPatternString() {
        return this.pattern.join('-');
    }
    
    setPattern(patternString) {
        if (!patternString) return;
        
        this.clear();
        const patternArray = patternString.split('-').map(id => parseInt(id));
        
        patternArray.forEach(dotId => {
            const dot = this.dots.find(d => d.id === dotId);
            if (dot) {
                this.activateDot(dot);
            }
        });
        
        this.draw();
    }
    
    clear() {
        this.pattern = [];
        this.dots.forEach(dot => dot.active = false);
        this.isDrawing = false;
        this.currentPos = null;
        this.draw();
    }
    
    // Event callbacks (to be overridden)
    onPatternComplete(pattern) {
        console.log('Pattern completed:', pattern);
    }
    
    onPatternTooShort() {
        console.log('Pattern too short (minimum 4 dots)');
    }
}

// Export for use in other scripts
window.PatternLock = PatternLock;
