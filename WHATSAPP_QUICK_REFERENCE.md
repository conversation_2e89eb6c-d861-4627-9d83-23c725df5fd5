# 📱 WhatsApp Business API - Quick Reference

## 🚀 Quick Setup Commands

```bash
# Complete setup
php artisan whatsapp:setup

# Test connection
php artisan whatsapp:test-connection

# Send test message
php artisan whatsapp:send-test +************

# Check templates
php artisan whatsapp:check-templates

# View logs
php artisan whatsapp:logs --recent
```

## 🔧 Environment Variables

```env
# Required
WHATSAPP_ACCESS_TOKEN=your_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_id
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_verify_token

# Optional
WHATSAPP_ENABLED=true
WHATSAPP_FALLBACK_TO_SMS=true
WHATSAPP_DEFAULT_LANGUAGE=ar
WHATSAPP_RATE_LIMIT_PER_MINUTE=80
```

## 📝 Code Examples

### Send Simple Message
```php
use App\Services\WhatsAppService;

$whatsapp = app(WhatsAppService::class);
$result = $whatsapp->sendMessage('+************', 'مرحباً من ورشة الإصلاح!');
```

### Send Template Message
```php
$parameters = ['أحمد محمد', 'iPhone 13', 'TKT-001'];
$result = $whatsapp->sendTemplateMessage(
    '+************', 
    'repair_received_ar', 
    $parameters
);
```

### Handle Webhook
```php
// In WhatsAppWebhookController
public function handle(Request $request)
{
    $payload = $request->getContent();
    $signature = $request->header('X-Hub-Signature-256');
    
    if (!$this->validateSignature($payload, $signature)) {
        return response('Unauthorized', 401);
    }
    
    dispatch(new ProcessWhatsAppWebhook($payload));
    return response('OK', 200);
}
```

## 🎯 Template Examples

### Repair Status Templates
```
repair_received_ar:
مرحباً {{1}}, تم استلام جهازك {{2}} بنجاح. رقم التذكرة: {{3}}

repair_completed_ar:
تهانينا {{1}}! تم إصلاح جهازك {{2}} بنجاح. التكلفة: {{3}} ريال

payment_reminder_ar:
عزيزي {{1}}, نذكركم بفاتورة رقم {{2}} بمبلغ {{3}} ريال
```

## 🔍 Debugging Commands

```bash
# Check system status
php artisan whatsapp:status

# Validate configuration
php artisan whatsapp:validate-config

# Test webhook endpoint
curl -X GET "https://yourdomain.com/api/whatsapp/webhook?hub.mode=subscribe&hub.challenge=test&hub.verify_token=YOUR_TOKEN"

# View failed jobs
php artisan queue:failed

# Monitor queue
php artisan queue:monitor redis:whatsapp
```

## 📊 Database Queries

```sql
-- Recent messages
SELECT * FROM whatsapp_messages 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) 
ORDER BY created_at DESC;

-- Failed messages
SELECT * FROM whatsapp_messages 
WHERE status = 'failed' 
ORDER BY created_at DESC;

-- Template usage
SELECT name, usage_count, success_rate 
FROM whatsapp_templates 
WHERE is_active = 1;
```

## 🚨 Common Errors & Quick Fixes

### Webhook Verification Failed
```bash
# Check verify token
echo $WHATSAPP_WEBHOOK_VERIFY_TOKEN

# Test webhook manually
curl -X GET "https://yourdomain.com/api/whatsapp/webhook?hub.mode=subscribe&hub.challenge=test&hub.verify_token=YOUR_TOKEN"
```

### Template Not Found
```bash
# Reseed templates
php artisan db:seed --class=WhatsAppTemplateSeeder

# Check template status
php artisan whatsapp:list-templates
```

### Rate Limit Exceeded
```php
// Implement rate limiting
use Illuminate\Support\Facades\RateLimiter;

if (RateLimiter::tooManyAttempts('whatsapp:'.$phone, 5)) {
    throw new Exception('Rate limit exceeded');
}
RateLimiter::hit('whatsapp:'.$phone, 60);
```

### Invalid Phone Number
```php
// Correct format
$phone = '+************';  // ✅
$phone = '************';   // ❌
$phone = '0501234567';     // ❌

// Validation
function validatePhone($phone) {
    return preg_match('/^\+[1-9]\d{1,14}$/', $phone);
}
```

## 🔄 Queue Management

```bash
# Start workers
php artisan queue:work --queue=whatsapp,default

# Restart workers
php artisan queue:restart

# Monitor queues
php artisan horizon:status

# Process failed jobs
php artisan queue:retry all
```

## 📱 Meta Developer Console URLs

- **App Dashboard**: https://developers.facebook.com/apps/
- **WhatsApp Manager**: https://business.facebook.com/wa/manage/
- **Message Templates**: https://business.facebook.com/wa/manage/message-templates/
- **Webhook Configuration**: https://developers.facebook.com/apps/YOUR_APP_ID/webhooks/

## 🔐 Security Checklist

- [ ] HTTPS enabled
- [ ] Webhook signature validation
- [ ] Rate limiting implemented
- [ ] Access tokens secured
- [ ] Error logging enabled
- [ ] Queue workers monitored

## 📞 Support Resources

- **Documentation**: https://developers.facebook.com/docs/whatsapp
- **Community**: https://developers.facebook.com/community/
- **Status Page**: https://developers.facebook.com/status/

## 🎯 Production Monitoring

```bash
# System health
php artisan whatsapp:health-check

# API usage
php artisan whatsapp:usage-report

# Error summary
php artisan whatsapp:error-summary --hours=24

# Performance metrics
php artisan whatsapp:metrics --period=today
```

## 🔧 Maintenance Commands

```bash
# Clean old logs
php artisan whatsapp:cleanup-logs --days=30

# Update template status
php artisan whatsapp:sync-templates

# Refresh webhook configuration
php artisan whatsapp:refresh-webhook

# Generate usage report
php artisan whatsapp:usage-report --export
```

---

**💡 Pro Tips:**
- Always test in development first
- Monitor rate limits closely
- Keep templates simple and clear
- Use queues for bulk operations
- Implement proper error handling
- Regular backup of message data
