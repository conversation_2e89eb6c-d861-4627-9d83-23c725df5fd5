/**
 * NJ Repair Shop - UI Enhancement System
 * Interactive features and user experience improvements
 */

class UIEnhancements {
    constructor() {
        this.init();
    }

    init() {
        this.initializeTooltips();
        this.initializePopovers();
        this.initializeAnimations();
        this.initializeSearchEnhancements();
        this.initializeTableEnhancements();
        this.initializeFormEnhancements();
        this.initializeModalEnhancements();
        this.initializeKeyboardShortcuts();
        this.initializeProgressBars();
        this.initializeLazyLoading();
    }

    // Initialize Bootstrap tooltips
    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Initialize Bootstrap popovers
    initializePopovers() {
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }

    // Initialize scroll animations
    initializeAnimations() {
        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe elements with animation class
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });

        // Add CSS for animations
        this.addAnimationStyles();
    }

    addAnimationStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .animate-on-scroll {
                opacity: 0;
                transform: translateY(20px);
                transition: opacity 0.6s ease, transform 0.6s ease;
            }
            
            .animate-fade-in {
                opacity: 1;
                transform: translateY(0);
            }
            
            .pulse-animation {
                animation: pulse 2s infinite;
            }
            
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
            
            .slide-in-right {
                animation: slideInRight 0.5s ease-out;
            }
            
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            .bounce-in {
                animation: bounceIn 0.6s ease-out;
            }
            
            @keyframes bounceIn {
                0% {
                    transform: scale(0.3);
                    opacity: 0;
                }
                50% {
                    transform: scale(1.05);
                }
                70% {
                    transform: scale(0.9);
                }
                100% {
                    transform: scale(1);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Enhanced search functionality
    initializeSearchEnhancements() {
        const searchInputs = document.querySelectorAll('input[type="search"], .search-input');
        
        searchInputs.forEach(input => {
            let searchTimeout;
            
            // Add search icon
            this.addSearchIcon(input);
            
            // Add clear button
            this.addClearButton(input);
            
            // Add live search functionality
            input.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.performLiveSearch(input, e.target.value);
                }, 300);
            });
        });
    }

    addSearchIcon(input) {
        if (input.parentElement.classList.contains('search-container')) return;
        
        const container = document.createElement('div');
        container.className = 'search-container position-relative';
        input.parentNode.insertBefore(container, input);
        container.appendChild(input);
        
        const icon = document.createElement('i');
        icon.className = 'bi bi-search search-icon position-absolute';
        container.appendChild(icon);
        
        input.style.paddingLeft = '2.5rem';
    }

    addClearButton(input) {
        const clearBtn = document.createElement('button');
        clearBtn.type = 'button';
        clearBtn.className = 'btn btn-link position-absolute end-0 top-50 translate-middle-y';
        clearBtn.style.display = 'none';
        clearBtn.innerHTML = '<i class="bi bi-x-circle"></i>';
        
        input.parentElement.appendChild(clearBtn);
        
        // Show/hide clear button
        input.addEventListener('input', () => {
            clearBtn.style.display = input.value ? 'block' : 'none';
        });
        
        // Clear input on click
        clearBtn.addEventListener('click', () => {
            input.value = '';
            input.focus();
            clearBtn.style.display = 'none';
            input.dispatchEvent(new Event('input'));
        });
    }

    performLiveSearch(input, query) {
        // This would typically make an AJAX request
        // For now, we'll just highlight matching text
        if (query.length < 2) return;
        
        const searchableElements = document.querySelectorAll('.searchable');
        searchableElements.forEach(element => {
            const text = element.textContent.toLowerCase();
            const matches = text.includes(query.toLowerCase());
            
            if (matches) {
                element.classList.add('search-highlight');
            } else {
                element.classList.remove('search-highlight');
            }
        });
    }

    // Enhanced table functionality
    initializeTableEnhancements() {
        const tables = document.querySelectorAll('.table-enhanced');
        
        tables.forEach(table => {
            this.addTableSorting(table);
            this.addRowHoverEffects(table);
            this.addColumnResizing(table);
        });
    }

    addTableSorting(table) {
        const headers = table.querySelectorAll('th[data-sortable="true"]');
        
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.innerHTML += ' <i class="bi bi-arrow-down-up sort-icon"></i>';
            
            header.addEventListener('click', () => {
                this.sortTable(table, header);
            });
        });
    }

    sortTable(table, header) {
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        const isAscending = header.classList.contains('sort-asc');
        
        // Remove existing sort classes
        table.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
        
        // Add new sort class
        header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
        
        // Sort rows
        rows.sort((a, b) => {
            const aText = a.children[columnIndex].textContent.trim();
            const bText = b.children[columnIndex].textContent.trim();
            
            const comparison = aText.localeCompare(bText, undefined, { numeric: true });
            return isAscending ? -comparison : comparison;
        });
        
        // Reorder rows
        rows.forEach(row => tbody.appendChild(row));
    }

    addRowHoverEffects(table) {
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            row.addEventListener('mouseenter', () => {
                row.style.transform = 'scale(1.01)';
                row.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
            });
            
            row.addEventListener('mouseleave', () => {
                row.style.transform = 'scale(1)';
                row.style.boxShadow = 'none';
            });
        });
    }

    addColumnResizing(table) {
        // Add resize handles to table headers
        const headers = table.querySelectorAll('th');
        
        headers.forEach((header, index) => {
            if (index < headers.length - 1) { // Don't add to last column
                const resizer = document.createElement('div');
                resizer.className = 'column-resizer';
                resizer.style.cssText = `
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 5px;
                    height: 100%;
                    cursor: col-resize;
                    background: transparent;
                `;
                
                header.style.position = 'relative';
                header.appendChild(resizer);
                
                this.makeResizable(resizer, header);
            }
        });
    }

    makeResizable(resizer, header) {
        let startX, startWidth;
        
        resizer.addEventListener('mousedown', (e) => {
            startX = e.clientX;
            startWidth = parseInt(document.defaultView.getComputedStyle(header).width, 10);
            document.addEventListener('mousemove', doDrag);
            document.addEventListener('mouseup', stopDrag);
        });
        
        function doDrag(e) {
            header.style.width = (startWidth + e.clientX - startX) + 'px';
        }
        
        function stopDrag() {
            document.removeEventListener('mousemove', doDrag);
            document.removeEventListener('mouseup', stopDrag);
        }
    }

    // Enhanced form functionality
    initializeFormEnhancements() {
        this.addFormValidation();
        this.addAutoSave();
        this.addCharacterCounters();
    }

    addFormValidation() {
        const forms = document.querySelectorAll('form[data-validate="true"]');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    }

    validateForm(form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!input.checkValidity()) {
                isValid = false;
                this.showFieldError(input);
            } else {
                this.clearFieldError(input);
            }
        });
        
        return isValid;
    }

    showFieldError(input) {
        const feedback = input.parentElement.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.style.display = 'block';
        }
        input.classList.add('is-invalid');
    }

    clearFieldError(input) {
        const feedback = input.parentElement.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.style.display = 'none';
        }
        input.classList.remove('is-invalid');
        input.classList.add('is-valid');
    }

    addAutoSave() {
        const autoSaveForms = document.querySelectorAll('form[data-autosave="true"]');
        
        autoSaveForms.forEach(form => {
            let saveTimeout;
            
            form.addEventListener('input', () => {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    this.autoSaveForm(form);
                }, 2000);
            });
        });
    }

    autoSaveForm(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        // Save to localStorage
        localStorage.setItem(`autosave_${form.id}`, JSON.stringify(data));
        
        // Show auto-save indicator
        this.showAutoSaveIndicator();
    }

    showAutoSaveIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'auto-save-indicator position-fixed bottom-0 end-0 m-3 p-2 bg-success text-white rounded';
        indicator.innerHTML = '<i class="bi bi-check-circle me-1"></i>Auto-saved';
        document.body.appendChild(indicator);
        
        setTimeout(() => {
            indicator.remove();
        }, 2000);
    }

    addCharacterCounters() {
        const textareas = document.querySelectorAll('textarea[data-max-length]');
        
        textareas.forEach(textarea => {
            const maxLength = parseInt(textarea.getAttribute('data-max-length'));
            const counter = document.createElement('div');
            counter.className = 'character-counter text-muted small mt-1';
            textarea.parentElement.appendChild(counter);
            
            const updateCounter = () => {
                const remaining = maxLength - textarea.value.length;
                counter.textContent = `${remaining} characters remaining`;
                counter.className = `character-counter small mt-1 ${remaining < 50 ? 'text-warning' : remaining < 10 ? 'text-danger' : 'text-muted'}`;
            };
            
            textarea.addEventListener('input', updateCounter);
            updateCounter();
        });
    }

    // Modal enhancements
    initializeModalEnhancements() {
        const modals = document.querySelectorAll('.modal');
        
        modals.forEach(modal => {
            modal.addEventListener('show.bs.modal', () => {
                modal.classList.add('slide-in-right');
            });
            
            modal.addEventListener('hidden.bs.modal', () => {
                modal.classList.remove('slide-in-right');
            });
        });
    }

    // Keyboard shortcuts
    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('input[type="search"], .search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }
            
            // Escape to close modals
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    bootstrap.Modal.getInstance(openModal).hide();
                }
            }
        });
    }

    // Progress bars
    initializeProgressBars() {
        const progressBars = document.querySelectorAll('.progress-bar[data-animate="true"]');
        
        progressBars.forEach(bar => {
            const targetWidth = bar.style.width || bar.getAttribute('aria-valuenow') + '%';
            bar.style.width = '0%';
            
            setTimeout(() => {
                bar.style.transition = 'width 1s ease-in-out';
                bar.style.width = targetWidth;
            }, 100);
        });
    }

    // Lazy loading for images
    initializeLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    }
}

// Initialize UI enhancements when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new UIEnhancements();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIEnhancements;
}
