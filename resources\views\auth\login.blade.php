@extends('layouts.guest')

@section('content')
<div class="auth-card">
    <div class="auth-header">
        <div class="auth-logo">
            <i class="bi bi-tools"></i>
        </div>
        <h1 class="auth-title">ورشة إصلاح NJ</h1>
        <p class="auth-subtitle">نظام إدارة الورشة</p>
    </div>
    <div class="auth-body">
        <!-- Session Status -->
        @if (session('status'))
            <div class="alert alert-success mb-4" role="alert">
                {{ session('status') }}
            </div>
        @endif

        @if (session('success'))
            <div class="alert alert-success mb-4" role="alert">
                <i class="bi bi-check-circle"></i> {{ session('success') }}
            </div>
        @endif

        @if (session('error'))
            <div class="alert alert-danger mb-4" role="alert">
                <i class="bi bi-exclamation-triangle"></i> {{ session('error') }}
            </div>
        @endif

        <form method="POST" action="{{ route('login') }}">
            @csrf

            <!-- Email Address -->
            <div class="mb-3">
                <label for="email" class="form-label">البريد الإلكتروني</label>
                <input id="email"
                       class="form-control @error('email') is-invalid @enderror"
                       type="email"
                       name="email"
                       value="{{ old('email') }}"
                       required
                       autofocus
                       autocomplete="username"
                       placeholder="أدخل البريد الإلكتروني">
                @error('email')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Password -->
            <div class="mb-3">
                <label for="password" class="form-label">كلمة المرور</label>
                <input id="password"
                       class="form-control @error('password') is-invalid @enderror"
                       type="password"
                       name="password"
                       required
                       autocomplete="current-password"
                       placeholder="أدخل كلمة المرور">
                @error('password')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Remember Me -->
            <div class="mb-4 form-check">
                <input id="remember_me" type="checkbox" class="form-check-input" name="remember">
                <label for="remember_me" class="form-check-label">
                    تذكرني
                </label>
            </div>

            <!-- Login Button -->
            <div class="mb-3">
                <button type="submit" class="btn btn-primary btn-login">
                    <i class="bi bi-box-arrow-in-right"></i>
                    تسجيل الدخول
                </button>
            </div>

            <!-- Forgot Password -->
            @if (Route::has('password.request'))
                <div class="text-center">
                    <a class="forgot-password" href="{{ route('password.request') }}">
                        <i class="bi bi-question-circle"></i>
                        نسيت كلمة المرور؟
                    </a>
                </div>
            @endif
        </form>
    </div>
</div>
@endsection
