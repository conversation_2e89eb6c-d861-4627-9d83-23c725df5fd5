<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_message_templates', function (Blueprint $table) {
            $table->id();
            
            // Template identification
            $table->string('name')->unique(); // Internal name
            $table->string('whatsapp_name')->unique(); // WhatsApp API template name
            $table->string('display_name'); // User-friendly name
            $table->string('display_name_ar'); // Arabic display name
            $table->text('description')->nullable();
            $table->text('description_ar')->nullable();
            
            // Category relationship
            $table->foreignId('category_id')->constrained('whatsapp_template_categories')->onDelete('cascade');
            
            // Template content
            $table->string('language', 10)->default('ar');
            $table->text('header_text')->nullable();
            $table->text('body_text');
            $table->text('footer_text')->nullable();
            $table->json('buttons')->nullable(); // Call-to-action buttons
            
            // WhatsApp API specific
            $table->enum('status', [
                'DRAFT',
                'PENDING',
                'APPROVED', 
                'REJECTED',
                'DISABLED',
                'PAUSED'
            ])->default('DRAFT');
            
            $table->string('whatsapp_template_id')->nullable(); // From WhatsApp API
            $table->enum('quality_rating', ['GREEN', 'YELLOW', 'RED', 'UNKNOWN'])->default('UNKNOWN');
            $table->text('rejection_reason')->nullable();
            
            // Template structure
            $table->json('variables')->nullable(); // Variable definitions
            $table->json('sample_values')->nullable(); // Sample values for variables
            $table->boolean('has_media')->default(false);
            $table->string('media_type')->nullable(); // image, document, video
            
            // Usage and performance
            $table->boolean('is_active')->default(true);
            $table->integer('usage_count')->default(0);
            $table->integer('success_count')->default(0);
            $table->integer('failure_count')->default(0);
            $table->decimal('success_rate', 5, 2)->default(0);
            $table->timestamp('last_used_at')->nullable();
            
            // Approval workflow
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->text('approval_notes')->nullable();
            
            // Scheduling and automation
            $table->boolean('auto_send_enabled')->default(false);
            $table->json('auto_send_triggers')->nullable(); // Conditions for auto-sending
            $table->json('send_schedule')->nullable(); // Time-based sending rules
            
            // Compliance and opt-out
            $table->boolean('requires_opt_in')->default(false);
            $table->boolean('includes_opt_out')->default(true);
            $table->text('opt_out_instructions')->nullable();
            
            $table->timestamps();
            
            // Foreign keys
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
            
            // Indexes
            $table->index(['category_id', 'status']);
            $table->index(['is_active', 'status']);
            $table->index(['language', 'status']);
            $table->index('quality_rating');
            $table->index('usage_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_message_templates');
    }
};
