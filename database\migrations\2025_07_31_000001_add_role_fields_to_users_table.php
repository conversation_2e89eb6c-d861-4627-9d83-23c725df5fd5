<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Role and permissions
            $table->enum('role', ['admin', 'manager', 'technician', 'receptionist'])
                  ->default('receptionist')
                  ->after('email');
            
            // Profile information
            $table->string('phone_number')->nullable()->after('role');
            $table->text('address')->nullable()->after('phone_number');
            $table->date('hire_date')->nullable()->after('address');
            $table->decimal('hourly_rate', 8, 2)->nullable()->after('hire_date');
            
            // Status and preferences
            $table->boolean('is_active')->default(true)->after('hourly_rate');
            $table->string('preferred_language', 2)->default('ar')->after('is_active');
            $table->json('permissions')->nullable()->after('preferred_language');
            
            // Tracking
            $table->timestamp('last_login_at')->nullable()->after('permissions');
            $table->string('last_login_ip')->nullable()->after('last_login_at');
            
            // Indexes
            $table->index('role');
            $table->index('is_active');
            $table->index('last_login_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['role']);
            $table->dropIndex(['is_active']);
            $table->dropIndex(['last_login_at']);
            
            $table->dropColumn([
                'role',
                'phone_number',
                'address',
                'hire_date',
                'hourly_rate',
                'is_active',
                'preferred_language',
                'permissions',
                'last_login_at',
                'last_login_ip'
            ]);
        });
    }
};
