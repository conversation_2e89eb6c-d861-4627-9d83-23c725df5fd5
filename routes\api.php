<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WhatsAppController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// WhatsApp Business API Routes
Route::prefix('whatsapp')->name('whatsapp.')->group(function () {
    // Webhook routes (no authentication required)
    Route::get('/webhook', [WhatsAppController::class, 'verifyWebhook'])->name('webhook.verify');
    Route::post('/webhook', [WhatsAppController::class, 'handleWebhook'])->name('webhook.handle');

    // Development/Testing routes
    Route::post('/test-message', [WhatsAppController::class, 'sendTestMessage'])->name('test.message');

    // Protected routes (require authentication)
    Route::middleware(['auth:sanctum'])->group(function () {
        // Business profile management
        Route::get('/profile', [WhatsAppController::class, 'getBusinessProfile'])->name('profile.get');
        Route::put('/profile', [WhatsAppController::class, 'updateBusinessProfile'])->name('profile.update');

        // Analytics and reporting
        Route::get('/analytics', [WhatsAppController::class, 'getAnalytics'])->name('analytics');
        Route::get('/conversation/{customer_phone}', [WhatsAppController::class, 'getConversation'])->name('conversation');

        // Manual message sending
        Route::post('/send-message', [WhatsAppController::class, 'sendManualMessage'])->name('send.message');
    });
});
