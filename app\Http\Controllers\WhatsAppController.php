<?php

namespace App\Http\Controllers;

use App\Services\WhatsAppService;
use App\Services\WhatsAppBotService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class WhatsAppController extends Controller
{
    protected WhatsAppService $whatsappService;
    protected WhatsAppBotService $botService;

    public function __construct(WhatsAppService $whatsappService, WhatsAppBotService $botService)
    {
        $this->whatsappService = $whatsappService;
        $this->botService = $botService;
    }

    /**
     * Handle webhook verification
     */
    public function verifyWebhook(Request $request): Response
    {
        $mode = $request->query('hub_mode');
        $token = $request->query('hub_verify_token');
        $challenge = $request->query('hub_challenge');

        if ($mode === 'subscribe' && $token === config('whatsapp.webhook_verify_token')) {
            Log::info('WhatsApp webhook verified successfully');
            return response($challenge, 200);
        }

        Log::warning('WhatsApp webhook verification failed', [
            'mode' => $mode,
            'token' => $token
        ]);

        return response('Forbidden', 403);
    }

    /**
     * Handle incoming webhooks
     */
    public function handleWebhook(Request $request): Response
    {
        try {
            // Verify webhook signature
            $signature = $request->header('X-Hub-Signature-256');
            if ($signature && !$this->whatsappService->verifyWebhookSignature($request->getContent(), $signature)) {
                Log::warning('Invalid WhatsApp webhook signature');
                return response('Unauthorized', 401);
            }

            $data = $request->json()->all();
            
            Log::info('WhatsApp webhook received', ['data' => $data]);

            // Process webhook data
            if (isset($data['entry'])) {
                foreach ($data['entry'] as $entry) {
                    if (isset($entry['changes'])) {
                        foreach ($entry['changes'] as $change) {
                            $this->processWebhookChange($change);
                        }
                    }
                }
            }

            return response('OK', 200);

        } catch (\Exception $e) {
            Log::error('Error processing WhatsApp webhook', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response('Internal Server Error', 500);
        }
    }

    /**
     * Process webhook change
     */
    protected function processWebhookChange(array $change): void
    {
        $field = $change['field'] ?? null;
        $value = $change['value'] ?? [];

        if ($field !== 'messages') {
            return;
        }

        // Process messages
        if (isset($value['messages'])) {
            foreach ($value['messages'] as $message) {
                $this->processIncomingMessage($message);
            }
        }

        // Process message status updates
        if (isset($value['statuses'])) {
            foreach ($value['statuses'] as $status) {
                $this->processMessageStatus($status);
            }
        }
    }

    /**
     * Process incoming message
     */
    protected function processIncomingMessage(array $messageData): void
    {
        try {
            // Skip if message is from business (outbound)
            if (isset($messageData['from']) && $messageData['from'] === config('whatsapp.phone_number_id')) {
                return;
            }

            Log::info('Processing incoming WhatsApp message', [
                'from' => $messageData['from'] ?? 'unknown',
                'type' => $messageData['type'] ?? 'unknown',
                'id' => $messageData['id'] ?? 'unknown'
            ]);

            // Process with bot service
            $this->botService->processIncomingMessage($messageData);

        } catch (\Exception $e) {
            Log::error('Error processing incoming WhatsApp message', [
                'error' => $e->getMessage(),
                'message_data' => $messageData
            ]);
        }
    }

    /**
     * Process message status update
     */
    protected function processMessageStatus(array $statusData): void
    {
        try {
            $messageId = $statusData['id'] ?? null;
            $status = $statusData['status'] ?? null;
            $timestamp = $statusData['timestamp'] ?? null;

            if (!$messageId || !$status) {
                return;
            }

            // Update message status in database
            $message = \App\Models\WhatsAppMessage::where('whatsapp_message_id', $messageId)->first();
            
            if ($message) {
                $updateData = ['status' => $status];
                
                if ($status === 'delivered' && $timestamp) {
                    $updateData['delivered_at'] = \Carbon\Carbon::createFromTimestamp($timestamp);
                } elseif ($status === 'read' && $timestamp) {
                    $updateData['read_at'] = \Carbon\Carbon::createFromTimestamp($timestamp);
                }
                
                $message->update($updateData);
                
                Log::info('Updated WhatsApp message status', [
                    'message_id' => $messageId,
                    'status' => $status
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Error processing WhatsApp message status', [
                'error' => $e->getMessage(),
                'status_data' => $statusData
            ]);
        }
    }

    /**
     * Send test message (for development)
     */
    public function sendTestMessage(Request $request): Response
    {
        if (!app()->environment('local')) {
            return response('Not allowed in production', 403);
        }

        $request->validate([
            'to' => 'required|string',
            'message' => 'required|string'
        ]);

        $result = $this->whatsappService->sendTextMessage(
            $request->input('to'),
            $request->input('message')
        );

        return response()->json($result);
    }

    /**
     * Get business profile
     */
    public function getBusinessProfile(): Response
    {
        $profile = $this->whatsappService->getBusinessProfile();
        return response()->json($profile);
    }

    /**
     * Update business profile
     */
    public function updateBusinessProfile(Request $request): Response
    {
        $request->validate([
            'about' => 'nullable|string|max:139',
            'address' => 'nullable|string|max:256',
            'description' => 'nullable|string|max:512',
            'email' => 'nullable|email|max:128',
            'websites' => 'nullable|array|max:2',
            'websites.*' => 'url|max:256'
        ]);

        $success = $this->whatsappService->updateBusinessProfile($request->all());
        
        return response()->json([
            'success' => $success,
            'message' => $success ? 'Profile updated successfully' : 'Failed to update profile'
        ]);
    }

    /**
     * Get WhatsApp analytics
     */
    public function getAnalytics(Request $request): Response
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date'
        ]);

        $startDate = $request->input('start_date', now()->subDays(7));
        $endDate = $request->input('end_date', now());

        $analytics = [
            'total_messages' => \App\Models\WhatsAppMessage::whereBetween('created_at', [$startDate, $endDate])->count(),
            'inbound_messages' => \App\Models\WhatsAppMessage::inbound()->whereBetween('created_at', [$startDate, $endDate])->count(),
            'outbound_messages' => \App\Models\WhatsAppMessage::outbound()->whereBetween('created_at', [$startDate, $endDate])->count(),
            'auto_responses' => \App\Models\WhatsAppMessage::autoResponded()->whereBetween('created_at', [$startDate, $endDate])->count(),
            'unique_customers' => \App\Models\WhatsAppMessage::whereBetween('created_at', [$startDate, $endDate])->distinct('customer_phone')->count(),
            'active_sessions' => \App\Models\WhatsAppSession::active()->count(),
            'popular_intents' => \App\Models\WhatsAppMessage::selectRaw('intent, COUNT(*) as count')
                ->whereNotNull('intent')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('intent')
                ->orderByDesc('count')
                ->limit(10)
                ->get(),
            'daily_stats' => \App\Models\WhatsAppMessage::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('date')
                ->orderBy('date')
                ->get()
        ];

        return response()->json($analytics);
    }

    /**
     * Get conversation history
     */
    public function getConversation(Request $request): Response
    {
        $request->validate([
            'customer_phone' => 'required|string',
            'limit' => 'nullable|integer|min:1|max:100'
        ]);

        $customerPhone = $request->input('customer_phone');
        $limit = $request->input('limit', 50);

        $messages = \App\Models\WhatsAppMessage::fromCustomer($customerPhone)
            ->with(['customer', 'repairTicket'])
            ->orderByDesc('created_at')
            ->limit($limit)
            ->get()
            ->reverse()
            ->values();

        return response()->json([
            'customer_phone' => $customerPhone,
            'messages' => $messages,
            'total_count' => \App\Models\WhatsAppMessage::fromCustomer($customerPhone)->count()
        ]);
    }

    /**
     * Manual message sending (for staff)
     */
    public function sendManualMessage(Request $request): Response
    {
        $request->validate([
            'to' => 'required|string',
            'message' => 'required|string|max:4096',
            'type' => 'nullable|in:text,template',
            'template_name' => 'required_if:type,template|string',
            'template_params' => 'nullable|array'
        ]);

        $type = $request->input('type', 'text');
        
        if ($type === 'template') {
            $result = $this->whatsappService->sendTemplateMessage(
                $request->input('to'),
                $request->input('template_name'),
                $request->input('template_params', [])
            );
        } else {
            $result = $this->whatsappService->sendTextMessage(
                $request->input('to'),
                $request->input('message')
            );
        }

        return response()->json($result);
    }
}
