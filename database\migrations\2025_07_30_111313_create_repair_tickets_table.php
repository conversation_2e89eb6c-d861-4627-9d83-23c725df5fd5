<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('repair_tickets', function (Blueprint $table) {
            $table->id();
            $table->string('ticket_number')->unique(); // Auto-generated ticket number

            // Customer information
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');

            // Device information
            $table->foreignId('brand_id')->constrained()->onDelete('restrict');
            $table->string('device_model');
            $table->string('device_pattern_password')->nullable(); // Device pattern/password
            $table->foreignId('device_condition_id')->constrained()->onDelete('restrict');

            // Repair information
            $table->text('reported_problem');
            $table->text('technician_comments')->nullable();
            $table->foreignId('repair_status_id')->constrained()->onDelete('restrict');

            // Dates and tracking
            $table->timestamp('received_date');
            $table->timestamp('estimated_completion_date')->nullable();
            $table->timestamp('completed_date')->nullable();

            // Pricing information
            $table->decimal('estimated_cost', 10, 2)->nullable();
            $table->decimal('final_cost', 10, 2)->nullable();

            // Staff tracking
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('assigned_technician_id')->nullable()->constrained('users')->onDelete('set null');

            $table->timestamps();

            // Indexes for better performance
            $table->index('ticket_number');
            $table->index('customer_id');
            $table->index('brand_id');
            $table->index('repair_status_id');
            $table->index('received_date');
            $table->index('created_by');
            $table->index('assigned_technician_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_tickets');
    }
};
