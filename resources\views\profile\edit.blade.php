@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="bi bi-person-gear"></i>
                        {{ __('app.nav.profile') }}
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>{{ __('app.setup_required') }}</strong><br>
                        هذه الصفحة مؤقتة. سيتم تفعيل إدارة الملف الشخصي بعد إعداد نظام المصادقة.
                        <br><br>
                        <strong>This is a temporary page.</strong> Profile management will be available after authentication setup is complete.
                    </div>

                    <h5>{{ __('app.setup_steps') }}</h5>
                    <ol>
                        <li>Run: <code>php artisan migrate</code></li>
                        <li>Run: <code>php artisan db:seed --class=UserSeeder</code></li>
                        <li>Enable authentication in routes/web.php</li>
                        <li>Clear caches: <code>php artisan config:clear</code></li>
                    </ol>

                    <div class="mt-4">
                        <a href="{{ route('dashboard') }}" class="btn btn-primary">
                            <i class="bi bi-arrow-left"></i>
                            {{ __('app.back_to_dashboard') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
