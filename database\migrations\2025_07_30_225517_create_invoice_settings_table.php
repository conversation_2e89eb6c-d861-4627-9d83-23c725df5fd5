<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value');
            $table->string('type')->default('string'); // string, number, boolean, json
            $table->text('description')->nullable();
            $table->timestamps();
        });

        // إدراج الإعدادات الافتراضية
        DB::table('invoice_settings')->insert([
            [
                'key' => 'invoice_prefix',
                'value' => 'INV',
                'type' => 'string',
                'description' => 'بادئة رقم الفاتورة',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'key' => 'invoice_number_length',
                'value' => '6',
                'type' => 'number',
                'description' => 'طول رقم الفاتورة',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'key' => 'default_tax_rate',
                'value' => '15',
                'type' => 'number',
                'description' => 'نسبة الضريبة الافتراضية',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'key' => 'payment_terms_days',
                'value' => '30',
                'type' => 'number',
                'description' => 'مدة الدفع بالأيام',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'key' => 'company_details',
                'value' => json_encode([
                    'name' => 'ورشة الإصلاح NJ',
                    'address' => 'الرياض، المملكة العربية السعودية',
                    'phone' => '+966 50 123 4567',
                    'email' => '<EMAIL>',
                    'tax_number' => '*********'
                ]),
                'type' => 'json',
                'description' => 'بيانات الشركة',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'key' => 'invoice_terms',
                'value' => 'يرجى الدفع خلال 30 يوم من تاريخ الفاتورة. شكراً لثقتكم بنا.',
                'type' => 'string',
                'description' => 'شروط وأحكام الفاتورة',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_settings');
    }
};
