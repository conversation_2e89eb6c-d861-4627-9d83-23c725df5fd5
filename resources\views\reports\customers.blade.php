@extends('layouts.app')

@section('title', 'تقرير تحليل العملاء')

@push('styles')
<style>
.customers-header {
    background: linear-gradient(45deg, #17a2b8, #138496);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.filter-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.customers-table {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    overflow: hidden;
}

.table th {
    background: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.customer-info {
    display: flex;
    flex-direction: column;
}

.customer-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.customer-phone {
    color: #6c757d;
    font-size: 0.875rem;
}

.amount-display {
    font-weight: 600;
    font-size: 1.1rem;
}

.amount-positive {
    color: #28a745;
}

.amount-negative {
    color: #dc3545;
}

.amount-neutral {
    color: #6c757d;
}

.stats-summary {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: white;
    border-radius: 0.35rem;
    border: 1px solid #e3e6f0;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.3;
}

@media (max-width: 768px) {
    .customers-header {
        padding: 1rem;
    }
    
    .filter-card {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="customers-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">تقرير تحليل العملاء</h1>
                <p class="mb-0 opacity-75">تحليل مفصل لأداء العملاء والمبيعات</p>
            </div>
            <div>
                <a href="{{ route('reports.financial') }}" class="btn btn-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="GET" action="{{ route('reports.customers') }}" class="row g-3">
            <div class="col-md-4">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" 
                       class="form-control" 
                       id="date_from" 
                       name="date_from" 
                       value="{{ $dateFrom }}">
            </div>
            
            <div class="col-md-4">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" 
                       class="form-control" 
                       id="date_to" 
                       name="date_to" 
                       value="{{ $dateTo }}">
            </div>
            
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>تطبيق الفلتر
                    </button>
                    <a href="{{ route('reports.customers') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Summary Statistics -->
    <div class="stats-summary">
        <h5 class="mb-3">ملخص إحصائي</h5>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value text-primary">{{ $customerStats->count() }}</div>
                <div class="stat-label">إجمالي العملاء</div>
            </div>
            <div class="stat-item">
                <div class="stat-value text-success">@arabicCurrency($customerStats->sum('total_amount'))</div>
                <div class="stat-label">إجمالي المبيعات</div>
            </div>
            <div class="stat-item">
                <div class="stat-value text-info">@arabicCurrency($customerStats->sum('total_paid'))</div>
                <div class="stat-label">إجمالي المدفوع</div>
            </div>
            <div class="stat-item">
                <div class="stat-value text-warning">@arabicCurrency($customerStats->sum('outstanding'))</div>
                <div class="stat-label">إجمالي المستحق</div>
            </div>
        </div>
    </div>

    <!-- Customers Table -->
    <div class="customers-table">
        @if($customerStats->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>العميل</th>
                            <th>عدد الفواتير</th>
                            <th>إجمالي المبيعات</th>
                            <th>إجمالي المدفوع</th>
                            <th>المبلغ المستحق</th>
                            <th>نسبة السداد</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($customerStats as $stat)
                        <tr>
                            <td>
                                <div class="customer-info">
                                    <div class="customer-name">
                                        <a href="{{ route('customers.show', $stat['customer']) }}" class="text-decoration-none">
                                            {{ $stat['customer']->name }}
                                        </a>
                                    </div>
                                    <div class="customer-phone">{{ $stat['customer']->phone_number }}</div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ $stat['total_invoices'] }}</span>
                            </td>
                            <td>
                                <span class="amount-display amount-positive">
                                    @arabicCurrency($stat['total_amount'])
                                </span>
                            </td>
                            <td>
                                <span class="amount-display amount-positive">
                                    @arabicCurrency($stat['total_paid'])
                                </span>
                            </td>
                            <td>
                                <span class="amount-display {{ $stat['outstanding'] > 0 ? 'amount-negative' : 'amount-neutral' }}">
                                    @arabicCurrency($stat['outstanding'])
                                </span>
                            </td>
                            <td>
                                @php
                                    $paymentRate = $stat['total_amount'] > 0 ? ($stat['total_paid'] / $stat['total_amount']) * 100 : 0;
                                @endphp
                                <div class="d-flex align-items-center">
                                    <div class="progress me-2" style="width: 60px; height: 8px;">
                                        <div class="progress-bar 
                                            @if($paymentRate >= 80) bg-success 
                                            @elseif($paymentRate >= 50) bg-warning 
                                            @else bg-danger @endif" 
                                            style="width: {{ $paymentRate }}%"></div>
                                    </div>
                                    <small class="text-muted">{{ number_format($paymentRate, 1) }}%</small>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <h5>لا توجد بيانات عملاء</h5>
                <p>لا توجد بيانات عملاء للفترة المحددة.</p>
                <a href="{{ route('customers.index') }}" class="btn btn-primary">
                    <i class="fas fa-users me-2"></i>إدارة العملاء
                </a>
            </div>
        @endif
    </div>

    <!-- Period Info -->
    <div class="mt-3">
        <small class="text-muted">
            <i class="fas fa-calendar me-1"></i>
            فترة التقرير: من {{ \Carbon\Carbon::parse($dateFrom)->format('Y-m-d') }} إلى {{ \Carbon\Carbon::parse($dateTo)->format('Y-m-d') }}
        </small>
    </div>
</div>
@endsection
