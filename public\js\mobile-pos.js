/**
 * Mobile POS System JavaScript
 * Optimized for touch devices and mobile interfaces
 */

class MobilePOSSystem {
    constructor() {
        this.cart = [];
        this.isCartOpen = false;
        this.touchStartY = 0;
        this.touchEndY = 0;
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupTouchGestures();
        this.updateCartDisplay();
        this.loadProducts();
    }

    bindEvents() {
        // Product selection
        $(document).on('click', '.mobile-product-card:not(.out-of-stock)', (e) => {
            const productId = $(e.currentTarget).data('product-id');
            this.addToCart(productId);
            this.animateAddToCart($(e.currentTarget));
        });

        // Category filtering
        $(document).on('click', '.mobile-category', (e) => {
            e.preventDefault();
            const category = $(e.currentTarget).data('category');
            this.filterByCategory(category);
            $('.mobile-category').removeClass('active');
            $(e.currentTarget).addClass('active');
        });

        // Search functionality
        $('#mobileSearchInput').on('input', (e) => {
            const query = $(e.target).val();
            this.searchProducts(query);
        });

        // Cart FAB
        $('#mobileCartFab').on('click', () => {
            this.toggleCart();
        });

        // Cart overlay
        $('#mobileOverlay').on('click', () => {
            this.closeCart();
        });

        // Quantity controls
        $(document).on('click', '.mobile-quantity-btn', (e) => {
            const action = $(e.currentTarget).data('action');
            const itemId = $(e.currentTarget).closest('.mobile-cart-item').data('item-id');
            this.updateQuantity(itemId, action);
        });

        // Clear cart
        $('#mobileClearCartBtn').on('click', () => {
            this.clearCart();
        });

        // Checkout
        $('#mobileCheckoutBtn').on('click', () => {
            this.openCheckout();
        });

        // Settings
        $('#mobileSettingsBtn').on('click', () => {
            this.openSettings();
        });

        // Sync
        $('#mobileSyncBtn').on('click', () => {
            this.syncData();
        });

        // Prevent scroll when cart is open
        $(document).on('touchmove', (e) => {
            if (this.isCartOpen && !$(e.target).closest('.mobile-cart-items').length) {
                e.preventDefault();
            }
        });
    }

    setupTouchGestures() {
        const cartDrawer = document.getElementById('mobileCartDrawer');
        
        // Swipe down to close cart
        cartDrawer.addEventListener('touchstart', (e) => {
            this.touchStartY = e.touches[0].clientY;
        });

        cartDrawer.addEventListener('touchend', (e) => {
            this.touchEndY = e.changedTouches[0].clientY;
            const swipeDistance = this.touchEndY - this.touchStartY;
            
            if (swipeDistance > 100) { // Swipe down threshold
                this.closeCart();
            }
        });

        // Pull to refresh products
        let pullToRefreshStartY = 0;
        let isPulling = false;
        
        $('.mobile-products').on('touchstart', (e) => {
            if ($(e.target).closest('.mobile-products')[0].scrollTop === 0) {
                pullToRefreshStartY = e.touches[0].clientY;
                isPulling = true;
            }
        });

        $('.mobile-products').on('touchmove', (e) => {
            if (isPulling) {
                const currentY = e.touches[0].clientY;
                const pullDistance = currentY - pullToRefreshStartY;
                
                if (pullDistance > 80) {
                    this.showPullToRefresh();
                }
            }
        });

        $('.mobile-products').on('touchend', (e) => {
            if (isPulling) {
                const currentY = e.changedTouches[0].clientY;
                const pullDistance = currentY - pullToRefreshStartY;
                
                if (pullDistance > 80) {
                    this.refreshProducts();
                }
                
                isPulling = false;
                this.hidePullToRefresh();
            }
        });
    }

    addToCart(productId, quantity = 1) {
        const product = this.getProductById(productId);
        if (!product || product.current_stock <= 0) {
            this.showToast('Product not available', 'warning');
            this.vibrate(200);
            return;
        }

        const existingItem = this.cart.find(item => item.id === productId);
        
        if (existingItem) {
            if (existingItem.quantity + quantity <= product.current_stock) {
                existingItem.quantity += quantity;
            } else {
                this.showToast('Insufficient stock', 'warning');
                this.vibrate(200);
                return;
            }
        } else {
            this.cart.push({
                id: productId,
                name: product.name,
                price: parseFloat(product.selling_price),
                quantity: quantity,
                stock: product.current_stock
            });
        }

        this.updateCartDisplay();
        this.playSound('add');
        this.vibrate(50);
    }

    removeFromCart(productId) {
        this.cart = this.cart.filter(item => item.id !== productId);
        this.updateCartDisplay();
        this.vibrate(100);
    }

    updateQuantity(productId, action) {
        const item = this.cart.find(item => item.id === productId);
        if (!item) return;

        if (action === 'increase') {
            if (item.quantity < item.stock) {
                item.quantity++;
            } else {
                this.showToast('Insufficient stock', 'warning');
                this.vibrate(200);
                return;
            }
        } else if (action === 'decrease') {
            if (item.quantity > 1) {
                item.quantity--;
            } else {
                this.removeFromCart(productId);
                return;
            }
        }

        this.updateCartDisplay();
        this.vibrate(30);
    }

    clearCart() {
        if (this.cart.length === 0) return;
        
        if (confirm('Are you sure you want to clear the cart?')) {
            this.cart = [];
            this.updateCartDisplay();
            this.vibrate(100);
        }
    }

    updateCartDisplay() {
        const cartItems = $('#mobileCartItems');
        const cartBadge = $('#mobileCartBadge');
        const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);
        
        // Update badge
        cartBadge.text(totalItems);
        if (totalItems > 0) {
            cartBadge.show();
        } else {
            cartBadge.hide();
        }
        
        // Update cart items
        if (this.cart.length === 0) {
            cartItems.html(`
                <div class="empty-cart">
                    <i class="bi bi-cart"></i>
                    <p>Empty cart</p>
                </div>
            `);
            $('#mobileCheckoutBtn').prop('disabled', true);
        } else {
            let itemsHtml = '';
            this.cart.forEach(item => {
                itemsHtml += `
                    <div class="mobile-cart-item" data-item-id="${item.id}">
                        <div class="mobile-cart-item-info">
                            <div class="mobile-cart-item-name">${item.name}</div>
                            <div class="mobile-cart-item-price">${item.price.toFixed(2)} SAR × ${item.quantity}</div>
                        </div>
                        <div class="mobile-quantity-controls">
                            <button class="mobile-quantity-btn" data-action="decrease">
                                <i class="bi bi-dash"></i>
                            </button>
                            <div class="mobile-quantity-display">${item.quantity}</div>
                            <button class="mobile-quantity-btn" data-action="increase">
                                <i class="bi bi-plus"></i>
                            </button>
                        </div>
                    </div>
                `;
            });
            
            cartItems.html(itemsHtml);
            $('#mobileCheckoutBtn').prop('disabled', false);
        }

        this.updateTotals();
    }

    updateTotals() {
        const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const taxRate = 0.15; // 15% tax
        const taxAmount = subtotal * taxRate;
        const total = subtotal + taxAmount;

        $('#mobileSubtotal').text(subtotal.toFixed(2) + ' SAR');
        $('#mobileTax').text(taxAmount.toFixed(2) + ' SAR');
        $('#mobileTotal').text(total.toFixed(2) + ' SAR');
    }

    toggleCart() {
        if (this.isCartOpen) {
            this.closeCart();
        } else {
            this.openCart();
        }
    }

    openCart() {
        $('#mobileCartDrawer').addClass('open');
        $('#mobileOverlay').addClass('show');
        this.isCartOpen = true;
        $('body').addClass('overflow-hidden');
    }

    closeCart() {
        $('#mobileCartDrawer').removeClass('open');
        $('#mobileOverlay').removeClass('show');
        this.isCartOpen = false;
        $('body').removeClass('overflow-hidden');
    }

    openCheckout() {
        if (this.cart.length === 0) return;
        
        // For mobile, we'll use a simplified checkout flow
        this.showCheckoutModal();
    }

    showCheckoutModal() {
        const total = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) * 1.15;
        
        const modal = $(`
            <div class="modal fade" id="mobileCheckoutModal" tabindex="-1">
                <div class="modal-dialog modal-fullscreen-sm-down">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Checkout</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">Payment Method</label>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary payment-method-btn" data-method="cash">
                                        <i class="bi bi-cash-coin"></i> Cash
                                    </button>
                                    <button class="btn btn-outline-primary payment-method-btn" data-method="card">
                                        <i class="bi bi-credit-card"></i> Card
                                    </button>
                                    <button class="btn btn-outline-primary payment-method-btn" data-method="mobile">
                                        <i class="bi bi-phone"></i> Mobile Payment
                                    </button>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <strong>Total: ${total.toFixed(2)} SAR</strong>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-success" id="confirmPaymentBtn" disabled>
                                Complete Payment
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `);

        $('body').append(modal);
        
        // Payment method selection
        modal.on('click', '.payment-method-btn', function() {
            modal.find('.payment-method-btn').removeClass('btn-primary').addClass('btn-outline-primary');
            $(this).removeClass('btn-outline-primary').addClass('btn-primary');
            modal.find('#confirmPaymentBtn').prop('disabled', false);
        });

        // Confirm payment
        modal.on('click', '#confirmPaymentBtn', () => {
            this.processPayment();
            modal.modal('hide');
        });

        // Clean up on close
        modal.on('hidden.bs.modal', function() {
            $(this).remove();
        });

        modal.modal('show');
    }

    processPayment() {
        // Simulate payment processing
        this.showToast('Processing payment...', 'info');
        
        setTimeout(() => {
            this.completeTransaction();
        }, 2000);
    }

    completeTransaction() {
        // Clear cart
        this.cart = [];
        this.updateCartDisplay();
        this.closeCart();
        
        // Show success
        this.showToast('Payment successful!', 'success');
        this.playSound('success');
        this.vibrate([100, 50, 100]);
        
        // Show receipt option
        setTimeout(() => {
            if (confirm('Would you like to print a receipt?')) {
                this.printReceipt();
            }
        }, 1000);
    }

    printReceipt() {
        // Mobile receipt printing would integrate with device capabilities
        this.showToast('Receipt sent to printer', 'info');
    }

    // Utility methods
    getProductById(id) {
        return window.mobileProducts?.find(p => p.id == id);
    }

    filterByCategory(categoryId) {
        const products = $('.mobile-product-card');
        
        if (!categoryId) {
            products.show();
        } else {
            products.each(function() {
                const productCategory = $(this).data('category');
                if (productCategory == categoryId) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }
    }

    searchProducts(query) {
        if (!query) {
            $('.mobile-product-card').show();
            return;
        }

        const searchTerm = query.toLowerCase();
        $('.mobile-product-card').each(function() {
            const name = $(this).data('name');
            const barcode = $(this).data('barcode');
            
            if (name.includes(searchTerm) || (barcode && barcode.includes(searchTerm))) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }

    animateAddToCart($element) {
        $element.addClass('add-to-cart-success');
        setTimeout(() => {
            $element.removeClass('add-to-cart-success');
        }, 600);
    }

    showPullToRefresh() {
        // Show pull to refresh indicator
        if (!$('#pullToRefreshIndicator').length) {
            $('.mobile-products').prepend(`
                <div id="pullToRefreshIndicator" class="text-center p-3">
                    <i class="bi bi-arrow-clockwise"></i> Release to refresh
                </div>
            `);
        }
    }

    hidePullToRefresh() {
        $('#pullToRefreshIndicator').remove();
    }

    refreshProducts() {
        this.showToast('Refreshing products...', 'info');
        $('#loadingProducts').show();
        
        // Simulate API call
        setTimeout(() => {
            $('#loadingProducts').hide();
            this.showToast('Products updated', 'success');
        }, 1500);
    }

    loadProducts() {
        // Load products from API or local storage
        console.log('Loading mobile products...');
    }

    syncData() {
        this.showToast('Syncing data...', 'info');
        $('#mobileSyncBtn').addClass('rotating');
        
        setTimeout(() => {
            $('#mobileSyncBtn').removeClass('rotating');
            this.showToast('Data synced successfully', 'success');
        }, 2000);
    }

    openSettings() {
        // Open mobile settings modal
        this.showToast('Settings coming soon', 'info');
    }

    // Device capabilities
    vibrate(pattern) {
        if ('vibrate' in navigator) {
            navigator.vibrate(pattern);
        }
    }

    playSound(type) {
        // Simple audio feedback for mobile
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        const frequencies = {
            'add': 800,
            'success': 600,
            'error': 300
        };

        oscillator.frequency.value = frequencies[type] || 500;
        oscillator.type = 'sine';
        
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    }

    showToast(message, type = 'info') {
        const toast = $(`
            <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0 position-fixed" 
                 style="top: 20px; right: 20px; z-index: 9999;" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `);

        $('body').append(toast);
        const bsToast = new bootstrap.Toast(toast[0]);
        bsToast.show();

        toast.on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }
}

// Initialize mobile POS system
$(document).ready(function() {
    window.mobilePOS = new MobilePOSSystem();
    
    // Add rotating animation for sync button
    $('<style>').text(`
        .rotating {
            animation: rotate 1s linear infinite;
        }
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .overflow-hidden {
            overflow: hidden !important;
        }
    `).appendTo('head');
});
