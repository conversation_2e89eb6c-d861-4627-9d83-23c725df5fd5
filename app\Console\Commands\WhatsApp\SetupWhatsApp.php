<?php

namespace App\Console\Commands\WhatsApp;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Artisan;

class SetupWhatsApp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'whatsapp:setup 
                            {--force : Force setup even if already configured}
                            {--test : Run in test mode}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up WhatsApp Business API integration for NJ Repair Shop';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Setting up WhatsApp Business API Integration...');
        $this->newLine();

        // Check if already configured
        if (!$this->option('force') && $this->isAlreadyConfigured()) {
            $this->warn('WhatsApp integration appears to be already configured.');
            $this->info('Use --force to reconfigure.');
            return Command::FAILURE;
        }

        try {
            // Step 1: Validate environment configuration
            $this->validateEnvironment();

            // Step 2: Run database migrations
            $this->runMigrations();

            // Step 3: Test API connectivity
            $this->testApiConnectivity();

            // Step 4: Seed templates
            $this->seedTemplates();

            // Step 5: Verify webhook endpoint
            $this->verifyWebhook();

            // Step 6: Display setup summary
            $this->displaySetupSummary();

            $this->newLine();
            $this->info('✅ WhatsApp Business API setup completed successfully!');

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ WhatsApp setup failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Check if WhatsApp is already configured.
     */
    private function isAlreadyConfigured(): bool
    {
        return !empty(config('whatsapp.auth.access_token')) && 
               !empty(config('whatsapp.auth.phone_number_id')) &&
               DB::getSchemaBuilder()->hasTable('whatsapp_messages');
    }

    /**
     * Validate environment configuration.
     */
    private function validateEnvironment(): void
    {
        $this->info('🔍 Validating environment configuration...');

        $requiredVars = [
            'WHATSAPP_ACCESS_TOKEN' => 'WhatsApp Access Token',
            'WHATSAPP_PHONE_NUMBER_ID' => 'Phone Number ID',
            'WHATSAPP_BUSINESS_ACCOUNT_ID' => 'Business Account ID',
            'WHATSAPP_WEBHOOK_VERIFY_TOKEN' => 'Webhook Verify Token',
        ];

        $missing = [];
        foreach ($requiredVars as $var => $description) {
            if (empty(env($var))) {
                $missing[] = "{$var} ({$description})";
            }
        }

        if (!empty($missing)) {
            $this->error('Missing required environment variables:');
            foreach ($missing as $var) {
                $this->line("  • {$var}");
            }
            throw new \Exception('Please configure all required environment variables in your .env file');
        }

        $this->info('   ✓ All required environment variables are configured');
    }

    /**
     * Run database migrations.
     */
    private function runMigrations(): void
    {
        $this->info('📊 Running database migrations...');

        $tables = ['whatsapp_messages', 'whatsapp_templates', 'whatsapp_webhooks', 'whatsapp_contacts'];
        $missingTables = [];

        foreach ($tables as $table) {
            if (!DB::getSchemaBuilder()->hasTable($table)) {
                $missingTables[] = $table;
            }
        }

        if (!empty($missingTables)) {
            $this->info('   Running migrations for missing tables...');
            Artisan::call('migrate', ['--force' => true]);
            $this->info('   ✓ Database migrations completed');
        } else {
            $this->info('   ✓ All WhatsApp tables already exist');
        }
    }

    /**
     * Test API connectivity.
     */
    private function testApiConnectivity(): void
    {
        $this->info('🔗 Testing WhatsApp API connectivity...');

        $accessToken = config('whatsapp.auth.access_token');
        $phoneNumberId = config('whatsapp.auth.phone_number_id');
        $apiVersion = config('whatsapp.api.version');

        if ($this->option('test')) {
            $this->info('   ⚠️  Skipping API test in test mode');
            return;
        }

        try {
            $response = Http::withToken($accessToken)
                ->get("https://graph.facebook.com/{$apiVersion}/{$phoneNumberId}");

            if ($response->successful()) {
                $data = $response->json();
                $this->info("   ✓ API connectivity successful");
                $this->info("   Phone Number: {$data['display_phone_number']}");
                $this->info("   Status: {$data['code_verification_status']}");
            } else {
                throw new \Exception("API test failed: " . $response->body());
            }

        } catch (\Exception $e) {
            $this->warn("   ⚠️  API connectivity test failed: " . $e->getMessage());
            $this->warn("   This might be normal if you haven't completed Meta approval yet");
        }
    }

    /**
     * Seed WhatsApp templates.
     */
    private function seedTemplates(): void
    {
        $this->info('📝 Seeding WhatsApp templates...');

        try {
            Artisan::call('db:seed', [
                '--class' => 'WhatsAppTemplateSeeder',
                '--force' => true
            ]);
            $this->info('   ✓ WhatsApp templates seeded successfully');
        } catch (\Exception $e) {
            $this->warn("   ⚠️  Template seeding failed: " . $e->getMessage());
        }
    }

    /**
     * Verify webhook endpoint.
     */
    private function verifyWebhook(): void
    {
        $this->info('🔗 Verifying webhook endpoint...');

        $webhookUrl = url('/api/whatsapp/webhook');
        
        try {
            $response = Http::timeout(10)->get($webhookUrl, [
                'hub.mode' => 'subscribe',
                'hub.challenge' => 'test_challenge',
                'hub.verify_token' => config('whatsapp.webhook.verify_token')
            ]);

            if ($response->successful() && $response->body() === 'test_challenge') {
                $this->info("   ✓ Webhook endpoint is accessible: {$webhookUrl}");
            } else {
                $this->warn("   ⚠️  Webhook verification failed");
                $this->warn("   Make sure your webhook endpoint is properly configured");
            }

        } catch (\Exception $e) {
            $this->warn("   ⚠️  Webhook test failed: " . $e->getMessage());
            $this->warn("   URL: {$webhookUrl}");
        }
    }

    /**
     * Display setup summary.
     */
    private function displaySetupSummary(): void
    {
        $this->newLine();
        $this->info('📋 Setup Summary:');

        $this->table(
            ['Component', 'Status', 'Details'],
            [
                ['Environment Variables', '✅ Configured', 'All required variables set'],
                ['Database Tables', '✅ Ready', 'All WhatsApp tables created'],
                ['Templates', '✅ Seeded', DB::table('whatsapp_templates')->count() . ' templates created'],
                ['Webhook Endpoint', '🔗 Configured', url('/api/whatsapp/webhook')],
                ['API Version', '📡 Set', config('whatsapp.api.version')],
            ]
        );

        $this->newLine();
        $this->info('🔧 Configuration Details:');
        $this->line('   • Phone Number ID: ' . config('whatsapp.auth.phone_number_id'));
        $this->line('   • Business Account ID: ' . config('whatsapp.auth.business_account_id'));
        $this->line('   • Webhook URL: ' . url('/api/whatsapp/webhook'));
        $this->line('   • Default Language: ' . config('whatsapp.templates.default_language'));

        $this->newLine();
        $this->info('📝 Next Steps:');
        $this->line('   1. Submit message templates for approval in Meta Business Manager');
        $this->line('   2. Configure webhook URL in Meta Developer Console');
        $this->line('   3. Test message sending with approved templates');
        $this->line('   4. Set up queue workers for message processing');
        $this->line('   5. Configure SSL certificate for webhook endpoint');

        $this->newLine();
        $this->warn('⚠️  Important Notes:');
        $this->line('   • Templates must be approved by Meta before use (24-48 hours)');
        $this->line('   • Webhook URL must be accessible via HTTPS');
        $this->line('   • Test thoroughly before going live');
        $this->line('   • Monitor API usage and rate limits');

        $this->newLine();
        $this->info('🧪 Testing Commands:');
        $this->line('   • Test API connection: php artisan whatsapp:test-connection');
        $this->line('   • Check templates: php artisan whatsapp:check-templates');
        $this->line('   • Send test message: php artisan whatsapp:send-test');
        $this->line('   • View webhook logs: php artisan whatsapp:webhook-logs');
    }
}
