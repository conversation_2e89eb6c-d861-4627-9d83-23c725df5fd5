<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\NotificationTemplate;
use App\Models\Customer;
use App\Models\RepairTicket;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Exception;

class NotificationService
{
    /**
     * Create and send notification using template.
     */
    public function sendFromTemplate(
        string $templateSlug,
        Customer $customer,
        array $data = [],
        RepairTicket $repairTicket = null,
        string $scheduledAt = null
    ): Notification {
        $template = NotificationTemplate::where('slug', $templateSlug)
            ->where('is_active', true)
            ->firstOrFail();

        // Prepare template data
        $templateData = array_merge([
            'customer_name' => $customer->name,
            'customer_phone' => $customer->phone_number,
            'shop_name' => config('app.name', 'ورشة NJ للإصلاح'),
            'shop_phone' => config('app.shop_phone', '0501234567'),
        ], $data);

        if ($repairTicket) {
            $templateData = array_merge($templateData, [
                'ticket_number' => $repairTicket->ticket_number,
                'device_model' => $repairTicket->device_model,
                'device_brand' => $repairTicket->brand->name,
                'status' => $repairTicket->repairStatus->name,
                'estimated_date' => $repairTicket->estimated_completion_date?->format('Y-m-d'),
                'received_date' => $repairTicket->received_date->format('Y-m-d'),
                'cost' => $repairTicket->final_cost ? number_format($repairTicket->final_cost, 2) . ' ريال' : 'غير محدد',
            ]);
        }

        // Render message
        $message = $template->render($templateData);
        $subject = $template->renderSubject($templateData);

        // Determine recipient
        $recipientPhone = $template->type === 'email' ? null : $customer->phone_number;
        $recipientEmail = $template->type === 'email' ? $customer->email : null;

        // Create notification
        $notification = Notification::create([
            'customer_id' => $customer->id,
            'repair_ticket_id' => $repairTicket?->id,
            'type' => $template->type,
            'category' => $template->category,
            'recipient_phone' => $recipientPhone,
            'recipient_email' => $recipientEmail,
            'subject' => $subject,
            'message' => $message,
            'template_data' => $templateData,
            'scheduled_at' => $scheduledAt ? now()->parse($scheduledAt) : null,
            'created_by' => auth()->id() ?? 1,
        ]);

        // Send immediately if not scheduled
        if (!$scheduledAt) {
            $this->sendNotification($notification);
        }

        return $notification;
    }

    /**
     * Send a notification.
     */
    public function sendNotification(Notification $notification): bool
    {
        try {
            $success = match($notification->type) {
                'sms' => $this->sendSMS($notification),
                'whatsapp' => $this->sendWhatsApp($notification),
                'email' => $this->sendEmail($notification),
                default => false
            };

            if ($success) {
                $notification->markAsSent();
                Log::info("Notification sent successfully", ['id' => $notification->id]);
                return true;
            } else {
                $notification->markAsFailed('Unknown error occurred');
                return false;
            }
        } catch (Exception $e) {
            $notification->markAsFailed($e->getMessage());
            Log::error("Failed to send notification", [
                'id' => $notification->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send SMS notification.
     */
    private function sendSMS(Notification $notification): bool
    {
        // For now, we'll simulate SMS sending
        // In production, integrate with SMS providers like:
        // - STC Business Solutions
        // - Mobily SMS
        // - Zain SMS Gateway
        // - International providers like Twilio

        try {
            // Simulate API call
            $response = $this->simulateSMSProvider([
                'to' => $notification->recipient_phone,
                'message' => $notification->message,
                'from' => config('app.sms_sender_name', 'NJ-REPAIR')
            ]);

            if ($response['success']) {
                $notification->update([
                    'external_id' => $response['message_id'],
                    'cost' => $response['cost'] ?? 0.05 // 5 halalas per SMS
                ]);
                return true;
            }

            return false;
        } catch (Exception $e) {
            Log::error("SMS sending failed", ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Send WhatsApp notification.
     */
    private function sendWhatsApp(Notification $notification): bool
    {
        // For WhatsApp Business API integration
        // Requires WhatsApp Business API setup
        
        try {
            // Simulate WhatsApp API call
            $response = $this->simulateWhatsAppProvider([
                'to' => $notification->recipient_phone,
                'message' => $notification->message,
                'type' => 'text'
            ]);

            if ($response['success']) {
                $notification->update([
                    'external_id' => $response['message_id'],
                    'cost' => $response['cost'] ?? 0.02 // 2 halalas per WhatsApp message
                ]);
                return true;
            }

            return false;
        } catch (Exception $e) {
            Log::error("WhatsApp sending failed", ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Send email notification.
     */
    private function sendEmail(Notification $notification): bool
    {
        try {
            // Use Laravel's built-in mail system
            // For now, simulate email sending
            $response = $this->simulateEmailProvider([
                'to' => $notification->recipient_email,
                'subject' => $notification->subject,
                'message' => $notification->message
            ]);

            if ($response['success']) {
                $notification->update([
                    'external_id' => $response['message_id'],
                    'cost' => 0 // Email is typically free
                ]);
                return true;
            }

            return false;
        } catch (Exception $e) {
            Log::error("Email sending failed", ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Process scheduled notifications.
     */
    public function processScheduledNotifications(): int
    {
        $dueNotifications = Notification::due()->limit(100)->get();
        $processed = 0;

        foreach ($dueNotifications as $notification) {
            if ($this->sendNotification($notification)) {
                $processed++;
            }
        }

        Log::info("Processed scheduled notifications", ['count' => $processed]);
        return $processed;
    }

    /**
     * Send status update notification.
     */
    public function sendStatusUpdate(RepairTicket $repairTicket, string $oldStatus, string $newStatus): bool
    {
        return $this->sendFromTemplate(
            'status-update',
            $repairTicket->customer,
            [
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
            ],
            $repairTicket
        ) !== null;
    }

    /**
     * Send pickup ready notification.
     */
    public function sendPickupReady(RepairTicket $repairTicket): bool
    {
        return $this->sendFromTemplate(
            'pickup-ready',
            $repairTicket->customer,
            [],
            $repairTicket
        ) !== null;
    }

    /**
     * Simulate SMS provider response (for development).
     */
    private function simulateSMSProvider(array $data): array
    {
        // Simulate success/failure
        $success = rand(1, 10) > 1; // 90% success rate

        return [
            'success' => $success,
            'message_id' => $success ? 'SMS_' . uniqid() : null,
            'cost' => $success ? 0.05 : 0,
            'error' => $success ? null : 'Simulated SMS failure'
        ];
    }

    /**
     * Simulate WhatsApp provider response (for development).
     */
    private function simulateWhatsAppProvider(array $data): array
    {
        $success = rand(1, 10) > 1; // 90% success rate

        return [
            'success' => $success,
            'message_id' => $success ? 'WA_' . uniqid() : null,
            'cost' => $success ? 0.02 : 0,
            'error' => $success ? null : 'Simulated WhatsApp failure'
        ];
    }

    /**
     * Simulate email provider response (for development).
     */
    private function simulateEmailProvider(array $data): array
    {
        $success = rand(1, 10) > 2; // 80% success rate

        return [
            'success' => $success,
            'message_id' => $success ? 'EMAIL_' . uniqid() : null,
            'cost' => 0,
            'error' => $success ? null : 'Simulated email failure'
        ];
    }
}
