/**
 * NJ Repair Shop - Theme Manager
 * Dark/Light mode toggle with system preference detection
 */

class ThemeManager {
    constructor() {
        this.currentTheme = 'light';
        this.init();
    }

    init() {
        this.loadSavedTheme();
        this.createThemeToggle();
        this.applyTheme();
        this.watchSystemTheme();
    }

    loadSavedTheme() {
        const savedTheme = localStorage.getItem('nj-theme');
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        if (savedTheme) {
            this.currentTheme = savedTheme;
        } else if (systemPrefersDark) {
            this.currentTheme = 'dark';
        }
    }

    createThemeToggle() {
        // Find or create theme toggle button
        let themeToggle = document.querySelector('.theme-toggle');
        
        if (!themeToggle) {
            themeToggle = document.createElement('button');
            themeToggle.className = 'btn btn-outline-light theme-toggle';
            themeToggle.setAttribute('data-bs-toggle', 'tooltip');
            themeToggle.setAttribute('data-bs-placement', 'bottom');
            themeToggle.setAttribute('title', 'Toggle theme');
            
            // Add to navbar
            const navbar = document.querySelector('.navbar-nav');
            if (navbar) {
                const li = document.createElement('li');
                li.className = 'nav-item';
                li.appendChild(themeToggle);
                navbar.appendChild(li);
            }
        }

        this.updateToggleButton(themeToggle);
        
        themeToggle.addEventListener('click', () => {
            this.toggleTheme();
        });
    }

    updateToggleButton(button) {
        const icon = this.currentTheme === 'dark' ? 'bi-sun' : 'bi-moon';
        const text = this.currentTheme === 'dark' ? 'Light Mode' : 'Dark Mode';
        
        button.innerHTML = `<i class="bi ${icon}"></i>`;
        button.setAttribute('title', text);
        
        // Update tooltip if it exists
        const tooltip = bootstrap.Tooltip.getInstance(button);
        if (tooltip) {
            tooltip.setContent({ '.tooltip-inner': text });
        }
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        this.saveTheme();
        this.updateToggleButton(document.querySelector('.theme-toggle'));
        
        // Show notification
        if (window.showInfo) {
            window.showInfo(`Switched to ${this.currentTheme} mode`);
        }
    }

    applyTheme() {
        const html = document.documentElement;
        const body = document.body;
        
        if (this.currentTheme === 'dark') {
            html.setAttribute('data-bs-theme', 'dark');
            body.classList.add('dark-theme');
            this.applyDarkStyles();
        } else {
            html.removeAttribute('data-bs-theme');
            body.classList.remove('dark-theme');
            this.removeDarkStyles();
        }
        
        // Update charts if they exist
        this.updateChartThemes();
    }

    applyDarkStyles() {
        let darkStyleSheet = document.getElementById('dark-theme-styles');
        
        if (!darkStyleSheet) {
            darkStyleSheet = document.createElement('style');
            darkStyleSheet.id = 'dark-theme-styles';
            document.head.appendChild(darkStyleSheet);
        }

        darkStyleSheet.textContent = `
            .dark-theme {
                --bs-body-bg: #1a1d23;
                --bs-body-color: #e9ecef;
                --bs-border-color: #495057;
            }
            
            .dark-theme .navbar {
                background-color: #212529 !important;
            }
            
            .dark-theme .card {
                background-color: #2d3748;
                border-color: #4a5568;
                color: #e2e8f0;
            }
            
            .dark-theme .card-header {
                background-color: #374151;
                border-color: #4a5568;
                color: #f7fafc;
            }
            
            .dark-theme .table {
                --bs-table-bg: #2d3748;
                --bs-table-color: #e2e8f0;
                --bs-table-border-color: #4a5568;
                --bs-table-striped-bg: #374151;
                --bs-table-hover-bg: #4a5568;
            }
            
            .dark-theme .table thead th {
                background: linear-gradient(135deg, #4a5568, #2d3748);
                color: #f7fafc;
                border-color: #4a5568;
            }
            
            .dark-theme .form-control,
            .dark-theme .form-select {
                background-color: #374151;
                border-color: #4a5568;
                color: #e2e8f0;
            }
            
            .dark-theme .form-control:focus,
            .dark-theme .form-select:focus {
                background-color: #374151;
                border-color: #60a5fa;
                color: #e2e8f0;
                box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25);
            }
            
            .dark-theme .btn-outline-secondary {
                color: #9ca3af;
                border-color: #4a5568;
            }
            
            .dark-theme .btn-outline-secondary:hover {
                background-color: #4a5568;
                border-color: #4a5568;
                color: #f7fafc;
            }
            
            .dark-theme .dropdown-menu {
                background-color: #374151;
                border-color: #4a5568;
            }
            
            .dark-theme .dropdown-item {
                color: #e2e8f0;
            }
            
            .dark-theme .dropdown-item:hover {
                background-color: #4a5568;
                color: #f7fafc;
            }
            
            .dark-theme .modal-content {
                background-color: #2d3748;
                color: #e2e8f0;
            }
            
            .dark-theme .modal-header {
                background: linear-gradient(135deg, #4a5568, #2d3748);
                border-color: #4a5568;
            }
            
            .dark-theme .modal-footer {
                background-color: #374151;
                border-color: #4a5568;
            }
            
            .dark-theme .toast {
                background-color: #374151;
                color: #e2e8f0;
            }
            
            .dark-theme .toast-header {
                background-color: #4a5568;
                color: #f7fafc;
            }
            
            .dark-theme .border {
                border-color: #4a5568 !important;
            }
            
            .dark-theme .text-muted {
                color: #9ca3af !important;
            }
            
            .dark-theme .bg-light {
                background-color: #374151 !important;
                color: #e2e8f0;
            }
            
            .dark-theme .stats-card {
                background: linear-gradient(135deg, #4a5568, #2d3748);
            }
            
            .dark-theme .search-container .search-icon {
                color: #9ca3af;
            }
            
            .dark-theme .pagination .page-link {
                background-color: #374151;
                border-color: #4a5568;
                color: #e2e8f0;
            }
            
            .dark-theme .pagination .page-link:hover {
                background-color: #4a5568;
                border-color: #60a5fa;
                color: #f7fafc;
            }
            
            .dark-theme .pagination .page-item.active .page-link {
                background-color: #60a5fa;
                border-color: #60a5fa;
            }
            
            .dark-theme .alert-info {
                background-color: #1e3a8a;
                border-color: #3b82f6;
                color: #dbeafe;
            }
            
            .dark-theme .alert-success {
                background-color: #166534;
                border-color: #22c55e;
                color: #dcfce7;
            }
            
            .dark-theme .alert-warning {
                background-color: #92400e;
                border-color: #f59e0b;
                color: #fef3c7;
            }
            
            .dark-theme .alert-danger {
                background-color: #991b1b;
                border-color: #ef4444;
                color: #fee2e2;
            }
        `;
    }

    removeDarkStyles() {
        const darkStyleSheet = document.getElementById('dark-theme-styles');
        if (darkStyleSheet) {
            darkStyleSheet.remove();
        }
    }

    updateChartThemes() {
        // Update Chart.js themes if charts exist
        if (window.dashboardEnhancements && window.dashboardEnhancements.charts) {
            const isDark = this.currentTheme === 'dark';
            const textColor = isDark ? '#e2e8f0' : '#374151';
            const gridColor = isDark ? '#4a5568' : '#e5e7eb';
            
            Object.values(window.dashboardEnhancements.charts).forEach(chart => {
                if (chart && chart.options) {
                    // Update text colors
                    if (chart.options.plugins && chart.options.plugins.legend) {
                        chart.options.plugins.legend.labels.color = textColor;
                    }
                    
                    // Update scale colors
                    if (chart.options.scales) {
                        Object.values(chart.options.scales).forEach(scale => {
                            if (scale.ticks) scale.ticks.color = textColor;
                            if (scale.grid) scale.grid.color = gridColor;
                        });
                    }
                    
                    chart.update();
                }
            });
        }
    }

    saveTheme() {
        localStorage.setItem('nj-theme', this.currentTheme);
    }

    watchSystemTheme() {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        
        mediaQuery.addEventListener('change', (e) => {
            // Only auto-switch if user hasn't manually set a preference
            if (!localStorage.getItem('nj-theme')) {
                this.currentTheme = e.matches ? 'dark' : 'light';
                this.applyTheme();
                this.updateToggleButton(document.querySelector('.theme-toggle'));
            }
        });
    }

    // Public methods
    setTheme(theme) {
        if (['light', 'dark'].includes(theme)) {
            this.currentTheme = theme;
            this.applyTheme();
            this.saveTheme();
            this.updateToggleButton(document.querySelector('.theme-toggle'));
        }
    }

    getTheme() {
        return this.currentTheme;
    }

    resetToSystem() {
        localStorage.removeItem('nj-theme');
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        this.currentTheme = systemPrefersDark ? 'dark' : 'light';
        this.applyTheme();
        this.updateToggleButton(document.querySelector('.theme-toggle'));
    }
}

// Initialize theme manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.themeManager = new ThemeManager();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
