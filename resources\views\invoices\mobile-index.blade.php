@extends('layouts.mobile')

@section('title', __('app.invoices.title'))

@push('styles')
<style>
    .mobile-invoice-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px 15px;
        position: sticky;
        top: 0;
        z-index: 1000;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .mobile-search-bar {
        background: white;
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
        position: sticky;
        top: 80px;
        z-index: 999;
    }
    
    .mobile-search-input {
        border-radius: 25px;
        padding: 12px 20px;
        border: 2px solid #e9ecef;
        width: 100%;
        font-size: 1rem;
    }
    
    .mobile-search-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .mobile-filters {
        background: #f8f9fa;
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .mobile-filter-chip {
        display: inline-block;
        padding: 8px 16px;
        margin-right: 8px;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 20px;
        font-size: 0.9rem;
        color: #495057;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .mobile-filter-chip.active,
    .mobile-filter-chip:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
        text-decoration: none;
    }
    
    .mobile-invoice-list {
        padding: 15px;
        background: #f8f9fa;
    }
    
    .mobile-invoice-card {
        background: white;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
    }
    
    .mobile-invoice-card:active {
        transform: scale(0.98);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    }
    
    .mobile-invoice-header-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
    }
    
    .mobile-invoice-number {
        font-size: 1.1rem;
        font-weight: 700;
        color: #495057;
        margin-bottom: 4px;
    }
    
    .mobile-invoice-date {
        font-size: 0.85rem;
        color: #6c757d;
    }
    
    .mobile-invoice-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .mobile-invoice-status.paid {
        background: #d4edda;
        color: #155724;
    }
    
    .mobile-invoice-status.partial {
        background: #fff3cd;
        color: #856404;
    }
    
    .mobile-invoice-status.unpaid {
        background: #f8d7da;
        color: #721c24;
    }
    
    .mobile-invoice-status.overdue {
        background: #dc3545;
        color: white;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
    
    .mobile-invoice-customer {
        font-size: 1rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .mobile-invoice-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid #f1f3f4;
    }
    
    .mobile-invoice-amount {
        font-size: 1.2rem;
        font-weight: 700;
        color: #28a745;
    }
    
    .mobile-invoice-remaining {
        font-size: 0.9rem;
        color: #dc3545;
        font-weight: 600;
    }
    
    .mobile-invoice-actions {
        display: flex;
        gap: 8px;
        margin-top: 12px;
    }
    
    .mobile-action-btn {
        flex: 1;
        padding: 8px 12px;
        border: 1px solid #dee2e6;
        background: white;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 500;
        color: #495057;
        text-align: center;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .mobile-action-btn:hover,
    .mobile-action-btn:active {
        background: #f8f9fa;
        color: #495057;
        text-decoration: none;
        transform: scale(0.98);
    }
    
    .mobile-action-btn.primary {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }
    
    .mobile-action-btn.success {
        background: #28a745;
        color: white;
        border-color: #28a745;
    }
    
    .mobile-fab {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 50%;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
        z-index: 1000;
        transition: all 0.3s ease;
    }
    
    .mobile-fab:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
    }
    
    .mobile-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        padding: 15px;
        background: white;
        border-bottom: 1px solid #e9ecef;
    }
    
    .mobile-stat-item {
        text-align: center;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .mobile-stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #495057;
        margin-bottom: 4px;
    }
    
    .mobile-stat-label {
        font-size: 0.8rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .mobile-empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .mobile-empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
    
    .mobile-loading {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px;
    }
    
    .mobile-loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* Pull to refresh */
    .mobile-pull-refresh {
        text-align: center;
        padding: 20px;
        color: #6c757d;
        background: white;
        border-bottom: 1px solid #e9ecef;
        transform: translateY(-100%);
        transition: transform 0.3s ease;
    }
    
    .mobile-pull-refresh.show {
        transform: translateY(0);
    }
    
    /* RTL Support */
    body[dir="rtl"] .mobile-filter-chip {
        margin-right: 0;
        margin-left: 8px;
    }
    
    body[dir="rtl"] .mobile-fab {
        right: auto;
        left: 20px;
    }
    
    body[dir="rtl"] .mobile-invoice-customer {
        flex-direction: row-reverse;
    }
</style>
@endpush

@section('content')
<div class="mobile-invoice-container">
    <!-- Header -->
    <div class="mobile-invoice-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h4 mb-1">
                    <i class="bi bi-receipt"></i> {{ __('app.invoices.title') }}
                </h1>
                <p class="mb-0 opacity-75">{{ __('app.invoices.manage_invoices') }}</p>
            </div>
            <button class="btn btn-outline-light btn-sm" id="mobileInvoiceMenu">
                <i class="bi bi-three-dots-vertical"></i>
            </button>
        </div>
    </div>

    <!-- Stats -->
    <div class="mobile-stats">
        <div class="mobile-stat-item">
            <div class="mobile-stat-value">{{ $stats['total'] ?? 0 }}</div>
            <div class="mobile-stat-label">{{ __('app.invoices.total') }}</div>
        </div>
        <div class="mobile-stat-item">
            <div class="mobile-stat-value">{{ $stats['unpaid'] ?? 0 }}</div>
            <div class="mobile-stat-label">{{ __('app.invoices.unpaid') }}</div>
        </div>
        <div class="mobile-stat-item">
            <div class="mobile-stat-value">{{ number_format($stats['total_amount'] ?? 0, 0) }}</div>
            <div class="mobile-stat-label">{{ __('app.invoices.total_amount') }}</div>
        </div>
        <div class="mobile-stat-item">
            <div class="mobile-stat-value">{{ number_format($stats['outstanding'] ?? 0, 0) }}</div>
            <div class="mobile-stat-label">{{ __('app.invoices.outstanding') }}</div>
        </div>
    </div>

    <!-- Search -->
    <div class="mobile-search-bar">
        <input type="text" class="mobile-search-input" id="mobileInvoiceSearch" 
               placeholder="{{ __('app.invoices.search_placeholder') }}" autocomplete="off">
    </div>

    <!-- Filters -->
    <div class="mobile-filters">
        <a href="#" class="mobile-filter-chip active" data-filter="all">{{ __('app.invoices.all') }}</a>
        <a href="#" class="mobile-filter-chip" data-filter="paid">{{ __('app.invoices.paid') }}</a>
        <a href="#" class="mobile-filter-chip" data-filter="unpaid">{{ __('app.invoices.unpaid') }}</a>
        <a href="#" class="mobile-filter-chip" data-filter="partial">{{ __('app.invoices.partial') }}</a>
        <a href="#" class="mobile-filter-chip" data-filter="overdue">{{ __('app.invoices.overdue') }}</a>
    </div>

    <!-- Pull to Refresh Indicator -->
    <div class="mobile-pull-refresh" id="mobilePullRefresh">
        <i class="bi bi-arrow-clockwise"></i>
        <div>{{ __('app.invoices.pull_to_refresh') }}</div>
    </div>

    <!-- Invoice List -->
    <div class="mobile-invoice-list" id="mobileInvoiceList">
        @forelse($invoices as $invoice)
            <div class="mobile-invoice-card" data-invoice-id="{{ $invoice->id }}" data-status="{{ $invoice->payment_status }}">
                <div class="mobile-invoice-header-row">
                    <div>
                        <div class="mobile-invoice-number">#{{ $invoice->invoice_number }}</div>
                        <div class="mobile-invoice-date">{{ $invoice->invoice_date->format('M d, Y') }}</div>
                    </div>
                    <div class="mobile-invoice-status {{ $invoice->payment_status }}">
                        {{ __('app.invoices.status.' . $invoice->payment_status) }}
                    </div>
                </div>

                <div class="mobile-invoice-customer">
                    <i class="bi bi-person-circle"></i>
                    {{ $invoice->customer->name }}
                </div>

                @if($invoice->repairTicket)
                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="bi bi-tools"></i> {{ $invoice->repairTicket->device_model }}
                        </small>
                    </div>
                @endif

                <div class="mobile-invoice-details">
                    <div>
                        <div class="mobile-invoice-amount">{{ number_format($invoice->total_amount, 2) }} {{ __('app.currency') }}</div>
                        @if($invoice->remaining_amount > 0)
                            <div class="mobile-invoice-remaining">{{ __('app.invoices.remaining') }}: {{ number_format($invoice->remaining_amount, 2) }} {{ __('app.currency') }}</div>
                        @endif
                    </div>
                    <div class="text-end">
                        @if($invoice->due_date)
                            <small class="text-muted">
                                {{ __('app.invoices.due') }}: {{ $invoice->due_date->format('M d') }}
                            </small>
                        @endif
                    </div>
                </div>

                <div class="mobile-invoice-actions">
                    <a href="{{ route('invoices.show', $invoice) }}" class="mobile-action-btn">
                        <i class="bi bi-eye"></i> {{ __('app.view') }}
                    </a>
                    @if($invoice->payment_status !== 'paid')
                        <a href="{{ route('payments.create', ['invoice' => $invoice->id]) }}" class="mobile-action-btn success">
                            <i class="bi bi-credit-card"></i> {{ __('app.invoices.add_payment') }}
                        </a>
                    @endif
                    <button class="mobile-action-btn" onclick="shareInvoice({{ $invoice->id }})">
                        <i class="bi bi-share"></i> {{ __('app.share') }}
                    </button>
                </div>
            </div>
        @empty
            <div class="mobile-empty-state">
                <i class="bi bi-receipt"></i>
                <h5>{{ __('app.invoices.no_invoices') }}</h5>
                <p>{{ __('app.invoices.no_invoices_message') }}</p>
                <a href="{{ route('invoices.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus"></i> {{ __('app.invoices.create_first') }}
                </a>
            </div>
        @endforelse

        <!-- Loading indicator -->
        <div class="mobile-loading" id="mobileInvoiceLoading" style="display: none;">
            <div class="mobile-loading-spinner"></div>
        </div>
    </div>

    <!-- FAB -->
    <a href="{{ route('invoices.create') }}" class="mobile-fab">
        <i class="bi bi-plus"></i>
    </a>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let isLoading = false;
    let currentFilter = 'all';
    let searchQuery = '';
    let pullToRefreshStartY = 0;
    let isPulling = false;

    // Search functionality
    const searchInput = document.getElementById('mobileInvoiceSearch');
    searchInput.addEventListener('input', debounce(function(e) {
        searchQuery = e.target.value.toLowerCase();
        filterInvoices();
    }, 300));

    // Filter functionality
    document.querySelectorAll('.mobile-filter-chip').forEach(chip => {
        chip.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active filter
            document.querySelectorAll('.mobile-filter-chip').forEach(c => c.classList.remove('active'));
            this.classList.add('active');
            
            currentFilter = this.dataset.filter;
            filterInvoices();
        });
    });

    // Pull to refresh
    const invoiceList = document.getElementById('mobileInvoiceList');
    const pullRefreshIndicator = document.getElementById('mobilePullRefresh');
    
    invoiceList.addEventListener('touchstart', function(e) {
        if (invoiceList.scrollTop === 0) {
            pullToRefreshStartY = e.touches[0].clientY;
            isPulling = true;
        }
    });

    invoiceList.addEventListener('touchmove', function(e) {
        if (isPulling) {
            const currentY = e.touches[0].clientY;
            const pullDistance = currentY - pullToRefreshStartY;
            
            if (pullDistance > 80) {
                pullRefreshIndicator.classList.add('show');
            } else {
                pullRefreshIndicator.classList.remove('show');
            }
        }
    });

    invoiceList.addEventListener('touchend', function(e) {
        if (isPulling) {
            const currentY = e.changedTouches[0].clientY;
            const pullDistance = currentY - pullToRefreshStartY;
            
            if (pullDistance > 80) {
                refreshInvoices();
            }
            
            isPulling = false;
            pullRefreshIndicator.classList.remove('show');
        }
    });

    // Invoice card click
    document.querySelectorAll('.mobile-invoice-card').forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't navigate if clicking on action buttons
            if (e.target.closest('.mobile-action-btn')) {
                return;
            }
            
            const invoiceId = this.dataset.invoiceId;
            window.location.href = `/invoices/${invoiceId}`;
        });
    });

    // Functions
    function filterInvoices() {
        const cards = document.querySelectorAll('.mobile-invoice-card');
        
        cards.forEach(card => {
            const status = card.dataset.status;
            const text = card.textContent.toLowerCase();
            
            let showCard = true;
            
            // Filter by status
            if (currentFilter !== 'all' && status !== currentFilter) {
                showCard = false;
            }
            
            // Filter by search query
            if (searchQuery && !text.includes(searchQuery)) {
                showCard = false;
            }
            
            card.style.display = showCard ? 'block' : 'none';
        });
    }

    function refreshInvoices() {
        if (isLoading) return;
        
        isLoading = true;
        document.getElementById('mobileInvoiceLoading').style.display = 'flex';
        
        // Simulate API call
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
});

// Share invoice function
function shareInvoice(invoiceId) {
    if (navigator.share) {
        navigator.share({
            title: 'Invoice #' + invoiceId,
            text: 'View invoice details',
            url: window.location.origin + '/invoices/' + invoiceId
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        const url = window.location.origin + '/invoices/' + invoiceId;
        navigator.clipboard.writeText(url).then(() => {
            showToast('Invoice link copied to clipboard', 'success');
        });
    }
}

// Toast notification function
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    document.getElementById('toastContainer').appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}
</script>
@endpush
