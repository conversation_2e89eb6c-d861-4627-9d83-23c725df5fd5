/**
 * NJ Repair Shop - Dashboard Enhancement System
 * Real-time updates, interactive charts, and dynamic content
 */

class DashboardEnhancements {
    constructor() {
        this.refreshInterval = null;
        this.charts = {};
        this.init();
    }

    init() {
        this.initializeRealTimeUpdates();
        this.initializeInteractiveCharts();
        this.initializeQuickActions();
        this.initializeSearchShortcuts();
        this.initializeNotificationCenter();
        this.initializeProgressAnimations();
    }

    // Real-time dashboard updates
    initializeRealTimeUpdates() {
        // Update statistics every 30 seconds
        this.refreshInterval = setInterval(() => {
            this.updateStatistics();
        }, 30000);

        // Update recent activities every 60 seconds
        setTimeout(() => {
            setInterval(() => {
                this.updateRecentActivities();
            }, 60000);
        }, 5000);
    }

    async updateStatistics() {
        try {
            const response = await fetch('/api/dashboard/stats', {
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                this.updateStatisticCards(data);
            }
        } catch (error) {
            console.log('Failed to update statistics:', error);
        }
    }

    updateStatisticCards(data) {
        // Update each statistic card with animation
        Object.keys(data).forEach(key => {
            const element = document.querySelector(`[data-stat="${key}"]`);
            if (element) {
                const currentValue = parseInt(element.textContent);
                const newValue = data[key];
                
                if (currentValue !== newValue) {
                    this.animateNumber(element, currentValue, newValue);
                    
                    // Add pulse animation to indicate change
                    element.closest('.stats-card').classList.add('pulse-animation');
                    setTimeout(() => {
                        element.closest('.stats-card').classList.remove('pulse-animation');
                    }, 2000);
                }
            }
        });
    }

    animateNumber(element, start, end) {
        const duration = 1000;
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const current = Math.floor(start + (end - start) * progress);
            element.textContent = current.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }

    async updateRecentActivities() {
        try {
            const response = await fetch('/api/dashboard/recent-activities');
            if (response.ok) {
                const data = await response.json();
                this.updateActivitiesList(data);
            }
        } catch (error) {
            console.log('Failed to update recent activities:', error);
        }
    }

    updateActivitiesList(activities) {
        const container = document.querySelector('#recent-activities-list');
        if (!container) return;

        // Fade out current content
        container.style.opacity = '0.5';
        
        setTimeout(() => {
            container.innerHTML = activities.map(activity => `
                <div class="d-flex align-items-center mb-3 activity-item slide-in-right">
                    <div class="flex-shrink-0">
                        <i class="bi ${this.getActivityIcon(activity.type)} text-primary"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-semibold">${activity.title}</div>
                        <div class="text-muted small">${activity.description}</div>
                        <div class="text-muted small">${activity.time_ago}</div>
                    </div>
                </div>
            `).join('');
            
            // Fade in new content
            container.style.opacity = '1';
        }, 300);
    }

    getActivityIcon(type) {
        const icons = {
            'ticket_created': 'bi-plus-circle',
            'ticket_updated': 'bi-pencil-square',
            'ticket_completed': 'bi-check-circle',
            'customer_added': 'bi-person-plus',
            'payment_received': 'bi-cash-coin'
        };
        return icons[type] || 'bi-info-circle';
    }

    // Interactive charts with Chart.js
    initializeInteractiveCharts() {
        this.initializeTicketStatusChart();
        this.initializeRevenueChart();
        this.initializeBrandPopularityChart();
    }

    initializeTicketStatusChart() {
        const ctx = document.getElementById('ticketStatusChart');
        if (!ctx) return;

        const data = JSON.parse(ctx.dataset.chartData || '[]');
        
        this.charts.ticketStatus = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.map(item => item.name),
                datasets: [{
                    data: data.map(item => item.count),
                    backgroundColor: data.map(item => item.color),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 2000
                },
                onHover: (event, elements) => {
                    event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';
                },
                onClick: (event, elements) => {
                    if (elements.length > 0) {
                        const index = elements[0].index;
                        const status = data[index].name;
                        this.filterTicketsByStatus(status);
                    }
                }
            }
        });
    }

    initializeRevenueChart() {
        const ctx = document.getElementById('revenueChart');
        if (!ctx) return;

        const data = JSON.parse(ctx.dataset.chartData || '[]');
        
        this.charts.revenue = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => item.month),
                datasets: [{
                    label: 'Revenue',
                    data: data.map(item => item.revenue),
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#007bff',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                return `Revenue: $${context.parsed.y.toLocaleString()}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    initializeBrandPopularityChart() {
        const ctx = document.getElementById('brandChart');
        if (!ctx) return;

        const data = JSON.parse(ctx.dataset.chartData || '[]');
        
        this.charts.brands = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.map(item => item.name),
                datasets: [{
                    label: 'Tickets',
                    data: data.map(item => item.count),
                    backgroundColor: 'rgba(0, 123, 255, 0.8)',
                    borderColor: '#007bff',
                    borderWidth: 1,
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                animation: {
                    duration: 1500,
                    easing: 'easeOutBounce'
                }
            }
        });
    }

    // Quick actions
    initializeQuickActions() {
        const quickActionBtns = document.querySelectorAll('.quick-action-btn');
        
        quickActionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const action = btn.dataset.action;
                this.performQuickAction(action);
            });
        });
    }

    performQuickAction(action) {
        switch (action) {
            case 'new-ticket':
                window.location.href = '/repair-tickets/create';
                break;
            case 'new-customer':
                window.location.href = '/customers/create';
                break;
            case 'search':
                document.querySelector('.search-input')?.focus();
                break;
            case 'reports':
                window.location.href = '/reports';
                break;
        }
    }

    // Search shortcuts
    initializeSearchShortcuts() {
        const searchInput = document.querySelector('.dashboard-search');
        if (!searchInput) return;

        let searchTimeout;
        
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.performGlobalSearch(e.target.value);
            }, 300);
        });
    }

    async performGlobalSearch(query) {
        if (query.length < 2) {
            this.hideSearchResults();
            return;
        }

        try {
            const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
            if (response.ok) {
                const results = await response.json();
                this.showSearchResults(results);
            }
        } catch (error) {
            console.log('Search failed:', error);
        }
    }

    showSearchResults(results) {
        let resultsContainer = document.querySelector('.search-results');
        if (!resultsContainer) {
            resultsContainer = document.createElement('div');
            resultsContainer.className = 'search-results position-absolute bg-white border rounded shadow-lg';
            document.querySelector('.dashboard-search').parentElement.appendChild(resultsContainer);
        }

        resultsContainer.innerHTML = results.map(result => `
            <a href="${result.url}" class="d-block p-3 text-decoration-none border-bottom">
                <div class="fw-semibold">${result.title}</div>
                <div class="text-muted small">${result.description}</div>
            </a>
        `).join('');

        resultsContainer.style.display = 'block';
    }

    hideSearchResults() {
        const resultsContainer = document.querySelector('.search-results');
        if (resultsContainer) {
            resultsContainer.style.display = 'none';
        }
    }

    // Notification center
    initializeNotificationCenter() {
        this.checkForNotifications();
        setInterval(() => {
            this.checkForNotifications();
        }, 60000); // Check every minute
    }

    async checkForNotifications() {
        try {
            const response = await fetch('/api/notifications/unread');
            if (response.ok) {
                const data = await response.json();
                this.updateNotificationBadge(data.count);
            }
        } catch (error) {
            console.log('Failed to check notifications:', error);
        }
    }

    updateNotificationBadge(count) {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'inline-block';
                badge.classList.add('pulse-animation');
            } else {
                badge.style.display = 'none';
                badge.classList.remove('pulse-animation');
            }
        }
    }

    // Progress animations
    initializeProgressAnimations() {
        const progressBars = document.querySelectorAll('.progress-bar[data-animate="true"]');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const bar = entry.target;
                    const targetWidth = bar.dataset.width || bar.getAttribute('aria-valuenow') + '%';
                    
                    setTimeout(() => {
                        bar.style.width = targetWidth;
                    }, 200);
                    
                    observer.unobserve(bar);
                }
            });
        });

        progressBars.forEach(bar => {
            bar.style.width = '0%';
            bar.style.transition = 'width 1.5s ease-in-out';
            observer.observe(bar);
        });
    }

    filterTicketsByStatus(status) {
        // Navigate to tickets page with status filter
        window.location.href = `/repair-tickets?status=${encodeURIComponent(status)}`;
    }

    // Cleanup
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
    }
}

// Initialize dashboard enhancements when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.dashboard-page')) {
        window.dashboardEnhancements = new DashboardEnhancements();
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.dashboardEnhancements) {
        window.dashboardEnhancements.destroy();
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DashboardEnhancements;
}
