@extends('layouts.app')

@section('title', 'سجل حركات المخزون - ' . $inventory->name)

@push('styles')
<style>
.movements-header {
    background: linear-gradient(45deg, #6f42c1, #5a32a3);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.movement-card {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.2s ease;
}

.movement-card:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.movement-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e3e6f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.movement-body {
    padding: 1.5rem;
}

.movement-type-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.875rem;
}

.movement-in {
    background: #d4edda;
    color: #155724;
}

.movement-out {
    background: #f8d7da;
    color: #721c24;
}

.movement-adjustment {
    background: #fff3cd;
    color: #856404;
}

.quantity-display {
    font-size: 1.25rem;
    font-weight: bold;
}

.quantity-positive {
    color: #28a745;
}

.quantity-negative {
    color: #dc3545;
}

.quantity-neutral {
    color: #ffc107;
}

.stock-timeline {
    position: relative;
    padding-left: 2rem;
}

.stock-timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    padding-bottom: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -0.5rem;
    top: 0.5rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background: #fff;
    border: 3px solid #6f42c1;
    z-index: 1;
}

.timeline-item.movement-in::before {
    border-color: #28a745;
}

.timeline-item.movement-out::before {
    border-color: #dc3545;
}

.timeline-item.movement-adjustment::before {
    border-color: #ffc107;
}

.filter-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background: #fff;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1rem;
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
}

.movement-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    margin-bottom: 0.25rem;
}

.detail-value {
    font-weight: 600;
    color: #495057;
}

.export-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.btn-export {
    padding: 0.5rem 1rem;
    border-radius: 0.35rem;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-export:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .movements-header {
        padding: 1rem;
    }
    
    .movement-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .movement-body {
        padding: 1rem;
    }
    
    .movement-details {
        grid-template-columns: 1fr;
    }
    
    .export-buttons {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="movements-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">سجل حركات المخزون</h1>
                <p class="mb-0 opacity-75">{{ $inventory->name }} - {{ $inventory->sku }}</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('inventory.show', $inventory) }}" class="btn btn-light">
                    <i class="fas fa-eye me-2"></i>عرض العنصر
                </a>
                <a href="{{ route('inventory.index') }}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للمخزون
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value text-primary">@arabicNumber($movements->count())</div>
            <div class="stat-label">إجمالي الحركات</div>
        </div>
        <div class="stat-card">
            <div class="stat-value text-success">@arabicNumber($movementsIn)</div>
            <div class="stat-label">حركات داخلة</div>
        </div>
        <div class="stat-card">
            <div class="stat-value text-danger">@arabicNumber($movementsOut)</div>
            <div class="stat-label">حركات خارجة</div>
        </div>
        <div class="stat-card">
            <div class="stat-value text-info">@arabicNumber($inventory->current_stock)</div>
            <div class="stat-label">المخزون الحالي</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="GET" action="{{ route('inventory.movements', $inventory) }}" class="row g-3">
            <div class="col-md-3">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" 
                       class="form-control" 
                       id="date_from" 
                       name="date_from" 
                       value="{{ request('date_from') }}">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" 
                       class="form-control" 
                       id="date_to" 
                       name="date_to" 
                       value="{{ request('date_to') }}">
            </div>
            <div class="col-md-2">
                <label for="movement_type" class="form-label">نوع الحركة</label>
                <select class="form-select" id="movement_type" name="movement_type">
                    <option value="">جميع الأنواع</option>
                    <option value="purchase" {{ request('movement_type') === 'purchase' ? 'selected' : '' }}>شراء</option>
                    <option value="sale" {{ request('movement_type') === 'sale' ? 'selected' : '' }}>بيع</option>
                    <option value="adjustment" {{ request('movement_type') === 'adjustment' ? 'selected' : '' }}>تعديل</option>
                    <option value="return" {{ request('movement_type') === 'return' ? 'selected' : '' }}>إرجاع</option>
                    <option value="damage" {{ request('movement_type') === 'damage' ? 'selected' : '' }}>تلف</option>
                    <option value="transfer" {{ request('movement_type') === 'transfer' ? 'selected' : '' }}>نقل</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="direction" class="form-label">الاتجاه</label>
                <select class="form-select" id="direction" name="direction">
                    <option value="">جميع الاتجاهات</option>
                    <option value="in" {{ request('direction') === 'in' ? 'selected' : '' }}>داخل</option>
                    <option value="out" {{ request('direction') === 'out' ? 'selected' : '' }}>خارج</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-1">
                    <button type="submit" class="btn btn-primary flex-fill">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="{{ route('inventory.movements', $inventory) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Export Buttons -->
    <div class="export-buttons">
        <button type="button" class="btn btn-success btn-export" onclick="exportToExcel()">
            <i class="fas fa-file-excel me-2"></i>تصدير Excel
        </button>
        <button type="button" class="btn btn-danger btn-export" onclick="exportToPDF()">
            <i class="fas fa-file-pdf me-2"></i>تصدير PDF
        </button>
        <button type="button" class="btn btn-info btn-export" onclick="printReport()">
            <i class="fas fa-print me-2"></i>طباعة
        </button>
    </div>

    <!-- Movements List -->
    @if($movements->count() > 0)
        <div class="stock-timeline">
            @foreach($movements as $movement)
                <div class="timeline-item movement-{{ $movement->direction }}">
                    <div class="movement-card">
                        <div class="movement-header">
                            <div class="d-flex align-items-center gap-3">
                                <span class="movement-type-badge movement-{{ $movement->direction }}">
                                    @switch($movement->movement_type)
                                        @case('purchase')
                                            <i class="fas fa-shopping-cart me-1"></i>شراء
                                            @break
                                        @case('sale')
                                            <i class="fas fa-cash-register me-1"></i>بيع
                                            @break
                                        @case('adjustment')
                                            <i class="fas fa-balance-scale me-1"></i>تعديل
                                            @break
                                        @case('return')
                                            <i class="fas fa-undo me-1"></i>إرجاع
                                            @break
                                        @case('damage')
                                            <i class="fas fa-exclamation-triangle me-1"></i>تلف
                                            @break
                                        @case('transfer')
                                            <i class="fas fa-exchange-alt me-1"></i>نقل
                                            @break
                                        @default
                                            <i class="fas fa-arrows-alt me-1"></i>{{ $movement->movement_type }}
                                    @endswitch
                                </span>
                                
                                <div class="quantity-display quantity-{{ $movement->direction === 'in' ? 'positive' : 'negative' }}">
                                    {{ $movement->direction === 'in' ? '+' : '-' }}@arabicNumber($movement->quantity)
                                    {{ $inventory->unit_of_measure }}
                                </div>
                            </div>
                            
                            <div class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $movement->movement_date->format('Y-m-d H:i') }}
                            </div>
                        </div>
                        
                        <div class="movement-body">
                            <div class="movement-details">
                                <div class="detail-item">
                                    <div class="detail-label">المخزون قبل الحركة</div>
                                    <div class="detail-value">@arabicNumber($movement->stock_before)</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">المخزون بعد الحركة</div>
                                    <div class="detail-value">@arabicNumber($movement->stock_after)</div>
                                </div>
                                @if($movement->unit_cost)
                                <div class="detail-item">
                                    <div class="detail-label">تكلفة الوحدة</div>
                                    <div class="detail-value">@arabicCurrency($movement->unit_cost)</div>
                                </div>
                                @endif
                                @if($movement->total_cost)
                                <div class="detail-item">
                                    <div class="detail-label">التكلفة الإجمالية</div>
                                    <div class="detail-value">@arabicCurrency($movement->total_cost)</div>
                                </div>
                                @endif
                                @if($movement->reference_type && $movement->reference_number)
                                <div class="detail-item">
                                    <div class="detail-label">المرجع</div>
                                    <div class="detail-value">{{ $movement->reference_number }}</div>
                                </div>
                                @endif
                                @if($movement->createdBy)
                                <div class="detail-item">
                                    <div class="detail-label">بواسطة</div>
                                    <div class="detail-value">{{ $movement->createdBy->name }}</div>
                                </div>
                                @endif
                            </div>
                            
                            @if($movement->notes)
                            <div class="mt-3">
                                <div class="detail-label">الملاحظات</div>
                                <div class="detail-value">{{ $movement->notes }}</div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-center mt-4">
            {{ $movements->withQueryString()->links() }}
        </div>
    @else
        <div class="empty-state">
            <i class="fas fa-history"></i>
            <h5>لا توجد حركات مخزون</h5>
            <p>لم يتم العثور على حركات مخزون لهذا العنصر بناءً على المعايير المحددة.</p>
            @if(request()->hasAny(['date_from', 'date_to', 'movement_type', 'direction']))
                <a href="{{ route('inventory.movements', $inventory) }}" class="btn btn-primary">
                    <i class="fas fa-refresh me-2"></i>عرض جميع الحركات
                </a>
            @endif
        </div>
    @endif
</div>

@push('scripts')
<script>
function exportToExcel() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = `{{ route('inventory.movements', $inventory) }}?${params.toString()}`;
}

function exportToPDF() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.open(`{{ route('inventory.movements', $inventory) }}?${params.toString()}`, '_blank');
}

function printReport() {
    window.print();
}

// Auto-set date range shortcuts
document.addEventListener('DOMContentLoaded', function() {
    // Add quick date range buttons
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');
    
    // Create quick buttons container
    const quickButtons = document.createElement('div');
    quickButtons.className = 'mt-2 d-flex gap-1 flex-wrap';
    quickButtons.innerHTML = `
        <button type="button" class="btn btn-sm btn-outline-primary" onclick="setDateRange('today')">اليوم</button>
        <button type="button" class="btn btn-sm btn-outline-primary" onclick="setDateRange('week')">هذا الأسبوع</button>
        <button type="button" class="btn btn-sm btn-outline-primary" onclick="setDateRange('month')">هذا الشهر</button>
        <button type="button" class="btn btn-sm btn-outline-primary" onclick="setDateRange('quarter')">هذا الربع</button>
        <button type="button" class="btn btn-sm btn-outline-primary" onclick="setDateRange('year')">هذا العام</button>
    `;
    
    dateToInput.parentNode.appendChild(quickButtons);
    
    window.setDateRange = function(range) {
        const today = new Date();
        let fromDate, toDate;
        
        switch(range) {
            case 'today':
                fromDate = toDate = today.toISOString().split('T')[0];
                break;
            case 'week':
                const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
                fromDate = weekStart.toISOString().split('T')[0];
                toDate = new Date().toISOString().split('T')[0];
                break;
            case 'month':
                fromDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
                toDate = new Date().toISOString().split('T')[0];
                break;
            case 'quarter':
                const quarter = Math.floor(today.getMonth() / 3);
                fromDate = new Date(today.getFullYear(), quarter * 3, 1).toISOString().split('T')[0];
                toDate = new Date().toISOString().split('T')[0];
                break;
            case 'year':
                fromDate = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
                toDate = new Date().toISOString().split('T')[0];
                break;
        }
        
        dateFromInput.value = fromDate;
        dateToInput.value = toDate;
    };
});
</script>
@endpush
@endsection
