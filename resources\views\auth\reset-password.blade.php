@extends('layouts.guest')

@section('content')
<div class="auth-card">
    <div class="auth-header">
        <div class="auth-logo">
            <i class="bi bi-shield-lock"></i>
        </div>
        <h1 class="auth-title">إعادة تعيين كلمة المرور</h1>
        <p class="auth-subtitle">ورشة إصلاح NJ</p>
    </div>
    <div class="auth-body">
        <form method="POST" action="{{ route('password.store') }}">
            @csrf

            <!-- Password Reset Token -->
            <input type="hidden" name="token" value="{{ $request->route('token') }}">

            <!-- Email Address -->
            <div class="mb-3">
                <label for="email" class="form-label">البريد الإلكتروني</label>
                <input id="email"
                       class="form-control @error('email') is-invalid @enderror"
                       type="email"
                       name="email"
                       value="{{ old('email', $request->email) }}"
                       required
                       autofocus
                       autocomplete="username"
                       placeholder="أدخل البريد الإلكتروني">
                @error('email')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Password -->
            <div class="mb-3">
                <label for="password" class="form-label">كلمة المرور الجديدة</label>
                <input id="password"
                       class="form-control @error('password') is-invalid @enderror"
                       type="password"
                       name="password"
                       required
                       autocomplete="new-password"
                       placeholder="أدخل كلمة المرور الجديدة">
                @error('password')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Confirm Password -->
            <div class="mb-4">
                <label for="password_confirmation" class="form-label">تأكيد كلمة المرور</label>
                <input id="password_confirmation"
                       class="form-control @error('password_confirmation') is-invalid @enderror"
                       type="password"
                       name="password_confirmation"
                       required
                       autocomplete="new-password"
                       placeholder="أعد إدخال كلمة المرور">
                @error('password_confirmation')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Submit Button -->
            <div class="mb-3">
                <button type="submit" class="btn btn-primary btn-login">
                    <i class="bi bi-check-circle"></i>
                    إعادة تعيين كلمة المرور
                </button>
            </div>

            <!-- Back to Login -->
            <div class="text-center">
                <a class="forgot-password" href="{{ route('login') }}">
                    <i class="bi bi-arrow-left"></i>
                    العودة لتسجيل الدخول
                </a>
            </div>
        </form>
    </div>
</div>
@endsection
