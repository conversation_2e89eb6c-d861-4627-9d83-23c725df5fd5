<?php

namespace App\Http\Controllers;

use App\Models\RepairTicket;
use App\Models\Customer;
use App\Models\Brand;
use App\Models\RepairStatus;
use App\Models\DeviceCondition;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use App\Services\PatternValidationService;

class RepairTicketController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // Temporarily disabled until authentication is fully set up
        // $this->authorizeResource(RepairTicket::class, 'repairTicket');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = RepairTicket::with(['customer', 'brand', 'repairStatus', 'deviceCondition', 'assignedTo']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Filter by status
        if ($request->has('status') && $request->status) {
            $query->where('repair_status_id', $request->status);
        }

        // Filter by brand
        if ($request->has('brand') && $request->brand) {
            $query->where('brand_id', $request->brand);
        }

        // Filter by assigned technician
        if ($request->has('technician') && $request->technician) {
            if ($request->technician === 'unassigned') {
                $query->unassigned();
            } else {
                $query->assignedTo($request->technician);
            }
        }

        // Filter by date range
        if ($request->has('date_from') && $request->date_from && $request->has('date_to') && $request->date_to) {
            $query->byDateRange($request->date_from, $request->date_to);
        }

        // Filter by priority/urgency
        if ($request->has('priority') && $request->priority) {
            switch ($request->priority) {
                case 'overdue':
                    $query->overdue();
                    break;
                case 'urgent':
                    $query->urgent(2); // Due within 2 days
                    break;
                case 'pending':
                    $query->pending();
                    break;
                case 'completed':
                    $query->completed();
                    break;
            }
        }

        // Sort options
        $sortBy = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');

        $allowedSorts = ['created_at', 'received_date', 'ticket_number', 'estimated_completion_date', 'priority'];
        if (in_array($sortBy, $allowedSorts)) {
            if ($sortBy === 'priority') {
                // Custom priority sorting
                $query->orderByRaw("
                    CASE
                        WHEN estimated_completion_date < NOW() AND repair_tickets.completed_date IS NULL THEN 1
                        WHEN DATEDIFF(NOW(), received_date) > 7 THEN 2
                        ELSE 3
                    END " . $sortDirection);
            } else {
                $query->orderBy($sortBy, $sortDirection);
            }
        }

        $tickets = $query->paginate(15)->withQueryString();

        // Get filter options
        $statuses = RepairStatus::active()->ordered()->get();
        $brands = Brand::active()->orderBy('name')->get();
        $technicians = \App\Models\User::orderBy('name')->get();

        // Get statistics for dashboard cards
        $stats = [
            'total' => RepairTicket::count(),
            'pending' => RepairTicket::pending()->count(),
            'overdue' => RepairTicket::overdue()->count(),
            'completed_today' => RepairTicket::completed()
                ->whereDate('completed_date', today())->count(),
        ];

        return view('repair-tickets.index', compact('tickets', 'statuses', 'brands', 'technicians', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request): View
    {
        $customers = Customer::orderBy('name')->get();
        $brands = Brand::active()->orderBy('name')->get();
        $repairStatuses = RepairStatus::active()->ordered()->get();
        $deviceConditions = DeviceCondition::active()->ordered()->get();

        // Pre-select customer if provided
        $selectedCustomer = null;
        if ($request->has('customer_id')) {
            $selectedCustomer = Customer::find($request->customer_id);
        }

        return view('repair-tickets.create', compact(
            'customers', 'brands', 'repairStatuses', 'deviceConditions', 'selectedCustomer'
        ));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'brand_id' => 'required|exists:brands,id',
            'device_model' => 'required|string|max:255',
            'reported_problem' => 'required|string|max:1000',
            'device_pattern' => 'nullable|boolean',
            'security_pattern' => 'nullable|string|max:255',
            'visual_pattern' => 'nullable|string|max:255',
            'pattern_type' => 'nullable|in:none,text,visual,both',
            'device_condition_id' => 'required|exists:device_conditions,id',
            'repair_status_id' => 'required|exists:repair_statuses,id',
            'received_date' => 'required|date',
            'estimated_completion_date' => 'nullable|date|after:received_date',
            'estimated_cost' => 'nullable|numeric|min:0|max:99999999.99',
            'technician_comments' => 'nullable|string|max:2000',
        ]);

        // Validate visual pattern if provided
        if (!empty($validated['visual_pattern'])) {
            $patternValidation = PatternValidationService::validateVisualPattern($validated['visual_pattern']);
            if (!$patternValidation['valid']) {
                return back()->withErrors(['visual_pattern' => implode(', ', $patternValidation['errors'])])->withInput();
            }
        }

        // Determine pattern type automatically
        $validated['pattern_type'] = PatternValidationService::determinePatternType(
            $validated['security_pattern'] ?? null,
            $validated['visual_pattern'] ?? null
        );

        // Add the authenticated user as the creator
        $validated['created_by'] = Auth::id() ?? 1; // Default to user ID 1 if no auth

        $ticket = RepairTicket::create($validated);

        return redirect()->route('repair-tickets.show', $ticket)
                        ->with('success', 'Repair ticket created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(RepairTicket $repairTicket): View
    {
        $repairTicket->load([
            'customer',
            'brand',
            'repairStatus',
            'deviceCondition',
            'createdBy',
            'assignedTo'
        ]);

        return view('repair-tickets.show', compact('repairTicket'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RepairTicket $repairTicket): View
    {
        $customers = Customer::orderBy('name')->get();
        $brands = Brand::active()->orderBy('name')->get();
        $repairStatuses = RepairStatus::active()->ordered()->get();
        $deviceConditions = DeviceCondition::active()->ordered()->get();

        return view('repair-tickets.edit', compact(
            'repairTicket', 'customers', 'brands', 'repairStatuses', 'deviceConditions'
        ));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, RepairTicket $repairTicket): RedirectResponse
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'brand_id' => 'required|exists:brands,id',
            'device_model' => 'required|string|max:255',
            'reported_problem' => 'required|string|max:1000',
            'device_pattern' => 'nullable|boolean',
            'security_pattern' => 'nullable|string|max:255',
            'visual_pattern' => 'nullable|string|max:255',
            'pattern_type' => 'nullable|in:none,text,visual,both',
            'device_condition_id' => 'required|exists:device_conditions,id',
            'repair_status_id' => 'required|exists:repair_statuses,id',
            'received_date' => 'required|date',
            'estimated_completion_date' => 'nullable|date|after:received_date',
            'estimated_cost' => 'nullable|numeric|min:0|max:99999999.99',
            'final_cost' => 'nullable|numeric|min:0|max:99999999.99',
            'technician_comments' => 'nullable|string|max:2000',
            'assigned_technician_id' => 'nullable|exists:users,id',
        ]);

        // Validate visual pattern if provided
        if (!empty($validated['visual_pattern'])) {
            $patternValidation = PatternValidationService::validateVisualPattern($validated['visual_pattern']);
            if (!$patternValidation['valid']) {
                return back()->withErrors(['visual_pattern' => implode(', ', $patternValidation['errors'])])->withInput();
            }
        }

        // Determine pattern type automatically
        $validated['pattern_type'] = PatternValidationService::determinePatternType(
            $validated['security_pattern'] ?? null,
            $validated['visual_pattern'] ?? null
        );

        // Check if status changed to final and set completion date
        $newStatus = RepairStatus::find($validated['repair_status_id']);
        if ($newStatus->is_final && !$repairTicket->completed_date) {
            $validated['completed_date'] = now();
        } elseif (!$newStatus->is_final) {
            $validated['completed_date'] = null;
        }

        $repairTicket->update($validated);

        return redirect()->route('repair-tickets.show', $repairTicket)
                        ->with('success', 'Repair ticket updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RepairTicket $repairTicket): RedirectResponse
    {
        $repairTicket->delete();

        return redirect()->route('repair-tickets.index')
                        ->with('success', 'Repair ticket deleted successfully.');
    }

    /**
     * Update ticket status quickly
     */
    public function updateStatus(Request $request, RepairTicket $repairTicket): RedirectResponse
    {
        $validated = $request->validate([
            'repair_status_id' => 'required|exists:repair_statuses,id',
            'technician_comments' => 'nullable|string|max:2000',
        ]);

        // Check if status changed to final and set completion date
        $newStatus = RepairStatus::find($validated['repair_status_id']);
        if ($newStatus->is_final && !$repairTicket->completed_date) {
            $validated['completed_date'] = now();
        } elseif (!$newStatus->is_final) {
            $validated['completed_date'] = null;
        }

        $repairTicket->update($validated);

        return redirect()->back()
                        ->with('success', 'Ticket status updated successfully.');
    }

    /**
     * Handle bulk operations on multiple tickets
     */
    public function bulkAction(Request $request): RedirectResponse
    {
        $request->validate([
            'tickets' => 'required|array|min:1',
            'tickets.*' => 'exists:repair_tickets,id',
            'action' => 'required|in:update_status,assign_technician,delete',
            'status_id' => 'required_if:action,update_status|exists:repair_statuses,id',
            'technician_id' => 'required_if:action,assign_technician|exists:users,id',
        ]);

        $ticketIds = $request->tickets;
        $action = $request->action;
        $affectedCount = 0;

        switch ($action) {
            case 'update_status':
                $affectedCount = RepairTicket::whereIn('id', $ticketIds)
                    ->update([
                        'repair_status_id' => $request->status_id,
                        'updated_at' => now(),
                    ]);

                // Update completion date if status is final
                $status = RepairStatus::find($request->status_id);
                if ($status && $status->is_final) {
                    RepairTicket::whereIn('id', $ticketIds)
                        ->whereNull('completed_date')
                        ->update(['completed_date' => now()]);
                }

                $message = "Updated status for {$affectedCount} tickets.";
                break;

            case 'assign_technician':
                $affectedCount = RepairTicket::whereIn('id', $ticketIds)
                    ->update([
                        'assigned_to' => $request->technician_id,
                        'updated_at' => now(),
                    ]);

                $message = "Assigned technician to {$affectedCount} tickets.";
                break;

            case 'delete':
                $affectedCount = RepairTicket::whereIn('id', $ticketIds)->count();
                RepairTicket::whereIn('id', $ticketIds)->delete();

                $message = "Deleted {$affectedCount} tickets.";
                break;

            default:
                return redirect()->route('repair-tickets.index')
                                ->with('error', 'Invalid action selected.');
        }

        return redirect()->route('repair-tickets.index')
                        ->with('success', $message);
    }
}
