@extends('layouts.app')

@section('title', __('app.advanced_reports.dashboard'))

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">
<style>
    .dashboard-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        overflow: hidden;
    }
    
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .kpi-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        position: relative;
    }
    
    .kpi-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .kpi-card:hover::before {
        opacity: 1;
    }
    
    .kpi-value {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .kpi-label {
        font-size: 0.9rem;
        opacity: 0.9;
        margin-bottom: 0.5rem;
    }
    
    .kpi-change {
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }
    
    .kpi-change.positive {
        color: #4ade80;
    }
    
    .kpi-change.negative {
        color: #f87171;
    }
    
    .chart-container {
        position: relative;
        height: 300px;
        margin: 20px 0;
    }
    
    .metric-item {
        padding: 15px;
        border-left: 4px solid #e5e7eb;
        margin-bottom: 10px;
        background: #f9fafb;
        border-radius: 0 8px 8px 0;
        transition: all 0.3s ease;
    }
    
    .metric-item:hover {
        border-left-color: #3b82f6;
        background: #eff6ff;
    }
    
    .metric-value {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
    }
    
    .metric-label {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 0.25rem;
    }
    
    .metric-change {
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .performance-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .performance-indicator.excellent {
        background-color: #10b981;
    }
    
    .performance-indicator.good {
        background-color: #f59e0b;
    }
    
    .performance-indicator.needs_improvement {
        background-color: #ef4444;
    }
    
    .quick-stats {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .quick-stat-item {
        text-align: center;
        padding: 10px;
    }
    
    .quick-stat-value {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }
    
    .quick-stat-label {
        font-size: 0.8rem;
        opacity: 0.9;
    }
    
    .period-selector {
        background: white;
        border-radius: 10px;
        padding: 5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .period-btn {
        border: none;
        background: transparent;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .period-btn.active {
        background: #3b82f6;
        color: white;
    }
    
    .period-btn:hover:not(.active) {
        background: #f3f4f6;
    }
    
    .trend-chart {
        height: 250px;
    }
    
    .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
    }
    
    .export-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .export-btn {
        padding: 8px 16px;
        border-radius: 8px;
        border: 1px solid #d1d5db;
        background: white;
        color: #374151;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .export-btn:hover {
        background: #f9fafb;
        border-color: #9ca3af;
        color: #111827;
        text-decoration: none;
    }
    
    /* Mobile Responsive */
    @media (max-width: 768px) {
        .kpi-value {
            font-size: 2rem;
        }
        
        .chart-container {
            height: 250px;
        }
        
        .quick-stat-value {
            font-size: 1.4rem;
        }
        
        .export-buttons {
            justify-content: center;
        }
    }
    
    /* RTL Adjustments */
    body[dir="rtl"] .metric-item {
        border-left: none;
        border-right: 4px solid #e5e7eb;
        border-radius: 8px 0 0 8px;
    }
    
    body[dir="rtl"] .metric-item:hover {
        border-right-color: #3b82f6;
    }
    
    body[dir="rtl"] .performance-indicator {
        margin-right: 0;
        margin-left: 8px;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">
                <i class="bi bi-graph-up"></i> {{ __('app.advanced_reports.dashboard') }}
            </h1>
            <p class="text-muted mb-0">{{ __('app.advanced_reports.dashboard_subtitle') }}</p>
        </div>
        
        <!-- Period Selector -->
        <div class="period-selector">
            <button class="period-btn {{ $period === 'daily' ? 'active' : '' }}" data-period="daily">
                {{ __('app.report_types.daily') }}
            </button>
            <button class="period-btn {{ $period === 'weekly' ? 'active' : '' }}" data-period="weekly">
                {{ __('app.report_types.weekly') }}
            </button>
            <button class="period-btn {{ $period === 'monthly' ? 'active' : '' }}" data-period="monthly">
                {{ __('app.report_types.monthly') }}
            </button>
            <button class="period-btn {{ $period === 'yearly' ? 'active' : '' }}" data-period="yearly">
                {{ __('app.report_types.yearly') }}
            </button>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="quick-stats">
        <div class="row">
            <div class="col-md-3 quick-stat-item">
                <div class="quick-stat-value">{{ number_format($quickStats['today_revenue'], 2) }}</div>
                <div class="quick-stat-label">{{ __('app.advanced_reports.today_revenue') }}</div>
            </div>
            <div class="col-md-3 quick-stat-item">
                <div class="quick-stat-value">{{ number_format($quickStats['month_revenue'], 2) }}</div>
                <div class="quick-stat-label">{{ __('app.advanced_reports.month_revenue') }}</div>
            </div>
            <div class="col-md-3 quick-stat-item">
                <div class="quick-stat-value">{{ $quickStats['pending_invoices'] }}</div>
                <div class="quick-stat-label">{{ __('app.advanced_reports.pending_invoices') }}</div>
            </div>
            <div class="col-md-3 quick-stat-item">
                <div class="quick-stat-value">{{ $quickStats['active_repairs'] }}</div>
                <div class="quick-stat-label">{{ __('app.advanced_reports.active_repairs') }}</div>
            </div>
        </div>
    </div>

    <!-- KPI Cards -->
    <div class="row mb-4">
        @if($latestKpis)
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card dashboard-card kpi-card">
                    <div class="card-body">
                        <div class="kpi-value">{{ number_format($latestKpis->revenue_per_customer, 0) }}</div>
                        <div class="kpi-label">{{ __('app.kpis.revenue_per_customer') }}</div>
                        <div class="kpi-change positive">
                            <i class="bi bi-arrow-up"></i> +12.5%
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card dashboard-card kpi-card">
                    <div class="card-body">
                        <div class="kpi-value">{{ number_format($latestKpis->customer_retention_rate, 1) }}%</div>
                        <div class="kpi-label">{{ __('app.kpis.customer_retention_rate') }}</div>
                        <div class="kpi-change positive">
                            <i class="bi bi-arrow-up"></i> +3.2%
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card dashboard-card kpi-card">
                    <div class="card-body">
                        <div class="kpi-value">{{ number_format($latestKpis->first_time_fix_rate, 1) }}%</div>
                        <div class="kpi-label">{{ __('app.kpis.first_time_fix_rate') }}</div>
                        <div class="kpi-change negative">
                            <i class="bi bi-arrow-down"></i> -1.8%
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card dashboard-card kpi-card">
                    <div class="card-body">
                        <div class="kpi-value">{{ number_format($latestKpis->gross_margin_percentage, 1) }}%</div>
                        <div class="kpi-label">{{ __('app.kpis.gross_margin_percentage') }}</div>
                        <div class="kpi-change positive">
                            <i class="bi bi-arrow-up"></i> +2.1%
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <div class="row">
        <!-- Revenue Trends Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up"></i> {{ __('app.advanced_reports.revenue_trends') }}
                    </h5>
                    <div class="export-buttons">
                        <a href="#" class="export-btn" data-export="chart-png">
                            <i class="bi bi-image"></i> PNG
                        </a>
                        <a href="#" class="export-btn" data-export="chart-pdf">
                            <i class="bi bi-file-pdf"></i> PDF
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="col-lg-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-speedometer2"></i> {{ __('app.advanced_reports.performance_indicators') }}
                    </h5>
                </div>
                <div class="card-body">
                    @if($latestKpis)
                        @php
                            $performanceIndicators = $latestKpis->getPerformanceIndicators();
                        @endphp
                        
                        @foreach($performanceIndicators as $key => $indicator)
                            <div class="metric-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="metric-label">
                                            <span class="performance-indicator {{ $indicator['status'] }}"></span>
                                            {{ __('app.kpis.' . $key) }}
                                        </div>
                                        <div class="metric-value">{{ number_format($indicator['value'], 1) }}%</div>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-muted">{{ __('app.advanced_reports.target') }}: {{ $indicator['target'] }}%</small>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Financial Summary -->
        <div class="col-lg-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-currency-dollar"></i> {{ __('app.advanced_reports.financial_health') }}
                    </h5>
                </div>
                <div class="card-body">
                    @if($latestProfitLoss)
                        <div class="row">
                            <div class="col-6">
                                <div class="metric-item">
                                    <div class="metric-label">{{ __('app.financial_statements.total_revenue') }}</div>
                                    <div class="metric-value">{{ number_format($latestProfitLoss->total_revenue, 0) }}</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="metric-item">
                                    <div class="metric-label">{{ __('app.financial_statements.gross_profit') }}</div>
                                    <div class="metric-value">{{ number_format($latestProfitLoss->gross_profit, 0) }}</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="metric-item">
                                    <div class="metric-label">{{ __('app.financial_statements.operating_income') }}</div>
                                    <div class="metric-value">{{ number_format($latestProfitLoss->operating_income, 0) }}</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="metric-item">
                                    <div class="metric-label">{{ __('app.financial_statements.net_income') }}</div>
                                    <div class="metric-value {{ $latestProfitLoss->net_income >= 0 ? 'text-success' : 'text-danger' }}">
                                        {{ number_format($latestProfitLoss->net_income, 0) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- KPI Trends -->
        <div class="col-lg-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-down"></i> {{ __('app.advanced_reports.kpi_trends') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container trend-chart">
                        <canvas id="kpiTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <h6 class="mb-3">{{ __('app.advanced_reports.detailed_reports') }}</h6>
                    <div class="d-flex justify-content-center gap-3 flex-wrap">
                        <a href="{{ route('advanced-reports.profit-loss') }}" class="btn btn-primary">
                            <i class="bi bi-file-text"></i> {{ __('app.advanced_reports.profit_loss') }}
                        </a>
                        <a href="{{ route('advanced-reports.kpi-dashboard') }}" class="btn btn-info">
                            <i class="bi bi-speedometer2"></i> {{ __('app.advanced_reports.kpi_dashboard') }}
                        </a>
                        <a href="{{ route('advanced-reports.sales-performance') }}" class="btn btn-success">
                            <i class="bi bi-graph-up"></i> {{ __('app.advanced_reports.sales_performance') }}
                        </a>
                        <a href="{{ route('advanced-reports.inventory-analysis') }}" class="btn btn-warning">
                            <i class="bi bi-boxes"></i> {{ __('app.advanced_reports.inventory_analysis') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: @json($kpiTrends['labels'] ?? []),
            datasets: [{
                label: '{{ __("app.advanced_reports.revenue") }}',
                data: @json($kpiTrends['revenue_per_customer'] ?? []),
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // KPI Trends Chart
    const kpiCtx = document.getElementById('kpiTrendsChart').getContext('2d');
    const kpiChart = new Chart(kpiCtx, {
        type: 'radar',
        data: {
            labels: [
                '{{ __("app.kpis.customer_retention_rate") }}',
                '{{ __("app.kpis.first_time_fix_rate") }}',
                '{{ __("app.kpis.gross_margin_percentage") }}',
                '{{ __("app.kpis.inventory_turnover_ratio") }}'
            ],
            datasets: [{
                label: '{{ __("app.advanced_reports.current_performance") }}',
                data: @json([
                    $latestKpis->customer_retention_rate ?? 0,
                    $latestKpis->first_time_fix_rate ?? 0,
                    $latestKpis->gross_margin_percentage ?? 0,
                    ($latestKpis->inventory_turnover_ratio ?? 0) * 10
                ]),
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.2)',
                pointBackgroundColor: '#10b981'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // Period selector
    document.querySelectorAll('.period-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const period = this.dataset.period;
            window.location.href = `{{ route('advanced-reports.dashboard') }}?period=${period}`;
        });
    });

    // Export functionality
    document.querySelectorAll('.export-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const exportType = this.dataset.export;
            // Implement export functionality
            console.log('Export:', exportType);
        });
    });
});
</script>
@endpush
