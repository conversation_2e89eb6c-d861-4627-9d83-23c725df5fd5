@extends('layouts.app')

@section('title', 'Print Preview - ' . $repairTicket->ticket_number)

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Header with Print Actions -->
            <div class="d-flex justify-content-between align-items-center mb-4 no-print">
                <h1 class="h3 mb-0">
                    <i class="bi bi-eye"></i> Print Preview - {{ $repairTicket->ticket_number }}
                </h1>
                <div class="btn-group" role="group">
                    <a href="{{ route('repair-tickets.show', $repairTicket) }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Ticket
                    </a>
                    <button onclick="window.print()" class="btn btn-primary">
                        <i class="bi bi-printer"></i> Print
                    </button>
                    <a href="{{ route('print-export.ticket.print', $repairTicket) }}" class="btn btn-success" target="_blank">
                        <i class="bi bi-file-earmark-pdf"></i> Download PDF
                    </a>
                </div>
            </div>

            <!-- Print Content -->
            <div class="print-content">
                <div class="card">
                    <div class="card-body p-5">
                        <!-- Company Header -->
                        <div class="text-center mb-5 border-bottom pb-4">
                            <h1 class="display-6 text-primary mb-2">NJ Repair Shop</h1>
                            <p class="text-muted mb-0">
                                Mobile Phone & Computer Repair Services<br>
                                Phone: +**************** | Email: <EMAIL>
                            </p>
                        </div>

                        <!-- Ticket Header -->
                        <div class="bg-light p-4 rounded mb-4">
                            <div class="text-center">
                                <h2 class="text-primary mb-3">Ticket #{{ $repairTicket->ticket_number }}</h2>
                                <div class="row text-center">
                                    <div class="col-md-4">
                                        <strong>Received:</strong><br>
                                        {{ $repairTicket->received_date->format('M d, Y H:i') }}
                                    </div>
                                    @if($repairTicket->estimated_completion_date)
                                    <div class="col-md-4">
                                        <strong>Est. Completion:</strong><br>
                                        {{ $repairTicket->estimated_completion_date->format('M d, Y') }}
                                    </div>
                                    @endif
                                    @if($repairTicket->completed_date)
                                    <div class="col-md-4">
                                        <strong>Completed:</strong><br>
                                        {{ $repairTicket->completed_date->format('M d, Y H:i') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Customer Information -->
                        <div class="mb-4">
                            <h4 class="text-primary border-bottom pb-2 mb-3">Customer Information</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Name:</strong> {{ $repairTicket->customer->name }}</p>
                                    <p><strong>Phone:</strong> {{ $repairTicket->customer->phone_number }}</p>
                                </div>
                                <div class="col-md-6">
                                    @if($repairTicket->customer->email)
                                    <p><strong>Email:</strong> {{ $repairTicket->customer->email }}</p>
                                    @endif
                                    @if($repairTicket->customer->address)
                                    <p><strong>Address:</strong> {{ $repairTicket->customer->address }}</p>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Device Information -->
                        <div class="mb-4">
                            <h4 class="text-primary border-bottom pb-2 mb-3">Device Information</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Brand:</strong> {{ $repairTicket->brand->name }}</p>
                                    <p><strong>Model:</strong> {{ $repairTicket->device_model }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Condition:</strong> {{ $repairTicket->deviceCondition->name ?? 'N/A' }}</p>
                                    <p><strong>Has Pattern:</strong> {{ $repairTicket->device_pattern ? 'Yes' : 'No' }}</p>
                                    @if($repairTicket->hasSecurityPattern())
                                        <p><strong>Security Pattern:</strong> {{ $repairTicket->getMaskedSecurityPattern() }}</p>
                                    @endif
                                </div>
                            </div>
                            <div class="mt-3">
                                <p><strong>Reported Problem:</strong></p>
                                <p class="bg-light p-3 rounded">{{ $repairTicket->reported_problem }}</p>
                            </div>
                        </div>

                        <!-- Repair Information -->
                        <div class="mb-4">
                            <h4 class="text-primary border-bottom pb-2 mb-3">Repair Information</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Status:</strong>
                                        <span class="badge bg-{{ $repairTicket->repairStatus->name === 'Completed' ? 'success' : ($repairTicket->repairStatus->name === 'In Progress' ? 'primary' : 'warning') }}">
                                            {{ $repairTicket->repairStatus->name }}
                                        </span>
                                    </p>
                                    <p><strong>Priority:</strong>
                                        <span class="text-{{ $repairTicket->priority === 'high' ? 'danger' : ($repairTicket->priority === 'medium' ? 'warning' : 'success') }}">
                                            {{ ucfirst($repairTicket->priority) }}
                                        </span>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Technician:</strong> {{ $repairTicket->assignedTo->name ?? 'Unassigned' }}</p>
                                    <p><strong>Days Since Received:</strong> {{ $repairTicket->daysSinceReceived() }} days</p>
                                </div>
                            </div>
                            @if($repairTicket->technician_comments)
                            <div class="mt-3">
                                <p><strong>Technician Comments:</strong></p>
                                <p class="bg-light p-3 rounded">{{ $repairTicket->technician_comments }}</p>
                            </div>
                            @endif
                        </div>

                        <!-- Cost Information -->
                        @if($repairTicket->initial_cost || $repairTicket->final_cost)
                        <div class="mb-4">
                            <h4 class="text-primary border-bottom pb-2 mb-3">Cost Information</h4>
                            <div class="bg-light p-4 rounded">
                                @if($repairTicket->initial_cost)
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Initial Estimate:</span>
                                    <span>${{ number_format($repairTicket->initial_cost, 2) }}</span>
                                </div>
                                @endif
                                @if($repairTicket->final_cost)
                                <div class="d-flex justify-content-between border-top pt-2">
                                    <strong>Final Cost:</strong>
                                    <strong class="text-success">${{ number_format($repairTicket->final_cost, 2) }}</strong>
                                </div>
                                @endif
                            </div>
                        </div>
                        @endif

                        <!-- Security Pattern Information -->
                        @if($repairTicket->hasAnySecurityPattern())
                        <div class="mb-4">
                            <h4 class="text-primary border-bottom pb-2 mb-3">Security Pattern Information</h4>
                            <div class="bg-light p-4 rounded">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <strong>Pattern Type:</strong>
                                        @switch($repairTicket->pattern_type)
                                            @case('text')
                                                <span class="badge bg-primary">Text Password</span>
                                                @break
                                            @case('visual')
                                                <span class="badge bg-success">Visual Pattern</span>
                                                @break
                                            @case('both')
                                                <span class="badge bg-warning">Text + Visual</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">Unknown</span>
                                        @endswitch
                                    </div>
                                </div>

                                @if($repairTicket->hasSecurityPattern())
                                <div class="mb-3">
                                    <strong>Text Pattern:</strong>
                                    <span class="ms-2">{{ $repairTicket->getMaskedSecurityPattern() }}</span>
                                </div>
                                @endif

                                @if($repairTicket->hasVisualPattern())
                                <div class="mb-3">
                                    <strong>Visual Pattern:</strong>
                                    <span class="ms-2">{{ $repairTicket->getMaskedVisualPattern() }} ({{ count($repairTicket->getVisualPatternArray()) }} dots)</span>
                                </div>

                                <div class="text-center mt-3">
                                    <div class="d-inline-block border p-3 rounded bg-white">
                                        <div class="fw-bold mb-2">Visual Pattern Grid</div>
                                        <div style="display: grid; grid-template-columns: repeat(3, 30px); grid-gap: 8px; justify-content: center;">
                                            @php
                                                $patternGrid = \App\Services\PatternValidationService::generatePatternGrid($repairTicket->visual_pattern);
                                            @endphp
                                            @for($row = 0; $row < 3; $row++)
                                                @for($col = 0; $col < 3; $col++)
                                                    <div style="width: 30px; height: 30px; border-radius: 50%; border: 2px solid #333;
                                                                background-color: {{ $patternGrid[$row][$col]['active'] ? '#198754' : '#fff' }};
                                                                display: flex; align-items: center; justify-content: center;
                                                                font-size: 12px; color: white; font-weight: bold;">
                                                        @if($patternGrid[$row][$col]['active'])
                                                            {{ $patternGrid[$row][$col]['order'] }}
                                                        @endif
                                                    </div>
                                                @endfor
                                            @endfor
                                        </div>
                                        <div class="text-muted small mt-2">
                                            Pattern: {{ $repairTicket->visual_pattern }}
                                        </div>
                                    </div>
                                </div>
                                @endif

                                <div class="alert alert-warning mt-3 mb-0">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    <strong>Security Notice:</strong> This information is confidential and should be handled with care.
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Signature Section -->
                        <div class="row mt-5">
                            <div class="col-md-6">
                                <div class="text-center">
                                    <div class="border-top pt-2 mt-5">
                                        <strong>Customer Signature</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="text-center">
                                    <div class="border-top pt-2 mt-5">
                                        <strong>Technician Signature</strong>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Footer -->
                        <div class="text-center mt-5 pt-4 border-top">
                            <p class="text-muted mb-1">Thank you for choosing NJ Repair Shop!</p>
                            <p class="text-muted small">
                                This document was generated on {{ now()->format('M d, Y H:i') }}<br>
                                For questions or concerns, please contact <NAME_EMAIL>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    @media print {
        .no-print {
            display: none !important;
        }

        .container-fluid {
            padding: 0 !important;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        .card-body {
            padding: 0 !important;
        }

        body {
            -webkit-print-color-adjust: exact;
        }
    }
</style>
@endpush
