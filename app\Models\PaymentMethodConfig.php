<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentMethodConfig extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'method_code',
        'method_name_ar',
        'method_name_en',
        'icon_class',
        'is_active',
        'requires_reference',
        'requires_card_info',
        'requires_bank_info',
        'processing_fee_percentage',
        'processing_fee_fixed',
        'settlement_days',
        'additional_fields',
        'validation_rules',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_active' => 'boolean',
        'requires_reference' => 'boolean',
        'requires_card_info' => 'boolean',
        'requires_bank_info' => 'boolean',
        'processing_fee_percentage' => 'decimal:2',
        'processing_fee_fixed' => 'decimal:2',
        'additional_fields' => 'array',
        'validation_rules' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get active payment methods ordered by sort_order.
     */
    public static function getActivePaymentMethods()
    {
        return self::where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('method_name_ar')
            ->get();
    }

    /**
     * Get payment method name based on current locale.
     */
    public function getDisplayNameAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->method_name_ar : $this->method_name_en;
    }

    /**
     * Calculate processing fee for an amount.
     */
    public function calculateProcessingFee(float $amount): float
    {
        $percentageFee = ($amount * $this->processing_fee_percentage) / 100;
        return $percentageFee + $this->processing_fee_fixed;
    }

    /**
     * Get validation rules for this payment method.
     */
    public function getValidationRules(): array
    {
        $rules = [];

        if ($this->requires_reference) {
            $rules['payment_reference'] = 'required|string|max:255';
        }

        if ($this->requires_card_info) {
            $rules['card_last_four'] = 'required|string|size:4';
            $rules['card_type'] = 'nullable|string|max:50';
        }

        if ($this->requires_bank_info) {
            $rules['bank_name'] = 'required|string|max:255';
            $rules['account_number'] = 'nullable|string|max:50';
        }

        // Add custom validation rules from configuration
        if ($this->validation_rules) {
            $rules = array_merge($rules, $this->validation_rules);
        }

        return $rules;
    }

    /**
     * Get additional form fields for this payment method.
     */
    public function getAdditionalFields(): array
    {
        $fields = [];

        if ($this->requires_reference) {
            $fields[] = [
                'name' => 'payment_reference',
                'type' => 'text',
                'label_ar' => 'رقم المرجع',
                'label_en' => 'Reference Number',
                'required' => true,
                'placeholder_ar' => 'أدخل رقم المرجع',
                'placeholder_en' => 'Enter reference number'
            ];
        }

        if ($this->requires_card_info) {
            $fields[] = [
                'name' => 'card_last_four',
                'type' => 'text',
                'label_ar' => 'آخر 4 أرقام من البطاقة',
                'label_en' => 'Last 4 digits of card',
                'required' => true,
                'maxlength' => 4,
                'pattern' => '[0-9]{4}',
                'placeholder_ar' => '1234',
                'placeholder_en' => '1234'
            ];

            $fields[] = [
                'name' => 'card_type',
                'type' => 'select',
                'label_ar' => 'نوع البطاقة',
                'label_en' => 'Card Type',
                'required' => false,
                'options' => [
                    'visa' => 'Visa',
                    'mastercard' => 'Mastercard',
                    'amex' => 'American Express',
                    'other' => 'أخرى / Other'
                ]
            ];
        }

        if ($this->requires_bank_info) {
            $fields[] = [
                'name' => 'bank_name',
                'type' => 'text',
                'label_ar' => 'اسم البنك',
                'label_en' => 'Bank Name',
                'required' => true,
                'placeholder_ar' => 'أدخل اسم البنك',
                'placeholder_en' => 'Enter bank name'
            ];

            $fields[] = [
                'name' => 'account_number',
                'type' => 'text',
                'label_ar' => 'رقم الحساب (اختياري)',
                'label_en' => 'Account Number (Optional)',
                'required' => false,
                'placeholder_ar' => 'رقم الحساب',
                'placeholder_en' => 'Account number'
            ];
        }

        // Add custom additional fields from configuration
        if ($this->additional_fields) {
            $fields = array_merge($fields, $this->additional_fields);
        }

        return $fields;
    }

    /**
     * Seed default payment methods.
     */
    public static function seedDefaultMethods(): void
    {
        $defaultMethods = [
            [
                'method_code' => 'cash',
                'method_name_ar' => 'نقداً',
                'method_name_en' => 'Cash',
                'icon_class' => 'bi bi-cash-coin',
                'is_active' => true,
                'requires_reference' => false,
                'requires_card_info' => false,
                'requires_bank_info' => false,
                'processing_fee_percentage' => 0,
                'processing_fee_fixed' => 0,
                'settlement_days' => 0,
                'sort_order' => 1,
            ],
            [
                'method_code' => 'card',
                'method_name_ar' => 'بطاقة ائتمان/خصم',
                'method_name_en' => 'Credit/Debit Card',
                'icon_class' => 'bi bi-credit-card',
                'is_active' => true,
                'requires_reference' => true,
                'requires_card_info' => true,
                'requires_bank_info' => false,
                'processing_fee_percentage' => 2.5,
                'processing_fee_fixed' => 0,
                'settlement_days' => 2,
                'sort_order' => 2,
            ],
            [
                'method_code' => 'bank_transfer',
                'method_name_ar' => 'تحويل بنكي',
                'method_name_en' => 'Bank Transfer',
                'icon_class' => 'bi bi-bank',
                'is_active' => true,
                'requires_reference' => true,
                'requires_card_info' => false,
                'requires_bank_info' => true,
                'processing_fee_percentage' => 0,
                'processing_fee_fixed' => 5,
                'settlement_days' => 1,
                'sort_order' => 3,
            ],
            [
                'method_code' => 'mobile_payment',
                'method_name_ar' => 'دفع عبر الجوال',
                'method_name_en' => 'Mobile Payment',
                'icon_class' => 'bi bi-phone',
                'is_active' => true,
                'requires_reference' => true,
                'requires_card_info' => false,
                'requires_bank_info' => false,
                'processing_fee_percentage' => 1.5,
                'processing_fee_fixed' => 0,
                'settlement_days' => 0,
                'sort_order' => 4,
            ],
            [
                'method_code' => 'check',
                'method_name_ar' => 'شيك',
                'method_name_en' => 'Check',
                'icon_class' => 'bi bi-file-text',
                'is_active' => true,
                'requires_reference' => true,
                'requires_card_info' => false,
                'requires_bank_info' => true,
                'processing_fee_percentage' => 0,
                'processing_fee_fixed' => 0,
                'settlement_days' => 7,
                'sort_order' => 5,
            ],
            [
                'method_code' => 'installment',
                'method_name_ar' => 'تقسيط',
                'method_name_en' => 'Installment',
                'icon_class' => 'bi bi-calendar-check',
                'is_active' => true,
                'requires_reference' => false,
                'requires_card_info' => false,
                'requires_bank_info' => false,
                'processing_fee_percentage' => 0,
                'processing_fee_fixed' => 0,
                'settlement_days' => 30,
                'sort_order' => 6,
            ],
        ];

        foreach ($defaultMethods as $method) {
            self::updateOrCreate(
                ['method_code' => $method['method_code']],
                $method
            );
        }
    }
}
