<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_items', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Item name
            $table->string('sku')->unique(); // Stock Keeping Unit (unique identifier)
            $table->string('barcode')->nullable()->unique(); // Barcode for scanning
            $table->text('description')->nullable(); // Item description
            $table->foreignId('category_id')->constrained('inventory_categories')->onDelete('restrict');
            $table->foreignId('brand_id')->nullable()->constrained('brands')->onDelete('set null'); // Compatible brand
            $table->string('model_compatibility')->nullable(); // Compatible device models
            $table->string('part_number')->nullable(); // Manufacturer part number
            $table->string('oem_number')->nullable(); // Original Equipment Manufacturer number

            // Stock Information
            $table->integer('current_stock')->default(0); // Current quantity in stock
            $table->integer('minimum_stock')->default(5); // Minimum stock level (reorder point)
            $table->integer('maximum_stock')->default(100); // Maximum stock level
            $table->integer('reorder_quantity')->default(20); // Quantity to reorder when minimum reached
            $table->string('unit_of_measure')->default('piece'); // Unit (piece, meter, kg, etc.)

            // Pricing Information
            $table->decimal('cost_price', 10, 2)->default(0); // Purchase cost per unit
            $table->decimal('selling_price', 10, 2)->default(0); // Selling price per unit
            $table->decimal('markup_percentage', 5, 2)->default(0); // Markup percentage
            $table->decimal('wholesale_price', 10, 2)->nullable(); // Wholesale price

            // Location and Storage
            $table->string('location')->nullable(); // Storage location (shelf, bin, etc.)
            $table->string('warehouse_section')->nullable(); // Warehouse section
            $table->text('storage_conditions')->nullable(); // Special storage requirements

            // Supplier Information
            $table->foreignId('primary_supplier_id')->nullable()->constrained('suppliers')->onDelete('set null');
            $table->json('alternative_suppliers')->nullable(); // Array of alternative supplier IDs
            $table->integer('lead_time_days')->default(7); // Lead time for reordering

            // Quality and Warranty
            $table->enum('condition', ['new', 'refurbished', 'used'])->default('new');
            $table->integer('warranty_months')->default(0); // Warranty period in months
            $table->date('expiry_date')->nullable(); // For items with expiry dates

            // Status and Flags
            $table->boolean('is_active')->default(true); // Active status
            $table->boolean('is_serialized')->default(false); // Track individual serial numbers
            $table->boolean('allow_backorder')->default(false); // Allow selling when out of stock
            $table->boolean('track_expiry')->default(false); // Track expiry dates

            // Metadata
            $table->json('specifications')->nullable(); // Technical specifications
            $table->json('images')->nullable(); // Array of image paths
            $table->text('notes')->nullable(); // Additional notes
            $table->timestamp('last_counted_at')->nullable(); // Last physical count date
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();

            // Indexes for better performance
            $table->index('name');
            $table->index('sku');
            $table->index('barcode');
            $table->index(['category_id', 'is_active']);
            $table->index(['brand_id', 'is_active']);
            $table->index(['current_stock', 'minimum_stock']); // For low stock alerts
            $table->index('is_active');
            $table->index('primary_supplier_id');
            $table->index('condition');
            $table->index('expiry_date');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_items');
    }
};
