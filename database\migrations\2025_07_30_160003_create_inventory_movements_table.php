<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_movements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('inventory_item_id')->constrained()->onDelete('cascade');
            $table->enum('type', [
                'purchase',      // Purchase from supplier
                'sale',          // Sale to customer
                'adjustment',    // Manual adjustment
                'return',        // Return from customer
                'damage',        // Damaged items
                'transfer',      // Transfer between locations
                'count',         // Physical count adjustment
                'usage'          // Used in repair
            ]);
            $table->integer('quantity'); // Positive for in, negative for out
            $table->integer('stock_before'); // Stock level before movement
            $table->integer('stock_after'); // Stock level after movement
            $table->decimal('unit_cost', 10, 2)->nullable(); // Cost per unit for this movement
            $table->decimal('total_cost', 10, 2)->nullable(); // Total cost for this movement
            
            // Reference Information
            $table->string('reference_type')->nullable(); // Type of reference (repair_ticket, purchase_order, etc.)
            $table->unsignedBigInteger('reference_id')->nullable(); // ID of the reference
            $table->string('reference_number')->nullable(); // Human-readable reference number
            
            // Location Information
            $table->string('from_location')->nullable(); // Source location
            $table->string('to_location')->nullable(); // Destination location
            
            // Additional Information
            $table->text('notes')->nullable(); // Movement notes
            $table->string('batch_number')->nullable(); // Batch/lot number
            $table->date('expiry_date')->nullable(); // Expiry date for this batch
            $table->json('serial_numbers')->nullable(); // Array of serial numbers if applicable
            
            // Tracking
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamp('movement_date')->default(now()); // When the movement occurred
            $table->timestamps();

            // Indexes for better performance
            $table->index('inventory_item_id');
            $table->index('type');
            $table->index(['reference_type', 'reference_id']);
            $table->index('movement_date');
            $table->index('created_by');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_movements');
    }
};
