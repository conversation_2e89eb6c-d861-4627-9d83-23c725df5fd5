<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>بوابة العملاء - ورشة إصلاح NJ</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .customer-portal {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .portal-header {
            background: linear-gradient(45deg, #0d6efd, #6610f2);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        
        .stats-card {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            text-align: center;
        }
        
        .stats-card.pending {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
        }
        
        .stats-card.completed {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        
        .ticket-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            transition: transform 0.2s;
        }
        
        .ticket-card:hover {
            transform: translateY(-5px);
        }
        
        .status-badge {
            font-size: 0.9rem;
            padding: 8px 15px;
            border-radius: 20px;
        }
        
        .btn-portal {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
        }
        
        .welcome-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="customer-portal">
            <!-- Header -->
            <div class="portal-header">
                <h2><i class="bi bi-person-circle"></i> مرحباً {{ $customer->name }}</h2>
                <p class="mb-0">بوابة متابعة أجهزتك في ورشة إصلاح NJ</p>
                <div class="mt-3">
                    <a href="{{ route('customer.logout') }}" class="btn btn-light btn-sm">
                        <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                    </a>
                </div>
            </div>

            <!-- Statistics -->
            <div class="container py-4">
                <div class="row">
                    <div class="col-md-4">
                        <div class="stats-card">
                            <h3>{{ $stats['total_tickets'] }}</h3>
                            <p class="mb-0"><i class="bi bi-ticket"></i> إجمالي الأجهزة</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card pending">
                            <h3>{{ $stats['pending_tickets'] }}</h3>
                            <p class="mb-0"><i class="bi bi-clock"></i> قيد الإصلاح</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card completed">
                            <h3>{{ $stats['completed_tickets'] }}</h3>
                            <p class="mb-0"><i class="bi bi-check-circle"></i> مكتملة</p>
                        </div>
                    </div>
                </div>

                <!-- Welcome Message -->
                @if($stats['total_tickets'] === 0)
                <div class="welcome-section text-center">
                    <i class="bi bi-tools display-1 text-primary"></i>
                    <h4>مرحباً بك في ورشة إصلاح NJ</h4>
                    <p class="text-muted">لا توجد أجهزة مسجلة باسمك حالياً. عندما تحضر جهازك للإصلاح، ستظهر تفاصيله هنا.</p>
                </div>
                @endif

                <!-- Tickets List -->
                @if($tickets->count() > 0)
                <div class="row">
                    <div class="col-12">
                        <h4 class="mb-4"><i class="bi bi-list"></i> أجهزتك</h4>
                        
                        @foreach($tickets as $ticket)
                        <div class="ticket-card card">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <h6 class="text-primary mb-1">{{ $ticket->ticket_number }}</h6>
                                        <small class="text-muted">{{ $ticket->created_at->format('Y-m-d') }}</small>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>{{ $ticket->brand->name }}</strong><br>
                                        <small>{{ $ticket->device_model }}</small>
                                    </div>
                                    <div class="col-md-3">
                                        <span class="status-badge badge" style="background-color: {{ $ticket->repairStatus->color ?? '#6c757d' }}">
                                            {{ $ticket->repairStatus->name }}
                                        </span>
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <a href="{{ route('customer.ticket', $ticket->ticket_number) }}" 
                                           class="btn btn-primary btn-portal btn-sm">
                                            <i class="bi bi-eye"></i> التفاصيل
                                        </a>
                                    </div>
                                </div>
                                
                                @if($ticket->repairStatus->is_final)
                                <div class="mt-3 p-3 bg-success bg-opacity-10 rounded">
                                    <i class="bi bi-check-circle text-success"></i>
                                    <strong class="text-success">جهازك جاهز للاستلام!</strong>
                                    <p class="mb-0 mt-2">يرجى زيارة الورشة لاستلام جهازك في أقرب وقت ممكن.</p>
                                </div>
                                @endif
                            </div>
                        </div>
                        @endforeach

                        <!-- Pagination -->
                        {{ $tickets->links() }}
                    </div>
                </div>
                @endif

                <!-- Contact Information Update -->
                <div class="row mt-5">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="bi bi-person-gear"></i> تحديث معلومات الاتصال</h6>
                            </div>
                            <div class="card-body">
                                <form action="{{ route('customer.update-contact') }}" method="POST">
                                    @csrf
                                    <div class="mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="text" class="form-control" value="{{ $customer->phone_number }}" readonly>
                                        <small class="text-muted">لا يمكن تغيير رقم الهاتف</small>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" name="email" class="form-control" 
                                               value="{{ $customer->email }}" placeholder="<EMAIL>">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">العنوان</label>
                                        <textarea name="address" class="form-control" rows="3" 
                                                  placeholder="عنوانك الكامل">{{ $customer->address }}</textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-portal">
                                        <i class="bi bi-save"></i> حفظ التغييرات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="bi bi-telephone"></i> معلومات الاتصال</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong><i class="bi bi-geo-alt"></i> العنوان:</strong><br>
                                    شارع الملك فهد، الرياض، المملكة العربية السعودية
                                </div>
                                <div class="mb-3">
                                    <strong><i class="bi bi-telephone"></i> الهاتف:</strong><br>
                                    <a href="tel:+966501234567" class="text-decoration-none">+966 50 123 4567</a>
                                </div>
                                <div class="mb-3">
                                    <strong><i class="bi bi-clock"></i> ساعات العمل:</strong><br>
                                    السبت - الخميس: 9:00 ص - 10:00 م<br>
                                    الجمعة: 2:00 م - 10:00 م
                                </div>
                                <div class="mb-3">
                                    <strong><i class="bi bi-envelope"></i> البريد الإلكتروني:</strong><br>
                                    <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 1050">
        <div class="toast show" role="alert">
            <div class="toast-header bg-success text-white">
                <i class="bi bi-check-circle me-2"></i>
                <strong class="me-auto">نجح</strong>
            </div>
            <div class="toast-body">
                {{ session('success') }}
            </div>
        </div>
    </div>
    @endif

    @if($errors->any())
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 1050">
        <div class="toast show" role="alert">
            <div class="toast-header bg-danger text-white">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong class="me-auto">خطأ</strong>
            </div>
            <div class="toast-body">
                @foreach($errors->all() as $error)
                    <div>{{ $error }}</div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-hide toasts after 5 seconds
        setTimeout(function() {
            const toasts = document.querySelectorAll('.toast');
            toasts.forEach(toast => {
                toast.classList.remove('show');
            });
        }, 5000);
    </script>
</body>
</html>
