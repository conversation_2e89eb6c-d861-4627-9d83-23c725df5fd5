<?php

namespace App\Http\Controllers;

use App\Models\PosQuickSaleTemplate;
use App\Models\InventoryItem;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;

class PosQuickSaleTemplateController extends Controller
{
    /**
     * Display a listing of quick sale templates.
     */
    public function index(Request $request): View
    {
        $query = PosQuickSaleTemplate::with('createdBy');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('template_name', 'like', "%{$search}%")
                  ->orWhere('template_code', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $templates = $query->orderBy('sort_order')
            ->orderBy('template_name')
            ->paginate(15)
            ->withQueryString();

        // Get categories for filter
        $categories = PosQuickSaleTemplate::select('category')
            ->distinct()
            ->orderBy('category')
            ->pluck('category');

        return view('pos-templates.index', compact('templates', 'categories'));
    }

    /**
     * Show the form for creating a new template.
     */
    public function create(): View
    {
        $inventoryItems = InventoryItem::where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('pos-templates.create', compact('inventoryItems'));
    }

    /**
     * Store a newly created template.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'template_name' => 'required|string|max:255',
            'category' => 'required|string|max:100',
            'icon_class' => 'nullable|string|max:100',
            'color_class' => 'nullable|string|max:50',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'items' => 'required|array|min:1',
            'items.*.inventory_item_id' => 'nullable|exists:inventory_items,id',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.sku' => 'nullable|string|max:100',
            'items.*.barcode' => 'nullable|string|max:100',
        ]);

        $validated['is_active'] = $validated['is_active'] ?? true;
        $validated['sort_order'] = $validated['sort_order'] ?? 0;
        $validated['icon_class'] = $validated['icon_class'] ?? 'bi bi-bag';
        $validated['color_class'] = $validated['color_class'] ?? 'primary';

        $template = PosQuickSaleTemplate::createFromItems(
            $validated['template_name'],
            $validated['items'],
            [
                'category' => $validated['category'],
                'icon_class' => $validated['icon_class'],
                'color_class' => $validated['color_class'],
                'is_active' => $validated['is_active'],
                'sort_order' => $validated['sort_order'],
            ]
        );

        return redirect()->route('pos-templates.show', $template)
            ->with('success', 'تم إنشاء قالب البيع السريع بنجاح');
    }

    /**
     * Display the specified template.
     */
    public function show(PosQuickSaleTemplate $posTemplate): View
    {
        $posTemplate->load('createdBy');
        
        // Get items with inventory status
        $itemsWithStatus = $posTemplate->getItemsWithInventoryStatus();

        return view('pos-templates.show', compact('posTemplate', 'itemsWithStatus'));
    }

    /**
     * Show the form for editing the specified template.
     */
    public function edit(PosQuickSaleTemplate $posTemplate): View
    {
        $inventoryItems = InventoryItem::where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('pos-templates.edit', compact('posTemplate', 'inventoryItems'));
    }

    /**
     * Update the specified template.
     */
    public function update(Request $request, PosQuickSaleTemplate $posTemplate): RedirectResponse
    {
        $validated = $request->validate([
            'template_name' => 'required|string|max:255',
            'category' => 'required|string|max:100',
            'icon_class' => 'nullable|string|max:100',
            'color_class' => 'nullable|string|max:50',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'items' => 'required|array|min:1',
            'items.*.inventory_item_id' => 'nullable|exists:inventory_items,id',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.sku' => 'nullable|string|max:100',
            'items.*.barcode' => 'nullable|string|max:100',
        ]);

        $validated['is_active'] = $validated['is_active'] ?? false;
        $validated['sort_order'] = $validated['sort_order'] ?? 0;
        $validated['icon_class'] = $validated['icon_class'] ?? 'bi bi-bag';
        $validated['color_class'] = $validated['color_class'] ?? 'primary';

        $posTemplate->update([
            'template_name' => $validated['template_name'],
            'category' => $validated['category'],
            'icon_class' => $validated['icon_class'],
            'color_class' => $validated['color_class'],
            'is_active' => $validated['is_active'],
            'sort_order' => $validated['sort_order'],
        ]);

        $posTemplate->updateItems($validated['items']);

        return redirect()->route('pos-templates.show', $posTemplate)
            ->with('success', 'تم تحديث قالب البيع السريع بنجاح');
    }

    /**
     * Remove the specified template.
     */
    public function destroy(PosQuickSaleTemplate $posTemplate): RedirectResponse
    {
        $posTemplate->delete();

        return redirect()->route('pos-templates.index')
            ->with('success', 'تم حذف قالب البيع السريع بنجاح');
    }

    /**
     * Toggle template status.
     */
    public function toggleStatus(PosQuickSaleTemplate $posTemplate): RedirectResponse
    {
        $posTemplate->update(['is_active' => !$posTemplate->is_active]);
        
        $status = $posTemplate->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';
        
        return back()->with('success', $status . ' قالب البيع السريع بنجاح');
    }

    /**
     * Seed default templates.
     */
    public function seedDefaults(): RedirectResponse
    {
        try {
            PosQuickSaleTemplate::seedDefaultTemplates();
            return back()->with('success', 'تم إضافة قوالب البيع السريع الافتراضية بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء إضافة القوالب الافتراضية: ' . $e->getMessage());
        }
    }

    /**
     * Get templates for POS interface.
     */
    public function getTemplatesForPos(Request $request): JsonResponse
    {
        $category = $request->get('category');
        
        $query = PosQuickSaleTemplate::active()
            ->orderBy('sort_order')
            ->orderBy('template_name');

        if ($category) {
            $query->where('category', $category);
        }

        $templates = $query->get()->map(function ($template) {
            return [
                'id' => $template->id,
                'name' => $template->template_name,
                'code' => $template->template_code,
                'category' => $template->category,
                'category_display' => $template->category_display,
                'total_price' => $template->total_price,
                'icon_class' => $template->icon_class,
                'color_class' => $template->color_class,
                'can_be_used' => $template->canBeUsed(),
                'availability_status' => $template->availability_status,
                'items_count' => count($template->items),
                'items' => $template->getItemsWithInventoryStatus(),
            ];
        });

        return response()->json([
            'success' => true,
            'templates' => $templates
        ]);
    }

    /**
     * Add template to transaction.
     */
    public function addToTransaction(Request $request, PosQuickSaleTemplate $posTemplate): JsonResponse
    {
        $validated = $request->validate([
            'transaction_id' => 'required|exists:pos_transactions,id'
        ]);

        try {
            $transaction = \App\Models\PosTransaction::find($validated['transaction_id']);
            
            if (!$posTemplate->canBeUsed()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بعض العناصر في القالب غير متوفرة في المخزون'
                ], 400);
            }

            $addedItems = $posTemplate->addToTransaction($transaction);

            return response()->json([
                'success' => true,
                'message' => 'تم إضافة عناصر القالب إلى المعاملة بنجاح',
                'added_items' => $addedItems,
                'transaction' => $transaction->fresh(['items', 'payments'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Duplicate template.
     */
    public function duplicate(PosQuickSaleTemplate $posTemplate): RedirectResponse
    {
        try {
            $newTemplate = PosQuickSaleTemplate::createFromItems(
                $posTemplate->template_name . ' (Copy)',
                $posTemplate->items,
                [
                    'category' => $posTemplate->category,
                    'icon_class' => $posTemplate->icon_class,
                    'color_class' => $posTemplate->color_class,
                    'is_active' => false, // Start as inactive
                    'sort_order' => $posTemplate->sort_order + 1,
                ]
            );

            return redirect()->route('pos-templates.edit', $newTemplate)
                ->with('success', 'تم نسخ قالب البيع السريع بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء نسخ القالب: ' . $e->getMessage());
        }
    }

    /**
     * Update sort order.
     */
    public function updateSortOrder(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'templates' => 'required|array',
            'templates.*.id' => 'required|exists:pos_quick_sale_templates,id',
            'templates.*.sort_order' => 'required|integer|min:0'
        ]);

        try {
            foreach ($validated['templates'] as $templateData) {
                PosQuickSaleTemplate::where('id', $templateData['id'])
                    ->update(['sort_order' => $templateData['sort_order']]);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث ترتيب القوالب بنجاح'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get template statistics.
     */
    public function getStatistics(PosQuickSaleTemplate $posTemplate): JsonResponse
    {
        // Get usage statistics from the last 30 days
        $usageStats = \App\Models\PosTransactionItem::join('pos_transactions', 'pos_transaction_items.transaction_id', '=', 'pos_transactions.id')
            ->where('pos_transactions.status', 'completed')
            ->whereDate('pos_transactions.transaction_date', '>=', now()->subDays(30))
            ->whereIn('pos_transaction_items.item_name', collect($posTemplate->items)->pluck('item_name'))
            ->selectRaw('COUNT(*) as usage_count, SUM(pos_transaction_items.total_price) as total_revenue')
            ->first();

        return response()->json([
            'success' => true,
            'statistics' => [
                'template_info' => [
                    'name' => $posTemplate->template_name,
                    'code' => $posTemplate->template_code,
                    'category' => $posTemplate->category_display,
                    'total_price' => $posTemplate->total_price,
                    'items_count' => count($posTemplate->items),
                    'created_at' => $posTemplate->created_at,
                    'created_by' => $posTemplate->createdBy->name,
                ],
                'usage_stats' => [
                    'total_usage' => $posTemplate->usage_count,
                    'last_30_days_usage' => $usageStats->usage_count ?? 0,
                    'last_30_days_revenue' => $usageStats->total_revenue ?? 0,
                ],
                'availability' => [
                    'can_be_used' => $posTemplate->canBeUsed(),
                    'availability_status' => $posTemplate->availability_status,
                    'items_status' => $posTemplate->getItemsWithInventoryStatus(),
                ]
            ]
        ]);
    }
}
