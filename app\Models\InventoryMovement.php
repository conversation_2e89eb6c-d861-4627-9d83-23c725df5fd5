<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class InventoryMovement extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'inventory_item_id',
        'type',
        'quantity',
        'stock_before',
        'stock_after',
        'unit_cost',
        'total_cost',
        'reference_type',
        'reference_id',
        'reference_number',
        'from_location',
        'to_location',
        'notes',
        'batch_number',
        'expiry_date',
        'serial_numbers',
        'created_by',
        'movement_date',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'quantity' => 'integer',
        'stock_before' => 'integer',
        'stock_after' => 'integer',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'expiry_date' => 'date',
        'serial_numbers' => 'array',
        'movement_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the inventory item that owns the movement.
     */
    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(InventoryItem::class);
    }

    /**
     * Get the user who created this movement.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the reference model (polymorphic relationship).
     */
    public function reference(): MorphTo
    {
        return $this->morphTo('reference', 'reference_type', 'reference_id');
    }

    /**
     * Scope for movements by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for inbound movements (positive quantity).
     */
    public function scopeInbound($query)
    {
        return $query->where('quantity', '>', 0);
    }

    /**
     * Scope for outbound movements (negative quantity).
     */
    public function scopeOutbound($query)
    {
        return $query->where('quantity', '<', 0);
    }

    /**
     * Scope for movements within date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('movement_date', [$startDate, $endDate]);
    }

    /**
     * Scope for movements by reference.
     */
    public function scopeByReference($query, $referenceType, $referenceId)
    {
        return $query->where('reference_type', $referenceType)
                    ->where('reference_id', $referenceId);
    }

    /**
     * Get movement type display name.
     */
    public function getTypeDisplayAttribute(): string
    {
        return match($this->type) {
            'purchase' => __('app.inventory.movement_types.purchase'),
            'sale' => __('app.inventory.movement_types.sale'),
            'adjustment' => __('app.inventory.movement_types.adjustment'),
            'return' => __('app.inventory.movement_types.return'),
            'damage' => __('app.inventory.movement_types.damage'),
            'transfer' => __('app.inventory.movement_types.transfer'),
            'count' => __('app.inventory.movement_types.count'),
            'usage' => __('app.inventory.movement_types.usage'),
            default => $this->type
        };
    }

    /**
     * Get movement direction.
     */
    public function getDirectionAttribute(): string
    {
        return $this->quantity > 0 ? 'in' : 'out';
    }

    /**
     * Get movement direction display.
     */
    public function getDirectionDisplayAttribute(): string
    {
        return $this->direction === 'in' 
            ? __('app.inventory.direction.in') 
            : __('app.inventory.direction.out');
    }

    /**
     * Get movement icon based on type.
     */
    public function getIconAttribute(): string
    {
        return match($this->type) {
            'purchase' => 'fas fa-shopping-cart',
            'sale' => 'fas fa-cash-register',
            'adjustment' => 'fas fa-edit',
            'return' => 'fas fa-undo',
            'damage' => 'fas fa-exclamation-triangle',
            'transfer' => 'fas fa-exchange-alt',
            'count' => 'fas fa-clipboard-list',
            'usage' => 'fas fa-tools',
            default => 'fas fa-arrow-right'
        };
    }

    /**
     * Get movement color based on type and direction.
     */
    public function getColorAttribute(): string
    {
        if ($this->quantity > 0) {
            return match($this->type) {
                'purchase' => 'success',
                'return' => 'info',
                'adjustment' => 'warning',
                'count' => 'primary',
                default => 'success'
            };
        } else {
            return match($this->type) {
                'sale' => 'primary',
                'damage' => 'danger',
                'adjustment' => 'warning',
                'transfer' => 'info',
                'usage' => 'secondary',
                default => 'danger'
            };
        }
    }

    /**
     * Get absolute quantity.
     */
    public function getAbsoluteQuantityAttribute(): int
    {
        return abs($this->quantity);
    }

    /**
     * Get formatted quantity with direction.
     */
    public function getFormattedQuantityAttribute(): string
    {
        $sign = $this->quantity > 0 ? '+' : '';
        return $sign . number_format($this->quantity);
    }

    /**
     * Get reference display name.
     */
    public function getReferenceDisplayAttribute(): string
    {
        if ($this->reference_number) {
            return $this->reference_number;
        }

        if ($this->reference_type && $this->reference_id) {
            return $this->reference_type . ' #' . $this->reference_id;
        }

        return __('app.inventory.no_reference');
    }

    /**
     * Get location display.
     */
    public function getLocationDisplayAttribute(): string
    {
        if ($this->type === 'transfer') {
            return ($this->from_location ?? __('app.inventory.unknown_location')) . 
                   ' → ' . 
                   ($this->to_location ?? __('app.inventory.unknown_location'));
        }

        return $this->to_location ?? $this->from_location ?? __('app.inventory.main_warehouse');
    }

    /**
     * Check if movement has cost information.
     */
    public function hasCostInfo(): bool
    {
        return $this->unit_cost !== null && $this->unit_cost > 0;
    }

    /**
     * Get cost per unit (calculated if not stored).
     */
    public function getCalculatedUnitCostAttribute(): float
    {
        if ($this->unit_cost !== null) {
            return $this->unit_cost;
        }

        if ($this->total_cost !== null && $this->absolute_quantity > 0) {
            return $this->total_cost / $this->absolute_quantity;
        }

        return 0;
    }

    /**
     * Get calculated total cost.
     */
    public function getCalculatedTotalCostAttribute(): float
    {
        if ($this->total_cost !== null) {
            return $this->total_cost;
        }

        if ($this->unit_cost !== null) {
            return $this->unit_cost * $this->absolute_quantity;
        }

        return 0;
    }

    /**
     * Get movement summary for display.
     */
    public function getSummaryAttribute(): string
    {
        $summary = $this->type_display . ': ' . $this->formatted_quantity . ' ' . $this->inventoryItem->unit_of_measure;
        
        if ($this->reference_display !== __('app.inventory.no_reference')) {
            $summary .= ' (' . $this->reference_display . ')';
        }

        return $summary;
    }

    /**
     * Create a movement record.
     */
    public static function createMovement(
        InventoryItem $item,
        string $type,
        int $quantity,
        array $additionalData = []
    ): self {
        $stockBefore = $item->current_stock;
        
        // Update item stock
        $item->current_stock += $quantity;
        $item->save();

        return self::create(array_merge([
            'inventory_item_id' => $item->id,
            'type' => $type,
            'quantity' => $quantity,
            'stock_before' => $stockBefore,
            'stock_after' => $item->current_stock,
            'movement_date' => now(),
            'created_by' => auth()->id() ?? 1,
        ], $additionalData));
    }
}
