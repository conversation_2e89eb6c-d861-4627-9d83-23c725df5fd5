<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // POS Terminals/Stations
        Schema::create('pos_terminals', function (Blueprint $table) {
            $table->id();
            $table->string('terminal_code')->unique();
            $table->string('terminal_name');
            $table->string('location')->nullable();
            $table->boolean('is_active')->default(true);
            $table->json('configuration')->nullable();
            $table->foreignId('assigned_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('last_activity_at')->nullable();
            $table->decimal('cash_drawer_balance', 10, 2)->default(0);
            $table->decimal('opening_balance', 10, 2)->default(0);
            $table->timestamp('shift_started_at')->nullable();
            $table->foreignId('shift_opened_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index(['is_active', 'assigned_user_id']);
        });

        // POS Transactions (Quick sales not tied to repair tickets)
        Schema::create('pos_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_number')->unique();
            $table->foreignId('terminal_id')->constrained('pos_terminals')->onDelete('restrict');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            $table->string('customer_name')->nullable();
            $table->string('customer_phone')->nullable();
            $table->decimal('subtotal', 10, 2);
            $table->decimal('tax_rate', 5, 2)->default(0);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('discount_percentage', 5, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('total_amount', 10, 2);
            $table->enum('status', ['pending', 'completed', 'cancelled', 'refunded'])->default('pending');
            $table->timestamp('transaction_date');
            $table->foreignId('cashier_id')->constrained('users')->onDelete('restrict');
            $table->json('payment_breakdown')->nullable();
            $table->text('notes')->nullable();
            $table->boolean('receipt_printed')->default(false);
            $table->string('receipt_number')->nullable();
            $table->timestamps();

            $table->index(['transaction_date', 'status']);
            $table->index(['terminal_id', 'transaction_date']);
            $table->index(['customer_id', 'transaction_date']);
        });

        // POS Transaction Items
        Schema::create('pos_transaction_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('transaction_id')->constrained('pos_transactions')->onDelete('cascade');
            $table->foreignId('inventory_item_id')->nullable()->constrained()->onDelete('set null');
            $table->string('item_name');
            $table->string('item_sku')->nullable();
            $table->string('barcode')->nullable();
            $table->decimal('quantity', 8, 2);
            $table->string('unit_of_measure')->default('piece');
            $table->decimal('unit_price', 10, 2);
            $table->decimal('total_price', 10, 2);
            $table->decimal('cost_price', 10, 2)->nullable();
            $table->decimal('discount_percentage', 5, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->boolean('is_taxable')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['transaction_id', 'inventory_item_id']);
        });

        // POS Transaction Payments
        Schema::create('pos_transaction_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('transaction_id')->constrained('pos_transactions')->onDelete('cascade');
            $table->string('payment_method');
            $table->decimal('amount', 10, 2);
            $table->string('payment_reference')->nullable();
            $table->string('card_last_four')->nullable();
            $table->string('bank_name')->nullable();
            $table->json('payment_details')->nullable();
            $table->enum('status', ['pending', 'completed', 'failed', 'refunded'])->default('completed');
            $table->timestamp('payment_date');
            $table->decimal('change_amount', 10, 2)->default(0);
            $table->timestamps();

            $table->index(['transaction_id', 'payment_method']);
        });

        // Cash Drawer Operations
        Schema::create('cash_drawer_operations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('terminal_id')->constrained('pos_terminals')->onDelete('cascade');
            $table->enum('operation_type', ['open_shift', 'close_shift', 'cash_in', 'cash_out', 'count', 'adjustment']);
            $table->decimal('amount', 10, 2);
            $table->decimal('balance_before', 10, 2);
            $table->decimal('balance_after', 10, 2);
            $table->string('reason')->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('performed_by')->constrained('users')->onDelete('restrict');
            $table->timestamp('operation_date');
            $table->json('denomination_breakdown')->nullable();
            $table->timestamps();

            $table->index(['terminal_id', 'operation_date']);
            $table->index(['operation_type', 'operation_date']);
        });

        // Barcode Inventory (for quick barcode generation and management)
        Schema::create('barcode_inventory', function (Blueprint $table) {
            $table->id();
            $table->string('barcode')->unique();
            $table->enum('barcode_type', ['ean13', 'ean8', 'code128', 'code39', 'qr'])->default('ean13');
            $table->foreignId('inventory_item_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('item_name')->nullable();
            $table->decimal('price', 10, 2)->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_scanned_at')->nullable();
            $table->integer('scan_count')->default(0);
            $table->timestamps();

            $table->index(['barcode', 'is_active']);
            $table->index('inventory_item_id');
        });

        // POS Sessions/Shifts
        Schema::create('pos_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('session_number')->unique();
            $table->foreignId('terminal_id')->constrained('pos_terminals')->onDelete('restrict');
            $table->foreignId('user_id')->constrained()->onDelete('restrict');
            $table->timestamp('started_at');
            $table->timestamp('ended_at')->nullable();
            $table->decimal('opening_cash', 10, 2)->default(0);
            $table->decimal('closing_cash', 10, 2)->nullable();
            $table->decimal('expected_cash', 10, 2)->nullable();
            $table->decimal('cash_difference', 10, 2)->nullable();
            $table->integer('transactions_count')->default(0);
            $table->decimal('total_sales', 12, 2)->default(0);
            $table->decimal('total_cash', 10, 2)->default(0);
            $table->decimal('total_card', 10, 2)->default(0);
            $table->decimal('total_other', 10, 2)->default(0);
            $table->json('payment_summary')->nullable();
            $table->enum('status', ['active', 'closed', 'suspended'])->default('active');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['terminal_id', 'started_at']);
            $table->index(['user_id', 'started_at']);
            $table->index('status');
        });

        // Quick Sale Templates (for frequently sold items/combos)
        Schema::create('pos_quick_sale_templates', function (Blueprint $table) {
            $table->id();
            $table->string('template_name');
            $table->string('template_code')->unique();
            $table->json('items'); // Array of items with quantities and prices
            $table->decimal('total_price', 10, 2);
            $table->string('category')->nullable();
            $table->string('icon_class')->nullable();
            $table->string('color_class')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('usage_count')->default(0);
            $table->integer('sort_order')->default(0);
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();

            $table->index(['is_active', 'sort_order']);
            $table->index('category');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pos_quick_sale_templates');
        Schema::dropIfExists('pos_sessions');
        Schema::dropIfExists('barcode_inventory');
        Schema::dropIfExists('cash_drawer_operations');
        Schema::dropIfExists('pos_transaction_payments');
        Schema::dropIfExists('pos_transaction_items');
        Schema::dropIfExists('pos_transactions');
        Schema::dropIfExists('pos_terminals');
    }
};
