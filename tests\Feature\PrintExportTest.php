<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Customer;
use App\Models\RepairTicket;
use App\Models\RepairStatus;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\RepairTicketsExport;
use App\Exports\CustomersExport;

class PrintExportTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
    }

    /** @test */
    public function user_can_print_single_ticket_pdf()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create(['name' => 'John Doe']);
        $brand = Brand::factory()->create(['name' => 'Apple']);
        $status = RepairStatus::factory()->create(['name' => 'Completed']);
        
        $ticket = RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'brand_id' => $brand->id,
            'repair_status_id' => $status->id,
            'device_model' => 'iPhone 13',
            'final_cost' => 150.00
        ]);
        
        $response = $this->get(route('print-export.ticket.print', $ticket));
        
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/pdf');
        
        // Check that PDF contains ticket information
        $content = $response->getContent();
        $this->assertNotEmpty($content);
    }

    /** @test */
    public function user_can_preview_ticket_before_printing()
    {
        $this->actingAs($this->user);
        
        $ticket = RepairTicket::factory()->create();
        
        $response = $this->get(route('print-export.ticket.preview', $ticket));
        
        $response->assertStatus(200);
        $response->assertViewIs('print.ticket-preview');
        $response->assertViewHas('repairTicket');
        $response->assertSee($ticket->ticket_number);
    }

    /** @test */
    public function user_can_print_multiple_tickets()
    {
        $this->actingAs($this->user);
        
        $tickets = RepairTicket::factory()->count(3)->create();
        $ticketIds = $tickets->pluck('id')->toArray();
        
        $response = $this->post(route('print-export.tickets.print-multiple'), [
            'ticket_ids' => $ticketIds
        ]);
        
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/pdf');
    }

    /** @test */
    public function multiple_ticket_print_validates_ticket_ids()
    {
        $this->actingAs($this->user);
        
        $response = $this->post(route('print-export.tickets.print-multiple'), [
            'ticket_ids' => []
        ]);
        
        $response->assertSessionHasErrors('ticket_ids');
    }

    /** @test */
    public function user_can_export_tickets_to_excel()
    {
        $this->actingAs($this->user);
        
        Excel::fake();
        
        RepairTicket::factory()->count(5)->create();
        
        $response = $this->get(route('print-export.tickets.export-excel'));
        
        $response->assertStatus(200);
        
        Excel::assertDownloaded('repair_tickets_' . now()->format('Y_m_d') . '.xlsx', function (RepairTicketsExport $export) {
            return true;
        });
    }

    /** @test */
    public function user_can_export_customers_to_excel()
    {
        $this->actingAs($this->user);
        
        Excel::fake();
        
        Customer::factory()->count(10)->create();
        
        $response = $this->get(route('print-export.customers.export-excel'));
        
        $response->assertStatus(200);
        
        Excel::assertDownloaded('customers_' . now()->format('Y_m_d') . '.xlsx', function (CustomersExport $export) {
            return true;
        });
    }

    /** @test */
    public function excel_export_includes_correct_data()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create(['name' => 'John Doe']);
        $brand = Brand::factory()->create(['name' => 'Apple']);
        $status = RepairStatus::factory()->create(['name' => 'Completed']);
        
        $ticket = RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'brand_id' => $brand->id,
            'repair_status_id' => $status->id,
            'device_model' => 'iPhone 13',
            'final_cost' => 150.00
        ]);
        
        $export = new RepairTicketsExport();
        $data = $export->collection();
        
        $this->assertCount(1, $data);
        $this->assertEquals($ticket->ticket_number, $data->first()->ticket_number);
        $this->assertEquals('John Doe', $data->first()->customer->name);
    }

    /** @test */
    public function user_can_export_filtered_tickets()
    {
        $this->actingAs($this->user);
        
        Excel::fake();
        
        $pendingStatus = RepairStatus::factory()->create(['name' => 'Pending']);
        $completedStatus = RepairStatus::factory()->create(['name' => 'Completed']);
        
        RepairTicket::factory()->count(3)->create(['repair_status_id' => $pendingStatus->id]);
        RepairTicket::factory()->count(2)->create(['repair_status_id' => $completedStatus->id]);
        
        $response = $this->get(route('print-export.tickets.export-excel', ['status' => 'Pending']));
        
        $response->assertStatus(200);
        
        Excel::assertDownloaded('repair_tickets_' . now()->format('Y_m_d') . '.xlsx');
    }

    /** @test */
    public function user_can_export_reports_to_pdf()
    {
        $this->actingAs($this->user);
        
        RepairTicket::factory()->count(10)->create();
        Customer::factory()->count(5)->create();
        
        $response = $this->get(route('print-export.reports.export-pdf', ['type' => 'dashboard']));
        
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/pdf');
    }

    /** @test */
    public function pdf_export_includes_charts_and_statistics()
    {
        $this->actingAs($this->user);
        
        $pendingStatus = RepairStatus::factory()->create(['name' => 'Pending']);
        $completedStatus = RepairStatus::factory()->create(['name' => 'Completed']);
        
        RepairTicket::factory()->count(3)->create(['repair_status_id' => $pendingStatus->id]);
        RepairTicket::factory()->count(7)->create(['repair_status_id' => $completedStatus->id]);
        
        $response = $this->get(route('print-export.reports.export-pdf', ['type' => 'dashboard']));
        
        $response->assertStatus(200);
        
        // Check that the response contains PDF content
        $content = $response->getContent();
        $this->assertStringStartsWith('%PDF', $content);
    }

    /** @test */
    public function print_template_displays_all_ticket_information()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create([
            'name' => 'John Doe',
            'phone_number' => '+1234567890',
            'email' => '<EMAIL>'
        ]);
        
        $brand = Brand::factory()->create(['name' => 'Apple']);
        $status = RepairStatus::factory()->create(['name' => 'Completed']);
        
        $ticket = RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'brand_id' => $brand->id,
            'repair_status_id' => $status->id,
            'device_model' => 'iPhone 13 Pro',
            'reported_problem' => 'Screen cracked and not responding',
            'technician_comments' => 'Replaced screen and tested functionality',
            'initial_cost' => 150.00,
            'final_cost' => 175.00
        ]);
        
        $response = $this->get(route('print-export.ticket.preview', $ticket));
        
        $response->assertStatus(200);
        $response->assertSee($ticket->ticket_number);
        $response->assertSee('John Doe');
        $response->assertSee('+1234567890');
        $response->assertSee('<EMAIL>');
        $response->assertSee('Apple');
        $response->assertSee('iPhone 13 Pro');
        $response->assertSee('Screen cracked and not responding');
        $response->assertSee('Replaced screen and tested functionality');
        $response->assertSee('$150.00');
        $response->assertSee('$175.00');
        $response->assertSee('Completed');
    }

    /** @test */
    public function user_can_export_customer_analytics()
    {
        $this->actingAs($this->user);
        
        Excel::fake();
        
        $customer = Customer::factory()->create();
        RepairTicket::factory()->count(5)->create([
            'customer_id' => $customer->id,
            'final_cost' => 100.00
        ]);
        
        $response = $this->get(route('print-export.reports.export-excel', ['type' => 'customer-analytics']));
        
        $response->assertStatus(200);
        
        Excel::assertDownloaded('customer_analytics_' . now()->format('Y_m_d') . '.xlsx');
    }

    /** @test */
    public function export_handles_large_datasets()
    {
        $this->actingAs($this->user);
        
        Excel::fake();
        
        // Create a large number of tickets
        RepairTicket::factory()->count(1000)->create();
        
        $response = $this->get(route('print-export.tickets.export-excel'));
        
        $response->assertStatus(200);
        
        Excel::assertDownloaded('repair_tickets_' . now()->format('Y_m_d') . '.xlsx');
    }

    /** @test */
    public function print_pdf_has_proper_styling()
    {
        $this->actingAs($this->user);
        
        $ticket = RepairTicket::factory()->create();
        
        $response = $this->get(route('print-export.ticket.print', $ticket));
        
        $response->assertStatus(200);
        
        // Check that PDF is generated (starts with PDF header)
        $content = $response->getContent();
        $this->assertStringStartsWith('%PDF', $content);
        $this->assertGreaterThan(1000, strlen($content)); // PDF should have substantial content
    }

    /** @test */
    public function export_filename_includes_date()
    {
        $this->actingAs($this->user);
        
        Excel::fake();
        
        RepairTicket::factory()->count(5)->create();
        
        $response = $this->get(route('print-export.tickets.export-excel'));
        
        $expectedFilename = 'repair_tickets_' . now()->format('Y_m_d') . '.xlsx';
        
        Excel::assertDownloaded($expectedFilename);
    }

    /** @test */
    public function user_cannot_print_nonexistent_ticket()
    {
        $this->actingAs($this->user);
        
        $response = $this->get(route('print-export.ticket.print', 999));
        
        $response->assertStatus(404);
    }

    /** @test */
    public function export_respects_user_permissions()
    {
        // Test with user who doesn't have export permissions
        $limitedUser = User::factory()->create();
        $this->actingAs($limitedUser);
        
        RepairTicket::factory()->count(5)->create();
        
        $response = $this->get(route('print-export.tickets.export-excel'));
        
        // Assuming we have permission checks in place
        $response->assertStatus(200); // Or 403 if permissions are implemented
    }

    /** @test */
    public function bulk_print_handles_mixed_ticket_states()
    {
        $this->actingAs($this->user);
        
        $pendingStatus = RepairStatus::factory()->create(['name' => 'Pending']);
        $completedStatus = RepairStatus::factory()->create(['name' => 'Completed']);
        
        $ticket1 = RepairTicket::factory()->create(['repair_status_id' => $pendingStatus->id]);
        $ticket2 = RepairTicket::factory()->create(['repair_status_id' => $completedStatus->id]);
        
        $response = $this->post(route('print-export.tickets.print-multiple'), [
            'ticket_ids' => [$ticket1->id, $ticket2->id]
        ]);
        
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/pdf');
    }

    /** @test */
    public function export_includes_proper_headers()
    {
        $this->actingAs($this->user);
        
        $export = new RepairTicketsExport();
        $headers = $export->headings();
        
        $expectedHeaders = [
            'Ticket Number',
            'Customer Name',
            'Phone Number',
            'Brand',
            'Device Model',
            'Problem',
            'Status',
            'Priority',
            'Received Date',
            'Completed Date',
            'Initial Cost',
            'Final Cost',
            'Technician'
        ];
        
        $this->assertEquals($expectedHeaders, $headers);
    }
}
