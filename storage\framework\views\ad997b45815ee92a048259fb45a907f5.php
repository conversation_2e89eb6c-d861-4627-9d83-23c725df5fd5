<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="bi bi-house"></i> <?php echo e(__('app.dashboard.title')); ?>

            </h1>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card animate-on-scroll" data-bs-toggle="tooltip" data-bs-placement="top" title="Total number of registered customers">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="card-title display-6 mb-1"><?php echo App\Helpers\ArabicFormatter::formatNumber($stats['total_customers']); ?></h4>
                            <p class="card-text mb-0 opacity-75"><?php echo e(__('app.dashboard.total_customers')); ?></p>
                            <small class="text-white-50">
                                <i class="bi bi-arrow-up"></i> +<?php echo App\Helpers\ArabicFormatter::formatNumber($stats['new_customers_this_month'] ?? 0); ?> <?php echo e(__('app.dashboard.new_customers_this_month')); ?>

                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people display-4 opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo App\Helpers\ArabicFormatter::formatNumber($stats['total_tickets']); ?></h4>
                            <p class="card-text"><?php echo e(__('app.dashboard.total_tickets')); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-ticket display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo App\Helpers\ArabicFormatter::formatNumber($stats['pending_tickets']); ?></h4>
                            <p class="card-text"><?php echo e(__('app.dashboard.pending_tickets')); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-clock display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo App\Helpers\ArabicFormatter::formatNumber($stats['completed_today']); ?></h4>
                            <p class="card-text"><?php echo e(__('app.dashboard.completed_today')); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Tickets -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history"></i> <?php echo e(__('app.dashboard.recent_tickets')); ?>

                        </h5>
                        <a href="<?php echo e(route('repair-tickets.index')); ?>" class="btn btn-outline-primary btn-sm">
                            <?php echo e(__('app.dashboard.view_all')); ?>

                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if($recentTickets->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th><?php echo e(__('app.dashboard.ticket_number')); ?></th>
                                        <th><?php echo e(__('app.dashboard.customer')); ?></th>
                                        <th><?php echo e(__('app.dashboard.device')); ?></th>
                                        <th><?php echo e(__('app.dashboard.status')); ?></th>
                                        <th><?php echo e(__('app.dashboard.created')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $recentTickets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ticket): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <a href="<?php echo e(route('repair-tickets.show', $ticket)); ?>" class="text-decoration-none">
                                                    <strong><?php echo e($ticket->ticket_number); ?></strong>
                                                </a>
                                            </td>
                                            <td><?php echo e($ticket->customer->name); ?></td>
                                            <td><?php echo e($ticket->brand->name); ?> <?php echo e($ticket->device_model); ?></td>
                                            <td>
                                                <span class="badge" style="background-color: <?php echo e($ticket->repairStatus->color); ?>">
                                                    <?php echo e($ticket->repairStatus->name); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <small><?php echo e($ticket->created_at->diffForHumans()); ?></small>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-ticket display-1 text-muted"></i>
                            <h4 class="mt-3"><?php echo e(__('app.dashboard.no_tickets_yet')); ?></h4>
                            <p class="text-muted"><?php echo e(__('app.dashboard.create_first_ticket')); ?></p>
                            <a href="<?php echo e(route('repair-tickets.create')); ?>" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> <?php echo e(__('app.dashboard.create_ticket')); ?>

                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Tickets by Status -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pie-chart"></i> <?php echo e(__('app.dashboard.tickets_by_status')); ?>

                    </h5>
                </div>
                <div class="card-body">
                    <?php if($ticketsByStatus->count() > 0): ?>
                        <?php $__currentLoopData = $ticketsByStatus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="badge me-2" style="background-color: <?php echo e($status->color); ?>">
                                        &nbsp;
                                    </span>
                                    <span><?php echo e($status->name); ?></span>
                                </div>
                                <span class="badge bg-secondary"><?php echo e($status->repair_tickets_count); ?></span>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <i class="bi bi-pie-chart display-4 text-muted"></i>
                            <p class="text-muted mt-2"><?php echo e(__('app.dashboard.no_status_data')); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightning"></i> <?php echo e(__('app.dashboard.quick_actions')); ?>

                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="<?php echo e(route('repair-tickets.create')); ?>" class="btn btn-success w-100 mb-2">
                                <i class="bi bi-plus-circle"></i> <?php echo e(__('app.dashboard.new_repair_ticket')); ?>

                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo e(route('customers.create')); ?>" class="btn btn-primary w-100 mb-2">
                                <i class="bi bi-person-plus"></i> <?php echo e(__('app.dashboard.add_customer')); ?>

                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo e(route('customers.index')); ?>" class="btn btn-outline-primary w-100 mb-2">
                                <i class="bi bi-people"></i> <?php echo e(__('app.dashboard.view_customers')); ?>

                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo e(route('repair-tickets.index')); ?>" class="btn btn-outline-secondary w-100 mb-2">
                                <i class="bi bi-ticket"></i> <?php echo e(__('app.dashboard.all_tickets')); ?>

                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\nj\resources\views/dashboard.blade.php ENDPATH**/ ?>