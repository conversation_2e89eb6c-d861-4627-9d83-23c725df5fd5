<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\Invoice;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class PaymentController extends Controller
{
    /**
     * Display a listing of payments.
     */
    public function index(Request $request): View
    {
        $query = Payment::with(['invoice', 'customer', 'createdBy'])
            ->orderBy('payment_date', 'desc');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('payment_number', 'like', "%{$search}%")
                  ->orWhere('payment_reference', 'like', "%{$search}%")
                  ->orWhereHas('invoice', function($invoiceQuery) use ($search) {
                      $invoiceQuery->where('invoice_number', 'like', "%{$search}%");
                  })
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('phone_number', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('payment_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('payment_date', '<=', $request->date_to);
        }

        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        $payments = $query->paginate(20);

        // Statistics
        $stats = [
            'total_payments' => Payment::where('status', 'completed')->count(),
            'today_payments' => Payment::where('status', 'completed')->whereDate('payment_date', today())->count(),
            'total_amount' => Payment::where('status', 'completed')->sum('amount'),
            'today_amount' => Payment::where('status', 'completed')->whereDate('payment_date', today())->sum('amount'),
            'pending_payments' => Payment::where('status', 'pending')->count(),
            'pending_amount' => Payment::where('status', 'pending')->sum('amount'),
        ];

        $customers = Customer::orderBy('name')->get();

        return view('payments.index', compact('payments', 'stats', 'customers'));
    }

    /**
     * Show the form for creating a new payment.
     */
    public function create(Request $request): View
    {
        $invoices = Invoice::with('customer')
            ->whereIn('payment_status', ['pending', 'partial'])
            ->orderBy('invoice_date', 'desc')
            ->get();

        $customers = Customer::orderBy('name')->get();

        // Pre-select invoice if provided
        $selectedInvoice = null;
        if ($request->filled('invoice_id')) {
            $selectedInvoice = Invoice::with('customer')->find($request->invoice_id);
        }

        return view('payments.create', compact('invoices', 'customers', 'selectedInvoice'));
    }

    /**
     * Store a newly created payment.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'invoice_id' => 'required|exists:invoices,id',
            'amount' => 'required|numeric|min:0.01',
            'payment_date' => 'required|date',
            'payment_method' => 'required|in:cash,card,bank_transfer,check,mobile_payment,installment,other',
            'payment_reference' => 'nullable|string|max:255',
            'card_last_four' => 'nullable|string|size:4',
            'bank_name' => 'nullable|string|max:255',
            'check_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:1000',
            'receipt_number' => 'nullable|string|max:255',
        ]);

        DB::beginTransaction();
        try {
            $invoice = Invoice::findOrFail($validated['invoice_id']);

            // Check if payment amount doesn't exceed remaining amount
            $remainingAmount = $invoice->total_amount - $invoice->paid_amount;
            if ($validated['amount'] > $remainingAmount) {
                return back()->withInput()
                    ->with('error', 'مبلغ الدفع أكبر من المبلغ المتبقي (' . number_format($remainingAmount, 2) . ' ريال)');
            }

            // Generate payment number
            $paymentNumber = $this->generatePaymentNumber();

            // Create payment
            $payment = Payment::create([
                'payment_number' => $paymentNumber,
                'invoice_id' => $validated['invoice_id'],
                'customer_id' => $invoice->customer_id,
                'amount' => $validated['amount'],
                'payment_date' => $validated['payment_date'],
                'payment_method' => $validated['payment_method'],
                'payment_reference' => $validated['payment_reference'],
                'card_last_four' => $validated['card_last_four'],
                'bank_name' => $validated['bank_name'],
                'check_number' => $validated['check_number'],
                'notes' => $validated['notes'],
                'receipt_number' => $validated['receipt_number'],
                'status' => 'completed',
                'is_verified' => true,
                'verified_at' => now(),
                'verified_by' => Auth::id(),
                'created_by' => Auth::id(),
            ]);

            // Update invoice payment status
            $invoice->paid_amount += $validated['amount'];

            if ($invoice->paid_amount >= $invoice->total_amount) {
                $invoice->payment_status = 'paid';
                $invoice->paid_date = $validated['payment_date'];
            } else {
                $invoice->payment_status = 'partial';
            }

            $invoice->save();

            DB::commit();

            return redirect()->route('payments.show', $payment)
                ->with('success', 'تم تسجيل الدفعة بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء تسجيل الدفعة: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified payment.
     */
    public function show(Payment $payment): View
    {
        $payment->load(['invoice.customer', 'customer', 'createdBy', 'verifiedBy']);

        return view('payments.show', compact('payment'));
    }

    /**
     * Generate unique payment number.
     */
    private function generatePaymentNumber(): string
    {
        $lastPayment = Payment::latest('id')->first();
        $nextNumber = $lastPayment ? $lastPayment->id + 1 : 1;

        return 'PAY-' . str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get payment statistics.
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'today' => [
                'count' => Payment::where('status', 'completed')->whereDate('payment_date', today())->count(),
                'amount' => Payment::where('status', 'completed')->whereDate('payment_date', today())->sum('amount'),
            ],
            'this_month' => [
                'count' => Payment::where('status', 'completed')->whereMonth('payment_date', now()->month)->count(),
                'amount' => Payment::where('status', 'completed')->whereMonth('payment_date', now()->month)->sum('amount'),
            ],
            'by_method' => Payment::where('status', 'completed')
                ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as total')
                ->groupBy('payment_method')
                ->get()
        ];

        return response()->json($stats);
    }
}
