#!/bin/bash

# NJ Repair Shop Backup Script
# Run this script daily via cron

set -e

# Configuration
APP_PATH="/var/www/njrepair"
BACKUP_PATH="/var/backups/njrepair"
DB_NAME="njrepair_production"
DB_USER="njrepair"
RETENTION_DAYS=30
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_PATH/{database,files,logs}

echo "🔄 Starting backup process - $DATE"

# Database backup
echo "📊 Backing up database..."
mysqldump --single-transaction --routines --triggers \
    -u $DB_USER -p$DB_PASSWORD $DB_NAME | \
    gzip > $BACKUP_PATH/database/njrepair_db_$DATE.sql.gz

# Application files backup (excluding vendor and node_modules)
echo "📁 Backing up application files..."
tar --exclude='vendor' --exclude='node_modules' --exclude='storage/logs' \
    --exclude='.git' -czf $BACKUP_PATH/files/njrepair_files_$DATE.tar.gz \
    -C /var/www njrepair

# Storage files backup (uploads, etc.)
echo "💾 Backing up storage files..."
tar -czf $BACKUP_PATH/files/njrepair_storage_$DATE.tar.gz \
    -C $APP_PATH storage/app/public

# Environment file backup
echo "⚙️ Backing up environment configuration..."
cp $APP_PATH/.env $BACKUP_PATH/files/env_$DATE.backup

# Log files backup
echo "📝 Backing up recent logs..."
find $APP_PATH/storage/logs -name "*.log" -mtime -7 | \
    tar -czf $BACKUP_PATH/logs/njrepair_logs_$DATE.tar.gz -T -

# System information
echo "ℹ️ Saving system information..."
cat > $BACKUP_PATH/system_info_$DATE.txt <<EOF
Backup Date: $(date)
Server: $(hostname)
Laravel Version: $(cd $APP_PATH && php artisan --version)
PHP Version: $(php -v | head -n 1)
MySQL Version: $(mysql --version)
Disk Usage: $(df -h $APP_PATH | tail -n 1)
Memory Usage: $(free -h | grep Mem)
EOF

# Cleanup old backups
echo "🧹 Cleaning up old backups..."
find $BACKUP_PATH -type f -mtime +$RETENTION_DAYS -delete

# Calculate backup sizes
DB_SIZE=$(du -sh $BACKUP_PATH/database/njrepair_db_$DATE.sql.gz | cut -f1)
FILES_SIZE=$(du -sh $BACKUP_PATH/files/njrepair_files_$DATE.tar.gz | cut -f1)
STORAGE_SIZE=$(du -sh $BACKUP_PATH/files/njrepair_storage_$DATE.tar.gz | cut -f1)

echo "✅ Backup completed successfully!"
echo "📊 Backup sizes:"
echo "   Database: $DB_SIZE"
echo "   Application: $FILES_SIZE"
echo "   Storage: $STORAGE_SIZE"

# Send backup notification (if configured)
if [ ! -z "$BACKUP_NOTIFICATION_URL" ]; then
    curl -X POST "$BACKUP_NOTIFICATION_URL" \
        -H "Content-Type: application/json" \
        -d "{
            \"message\": \"NJ Repair Shop backup completed\",
            \"date\": \"$DATE\",
            \"sizes\": {
                \"database\": \"$DB_SIZE\",
                \"files\": \"$FILES_SIZE\",
                \"storage\": \"$STORAGE_SIZE\"
            }
        }" || echo "Failed to send notification"
fi

# Optional: Upload to cloud storage
if [ ! -z "$AWS_S3_BUCKET" ]; then
    echo "☁️ Uploading to S3..."
    aws s3 cp $BACKUP_PATH/database/njrepair_db_$DATE.sql.gz \
        s3://$AWS_S3_BUCKET/backups/database/ || echo "S3 upload failed"
    aws s3 cp $BACKUP_PATH/files/njrepair_files_$DATE.tar.gz \
        s3://$AWS_S3_BUCKET/backups/files/ || echo "S3 upload failed"
fi

echo "🎉 Backup process completed at $(date)"
