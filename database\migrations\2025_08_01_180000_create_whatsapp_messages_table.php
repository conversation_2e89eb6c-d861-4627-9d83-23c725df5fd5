<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_messages', function (Blueprint $table) {
            $table->id();
            
            // Message identification
            $table->string('whatsapp_message_id')->unique()->nullable();
            $table->string('conversation_id')->index();
            
            // Sender/Recipient information
            $table->string('from_phone_number', 20)->index();
            $table->string('to_phone_number', 20)->index();
            $table->string('contact_name')->nullable();
            
            // Message content
            $table->enum('type', ['text', 'template', 'image', 'document', 'audio', 'video', 'location', 'contact', 'interactive'])->index();
            $table->text('content')->nullable();
            $table->json('media_data')->nullable(); // For media messages
            $table->json('template_data')->nullable(); // For template messages
            $table->json('interactive_data')->nullable(); // For interactive messages
            
            // Message direction and status
            $table->enum('direction', ['inbound', 'outbound'])->index();
            $table->enum('status', ['pending', 'sent', 'delivered', 'read', 'failed', 'rejected'])->default('pending')->index();
            $table->text('error_message')->nullable();
            $table->integer('retry_count')->default(0);
            
            // Timestamps
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            
            // Business context
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // User who sent the message
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('cascade'); // Related customer
            $table->foreignId('repair_ticket_id')->nullable()->constrained()->onDelete('set null'); // Related repair ticket
            $table->foreignId('invoice_id')->nullable()->constrained()->onDelete('set null'); // Related invoice
            
            // Webhook and API data
            $table->json('webhook_data')->nullable(); // Raw webhook payload
            $table->json('api_response')->nullable(); // API response data
            $table->string('webhook_signature')->nullable();
            
            // Message context
            $table->string('context_type')->nullable(); // repair_update, payment_reminder, etc.
            $table->json('context_data')->nullable(); // Additional context information
            $table->boolean('is_automated')->default(false); // System-generated message
            
            // Pricing and billing
            $table->decimal('cost', 8, 4)->nullable(); // Message cost
            $table->string('currency', 3)->default('USD');
            $table->boolean('is_billable')->default(true);
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['from_phone_number', 'created_at']);
            $table->index(['to_phone_number', 'created_at']);
            $table->index(['status', 'created_at']);
            $table->index(['direction', 'type', 'created_at']);
            $table->index(['customer_id', 'created_at']);
            $table->index(['repair_ticket_id', 'created_at']);
            $table->index(['context_type', 'created_at']);
            $table->index(['is_automated', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_messages');
    }
};
