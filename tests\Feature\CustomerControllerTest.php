<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Customer;
use App\Models\RepairTicket;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class CustomerControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
    }

    /** @test */
    public function authenticated_user_can_view_customers_index()
    {
        $this->actingAs($this->user);
        
        Customer::factory()->count(5)->create();
        
        $response = $this->get(route('customers.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('customers.index');
        $response->assertViewHas('customers');
    }

    /** @test */
    public function guest_cannot_view_customers_index()
    {
        $response = $this->get(route('customers.index'));
        
        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function user_can_search_customers()
    {
        $this->actingAs($this->user);
        
        $customer1 = Customer::factory()->create(['name' => 'John <PERSON>e']);
        $customer2 = Customer::factory()->create(['name' => 'Jane Smith']);
        
        $response = $this->get(route('customers.index', ['search' => 'John']));
        
        $response->assertStatus(200);
        $response->assertSee('John Doe');
        $response->assertDontSee('Jane Smith');
    }

    /** @test */
    public function user_can_view_create_customer_form()
    {
        $this->actingAs($this->user);
        
        $response = $this->get(route('customers.create'));
        
        $response->assertStatus(200);
        $response->assertViewIs('customers.create');
    }

    /** @test */
    public function user_can_create_new_customer()
    {
        $this->actingAs($this->user);
        
        $customerData = [
            'name' => 'John Doe',
            'phone_number' => '+1234567890',
            'email' => '<EMAIL>',
            'address' => '123 Main St, City, State'
        ];
        
        $response = $this->post(route('customers.store'), $customerData);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        $this->assertDatabaseHas('customers', [
            'name' => 'John Doe',
            'phone_number' => '+1234567890',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function customer_creation_validates_required_fields()
    {
        $this->actingAs($this->user);
        
        $response = $this->post(route('customers.store'), []);
        
        $response->assertSessionHasErrors(['name', 'phone_number']);
    }

    /** @test */
    public function customer_creation_validates_unique_phone_number()
    {
        $this->actingAs($this->user);
        
        Customer::factory()->create(['phone_number' => '+1234567890']);
        
        $response = $this->post(route('customers.store'), [
            'name' => 'John Doe',
            'phone_number' => '+1234567890',
            'email' => '<EMAIL>'
        ]);
        
        $response->assertSessionHasErrors('phone_number');
    }

    /** @test */
    public function customer_creation_validates_unique_email()
    {
        $this->actingAs($this->user);
        
        Customer::factory()->create(['email' => '<EMAIL>']);
        
        $response = $this->post(route('customers.store'), [
            'name' => 'John Doe',
            'phone_number' => '+1234567890',
            'email' => '<EMAIL>'
        ]);
        
        $response->assertSessionHasErrors('email');
    }

    /** @test */
    public function user_can_view_single_customer()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create();
        RepairTicket::factory()->count(3)->create(['customer_id' => $customer->id]);
        
        $response = $this->get(route('customers.show', $customer));
        
        $response->assertStatus(200);
        $response->assertViewIs('customers.show');
        $response->assertViewHas('customer');
        $response->assertSee($customer->name);
    }

    /** @test */
    public function customer_show_page_displays_repair_tickets()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create();
        $ticket = RepairTicket::factory()->create(['customer_id' => $customer->id]);
        
        $response = $this->get(route('customers.show', $customer));
        
        $response->assertStatus(200);
        $response->assertSee($ticket->ticket_number);
    }

    /** @test */
    public function user_can_view_edit_customer_form()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create();
        
        $response = $this->get(route('customers.edit', $customer));
        
        $response->assertStatus(200);
        $response->assertViewIs('customers.edit');
        $response->assertViewHas('customer');
    }

    /** @test */
    public function user_can_update_customer()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create([
            'name' => 'John Doe',
            'email' => '<EMAIL>'
        ]);
        
        $updateData = [
            'name' => 'John Smith',
            'phone_number' => $customer->phone_number,
            'email' => '<EMAIL>',
            'address' => 'Updated Address'
        ];
        
        $response = $this->put(route('customers.update', $customer), $updateData);
        
        $response->assertRedirect(route('customers.show', $customer));
        $response->assertSessionHas('success');
        
        $this->assertDatabaseHas('customers', [
            'id' => $customer->id,
            'name' => 'John Smith',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function user_can_delete_customer()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create();
        
        $response = $this->delete(route('customers.destroy', $customer));
        
        $response->assertRedirect(route('customers.index'));
        $response->assertSessionHas('success');
        
        $this->assertDatabaseMissing('customers', [
            'id' => $customer->id
        ]);
    }

    /** @test */
    public function cannot_delete_customer_with_active_tickets()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create();
        $pendingStatus = \App\Models\RepairStatus::factory()->create(['name' => 'Pending']);
        
        RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'repair_status_id' => $pendingStatus->id
        ]);
        
        $response = $this->delete(route('customers.destroy', $customer));
        
        $response->assertRedirect();
        $response->assertSessionHas('error');
        
        $this->assertDatabaseHas('customers', [
            'id' => $customer->id
        ]);
    }

    /** @test */
    public function customers_are_paginated()
    {
        $this->actingAs($this->user);
        
        Customer::factory()->count(25)->create();
        
        $response = $this->get(route('customers.index'));
        
        $response->assertStatus(200);
        $response->assertViewHas('customers');
        
        $customers = $response->viewData('customers');
        $this->assertEquals(15, $customers->perPage()); // Assuming 15 per page
    }

    /** @test */
    public function user_can_export_customers_to_excel()
    {
        $this->actingAs($this->user);
        
        Customer::factory()->count(5)->create();
        
        $response = $this->get(route('print-export.customers.export-excel'));
        
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    }

    /** @test */
    public function customer_statistics_are_calculated_correctly()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create();
        
        RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'final_cost' => 100.00
        ]);
        
        RepairTicket::factory()->create([
            'customer_id' => $customer->id,
            'final_cost' => 200.00
        ]);
        
        $response = $this->get(route('customers.show', $customer));
        
        $response->assertStatus(200);
        $response->assertSee('$300.00'); // Total spent
        $response->assertSee('$150.00'); // Average cost
    }

    /** @test */
    public function user_can_create_ticket_from_customer_page()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create();
        
        $response = $this->get(route('customers.show', $customer));
        
        $response->assertStatus(200);
        $response->assertSee(route('repair-tickets.create', ['customer_id' => $customer->id]));
    }

    /** @test */
    public function customer_phone_number_is_formatted_correctly()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create([
            'phone_number' => '1234567890'
        ]);
        
        $response = $this->get(route('customers.show', $customer));
        
        $response->assertStatus(200);
        // Assuming phone formatting is applied in the view
        $response->assertSee($customer->phone_number);
    }

    /** @test */
    public function customer_email_validation_works()
    {
        $this->actingAs($this->user);
        
        $response = $this->post(route('customers.store'), [
            'name' => 'John Doe',
            'phone_number' => '+1234567890',
            'email' => 'invalid-email'
        ]);
        
        $response->assertSessionHasErrors('email');
    }

    /** @test */
    public function customer_can_have_multiple_repair_tickets()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create();
        RepairTicket::factory()->count(5)->create(['customer_id' => $customer->id]);
        
        $response = $this->get(route('customers.show', $customer));
        
        $response->assertStatus(200);
        $response->assertSee('5 tickets'); // Assuming this text appears
    }

    /** @test */
    public function customer_loyalty_level_is_displayed()
    {
        $this->actingAs($this->user);
        
        $customer = Customer::factory()->create();
        RepairTicket::factory()->count(10)->create(['customer_id' => $customer->id]);
        
        $response = $this->get(route('customers.show', $customer));
        
        $response->assertStatus(200);
        // Assuming loyalty level is displayed
        $response->assertSeeText(['Bronze', 'Silver', 'Gold', 'Platinum'], false);
    }

    /** @test */
    public function user_can_filter_active_customers()
    {
        $this->actingAs($this->user);
        
        // Active customer (recent ticket)
        $activeCustomer = Customer::factory()->create();
        RepairTicket::factory()->create([
            'customer_id' => $activeCustomer->id,
            'received_date' => now()->subDays(10)
        ]);
        
        // Inactive customer (old ticket)
        $inactiveCustomer = Customer::factory()->create();
        RepairTicket::factory()->create([
            'customer_id' => $inactiveCustomer->id,
            'received_date' => now()->subDays(40)
        ]);
        
        $response = $this->get(route('customers.index', ['filter' => 'active']));
        
        $response->assertStatus(200);
        $response->assertSee($activeCustomer->name);
        $response->assertDontSee($inactiveCustomer->name);
    }
}
