<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairStatus>
 */
class RepairStatusFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->randomElement([
                'Pending', 'In Progress', 'Waiting for Parts',
                'Completed', 'Cancelled', 'On Hold',
                'Under Review', 'Ready for Pickup', 'Delivered'
            ]),
            'color' => $this->faker->randomElement([
                '#6c757d', '#007bff', '#ffc107',
                '#28a745', '#dc3545', '#17a2b8'
            ]),
            'description' => $this->faker->optional(0.7)->sentence(),
        ];
    }
}
