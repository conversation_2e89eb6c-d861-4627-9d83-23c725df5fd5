@extends('layouts.app')

@section('title', __('app.pos.title'))

@push('styles')
<style>
    .pos-container {
        height: calc(100vh - 120px);
        overflow: hidden;
    }

    .pos-left-panel {
        height: 100%;
        overflow-y: auto;
        border-right: 2px solid #e9ecef;
    }

    .pos-right-panel {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
        padding: 15px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .product-card {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 10px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .product-card:hover {
        border-color: #0d6efd;
        box-shadow: 0 4px 8px rgba(13, 110, 253, 0.2);
        transform: translateY(-2px);
    }

    .product-card.out-of-stock {
        opacity: 0.5;
        cursor: not-allowed;
        background: #f8f9fa;
    }

    .product-name {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 5px;
        color: #495057;
    }

    .product-price {
        font-size: 1.1rem;
        font-weight: bold;
        color: #28a745;
    }

    .product-stock {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .cart-container {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .cart-items {
        flex: 1;
        overflow-y: auto;
        max-height: 40vh;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 15px;
    }

    .cart-item {
        padding: 10px 15px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .cart-item:last-child {
        border-bottom: none;
    }

    .cart-summary {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 15px;
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .summary-row.total {
        font-size: 1.2rem;
        font-weight: bold;
        border-top: 2px solid #dee2e6;
        padding-top: 10px;
        margin-top: 15px;
    }

    .barcode-scanner {
        position: relative;
        margin-bottom: 15px;
    }

    .scanner-input {
        font-size: 1.1rem;
        padding: 12px;
        border: 2px solid #0d6efd;
        border-radius: 8px;
    }

    .scanner-input:focus {
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .payment-methods {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        margin-bottom: 15px;
    }

    .payment-method {
        padding: 15px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }

    .payment-method:hover,
    .payment-method.active {
        border-color: #0d6efd;
        background: #e7f3ff;
    }

    .payment-method i {
        font-size: 1.5rem;
        margin-bottom: 5px;
        display: block;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .quantity-btn {
        width: 30px;
        height: 30px;
        border: 1px solid #dee2e6;
        background: white;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 0.9rem;
    }

    .quantity-btn:hover {
        background: #f8f9fa;
    }

    .quantity-input {
        width: 50px;
        text-align: center;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 5px;
    }

    .search-filters {
        padding: 15px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }

    .category-tabs {
        display: flex;
        gap: 5px;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }

    .category-tab {
        padding: 8px 15px;
        border: 1px solid #dee2e6;
        border-radius: 20px;
        background: white;
        cursor: pointer;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .category-tab:hover,
    .category-tab.active {
        background: #0d6efd;
        color: white;
        border-color: #0d6efd;
    }

    .pos-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }

    .btn-checkout {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        font-weight: 600;
        padding: 15px;
        font-size: 1.1rem;
    }

    .btn-clear {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        border: none;
        color: white;
        font-weight: 600;
        padding: 15px;
        font-size: 1.1rem;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .pos-container {
            height: auto;
            flex-direction: column;
        }

        .pos-left-panel {
            border-right: none;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 20px;
            height: auto;
        }

        .product-grid {
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            max-height: 50vh;
            padding: 10px;
        }

        .product-card {
            min-height: 100px;
            padding: 8px;
        }

        .product-name {
            font-size: 0.8rem;
        }

        .product-price {
            font-size: 1rem;
        }

        .cart-container {
            position: sticky;
            bottom: 0;
            background: white;
            z-index: 100;
            border-top: 2px solid #e9ecef;
            padding-top: 15px;
        }

        .cart-items {
            max-height: 30vh;
        }

        .cart-summary {
            padding: 15px;
            margin-bottom: 10px;
        }

        .payment-methods {
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .payment-method {
            padding: 10px;
            font-size: 0.9rem;
        }

        .payment-method i {
            font-size: 1.2rem;
        }

        .pos-actions {
            grid-template-columns: 1fr;
            gap: 8px;
        }

        .btn-checkout,
        .btn-clear {
            padding: 12px;
            font-size: 1rem;
        }

        .search-filters {
            padding: 10px;
        }

        .category-tabs {
            gap: 3px;
            margin-bottom: 10px;
        }

        .category-tab {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        .barcode-scanner input {
            font-size: 1rem;
            padding: 10px;
        }
    }

    /* Extra small screens */
    @media (max-width: 576px) {
        .product-grid {
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 8px;
        }

        .product-card {
            min-height: 90px;
            padding: 6px;
        }

        .product-name {
            font-size: 0.75rem;
        }

        .product-price {
            font-size: 0.9rem;
        }

        .payment-methods {
            grid-template-columns: 1fr;
        }

        .cart-summary {
            padding: 10px;
        }

        .summary-row {
            font-size: 0.9rem;
        }

        .summary-row.total {
            font-size: 1.1rem;
        }
    }

    /* Touch-friendly adjustments */
    @media (hover: none) and (pointer: coarse) {
        .product-card {
            min-height: 110px;
        }

        .quantity-btn {
            width: 35px;
            height: 35px;
            font-size: 1rem;
        }

        .payment-method {
            padding: 15px;
        }

        .btn-checkout,
        .btn-clear {
            padding: 15px;
            font-size: 1.1rem;
        }
    }

    /* RTL Adjustments */
    body[dir="rtl"] .pos-left-panel {
        border-right: none;
        border-left: 2px solid #e9ecef;
    }

    body[dir="rtl"] .product-card,
    body[dir="rtl"] .cart-item,
    body[dir="rtl"] .payment-method {
        text-align: right;
    }

    body[dir="rtl"] .quantity-controls {
        flex-direction: row-reverse;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="bi bi-shop"></i> {{ __('app.pos.title') }}
        </h1>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" id="fullscreenBtn">
                <i class="bi bi-fullscreen"></i> {{ __('app.pos.fullscreen') }}
            </button>
            <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#settingsModal">
                <i class="bi bi-gear"></i> {{ __('app.pos.settings') }}
            </button>
        </div>
    </div>

    <div class="row pos-container">
        <!-- Left Panel - Products -->
        <div class="col-lg-8 pos-left-panel">
            <!-- Search and Filters -->
            <div class="search-filters">
                <div class="row mb-3">
                    <div class="col-md-8">
                        <div class="barcode-scanner">
                            <input type="text"
                                   class="form-control scanner-input"
                                   id="barcodeInput"
                                   placeholder="{{ __('app.pos.scan_or_search') }}"
                                   autocomplete="off">
                            <div class="position-absolute top-50 end-0 translate-middle-y me-3">
                                <i class="bi bi-upc-scan text-primary"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="categoryFilter">
                            <option value="">{{ __('app.pos.all_categories') }}</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <!-- Category Tabs -->
                <div class="category-tabs">
                    <div class="category-tab active" data-category="">
                        <i class="bi bi-grid"></i> {{ __('app.pos.all') }}
                    </div>
                    @foreach($categories->take(6) as $category)
                        <div class="category-tab" data-category="{{ $category->id }}">
                            {{ $category->name }}
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Products Grid -->
            <div class="product-grid" id="productGrid">
                @foreach($products as $product)
                    <div class="product-card {{ $product->current_stock <= 0 ? 'out-of-stock' : '' }}"
                         data-product-id="{{ $product->id }}"
                         data-category="{{ $product->category_id }}"
                         data-name="{{ strtolower($product->name) }}"
                         data-barcode="{{ $product->barcode }}">
                        <div>
                            <div class="product-name">{{ $product->name }}</div>
                            <div class="product-price">{{ number_format($product->selling_price, 2) }} {{ __('app.currency') }}</div>
                        </div>
                        <div class="product-stock">
                            @if($product->current_stock > 0)
                                <i class="bi bi-check-circle text-success"></i> {{ $product->current_stock }} {{ __('app.pos.in_stock') }}
                            @else
                                <i class="bi bi-x-circle text-danger"></i> {{ __('app.pos.out_of_stock') }}
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Right Panel - Cart and Checkout -->
        <div class="col-lg-4 pos-right-panel">
            <div class="cart-container">
                <!-- Cart Header -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <i class="bi bi-cart"></i> {{ __('app.pos.cart') }}
                    </h5>
                    <span class="badge bg-primary" id="cartCount">0</span>
                </div>

                <!-- Cart Items -->
                <div class="cart-items" id="cartItems">
                    <div class="text-center text-muted p-4">
                        <i class="bi bi-cart display-4 opacity-50"></i>
                        <p class="mt-2">{{ __('app.pos.empty_cart') }}</p>
                    </div>
                </div>

                <!-- Cart Summary -->
                <div class="cart-summary">
                    <div class="summary-row">
                        <span>{{ __('app.pos.subtotal') }}:</span>
                        <span id="subtotal">0.00 {{ __('app.currency') }}</span>
                    </div>
                    <div class="summary-row">
                        <span>{{ __('app.pos.tax') }} (<span id="taxRate">15</span>%):</span>
                        <span id="taxAmount">0.00 {{ __('app.currency') }}</span>
                    </div>
                    <div class="summary-row">
                        <span>{{ __('app.pos.discount') }}:</span>
                        <span id="discountAmount">0.00 {{ __('app.currency') }}</span>
                    </div>
                    <div class="summary-row total">
                        <span>{{ __('app.pos.total') }}:</span>
                        <span id="totalAmount">0.00 {{ __('app.currency') }}</span>
                    </div>
                </div>

                <!-- Payment Methods -->
                <div class="payment-methods">
                    <div class="payment-method active" data-method="cash">
                        <i class="bi bi-cash-coin text-success"></i>
                        <div>{{ __('app.pos.cash') }}</div>
                    </div>
                    <div class="payment-method" data-method="card">
                        <i class="bi bi-credit-card text-primary"></i>
                        <div>{{ __('app.pos.card') }}</div>
                    </div>
                    <div class="payment-method" data-method="bank_transfer">
                        <i class="bi bi-bank text-info"></i>
                        <div>{{ __('app.pos.bank_transfer') }}</div>
                    </div>
                    <div class="payment-method" data-method="mobile_payment">
                        <i class="bi bi-phone text-warning"></i>
                        <div>{{ __('app.pos.mobile_payment') }}</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="pos-actions">
                    <button class="btn btn-clear" id="clearCartBtn">
                        <i class="bi bi-trash"></i> {{ __('app.pos.clear_cart') }}
                    </button>
                    <button class="btn btn-checkout" id="checkoutBtn" disabled>
                        <i class="bi bi-check-circle"></i> {{ __('app.pos.checkout') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Checkout Modal -->
@include('pos.modals.checkout')

<!-- Settings Modal -->
@include('pos.modals.settings')

<!-- Receipt Modal -->
@include('pos.modals.receipt')
@endsection

@push('scripts')
<script src="{{ asset('js/pos.js') }}"></script>
@endpush
